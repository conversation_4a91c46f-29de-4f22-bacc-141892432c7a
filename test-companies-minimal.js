const https = require('http');

// 测试登录接口
function testLogin() {
  const data = JSON.stringify({
    userCode: 'husky',
    password: '541888',
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
    },
  };

  const req = https.request(options, (res) => {
    console.log(`登录接口状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('登录响应:', JSON.stringify(response, null, 2));

        if (response.data && response.data.accessToken) {
          console.log('✅ 登录成功！');
          testCreateMinimalCompany(response.data.accessToken);
        } else {
          console.log('❌ 登录失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 创建最简单的公司（只有必须字段）
function testCreateMinimalCompany(token) {
  const timestamp = Date.now();
  const data = JSON.stringify({
    code: `COMP${timestamp}`,
    name: `测试公司${timestamp}`,
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/companies',
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Content-Length': data.length,
    },
  };

  const req = https.request(options, (res) => {
    console.log(`\n📝 创建最简公司状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('创建公司响应:', JSON.stringify(response, null, 2));

        if (response.code === 200) {
          console.log('✅ 创建最简公司成功！');
          const companyCode = JSON.parse(data).code;
          testGetCompanyDetail(token, companyCode);
        } else {
          console.log('❌ 创建最简公司失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 获取公司详情
function testGetCompanyDetail(token, companyCode) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: `/companies/${companyCode}`,
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };

  const req = https.request(options, (res) => {
    console.log(`\n🔍 获取公司详情状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('公司详情响应:', JSON.stringify(response, null, 2));

        if (response.code === 200) {
          console.log('✅ 获取公司详情成功！');
          console.log(`   公司名称: ${response.data.name}`);
          console.log(`   公司编码: ${response.data.code}`);
          console.log(
            `   店长: ${response.data.manager ? response.data.manager.nickname : '无'}`,
          );
          console.log(
            `   员工数量: ${response.data.employees ? response.data.employees.length : 0}`,
          );
          console.log('\n🎉 最简公司接口测试完成！');
        } else {
          console.log('❌ 获取公司详情失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

console.log('🚀 开始测试最简公司接口...');
testLogin();
