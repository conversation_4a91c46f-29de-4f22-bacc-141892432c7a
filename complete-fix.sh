#!/bin/bash

# Commission Contracts 完整修复脚本

set -e

echo "开始完整修复commission-contracts问题..."

# 1. 回滚失败的迁移
echo "1. 回滚失败的迁移..."
npm run migration:revert || echo "没有需要回滚的迁移"

# 2. 直接执行SQL修复数据库
echo "2. 执行数据库修复..."
PGPASSWORD=54188 psql -h 43.138.236.92 -U postgres -d manager << 'EOF'
-- 清理问题字段
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='paymentStatus') THEN
        ALTER TABLE commission_contracts DROP COLUMN "paymentStatus";
    END IF;
    DROP TYPE IF EXISTS commission_contracts_paymentstatus_enum;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Cleanup error: %', SQLERRM;
END $$;

-- 创建枚举类型
CREATE TYPE commission_contracts_paymentstatus_enum AS ENUM('unpaid', 'partial_paid', 'fully_paid', 'overdue');

-- 添加字段
ALTER TABLE commission_contracts ADD COLUMN "paymentStatus" commission_contracts_paymentstatus_enum NOT NULL DEFAULT 'unpaid';

-- 添加其他字段
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='paidAmount') THEN
        ALTER TABLE commission_contracts ADD COLUMN "paidAmount" numeric(15,2) NOT NULL DEFAULT 0;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='remainingDebtAmount') THEN
        ALTER TABLE commission_contracts ADD COLUMN "remainingDebtAmount" numeric(15,2) NOT NULL DEFAULT 0;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='lastPaymentDate') THEN
        ALTER TABLE commission_contracts ADD COLUMN "lastPaymentDate" date;
    END IF;
END $$;

-- 初始化数据
UPDATE commission_contracts SET "remainingDebtAmount" = "requestedDebtAmount" WHERE "remainingDebtAmount" = 0;

-- 添加注释
COMMENT ON COLUMN commission_contracts."paymentStatus" IS '还款状态';
COMMENT ON COLUMN commission_contracts."paidAmount" IS '已还款金额';
COMMENT ON COLUMN commission_contracts."remainingDebtAmount" IS '剩余欠款金额';
COMMENT ON COLUMN commission_contracts."lastPaymentDate" IS '最后还款日期';
EOF

# 3. 标记迁移为已完成
echo "3. 标记迁移为已完成..."
PGPASSWORD=54188 psql -h 43.138.236.92 -U postgres -d manager -c "INSERT INTO migrations (timestamp, name) VALUES (1749872208311, 'AddMissingCommissionContractFields1749872208311') ON CONFLICT DO NOTHING;"

# 4. 检查并安装Puppeteer依赖
echo "4. 检查Puppeteer依赖..."
if ! node -e "require('puppeteer')" 2>/dev/null; then
    echo "重新安装Puppeteer..."
    npm install puppeteer --save
fi

# 5. 检查系统依赖
echo "5. 检查系统依赖..."
if ! command -v google-chrome &> /dev/null && ! command -v chromium &> /dev/null; then
    echo "安装Chrome依赖..."
    yum update -y
    yum install -y \
        alsa-lib.x86_64 \
        atk.x86_64 \
        cups-libs.x86_64 \
        gtk3.x86_64 \
        libXcomposite.x86_64 \
        libXcursor.x86_64 \
        libXdamage.x86_64 \
        libXext.x86_64 \
        libXi.x86_64 \
        libXrandr.x86_64 \
        libXScrnSaver.x86_64 \
        libXtst.x86_64 \
        pango.x86_64 \
        xorg-x11-fonts-100dpi \
        xorg-x11-fonts-75dpi \
        xorg-x11-fonts-cyrillic \
        xorg-x11-fonts-misc \
        xorg-x11-fonts-Type1 \
        xorg-x11-utils
        
    # 下载并安装Chrome
    wget https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
    yum localinstall -y google-chrome-stable_current_x86_64.rpm
    rm -f google-chrome-stable_current_x86_64.rpm
fi

# 6. 重新构建项目
echo "6. 重新构建项目..."
npm run build

# 7. 重启服务
echo "7. 重启服务..."
pm2 restart backend

# 8. 等待服务启动
echo "8. 等待服务启动..."
sleep 10

# 9. 检查服务状态
echo "9. 检查服务状态..."
pm2 status

# 10. 验证数据库字段
echo "10. 验证数据库字段..."
PGPASSWORD=54188 psql -h 43.138.236.92 -U postgres -d manager -c "
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'commission_contracts' 
AND column_name IN ('paymentStatus', 'paidAmount', 'remainingDebtAmount', 'lastPaymentDate')
ORDER BY column_name;
"

echo "修复完成！"
echo ""
echo "修复内容："
echo "- 修复了数据库字段缺失问题"
echo "- 修复了代码中的空值访问问题"
echo "- 安装了PDF生成所需的系统依赖"
echo "- 重新安装了Puppeteer"
echo "- 添加了更好的错误处理和日志"
echo ""
echo "请测试commission-contracts接口和PDF生成功能。"
