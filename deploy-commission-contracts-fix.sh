#!/bin/bash

# Commission Contracts 500错误修复部署脚本
# 此脚本将修复commission-contracts接口的500错误问题

set -e

echo "开始修复commission-contracts接口500错误..."

# 1. 构建项目
echo "1. 构建项目..."
npm run build

# 2. 运行数据库迁移
echo "2. 运行数据库迁移..."
npm run migration:run

# 3. 重启PM2服务
echo "3. 重启PM2服务..."
pm2 restart backend

# 4. 等待服务启动
echo "4. 等待服务启动..."
sleep 5

# 5. 检查服务状态
echo "5. 检查服务状态..."
pm2 status

echo "修复完成！"
echo ""
echo "修复内容："
echo "- 添加了缺失的数据库字段：paymentStatus, paidAmount, remainingDebtAmount, lastPaymentDate"
echo "- 修复了calculatePaymentStatus方法中的空值访问问题"
echo "- 添加了安全的字段访问逻辑"
echo ""
echo "请测试commission-contracts接口是否正常工作。"
