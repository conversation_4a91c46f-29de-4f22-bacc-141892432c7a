#!/bin/bash

# ========================================
# 快速部署脚本
# ========================================
# 用于快速部署到服务器

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 服务器信息
SERVER_IP="*************"
SERVER_USER="root"
PROJECT_PATH="/var/www/web-manager-backend"

log_info "开始快速部署到服务器 $SERVER_IP"

# 1. 上传代码到服务器
log_info "上传代码到服务器..."
rsync -avz --exclude 'node_modules' --exclude 'dist' --exclude '.git' --exclude 'logs' \
    ./ $SERVER_USER@$SERVER_IP:$PROJECT_PATH/

# 2. 在服务器上执行部署
log_info "在服务器上执行部署..."
ssh $SERVER_USER@$SERVER_IP "cd $PROJECT_PATH && chmod +x deploy-production.sh && ./deploy-production.sh update"

log_success "部署完成！"
echo
echo "访问地址:"
echo "API文档: http://$SERVER_IP:3000/api"
echo "通过Nginx: http://$SERVER_IP/api/"
