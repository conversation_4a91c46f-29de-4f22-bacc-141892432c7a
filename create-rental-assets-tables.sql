-- 创建租赁资产表
CREATE TABLE IF NOT EXISTS rental_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "totalAmount" DECIMAL(15,2) NOT NULL DEFAULT 0 COMMENT '租赁资产总金额',
    "isDeleted" BOOLEAN NOT NULL DEFAULT false COMMENT '是否已删除',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    "deletedAt" TIMESTAMP NULL COMMENT '删除时间'
);

-- 创建租赁资产类型枚举
CREATE TYPE rental_asset_type AS ENUM (
    'short_term',
    'long_term'
);

-- 创建租赁资产明细表
CREATE TABLE IF NOT EXISTS rental_asset_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "rentalAssetId" UUID NOT NULL COMMENT '租赁资产ID',
    type rental_asset_type NOT NULL COMMENT '租赁资产类型',
    amount DECIMAL(15,2) NOT NULL COMMENT '明细金额（保留两位小数）',
    screenshot VARCHAR(500) NOT NULL COMMENT '截图URL（必填）',
    remark TEXT NULL COMMENT '备注（可选）',
    "isDeleted" BOOLEAN NOT NULL DEFAULT false COMMENT '是否已删除',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    "deletedAt" TIMESTAMP NULL COMMENT '删除时间',
    
    -- 外键约束
    CONSTRAINT fk_rental_asset_details_rental_asset 
        FOREIGN KEY ("rentalAssetId") REFERENCES rental_assets(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_rental_asset_details_rental_asset_id 
    ON rental_asset_details("rentalAssetId");
CREATE INDEX IF NOT EXISTS idx_rental_asset_details_type 
    ON rental_asset_details(type);
CREATE INDEX IF NOT EXISTS idx_rental_asset_details_created_at 
    ON rental_asset_details("createdAt");
CREATE INDEX IF NOT EXISTS idx_rental_asset_details_is_deleted 
    ON rental_asset_details("isDeleted");

-- 添加注释
COMMENT ON TABLE rental_assets IS '租赁资产表';
COMMENT ON TABLE rental_asset_details IS '租赁资产明细表';
COMMENT ON TYPE rental_asset_type IS '租赁资产类型：short_term=短期租赁, long_term=长期租赁';
