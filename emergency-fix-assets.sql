-- ========================================
-- 紧急修复运营资产模块数据库字段名问题
-- ========================================

\echo '🚨 开始紧急修复运营资产模块数据库字段名问题...'

-- 检查当前字段名状态
\echo '📋 检查当前字段名状态...'
SELECT
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
    AND table_name IN ('operating_assets', 'operating_asset_details')
    AND column_name IN ('createDate', 'create_date', 'createdAt', 'warehouseLogisticsType', 'warehouselogisticstype')
ORDER BY table_name, column_name;

\echo ''

-- 1. 修复 operating_assets 表的 createDate 字段
\echo '修复 operating_assets 表字段名...'
DO $$
BEGIN
    -- 检查并修复 create_date -> createdAt
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_assets' AND column_name = 'create_date') THEN
        ALTER TABLE operating_assets RENAME COLUMN create_date TO "createdAt";
        RAISE NOTICE 'operating_assets.create_date 已重命名为 createdAt';
    ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_assets' AND column_name = 'createDate') THEN
        ALTER TABLE operating_assets RENAME COLUMN "createDate" TO "createdAt";
        RAISE NOTICE 'operating_assets.createDate 已重命名为 createdAt';
    ELSE
        RAISE NOTICE 'operating_assets 表中未找到 create_date 或 createDate 字段';
    END IF;
END $$;

-- 2. 修复 operating_asset_details 表的字段
\echo '修复 operating_asset_details 表字段名...'
DO $$
BEGIN
    -- 检查并修复 create_date -> createdAt
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'create_date') THEN
        ALTER TABLE operating_asset_details RENAME COLUMN create_date TO "createdAt";
        RAISE NOTICE 'operating_asset_details.create_date 已重命名为 createdAt';
    ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'createDate') THEN
        ALTER TABLE operating_asset_details RENAME COLUMN "createDate" TO "createdAt";
        RAISE NOTICE 'operating_asset_details.createDate 已重命名为 createdAt';
    ELSE
        RAISE NOTICE 'operating_asset_details 表中未找到 create_date 或 createDate 字段';
    END IF;

    -- 检查并修复 warehouseLogisticsType -> warehouselogisticstype
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'warehouseLogisticsType') THEN
        ALTER TABLE operating_asset_details RENAME COLUMN "warehouseLogisticsType" TO warehouselogisticstype;
        RAISE NOTICE 'operating_asset_details.warehouseLogisticsType 已重命名为 warehouselogisticstype';
    ELSE
        RAISE NOTICE 'operating_asset_details 表中未找到 warehouseLogisticsType 字段';
    END IF;
END $$;

-- 3. 检查并创建缺失的枚举类型和字段
\echo '检查并创建缺失的枚举类型和字段...'
DO $$
BEGIN
    -- 检查并创建 warehouse_logistics_type_enum 枚举类型
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'warehouse_logistics_type_enum') THEN
        CREATE TYPE warehouse_logistics_type_enum AS ENUM('income', 'expense');
        RAISE NOTICE '已创建 warehouse_logistics_type_enum 枚举类型';
    ELSE
        RAISE NOTICE 'warehouse_logistics_type_enum 枚举类型已存在';
    END IF;

    -- 检查并创建 warehouselogisticstype 字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'warehouselogisticstype') THEN
        ALTER TABLE operating_asset_details
        ADD COLUMN warehouselogisticstype warehouse_logistics_type_enum NULL;

        COMMENT ON COLUMN operating_asset_details.warehouselogisticstype
        IS '仓储物流收支类型（仅仓储物流类型需要）';

        RAISE NOTICE '已创建 operating_asset_details.warehouselogisticstype 字段';
    ELSE
        RAISE NOTICE 'operating_asset_details.warehouselogisticstype 字段已存在';
    END IF;

    -- 检查并创建 createdAt 字段（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_assets' AND column_name = 'createdAt') THEN
        ALTER TABLE operating_assets
        ADD COLUMN "createdAt" VARCHAR(20) NULL;

        COMMENT ON COLUMN operating_assets."createdAt"
        IS '创建日期（字符串格式，必填）';

        RAISE NOTICE '已创建 operating_assets.createdAt 字段';
    ELSE
        RAISE NOTICE 'operating_assets.createdAt 字段已存在';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'createdAt') THEN
        ALTER TABLE operating_asset_details
        ADD COLUMN "createdAt" VARCHAR(20) NULL;

        COMMENT ON COLUMN operating_asset_details."createdAt"
        IS '创建日期（字符串格式，必填）';

        RAISE NOTICE '已创建 operating_asset_details.createdAt 字段';
    ELSE
        RAISE NOTICE 'operating_asset_details.createdAt 字段已存在';
    END IF;
END $$;

-- 4. 验证修复结果
\echo ''
\echo '📋 验证修复结果...'
SELECT
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
    AND table_name IN ('operating_assets', 'operating_asset_details')
    AND column_name IN ('createdAt', 'warehouselogisticstype', 'operatingAssetId')
ORDER BY table_name, column_name;

\echo ''
\echo '📋 检查枚举类型...'
SELECT
    t.typname as enum_name,
    e.enumlabel as enum_value
FROM pg_type t
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE t.typname = 'warehouse_logistics_type_enum'
ORDER BY e.enumsortorder;

\echo ''
\echo '📋 检查表结构完整性...'
SELECT
    table_name,
    COUNT(*) as column_count
FROM information_schema.columns
WHERE table_schema = 'public'
    AND table_name IN ('operating_assets', 'operating_asset_details')
GROUP BY table_name
ORDER BY table_name;

\echo ''
\echo '✅ 运营资产模块数据库修复完成！'
\echo ''
\echo '🔧 修复内容总结：'
\echo '   1. 统一字段名为实体映射要求的格式'
\echo '   2. 确保 warehouselogisticstype 字段存在且类型正确'
\echo '   3. 确保 createdAt 字段存在且类型正确'
\echo '   4. 创建必要的枚举类型'
\echo ''
\echo '⚠️  请重启应用程序以使更改生效'
