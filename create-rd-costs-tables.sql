-- 创建研发成本表
CREATE TABLE IF NOT EXISTS rd_costs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "totalAmount" DECIMAL(15,2) NOT NULL DEFAULT 0 COMMENT '研发成本总金额',
    "isDeleted" BOOLEAN NOT NULL DEFAULT false COMMENT '是否已删除',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    "deletedAt" TIMESTAMP NULL COMMENT '删除时间'
);

-- 创建研发成本明细表
CREATE TABLE IF NOT EXISTS rd_cost_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "rdCostId" UUID NOT NULL COMMENT '研发成本ID',
    amount DECIMAL(15,2) NOT NULL COMMENT '明细金额（保留两位小数）',
    screenshot VARCHAR(500) NOT NULL COMMENT '截图URL（必填）',
    remark TEXT NULL COMMENT '备注（可选）',
    "isDeleted" BOOLEAN NOT NULL DEFAULT false COMMENT '是否已删除',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    "deletedAt" TIMESTAMP NULL COMMENT '删除时间',
    
    -- 外键约束
    CONSTRAINT fk_rd_cost_details_rd_cost 
        FOREIGN KEY ("rdCostId") REFERENCES rd_costs(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_rd_cost_details_rd_cost_id 
    ON rd_cost_details("rdCostId");
CREATE INDEX IF NOT EXISTS idx_rd_cost_details_created_at 
    ON rd_cost_details("createdAt");
CREATE INDEX IF NOT EXISTS idx_rd_cost_details_is_deleted 
    ON rd_cost_details("isDeleted");

-- 添加注释
COMMENT ON TABLE rd_costs IS '研发成本表';
COMMENT ON TABLE rd_cost_details IS '研发成本明细表';
