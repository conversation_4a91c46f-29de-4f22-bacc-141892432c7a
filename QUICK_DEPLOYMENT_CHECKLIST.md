# ⚡ 快速部署检查清单

## 🚨 重要提醒

**❌ 不要直接上传文件到服务器！**
**✅ 请按照以下步骤安全部署！**

---

## 📋 部署前必做检查

### 1. 环境准备 (5分钟)
```bash
# 检查Node.js版本
node --version  # 需要 >= 16.x

# 检查PostgreSQL
psql --version  # 需要 >= 12.x

# 检查数据库连接
psql -h localhost -U postgres -d manager -c "SELECT version();"
```

### 2. 代码验证 (3分钟)
```bash
# 运行验证脚本
npm run verify:deployment

# 构建项目
npm run build

# 检查构建结果
ls -la dist/
```

### 3. 数据库备份 (2分钟) ⚠️ 必须执行
```bash
# 创建备份
pg_dump -h localhost -U postgres -d manager > backup_$(date +%Y%m%d_%H%M%S).sql

# 验证备份文件
ls -lh backup_*.sql
```

---

## 🚀 部署步骤

### 步骤1: 停止现有服务 (1分钟)
```bash
# 如果使用PM2
pm2 stop all

# 如果使用systemd
sudo systemctl stop manager-api
```

### 步骤2: 部署代码 (5分钟)
```bash
# 方式A: Git部署 (推荐)
git pull origin main
npm install --production
npm run build

# 方式B: 文件上传 (不推荐)
# 上传压缩包并解压
# tar -xzf app.tar.gz
# npm install --production
```

### 步骤3: 执行数据库迁移 (3分钟) ⚠️ 关键步骤
```bash
# 查看待执行的迁移
npm run migration:show

# 执行迁移
npm run migration:run

# 验证迁移结果
npm run verify:migration
```

### 步骤4: 启动服务 (2分钟)
```bash
# 使用PM2启动
pm2 start ecosystem.config.js

# 或使用systemd启动
sudo systemctl start manager-api
```

### 步骤5: 验证部署 (3分钟)
```bash
# 检查服务状态
pm2 status
# 或
sudo systemctl status manager-api

# 测试API接口
curl http://localhost:3000/health
curl http://localhost:3000/products?page=1&pageSize=5
curl http://localhost:3000/inventory?page=1&pageSize=5
```

---

## ✅ 成功标志

部署成功的标志：
- [ ] ✅ 应用正常启动 (pm2 status 显示 online)
- [ ] ✅ 健康检查通过 (curl /health 返回 200)
- [ ] ✅ 商品接口正常 (curl /products 返回数据)
- [ ] ✅ 库存接口正常 (curl /inventory 返回数据)
- [ ] ✅ 数据库迁移完成 (migration:show 无待执行迁移)
- [ ] ✅ 库存记录已生成 (verify:migration 显示库存数据)

---

## 🚨 常见问题快速解决

### 问题1: 迁移失败
```bash
# 查看错误详情
npm run migration:show

# 回滚迁移
npm run migration:revert

# 重新执行
npm run migration:run
```

### 问题2: 应用启动失败
```bash
# 查看错误日志
pm2 logs manager-api --lines 50

# 检查端口占用
netstat -tlnp | grep :3000

# 检查环境变量
cat .env
```

### 问题3: 数据库连接失败
```bash
# 检查数据库服务
sudo systemctl status postgresql

# 测试连接
psql -h localhost -U postgres -d manager

# 检查配置文件
cat .env | grep DB_
```

### 问题4: 库存数据未生成
```bash
# 检查商品数据
psql -h localhost -U postgres -d manager -c "SELECT COUNT(*) FROM products WHERE \"isDeleted\" = false;"

# 检查库存数据
psql -h localhost -U postgres -d manager -c "SELECT COUNT(*) FROM inventory_details WHERE \"isDeleted\" = false;"

# 手动触发迁移
npm run migration:revert
npm run migration:run
```

---

## 🔄 紧急回滚

如果部署失败，立即执行：

```bash
# 1. 停止应用
pm2 stop all

# 2. 回滚数据库
npm run migration:revert
npm run migration:revert

# 3. 恢复数据库备份
psql -h localhost -U postgres -d manager < backup_YYYYMMDD_HHMMSS.sql

# 4. 回滚代码
git checkout HEAD~1  # 或切换到上一个稳定版本

# 5. 重新构建启动
npm run build
pm2 start ecosystem.config.js
```

---

## 📞 部署支持

### 验证命令汇总
```bash
# 部署前验证
npm run verify:deployment

# 迁移后验证
npm run verify:migration

# 服务状态检查
pm2 status
curl http://localhost:3000/health

# 数据完整性检查
psql -h localhost -U postgres -d manager -c "
SELECT 
  (SELECT COUNT(*) FROM products WHERE \"isDeleted\" = false) as products,
  (SELECT COUNT(*) FROM inventory_details WHERE \"isDeleted\" = false) as inventory,
  (SELECT COUNT(*) FROM colors WHERE \"isDeleted\" = false) as colors;
"
```

### 监控命令
```bash
# 实时日志
pm2 logs manager-api --lines 100 -f

# 系统资源
pm2 monit

# 数据库连接
psql -h localhost -U postgres -d manager -c "SELECT count(*) FROM pg_stat_activity WHERE datname = 'manager';"
```

---

## 🎯 部署时间预估

- **准备阶段**: 10分钟
- **部署阶段**: 15分钟
- **验证阶段**: 5分钟
- **总计**: 约30分钟

**记住**: 宁可多花时间验证，也不要匆忙部署！ 🛡️

---

## 📋 部署完成报告模板

```
部署完成报告
================
部署时间: $(date)
部署版本: $(git rev-parse HEAD)
数据库备份: backup_YYYYMMDD_HHMMSS.sql

验证结果:
- [ ] 应用启动: ✅/❌
- [ ] API健康检查: ✅/❌  
- [ ] 数据库迁移: ✅/❌
- [ ] 库存数据生成: ✅/❌
- [ ] 接口功能测试: ✅/❌

问题记录:
- 无问题 / 记录具体问题

下次改进:
- 记录改进建议
```
