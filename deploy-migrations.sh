#!/bin/bash

# 数据库迁移部署脚本
# 使用方法: ./deploy-migrations.sh [backup|reset]

set -e  # 遇到错误立即退出

DB_HOST="localhost"
DB_USER="postgres"
DB_NAME="manager"
DB_PASSWORD="54188"
BACKUP_DIR="./backups"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 备份数据库
backup_database() {
    echo_info "开始备份数据库..."
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > $BACKUP_FILE
    echo_info "数据库备份完成: $BACKUP_FILE"
}

# 检查迁移状态
check_migrations() {
    echo_info "检查迁移状态..."
    npm run migration:show
}

# 执行迁移
run_migrations() {
    echo_info "开始执行数据库迁移..."
    npm run migration:run
    echo_info "数据库迁移完成"
}

# 重置数据库（危险操作）
reset_database() {
    echo_warn "⚠️  警告：这将删除所有数据库数据！"
    read -p "确认要重置数据库吗？输入 'YES' 确认: " confirm
    if [ "$confirm" = "YES" ]; then
        echo_info "重置数据库..."
        PGPASSWORD=$DB_PASSWORD dropdb -h $DB_HOST -U $DB_USER $DB_NAME || true
        PGPASSWORD=$DB_PASSWORD createdb -h $DB_HOST -U $DB_USER $DB_NAME
        npm run migration:run
        echo_info "数据库重置完成"
    else
        echo_info "取消重置操作"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    echo_info "验证部署结果..."
    
    # 检查客户表结构
    echo_info "检查客户表结构..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "\d customers"
    
    # 检查是否有数据
    echo_info "检查表数据..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT COUNT(*) as customer_count FROM customers;"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT COUNT(*) as income_count FROM incomes;"
    
    echo_info "部署验证完成"
}

# 主逻辑
case "${1:-backup}" in
    "backup")
        echo_info "=== 安全部署模式 ==="
        backup_database
        check_migrations
        run_migrations
        verify_deployment
        ;;
    "reset")
        echo_info "=== 重置部署模式 ==="
        backup_database
        reset_database
        verify_deployment
        ;;
    *)
        echo_error "使用方法: $0 [backup|reset]"
        echo_info "  backup: 安全部署（推荐）- 备份后执行迁移"
        echo_info "  reset:  重置部署 - 删除数据库重新创建"
        exit 1
        ;;
esac

echo_info "🎉 部署完成！"
echo_info "应用地址: http://127.0.0.1:8080"
echo_info "API文档: http://127.0.0.1:8080/api"
