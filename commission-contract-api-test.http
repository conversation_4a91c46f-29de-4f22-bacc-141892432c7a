### 测试佣金合同接口优化后的功能

### 1. 创建佣金合同 - 使用新的日期格式
POST http://localhost:3000/commission-contracts
Content-Type: application/json

{
  "companyName": "广州莱了贸易有限公司",
  "partyBName": "张三",
  "partyBIdCard": "110101199001011234",
  "applicationDate": "2025-06-25",
  "repaymentDate": "2026-12-25",
  "actualShipmentAmount": 12000.00,
  "requestedDebtAmount": 3000.00,
  "totalAccumulatedDebt": 8000.00,
  "brandDetails": [
    {
      "brandName": "PRLUO VALENTIN",
      "quantity": 25,
      "amount": 5000.00
    },
    {
      "brandName": "Nike",
      "quantity": 15,
      "amount": 3000.00
    }
  ]
}

### 2. 分页查询 - 使用新的日期范围参数
GET http://localhost:3000/commission-contracts?page=1&pageSize=10&applicationStartDate=2025-01-01&applicationEndDate=2025-12-31

### 3. 分页查询 - 使用归还日期范围
GET http://localhost:3000/commission-contracts?page=1&pageSize=10&repaymentStartDate=2025-01-01&repaymentEndDate=2026-12-31

### 4. 分页查询 - 组合查询
GET http://localhost:3000/commission-contracts?page=1&pageSize=10&applicationStartDate=2025-06-01&applicationEndDate=2025-06-30&repaymentStartDate=2026-01-01&repaymentEndDate=2026-12-31&status=draft

### 5. 查询单个合同详情
GET http://localhost:3000/commission-contracts/{{contractId}}

### 6. 更新合同 - 使用新的日期格式
PATCH http://localhost:3000/commission-contracts/{{contractId}}
Content-Type: application/json

{
  "applicationDate": "2025-07-01",
  "repaymentDate": "2027-01-01",
  "actualShipmentAmount": 15000.00,
  "remarks": "更新测试"
}

### 测试说明：
# 1. 创建合同时使用标准日期格式 YYYY-MM-DD
# 2. 查询时使用日期范围参数，更加直观
# 3. 返回数据包含标准日期格式和中文显示格式
# 4. 更新时也使用标准日期格式

### 预期结果：
# - 创建成功，返回 code: 200
# - 查询返回的数据包含 applicationDate 和 applicationDateDisplay 字段
# - 查询返回的数据包含 repaymentDate 和 repaymentDateDisplay 字段
# - 日期范围查询能正确筛选数据
# - 更新操作能正确处理日期字段
