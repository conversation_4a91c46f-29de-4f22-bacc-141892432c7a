-- 创建测试数据脚本
-- 用于演示销售需求订单到商品采购订单系统

-- 1. 先创建测试公司
INSERT INTO companies (code, name, address, "isDeleted") VALUES
('COMP001', '测试服装公司', '北京市朝阳区测试大街1号', false)
ON CONFLICT (code) DO NOTHING;

-- 2. 创建测试用户（销售人员、生产人员等）
INSERT INTO users (code, nickname, password, "routePermissions", "isSuperAdmin", "isCompanyAdmin", "companyCode", "isActive", "isDeleted") VALUES
('SALES001', '销售员张三', '$2b$10$uslnDBRpZZD9JG2iWJr3oeYpWj44QVDqI5dKuVOxGu8.ZLMy.XZFC', '[]', false, false, 'COMP001', true, false),
('SALES002', '销售员李四', '$2b$10$uslnDBRpZZD9JG2iWJr3oeYpWj44QVDqI5dKuVOxGu8.ZLMy.XZFC', '[]', false, false, 'COMP001', true, false),
('PROD001', '生产主管王五', '$2b$10$uslnDBRpZZD9JG2iWJr3oeYpWj44QVDqI5dKuVOxGu8.ZLMy.XZFC', '[]', false, false, 'COMP001', true, false),
('MANAGER001', '销售经理赵六', '$2b$10$uslnDBRpZZD9JG2iWJr3oeYpWj44QVDqI5dKuVOxGu8.ZLMy.XZFC', '[]', false, false, 'COMP001', true, false)
ON CONFLICT (code) DO NOTHING;

-- 3. 创建测试客户
INSERT INTO customers (code, name, "pinyinCode", phone, address, province, "isDeleted") VALUES
('CUST001', '北京时尚服饰有限公司', 'BJSSFSLXGS', '13800138001', '北京市朝阳区时尚大街1号', '北京市', false),
('CUST002', '上海潮流服装店', 'SHCLZFD', '13800138002', '上海市黄浦区潮流路88号', '上海市', false),
('CUST003', '广州服装批发市场', 'GZFZPFSC', '13800138003', '广州市白云区批发大道168号', '广东省', false)
ON CONFLICT (code) DO NOTHING;

-- 4. 创建测试供应商
INSERT INTO suppliers (code, name, phone, address, "isDeleted") VALUES
('SUP001', '东莞制衣厂', '13900139001', '东莞市虎门镇制衣工业园', false),
('SUP002', '佛山纺织有限公司', '13900139002', '佛山市南海区纺织城', false),
('SUP003', '杭州丝绸制品厂', '13900139003', '杭州市西湖区丝绸路99号', false)
ON CONFLICT (code) DO NOTHING;

-- 5. 创建测试品牌
INSERT INTO brands (code, name, "orderPrice", "isDeleted") VALUES
('BRAND001', '时尚前线', 0, false),
('BRAND002', '都市丽人', 0, false),
('BRAND003', '青春活力', 0, false)
ON CONFLICT (code) DO NOTHING;

-- 6. 创建测试商品分类
INSERT INTO product_categories (code, name, sizes, "isDeleted") VALUES
('CAT001', '女装上衣', '{"XS","S","M","L","XL","XXL"}', false),
('CAT002', '男装T恤', '{"S","M","L","XL","XXL","XXXL"}', false),
('CAT003', '休闲裤装', '{"28","29","30","31","32","33","34","35","36"}', false)
ON CONFLICT (code) DO NOTHING;

-- 7. 创建测试颜色
INSERT INTO colors (code, name, "isDeleted") VALUES
('COLOR001', '经典黑', false),
('COLOR002', '纯净白', false),
('COLOR003', '海军蓝', false),
('COLOR004', '玫瑰红', false),
('COLOR005', '薄荷绿', false)
ON CONFLICT (code) DO NOTHING;

-- 8. 创建测试商品
INSERT INTO products (code, name, "categoryCode", "brandCode", "supplierCode", "clothingCost", "retailPrice", "colorSizeCombinations", "isDeleted") VALUES
('PROD001', '时尚女士衬衫', 'CAT001', 'BRAND001', 'SUP001', 150.00, 299.00,
'[
  {
    "colorCode": "COLOR001",
    "colorName": "经典黑",
    "sizes": ["S", "M", "L", "XL"],
    "images": ["https://example.com/shirt_black.jpg"],
    "accessories": []
  },
  {
    "colorCode": "COLOR002",
    "colorName": "纯净白",
    "sizes": ["S", "M", "L", "XL"],
    "images": ["https://example.com/shirt_white.jpg"],
    "accessories": []
  }
]', false),
('PROD002', '男士休闲T恤', 'CAT002', 'BRAND002', 'SUP002', 80.00, 159.00,
'[
  {
    "colorCode": "COLOR003",
    "colorName": "海军蓝",
    "sizes": ["M", "L", "XL", "XXL"],
    "images": ["https://example.com/tshirt_navy.jpg"],
    "accessories": []
  },
  {
    "colorCode": "COLOR001",
    "colorName": "经典黑",
    "sizes": ["M", "L", "XL", "XXL"],
    "images": ["https://example.com/tshirt_black.jpg"],
    "accessories": []
  }
]', false),
('PROD003', '女士休闲裤', 'CAT003', 'BRAND003', 'SUP003', 200.00, 399.00,
'[
  {
    "colorCode": "COLOR004",
    "colorName": "玫瑰红",
    "sizes": ["28", "29", "30", "31", "32"],
    "images": ["https://example.com/pants_pink.jpg"],
    "accessories": []
  },
  {
    "colorCode": "COLOR005",
    "colorName": "薄荷绿",
    "sizes": ["28", "29", "30", "31", "32"],
    "images": ["https://example.com/pants_green.jpg"],
    "accessories": []
  }
]', false)
ON CONFLICT (code) DO NOTHING;

-- 9. 创建测试库存数据
INSERT INTO inventory_details ("skuCode", "productCode", "colorCode", "sizeCode", "totalStock", "actualStock", "purchasingStock", "needPurchaseStock", "isDeleted") VALUES
-- PROD001 库存
('PROD001-COLOR001-S', 'PROD001', 'COLOR001', 'S', 50, 45, 0, 0, false),
('PROD001-COLOR001-M', 'PROD001', 'COLOR001', 'M', 80, 75, 0, 0, false),
('PROD001-COLOR001-L', 'PROD001', 'COLOR001', 'L', 60, 55, 0, 0, false),
('PROD001-COLOR001-XL', 'PROD001', 'COLOR001', 'XL', 40, 35, 0, 0, false),
('PROD001-COLOR002-S', 'PROD001', 'COLOR002', 'S', 30, 25, 0, 0, false),
('PROD001-COLOR002-M', 'PROD001', 'COLOR002', 'M', 70, 65, 0, 0, false),
('PROD001-COLOR002-L', 'PROD001', 'COLOR002', 'L', 50, 45, 0, 0, false),
('PROD001-COLOR002-XL', 'PROD001', 'COLOR002', 'XL', 20, 15, 0, 0, false),

-- PROD002 库存
('PROD002-COLOR003-M', 'PROD002', 'COLOR003', 'M', 100, 95, 0, 0, false),
('PROD002-COLOR003-L', 'PROD002', 'COLOR003', 'L', 120, 110, 0, 0, false),
('PROD002-COLOR003-XL', 'PROD002', 'COLOR003', 'XL', 80, 70, 0, 0, false),
('PROD002-COLOR003-XXL', 'PROD002', 'COLOR003', 'XXL', 60, 50, 0, 0, false),
('PROD002-COLOR001-M', 'PROD002', 'COLOR001', 'M', 90, 85, 0, 0, false),
('PROD002-COLOR001-L', 'PROD002', 'COLOR001', 'L', 110, 100, 0, 0, false),
('PROD002-COLOR001-XL', 'PROD002', 'COLOR001', 'XL', 70, 60, 0, 0, false),
('PROD002-COLOR001-XXL', 'PROD002', 'COLOR001', 'XXL', 50, 40, 0, 0, false),

-- PROD003 库存（故意设置较低库存以演示缺货情况）
('PROD003-COLOR004-28', 'PROD003', 'COLOR004', '28', 20, 15, 0, 0, false),
('PROD003-COLOR004-29', 'PROD003', 'COLOR004', '29', 25, 20, 0, 0, false),
('PROD003-COLOR004-30', 'PROD003', 'COLOR004', '30', 30, 25, 0, 0, false),
('PROD003-COLOR004-31', 'PROD003', 'COLOR004', '31', 15, 10, 0, 0, false),
('PROD003-COLOR004-32', 'PROD003', 'COLOR004', '32', 10, 5, 0, 0, false),
('PROD003-COLOR005-28', 'PROD003', 'COLOR005', '28', 18, 12, 0, 0, false),
('PROD003-COLOR005-29', 'PROD003', 'COLOR005', '29', 22, 18, 0, 0, false),
('PROD003-COLOR005-30', 'PROD003', 'COLOR005', '30', 28, 22, 0, 0, false),
('PROD003-COLOR005-31', 'PROD003', 'COLOR005', '31', 12, 8, 0, 0, false),
('PROD003-COLOR005-32', 'PROD003', 'COLOR005', '32', 8, 3, 0, 0, false)
ON CONFLICT ("skuCode") DO NOTHING;

-- 输出创建完成信息
SELECT 'Test data created successfully! You can now test the demand-purchase order system.' as result;
