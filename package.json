{"name": "web-manager-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node start-production.js", "dev": "copy .env.development .env && nest start --watch", "prod:setup": "copy .env.production .env", "prod:build": "npm run prod:setup && npm run build", "prod:start": "npm run prod:build && node start-production.js", "deploy": "chmod +x deploy-production.sh && ./deploy-production.sh full", "deploy:update": "chmod +x deploy-production.sh && ./deploy-production.sh update", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- migration:generate -d src/config/data-source.ts", "migration:run": "npm run typeorm -- migration:run -d src/config/data-source.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/config/data-source.ts", "migration:show": "npm run typeorm -- migration:show -d src/config/data-source.ts", "migration:verify": "chmod +x scripts/verify-migrations.sh && ./scripts/verify-migrations.sh", "migration:rollback": "chmod +x scripts/rollback-migration.sh && ./scripts/rollback-migration.sh", "verify:deployment": "node scripts/verify-deployment.js", "verify:migration": "psql -h localhost -U postgres -d manager -f scripts/verify-migration.sql", "deploy:migration": "chmod +x scripts/deploy-migration.sh && ./scripts/deploy-migration.sh", "deploy:migration:prod": "chmod +x scripts/deploy-migration.sh && ./scripts/deploy-migration.sh production", "verify:product-migration": "psql -h localhost -U postgres -d manager -f scripts/verify-product-migration.sql", "rollback:product-migration": "chmod +x scripts/rollback-product-migration.sh && ./scripts/rollback-product-migration.sh"}, "dependencies": {"@liaoliaots/nestjs-redis": "^10.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.1.6", "@nestjs/typeorm": "^10.0.2", "@types/multer": "^1.4.12", "@types/puppeteer": "^7.0.4", "axios": "^1.9.0", "bcrypt": "^6.0.0", "cache-manager": "^6.4.2", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cos-nodejs-sdk-v5": "^2.14.7", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "ioredis": "^5.6.1", "nest-winston": "^1.10.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.15.6", "pinyin": "^4.0.0", "puppeteer": "^24.10.2", "redis": "^5.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sqlite3": "^5.1.7", "typeorm": "^0.3.22", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/cache-manager": "^5.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/pinyin": "^2.10.2", "@types/supertest": "^6.0.2", "@types/xlsx": "^0.0.36", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}