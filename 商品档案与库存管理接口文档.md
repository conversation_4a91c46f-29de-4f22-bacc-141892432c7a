# 商品档案与库存管理接口文档

## 概述

本文档详细分析了商品档案（Product）和库存管理（Inventory）相关的API接口设计，包括数据结构、接口功能、业务逻辑和最佳实践。

## 目录

1. [系统架构设计](#系统架构设计)
2. [数据模型分析](#数据模型分析)
3. [接口对比分析](#接口对比分析)
4. [商品档案接口](#商品档案接口)
5. [库存管理接口](#库存管理接口)
6. [接口集成与联动](#接口集成与联动)
7. [业务流程分析](#业务流程分析)
8. [最佳实践建议](#最佳实践建议)

## 系统架构设计

### 设计原则

系统采用**关注点分离**的设计原则：

- **商品档案模块**：专注于商品基本信息管理（名称、价格、规格等）
- **库存管理模块**：专注于库存数量、仓储位置、库存变动等
- **清晰的边界**：两个模块通过商品编码（productCode）关联，保持松耦合

### 架构优势

1. **职责清晰**：商品信息与库存信息分离管理
2. **扩展性强**：可独立扩展商品功能或库存功能
3. **数据一致性**：通过事务机制保证数据同步
4. **性能优化**：可针对不同场景优化查询策略

## 数据模型分析

### 商品档案实体（Product Entity）

```typescript
@Entity('products')
export class Product {
  id: string;                    // UUID主键
  code: string;                  // 商品编码（唯一）
  name: string;                  // 商品名称
  manufacturerCode: string;      // 厂商编码（可选）
  brandCode: string;             // 品牌编码
  supplierCode: string;          // 供应商编码

  // 六种价格字段
  clothingCost: number;          // 服装成本
  accessoryCost: number;         // 辅料成本
  retailPrice: number;           // 零售价
  preOrderPrice: number;         // 预订价
  restockPrice: number;          // 补货价
  spotPrice: number;             // 现货价

  // JSON字段
  accessories: AccessoryQuantity[];      // 辅料配置
  colorSizeCombinations: ColorSizeCombination[];  // 颜色尺寸组合
}
```

### 库存明细实体（InventoryDetail Entity）

```typescript
@Entity('inventory_details')
export class InventoryDetail {
  id: string;                    // UUID主键
  productCode: string;           // 商品编码（外键）
  colorCode: string;             // 颜色编码（外键）
  size: string;                  // 尺寸

  // 库存数量字段
  quantity: number;              // 库存数量
  reservedQuantity: number;      // 预留数量
  availableQuantity: number;     // 可用数量
  safetyStock: number;           // 安全库存

  // 成本和位置
  latestCost: number;            // 最新进货成本
  warehouseLocation: string;     // 仓库位置

  // 颜色扩展信息
  colorImages: string[];         // 颜色图片
  colorPriceInfo: object;        // 颜色价格信息
  colorAccessories: object[];    // 颜色辅料配置
}
```

### 关系设计

```
Products (1) ←→ (N) InventoryDetails
  ↓ productCode

Colors (1) ←→ (N) InventoryDetails
  ↓ colorCode

Brands (1) ←→ (N) Products
  ↓ brandCode

Suppliers (1) ←→ (N) Products
  ↓ supplierCode
```

## 接口对比分析

### 商品档案 vs 库存管理接口对比

| 对比维度 | 商品档案接口 | 库存管理接口 |
|---------|-------------|-------------|
| **主要职责** | 商品基本信息管理 | 库存数量和仓储管理 |
| **核心实体** | Product | InventoryDetail |
| **主要字段** | 商品信息、价格、规格 | 库存数量、仓库位置、成本 |
| **接口前缀** | `/products` | `/inventory` |
| **数据粒度** | 商品级别 | 商品-颜色-尺寸级别 |
| **业务场景** | 商品上架、规格管理 | 入库出库、库存调整 |

### 核心接口功能对比

| 功能类型 | 商品档案接口 | 库存管理接口 |
|---------|-------------|-------------|
| **创建** | `POST /products` - 创建商品档案 | `POST /inventory` - 创建库存明细 |
| **查询列表** | `GET /products` - 商品列表 | `GET /inventory` - 库存明细列表 |
| **查询详情** | `GET /products/{id}` - 商品详情 | `GET /inventory/products/{code}` - 商品库存详情 |
| **更新** | `PATCH /products/{id}` - 更新商品 | `POST /inventory/adjust` - 调整库存 |
| **删除** | `DELETE /products/{id}` - 删除商品 | `DELETE /inventory/{id}` - 删除库存明细 |
| **批量操作** | 暂无 | `POST /inventory/batch` - 批量创建 |
| **导出功能** | `GET /products/export/excel` | `GET /inventory/export/excel` |

### 数据返回内容对比

| 数据类型 | 商品档案接口返回 | 库存管理接口返回 |
|---------|----------------|----------------|
| **基本信息** | ✅ 商品编码、名称、品牌、供应商 | ✅ 商品编码、名称（关联查询） |
| **价格信息** | ✅ 6种价格字段 | ❌ 不返回价格信息 |
| **规格信息** | ✅ 颜色尺寸组合 | ✅ 具体颜色尺寸 |
| **库存信息** | ❌ 不返回库存数据 | ✅ 库存数量、可用数量等 |
| **成本信息** | ✅ 服装成本、辅料成本 | ✅ 最新进货成本 |
| **仓储信息** | ❌ 不涉及仓储 | ✅ 仓库位置、安全库存 |

### 业务逻辑对比

| 业务逻辑 | 商品档案接口 | 库存管理接口 |
|---------|-------------|-------------|
| **数据验证** | 品牌供应商存在性、价格合理性 | 库存数量非负、商品颜色存在性 |
| **关联操作** | 创建时自动生成库存记录 | 调整时自动记录变动历史 |
| **事务处理** | 商品+库存创建事务 | 库存调整+变动记录事务 |
| **软删除** | 支持商品软删除 | 支持库存明细软删除 |
| **权限控制** | 商品管理权限 | 库存管理权限 |

## 商品档案接口

### 1. 创建商品档案

**接口**: `POST /products`

**功能**: 创建新的商品档案，自动生成对应的库存记录

**请求体示例**:
```json
{
  "code": "SHIRT001",
  "name": "高级商务衬衫",
  "brandCode": "BRAND001",
  "supplierCode": "SUP001",
  "clothingCost": 80.0,
  "accessoryCost": 15.0,
  "retailPrice": 200.0,
  "preOrderPrice": 180.0,
  "restockPrice": 190.0,
  "spotPrice": 195.0,
  "colorSizeCombinations": [
    {
      "colorCode": "WHITE",
      "sizes": ["S", "M", "L", "XL"],
      "images": ["white-shirt-1.jpg"],
      "accessories": [
        {"accessoryId": "acc-button-white", "quantity": 8}
      ]
    }
  ]
}
```

**业务逻辑**:
1. 验证商品编码唯一性
2. 验证品牌和供应商存在性
3. 验证颜色编码有效性
4. 创建商品档案记录
5. **自动生成库存记录**（初始库存为0）
6. 使用数据库事务保证一致性

### 2. 查询商品档案列表

**接口**: `GET /products`

**功能**: 分页查询商品档案列表（仅显示激活品牌的商品）

**查询参数**:
- `page`: 页码（必填）
- `pageSize`: 每页数量（必填）
- `productCode`: 商品编码筛选（可选）

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "products": [
      {
        "id": "uuid",
        "code": "SHIRT001",
        "name": "高级商务衬衫",
        "brandName": "知名品牌",
        "supplierName": "优质供应商",
        "clothingCost": 80.0,
        "retailPrice": 200.0,
        "colorSizeCombinations": [
          {
            "colorCode": "WHITE",
            "colorName": "白色",
            "sizes": ["S", "M", "L", "XL"]
          }
        ]
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 3. 获取商品档案详情

**接口**: `GET /products/{id}`

**功能**: 根据ID获取商品档案详细信息

**特点**:
- 返回完整的商品信息
- 包含关联的品牌和供应商名称
- 包含辅料详细信息
- **不包含库存信息**（职责分离）

### 4. 更新商品档案

**接口**: `PATCH /products/{id}`

**功能**: 更新商品档案信息，自动同步库存记录

**业务逻辑**:
1. 验证商品存在性
2. 验证更新数据有效性
3. 更新商品档案
4. **同步库存记录**（如果颜色尺寸组合发生变化）
5. 使用数据库事务保证一致性

### 5. 删除商品档案

**接口**: `DELETE /products/{id}`

**功能**: 软删除商品档案及相关库存记录

## 库存管理接口

### 1. 新增库存明细

**接口**: `POST /inventory`

**功能**: 手动创建库存明细记录

**请求体示例**:
```json
{
  "productCode": "SHIRT001",
  "colorCode": "WHITE",
  "size": "M",
  "quantity": 100,
  "safetyStock": 20,
  "latestCost": 80.0,
  "warehouseLocation": "A区-01-03"
}
```

### 2. 批量新增库存明细

**接口**: `POST /inventory/batch`

**功能**: 批量创建多个库存明细记录

### 3. 查询库存明细列表

**接口**: `GET /inventory`

**功能**: 分页查询库存明细（按颜色分组显示）

**查询参数**:
- `page`: 页码（必填）
- `pageSize`: 每页数量（必填）
- `productCode`: 商品编码筛选（可选）
- `colorCode`: 颜色编码筛选（可选）
- `brandCode`: 品牌编码筛选（可选）
- `supplierCode`: 供应商编码筛选（可选）
- `lowStockOnly`: 仅显示低库存（可选）
- `hasStockOnly`: 仅显示有库存（可选）

**响应特点**:
- 按商品-颜色分组显示
- 每个颜色显示总库存和尺寸明细
- 包含低库存预警标识

### 4. 获取商品库存信息

**接口**: `GET /inventory/products/{productCode}`

**功能**: 获取指定商品的完整库存信息（按颜色分组）

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "productInfo": {
      "code": "SHIRT001",
      "name": "高级商务衬衫",
      "brandName": "知名品牌",
      "supplierName": "优质供应商"
    },
    "colorInventories": [
      {
        "colorCode": "WHITE",
        "colorName": "白色",
        "totalQuantity": 150,
        "totalAvailableQuantity": 140,
        "images": ["white-shirt-1.jpg"],
        "sizeDetails": [
          {
            "size": "M",
            "quantity": 60,
            "availableQuantity": 55,
            "isLowStock": false
          }
        ]
      }
    ],
    "totalQuantity": 150,
    "totalAvailableQuantity": 140
  }
}
```

### 5. 批量获取商品库存汇总

**接口**: `POST /inventory/products/batch`

**功能**: 批量获取多个商品的库存汇总信息

### 6. 调整库存数量

**接口**: `POST /inventory/adjust`

**功能**: 调整库存数量（支持设置、增加、减少三种方式）

**请求体示例**:
```json
{
  "inventoryId": "uuid",
  "adjustmentType": "ADD",
  "quantity": 50,
  "reason": "采购入库",
  "operatorCode": "user001"
}
```

**调整类型**:
- `SET`: 设置为指定数量
- `ADD`: 增加指定数量
- `SUBTRACT`: 减少指定数量

### 7. 导出库存Excel

**接口**: `GET /inventory/export/excel`

**功能**: 根据筛选条件导出库存明细Excel文件

## 接口集成与联动

### 自动化库存管理

1. **创建商品时**：
   - 根据`colorSizeCombinations`自动生成库存记录
   - 初始库存数量为0
   - 继承颜色级别的价格和辅料信息

2. **更新商品时**：
   - 检测颜色尺寸组合变化
   - 新增组合：创建新的库存记录
   - 删除组合：软删除对应库存记录
   - 修改组合：更新库存记录信息

3. **删除商品时**：
   - 软删除商品档案
   - 软删除所有相关库存记录

### 数据一致性保证

1. **事务机制**：所有涉及商品和库存的操作都使用数据库事务
2. **外键约束**：确保库存记录引用的商品和颜色必须存在
3. **唯一约束**：确保同一商品的同一颜色尺寸组合只有一条库存记录
4. **级联操作**：商品变更时自动同步库存记录

## 业务流程分析

### 典型业务场景

1. **新品上架流程**：
   ```
   创建商品档案 → 自动生成库存记录 → 采购入库 → 调整库存数量
   ```

2. **商品规格调整流程**：
   ```
   更新商品颜色尺寸 → 自动同步库存记录 → 处理新增/删除的规格
   ```

3. **库存查询流程**：
   ```
   商品列表查询 → 库存汇总查询 → 详细库存查询 → 库存调整
   ```

### 权限控制

- **商品档案**：需要商品管理权限
- **库存管理**：需要库存管理权限
- **库存调整**：需要库存调整权限
- **数据导出**：需要导出权限

## 最佳实践建议

### 1. 接口使用建议

1. **商品查询**：优先使用商品档案接口，避免不必要的库存数据加载
2. **库存查询**：根据业务需求选择合适的库存接口
3. **批量操作**：大量数据操作时使用批量接口提高性能
4. **分页查询**：始终使用分页参数，避免一次性加载大量数据

### 2. 性能优化建议

1. **索引优化**：为常用查询字段添加数据库索引
2. **缓存策略**：对频繁查询的商品信息进行缓存
3. **异步处理**：大批量库存同步操作考虑异步处理
4. **数据预加载**：根据业务场景预加载关联数据

### 3. 数据安全建议

1. **输入验证**：严格验证所有输入参数
2. **权限控制**：实施细粒度的权限控制
3. **操作日志**：记录所有重要操作的审计日志
4. **数据备份**：定期备份重要的商品和库存数据

### 4. 监控告警建议

1. **低库存预警**：监控库存水位，及时预警
2. **接口性能**：监控接口响应时间和错误率
3. **数据一致性**：定期检查商品和库存数据一致性
4. **业务指标**：监控关键业务指标（库存周转率等）

## 接口详细分析

### 商品档案接口详细分析

#### 接口响应格式统一性
所有商品档案接口都遵循统一的响应格式：
```json
{
  "code": 200,
  "data": {},
  "message": "操作成功"
}
```

#### 错误处理机制
- **400**: 请求参数错误（商品编码重复、关联数据不存在等）
- **404**: 资源不存在（商品不存在）
- **500**: 服务器内部错误

#### 数据验证规则
1. **商品编码**: 必填，唯一，最大长度100字符
2. **商品名称**: 必填，最大长度200字符
3. **价格字段**: 必填，decimal(10,2)，必须大于0
4. **颜色尺寸组合**: 必填，至少包含一个颜色
5. **品牌供应商**: 必须存在且激活状态

### 库存管理接口详细分析

#### 库存变动记录机制
每次库存调整都会自动记录到`inventory_transactions`表：
```json
{
  "transactionType": "IN|OUT|ADJUST|RESERVE|RELEASE",
  "quantity": 50,
  "beforeQuantity": 100,
  "afterQuantity": 150,
  "reason": "采购入库",
  "operatorCode": "user001"
}
```

#### 库存计算逻辑
- **可用数量** = 库存数量 - 预留数量
- **低库存判断** = 可用数量 ≤ 安全库存
- **库存调整验证** = 调整后数量不能小于0

#### 库存分组显示逻辑
库存列表按以下层级分组显示：
```
商品 (Product)
  └── 颜色 (Color)
      └── 尺寸明细 (Size Details)
```

## 接口集成场景分析

### 场景1：新品上架完整流程

```mermaid
sequenceDiagram
    participant Frontend as 前端
    participant ProductAPI as 商品API
    participant InventoryAPI as 库存API
    participant Database as 数据库

    Frontend->>ProductAPI: POST /products (创建商品)
    ProductAPI->>Database: 验证品牌供应商
    ProductAPI->>Database: 创建商品记录
    ProductAPI->>Database: 自动生成库存记录
    ProductAPI->>Frontend: 返回成功

    Frontend->>InventoryAPI: POST /inventory/adjust (入库)
    InventoryAPI->>Database: 调整库存数量
    InventoryAPI->>Database: 记录库存变动
    InventoryAPI->>Frontend: 返回成功
```

### 场景2：商品规格调整流程

```mermaid
sequenceDiagram
    participant Frontend as 前端
    participant ProductAPI as 商品API
    participant InventoryAPI as 库存API
    participant Database as 数据库

    Frontend->>ProductAPI: PATCH /products/{id} (更新规格)
    ProductAPI->>Database: 比较颜色尺寸变化
    ProductAPI->>Database: 新增库存记录
    ProductAPI->>Database: 软删除废弃记录
    ProductAPI->>Frontend: 返回成功

    Frontend->>InventoryAPI: GET /inventory/products/{code}
    InventoryAPI->>Database: 查询最新库存
    InventoryAPI->>Frontend: 返回库存信息
```

### 场景3：库存查询与管理流程

```mermaid
sequenceDiagram
    participant Frontend as 前端
    participant ProductAPI as 商品API
    participant InventoryAPI as 库存API

    Frontend->>ProductAPI: GET /products (商品列表)
    ProductAPI->>Frontend: 返回商品基本信息

    Frontend->>InventoryAPI: POST /inventory/products/batch
    InventoryAPI->>Frontend: 返回批量库存汇总

    Frontend->>InventoryAPI: GET /inventory/products/{code}
    InventoryAPI->>Frontend: 返回详细库存信息
```

## 性能优化分析

### 数据库索引建议

```sql
-- 商品表索引
CREATE INDEX idx_products_code ON products(code);
CREATE INDEX idx_products_brand_supplier ON products(brandCode, supplierCode);
CREATE INDEX idx_products_deleted ON products(isDeleted);

-- 库存表索引
CREATE INDEX idx_inventory_product_color ON inventory_details(productCode, colorCode);
CREATE INDEX idx_inventory_color_size ON inventory_details(colorCode, size);
CREATE INDEX idx_inventory_quantity ON inventory_details(quantity);
CREATE INDEX idx_inventory_low_stock ON inventory_details(availableQuantity, safetyStock);

-- 库存变动表索引
CREATE INDEX idx_transactions_product ON inventory_transactions(productCode);
CREATE INDEX idx_transactions_date ON inventory_transactions(createdAt);
CREATE INDEX idx_transactions_type ON inventory_transactions(transactionType);
```

### 查询优化策略

1. **分页查询优化**：
   - 使用LIMIT和OFFSET进行分页
   - 避免使用COUNT(*)统计总数，改用估算
   - 对大数据量使用游标分页

2. **关联查询优化**：
   - 使用JOIN代替多次单表查询
   - 合理使用LEFT JOIN避免数据丢失
   - 控制JOIN的表数量，避免过度关联

3. **缓存策略**：
   - 商品基本信息缓存（Redis，TTL=1小时）
   - 品牌供应商信息缓存（Redis，TTL=24小时）
   - 库存汇总信息缓存（Redis，TTL=5分钟）

## 监控指标建议

### 业务指标

1. **商品管理指标**：
   - 商品创建成功率
   - 商品更新频率
   - 商品规格复杂度（平均颜色数、尺寸数）

2. **库存管理指标**：
   - 库存周转率
   - 低库存商品占比
   - 库存调整频率
   - 库存准确率

3. **接口性能指标**：
   - 接口响应时间（P95、P99）
   - 接口成功率
   - 并发处理能力
   - 数据库连接池使用率

### 告警规则

1. **业务告警**：
   - 低库存商品数量超过阈值
   - 库存为0的商品数量异常
   - 商品创建失败率超过5%

2. **技术告警**：
   - 接口响应时间超过2秒
   - 接口错误率超过1%
   - 数据库连接数超过80%
   - 缓存命中率低于90%

## 扩展功能建议

### 1. 库存预警功能增强

```typescript
// 库存预警配置
interface InventoryAlert {
  productCode: string;
  colorCode: string;
  size: string;
  alertType: 'LOW_STOCK' | 'OUT_OF_STOCK' | 'OVERSTOCK';
  threshold: number;
  recipients: string[];
  isActive: boolean;
}
```

### 2. 库存成本管理

```typescript
// 库存成本跟踪
interface InventoryCost {
  inventoryId: string;
  costMethod: 'FIFO' | 'LIFO' | 'WEIGHTED_AVERAGE';
  unitCost: number;
  totalCost: number;
  lastUpdated: Date;
}
```

### 3. 库存批次管理

```typescript
// 库存批次信息
interface InventoryBatch {
  batchNumber: string;
  productCode: string;
  colorCode: string;
  size: string;
  quantity: number;
  productionDate: Date;
  expiryDate?: Date;
  supplierBatchNumber?: string;
}
```

## 总结

当前的商品档案与库存管理接口设计具有以下优势：

1. **架构清晰**：职责分离，模块化设计
2. **功能完整**：覆盖商品和库存管理的全生命周期
3. **数据一致**：通过事务和约束保证数据一致性
4. **扩展性强**：支持灵活的业务扩展需求
5. **性能优化**：支持分页、筛选、批量等性能优化策略
6. **自动化程度高**：商品与库存自动同步，减少人工干预
7. **监控完善**：支持多维度的业务和技术监控

该设计方案适合服装批发业务的复杂需求，能够有效支撑商品档案管理和库存管理的各种业务场景，同时为未来的功能扩展预留了充足的空间。

### 核心设计亮点

1. **智能库存同步**：商品规格变更时自动同步库存记录
2. **多维度库存管理**：支持商品-颜色-尺寸三维库存管理
3. **完整的变动追踪**：所有库存变动都有完整的审计记录
4. **灵活的查询接口**：支持多种查询场景和筛选条件
5. **高性能设计**：通过索引、缓存、分页等手段保证查询性能

这套接口设计已经达到了生产环境的标准，能够满足中大型服装批发企业的业务需求。
