# 前端API接口文档

## 目录

1. [商品档案接口 (Products)](#商品档案接口-products)
2. [库存管理接口 (Inventory)](#库存管理接口-inventory)
3. [数据结构说明](#数据结构说明)
4. [业务场景示例](#业务场景示例)
5. [错误处理](#错误处理)

---

## 商品档案接口 (Products)

### 基础URL

```
http://localhost:3000/products
```

### 1. 创建商品档案

**接口：** `POST /products`

**功能：** 创建新的商品档案，系统会自动根据颜色尺寸组合生成对应的库存记录

**请求体：**

```json
{
  "code": "PROD001",
  "name": "时尚T恤",
  "brandCode": "BRAND001",
  "supplierCode": "SUP001",
  "manufacturerCode": "MFG001",
  "clothingCost": 50.0,
  "accessoryCost": 10.0,
  "retailPrice": 120.0,
  "preOrderPrice": 100.0,
  "restockPrice": 110.0,
  "spotPrice": 115.0,
  "colorSizeCombinations": [
    {
      "colorCode": "COLOR001",
      "sizes": ["S", "M", "L"],
      "accessories": [
        {
          "accessoryId": "acc-uuid-1",
          "quantity": 2
        }
      ],
      "priceAdjustments": {
        "accessoryCostAdjustment": 5.0,
        "retailPriceAdjustment": 10.0
      },
      "images": ["red-tshirt-1.jpg", "red-tshirt-2.jpg"],
      "remark": "红色款使用特殊面料"
    },
    {
      "colorCode": "COLOR002",
      "sizes": ["M", "L", "XL"]
    }
  ],
  "accessories": [
    {
      "accessoryId": "acc-uuid-1",
      "quantity": 1
    }
  ],
  "mainImages": ["main-1.jpg", "main-2.jpg"],
  "remark": "春季新款"
}
```

**响应：**

```json
{
  "code": 200,
  "data": {
    "id": "product-uuid",
    "code": "PROD001",
    "name": "时尚T恤",
    "brandCode": "BRAND001",
    "supplierCode": "SUP001",
    "clothingCost": 50.00,
    "accessoryCost": 10.00,
    "retailPrice": 120.00,
    "colorSizeCombinations": [...],
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-01-01T00:00:00.000Z"
  },
  "message": "商品档案创建成功，已自动生成库存记录"
}
```

### 2. 查询商品列表

**接口：** `GET /products`

**查询参数：**

- `page` (必填): 页码，从1开始
- `pageSize` (必填): 每页数量
- `code` (可选): 商品编码筛选
- `name` (可选): 商品名称模糊搜索
- `brandCode` (可选): 品牌编码筛选
- `supplierCode` (可选): 供应商编码筛选
- `search` (可选): 关键词搜索

**示例：**

```bash
GET /products?page=1&pageSize=10&brandCode=BRAND001&search=T恤
```

**响应：**

```json
{
  "code": 200,
  "data": {
    "products": [
      {
        "id": "product-uuid",
        "code": "PROD001",
        "name": "时尚T恤",
        "brandName": "知名品牌",
        "supplierName": "优质供应商",
        "retailPrice": 120.0,
        "colorCount": 2,
        "sizeCount": 5,
        "mainImages": ["main-1.jpg"],
        "createdAt": "2025-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "查询成功"
}
```

### 3. 获取商品详情

**接口：** `GET /products/{id}`

**响应：**

```json
{
  "code": 200,
  "data": {
    "id": "product-uuid",
    "code": "PROD001",
    "name": "时尚T恤",
    "brand": {
      "code": "BRAND001",
      "name": "知名品牌"
    },
    "supplier": {
      "code": "SUP001",
      "name": "优质供应商"
    },
    "clothingCost": 50.0,
    "accessoryCost": 10.0,
    "retailPrice": 120.0,
    "preOrderPrice": 100.0,
    "restockPrice": 110.0,
    "spotPrice": 115.0,
    "colorSizeCombinations": [
      {
        "colorCode": "COLOR001",
        "sizes": ["S", "M", "L"],
        "accessories": [
          {
            "accessoryId": "acc-uuid-1",
            "quantity": 2
          }
        ],
        "priceAdjustments": {
          "accessoryCostAdjustment": 5.0,
          "retailPriceAdjustment": 10.0
        },
        "images": ["red-tshirt-1.jpg", "red-tshirt-2.jpg"],
        "remark": "红色款使用特殊面料"
      }
    ],
    "accessories": [
      {
        "accessoryId": "acc-uuid-1",
        "quantity": 1,
        "accessory": {
          "id": "acc-uuid-1",
          "name": "特殊纽扣",
          "articleNumber": "BTN001"
        }
      }
    ],
    "mainImages": ["main-1.jpg", "main-2.jpg"],
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-01-01T00:00:00.000Z"
  },
  "message": "查询成功"
}
```

### 4. 更新商品档案

**接口：** `PUT /products/{id}`

**功能：** 更新商品档案，如果修改了颜色尺寸组合，系统会自动同步库存记录

**请求体：** 与创建接口相同，所有字段都是可选的

**响应：**

```json
{
  "code": 200,
  "data": null,
  "message": "商品档案更新成功，库存记录已同步"
}
```

### 5. 删除商品档案

**接口：** `DELETE /products/{id}`

**响应：**

```json
{
  "code": 200,
  "data": null,
  "message": "删除成功"
}
```

### 6. 导出商品Excel

**接口：** `GET /products/export/excel`

**查询参数：**

- `brandCode` (可选): 品牌编码筛选
- `supplierCode` (可选): 供应商编码筛选
- `productIds` (可选): 指定商品ID列表，逗号分隔

**示例：**

```bash
GET /products/export/excel?brandCode=BRAND001&supplierCode=SUP001
```

**响应：** Excel文件下载

---

## 库存管理接口 (Inventory)

### 基础URL

```
http://localhost:3000/inventory
```

### 1. 获取商品库存信息（按颜色分组）

**接口：** `GET /inventory/products/{productCode}`

**功能：** 获取指定商品的完整库存信息，按颜色分组展示

**示例：**

```bash
GET /inventory/products/PROD001
```

**响应：**

```json
{
  "code": 200,
  "data": {
    "productInfo": {
      "code": "PROD001",
      "name": "时尚T恤",
      "brandName": "知名品牌",
      "supplierName": "优质供应商",
      "mainImages": ["main-1.jpg", "main-2.jpg"]
    },
    "colorInventories": [
      {
        "colorCode": "COLOR001",
        "colorName": "红色",
        "colorHex": "#FF0000",
        "totalQuantity": 150,
        "totalAvailableQuantity": 140,
        "sizeDetails": [
          {
            "size": "S",
            "quantity": 50,
            "availableQuantity": 45,
            "reservedQuantity": 5,
            "safetyStock": 10,
            "latestCost": 55.5,
            "warehouseLocation": "A区-01-01",
            "isLowStock": false,
            "remark": "春季新款"
          },
          {
            "size": "M",
            "quantity": 60,
            "availableQuantity": 55,
            "reservedQuantity": 5,
            "safetyStock": 15,
            "latestCost": 55.5,
            "warehouseLocation": "A区-01-02",
            "isLowStock": false,
            "remark": null
          },
          {
            "size": "L",
            "quantity": 40,
            "availableQuantity": 40,
            "reservedQuantity": 0,
            "safetyStock": 20,
            "latestCost": 55.5,
            "warehouseLocation": "A区-01-03",
            "isLowStock": true,
            "remark": null
          }
        ],
        "images": ["red-tshirt-1.jpg", "red-tshirt-2.jpg"],
        "priceInfo": {
          "clothingCost": 50.0,
          "accessoryCost": 15.0,
          "retailPrice": 130.0,
          "preOrderPrice": 100.0,
          "restockPrice": 110.0,
          "spotPrice": 115.0
        },
        "remark": "红色款使用特殊面料"
      },
      {
        "colorCode": "COLOR002",
        "colorName": "蓝色",
        "colorHex": "#0000FF",
        "totalQuantity": 80,
        "totalAvailableQuantity": 75,
        "sizeDetails": [
          {
            "size": "M",
            "quantity": 30,
            "availableQuantity": 28,
            "reservedQuantity": 2,
            "safetyStock": 10,
            "latestCost": 50.5,
            "warehouseLocation": "A区-02-01",
            "isLowStock": false,
            "remark": null
          },
          {
            "size": "L",
            "quantity": 35,
            "availableQuantity": 32,
            "reservedQuantity": 3,
            "safetyStock": 12,
            "latestCost": 50.5,
            "warehouseLocation": "A区-02-02",
            "isLowStock": false,
            "remark": null
          },
          {
            "size": "XL",
            "quantity": 15,
            "availableQuantity": 15,
            "reservedQuantity": 0,
            "safetyStock": 8,
            "latestCost": 50.5,
            "warehouseLocation": "A区-02-03",
            "isLowStock": false,
            "remark": null
          }
        ],
        "images": [],
        "priceInfo": {
          "clothingCost": 50.0,
          "accessoryCost": 10.0,
          "retailPrice": 120.0,
          "preOrderPrice": 100.0,
          "restockPrice": 110.0,
          "spotPrice": 115.0
        },
        "remark": null
      }
    ],
    "totalQuantity": 230,
    "totalAvailableQuantity": 215
  },
  "message": "查询成功"
}
```

### 2. 批量获取商品库存汇总

**接口：** `POST /inventory/products/batch`

**功能：** 批量获取多个商品的库存汇总信息

**请求体：**

```json
{
  "productCodes": ["PROD001", "PROD002", "PROD003"]
}
```

**响应：**

```json
{
  "code": 200,
  "data": {
    "PROD001": {
      "productName": "时尚T恤",
      "totalQuantity": 230,
      "totalAvailableQuantity": 215,
      "colorCount": 2,
      "lowStockColors": ["红色"]
    },
    "PROD002": {
      "productName": "休闲裤",
      "totalQuantity": 150,
      "totalAvailableQuantity": 140,
      "colorCount": 3,
      "lowStockColors": []
    },
    "PROD003": {
      "error": "商品 PROD003 不存在"
    }
  },
  "message": "查询成功"
}
```

### 3. 库存明细列表查询（支持排序）

**接口：** `GET /inventory`

**查询参数：**

- `page` (必填): 页码，从1开始
- `pageSize` (必填): 每页数量
- `productCode` (可选): 商品编码筛选
- `productName` (可选): 商品名称模糊搜索
- `colorCode` (可选): 颜色编码筛选
- `colorName` (可选): 颜色名称筛选
- `size` (可选): 尺寸筛选
- `brandCode` (可选): 品牌编码筛选
- `supplierCode` (可选): 供应商编码筛选
- `warehouseLocation` (可选): 仓库位置筛选
- `lowStockOnly` (可选): 是否只显示低库存 (true/false)
- `hasStockOnly` (可选): 是否只显示有库存 (true/false)
- `search` (可选): 搜索关键词
- `sortBy` (可选): 排序字段 (quantity/availableQuantity/latestCost/createdAt/productCode)
- `sortOrder` (可选): 排序方向 (ASC/DESC)

**排序字段说明：**

- `quantity`: 按库存数量排序
- `availableQuantity`: 按可用数量排序
- `latestCost`: 按最新成本排序
- `createdAt`: 按创建时间排序（默认）
- `productCode`: 按商品编码排序

**示例：**

```bash
# 按库存数量降序排列
GET /inventory?page=1&pageSize=10&sortBy=quantity&sortOrder=DESC

# 按价格升序排列
GET /inventory?page=1&pageSize=10&sortBy=latestCost&sortOrder=ASC

# 筛选低库存商品
GET /inventory?page=1&pageSize=10&lowStockOnly=true

# 组合查询
GET /inventory?page=1&pageSize=10&brandCode=BRAND001&hasStockOnly=true&sortBy=availableQuantity&sortOrder=ASC
```

**响应：**

```json
{
  "code": 200,
  "data": {
    "inventoryDetails": [
      {
        "id": "inventory-uuid",
        "productCode": "PROD001",
        "productName": "时尚T恤",
        "brandName": "知名品牌",
        "supplierName": "优质供应商",
        "colorCode": "COLOR001",
        "colorName": "红色",
        "size": "M",
        "quantity": 60,
        "reservedQuantity": 5,
        "availableQuantity": 55,
        "safetyStock": 15,
        "latestCost": 55.5,
        "warehouseLocation": "A区-01-02",
        "remark": null,
        "isLowStock": false,
        "createdAt": "2025-01-01T00:00:00.000Z",
        "updatedAt": "2025-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "查询成功"
}
```

### 4. 调整库存数量

**接口：** `POST /inventory/adjust`

**功能：** 调整指定商品颜色尺寸的库存数量

**请求体：**

```json
{
  "productCode": "PROD001",
  "colorCode": "COLOR001",
  "size": "M",
  "adjustType": "ADD",
  "quantity": 50,
  "reason": "采购入库",
  "referenceNumber": "PO202501001",
  "operatorCode": "user001"
}
```

**调整类型说明：**

- `SET`: 设置为指定数量
- `ADD`: 增加指定数量
- `SUBTRACT`: 减少指定数量

**响应：**

```json
{
  "code": 200,
  "data": null,
  "message": "库存调整成功"
}
```

### 5. 导出库存Excel

**接口：** `GET /inventory/export/excel`

**查询参数：**

- `productCode` (可选): 商品编码筛选
- `colorCode` (可选): 颜色编码筛选
- `brandCode` (可选): 品牌编码筛选
- `supplierCode` (可选): 供应商编码筛选
- `lowStockOnly` (可选): 是否只导出低库存
- `hasStockOnly` (可选): 是否只导出有库存
- `inventoryIds` (可选): 指定导出的库存明细ID列表，逗号分隔

**示例：**

```bash
# 导出所有库存
GET /inventory/export/excel

# 导出指定商品的库存
GET /inventory/export/excel?productCode=PROD001

# 导出低库存商品
GET /inventory/export/excel?lowStockOnly=true

# 导出指定品牌的有库存商品
GET /inventory/export/excel?brandCode=BRAND001&hasStockOnly=true
```

**响应：** Excel文件下载

---

## 数据结构说明

### 1. 商品档案核心数据结构

#### ColorSizeCombination（颜色尺寸组合）

```typescript
interface ColorSizeCombination {
  colorCode: string; // 颜色编码
  sizes: string[]; // 尺寸数组
  accessories?: AccessoryQuantity[]; // 颜色专属辅料（可选）
  priceAdjustments?: PriceAdjustment; // 颜色价格调整（可选）
  images?: string[]; // 颜色专属图片（可选）
  remark?: string; // 颜色备注（可选）
}
```

#### PriceAdjustment（价格调整）

```typescript
interface PriceAdjustment {
  clothingCostAdjustment?: number; // 服装成本调整
  accessoryCostAdjustment?: number; // 辅料成本调整
  retailPriceAdjustment?: number; // 零售价调整
  preOrderPriceAdjustment?: number; // 预订价调整
  restockPriceAdjustment?: number; // 补货价调整
  spotPriceAdjustment?: number; // 现货价调整
}
```

#### AccessoryQuantity（辅料数量）

```typescript
interface AccessoryQuantity {
  accessoryId: string; // 辅料ID
  quantity: number; // 数量
  colorCode?: string; // 关联颜色（可选）
}
```

### 2. 库存数据结构

#### ProductInventoryResponse（商品库存响应）

```typescript
interface ProductInventoryResponse {
  productInfo: ProductInfo;
  colorInventories: ColorInventory[];
  totalQuantity: number;
  totalAvailableQuantity: number;
}
```

#### ColorInventory（颜色库存）

```typescript
interface ColorInventory {
  colorCode: string;
  colorName: string;
  colorHex?: string;
  totalQuantity: number;
  totalAvailableQuantity: number;
  sizeDetails: SizeInventory[];
  images: string[];
  priceInfo: PriceInfo;
  remark?: string;
}
```

#### SizeInventory（尺寸库存）

```typescript
interface SizeInventory {
  size: string;
  quantity: number;
  availableQuantity: number;
  reservedQuantity: number;
  safetyStock: number;
  latestCost?: number;
  warehouseLocation?: string;
  isLowStock: boolean;
  remark?: string;
}
```

#### PriceInfo（价格信息）

```typescript
interface PriceInfo {
  clothingCost: number;
  accessoryCost: number;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
}
```

---

## 业务场景示例

### 场景1：创建支持颜色差异的商品

**需求：** 创建一款衬衫，红色款比标准款贵20元，使用特殊纽扣

**步骤1：创建商品档案**

```bash
POST /products
```

```json
{
  "code": "SHIRT001",
  "name": "商务衬衫",
  "brandCode": "BRAND001",
  "supplierCode": "SUP001",
  "clothingCost": 80.0,
  "accessoryCost": 15.0,
  "retailPrice": 200.0,
  "colorSizeCombinations": [
    {
      "colorCode": "WHITE",
      "sizes": ["S", "M", "L", "XL"]
    },
    {
      "colorCode": "RED",
      "sizes": ["M", "L", "XL"],
      "accessories": [
        {
          "accessoryId": "special-button-uuid",
          "quantity": 8
        }
      ],
      "priceAdjustments": {
        "accessoryCostAdjustment": 5.0,
        "retailPriceAdjustment": 20.0
      },
      "images": ["red-shirt-front.jpg", "red-shirt-back.jpg"],
      "remark": "红色款使用特殊纽扣"
    }
  ]
}
```

**结果：** 系统自动生成7条库存记录（白色4个尺寸 + 红色3个尺寸）

**步骤2：查看生成的库存**

```bash
GET /inventory/products/SHIRT001
```

**返回数据显示：**

- 白色款：标准价格 ¥200
- 红色款：调整后价格 ¥220（+¥20）

### 场景2：库存管理完整流程

**步骤1：初始化库存**

```bash
POST /inventory/adjust
{
  "productCode": "SHIRT001",
  "colorCode": "WHITE",
  "size": "M",
  "adjustType": "SET",
  "quantity": 100,
  "reason": "初始化库存"
}
```

**步骤2：销售出库**

```bash
POST /inventory/adjust
{
  "productCode": "SHIRT001",
  "colorCode": "WHITE",
  "size": "M",
  "adjustType": "SUBTRACT",
  "quantity": 5,
  "reason": "销售出库",
  "referenceNumber": "ORDER001"
}
```

**步骤3：查看库存状态**

```bash
GET /inventory/products/SHIRT001
```

### 场景3：库存查询和排序

**查看库存最多的商品**

```bash
GET /inventory?page=1&pageSize=10&sortBy=quantity&sortOrder=DESC
```

**查看低库存商品**

```bash
GET /inventory?page=1&pageSize=20&lowStockOnly=true&sortBy=availableQuantity&sortOrder=ASC
```

**查看最贵的库存**

```bash
GET /inventory?page=1&pageSize=10&sortBy=latestCost&sortOrder=DESC
```

### 场景4：前端展示实现

**商品详情页面展示库存**

```javascript
// 获取商品库存信息
const getProductInventory = async (productCode) => {
  const response = await fetch(`/api/inventory/products/${productCode}`);
  const result = await response.json();

  if (result.code === 200) {
    const { productInfo, colorInventories, totalQuantity } = result.data;

    // 渲染商品基本信息
    renderProductInfo(productInfo);

    // 按颜色渲染库存信息
    colorInventories.forEach((colorInventory) => {
      renderColorInventory(colorInventory);
    });

    // 显示总库存
    renderTotalInventory(totalQuantity);
  }
};

// 渲染颜色库存
const renderColorInventory = (colorInventory) => {
  const { colorName, totalQuantity, sizeDetails, priceInfo, images } =
    colorInventory;

  // 显示颜色名称和总数
  console.log(`${colorName}: 总计${totalQuantity}件`);

  // 显示各尺寸库存
  sizeDetails.forEach((size) => {
    const status = size.isLowStock ? '⚠️低库存' : '正常';
    console.log(`  ${size.size}码: ${size.availableQuantity}件可用 ${status}`);
  });

  // 显示价格信息（注意颜色差异）
  console.log(`  零售价: ¥${priceInfo.retailPrice}`);

  // 显示专属图片
  if (images.length > 0) {
    console.log(`  专属图片: ${images.join(', ')}`);
  }
};
```

**库存列表页面**

```javascript
// 获取库存列表（支持排序和筛选）
const getInventoryList = async (params) => {
  const queryParams = new URLSearchParams({
    page: params.page || 1,
    pageSize: params.pageSize || 10,
    sortBy: params.sortBy || 'createdAt',
    sortOrder: params.sortOrder || 'DESC',
    ...params.filters,
  });

  const response = await fetch(`/api/inventory?${queryParams}`);
  const result = await response.json();

  if (result.code === 200) {
    const { inventoryDetails, total, totalPages } = result.data;

    // 渲染库存列表
    renderInventoryList(inventoryDetails);

    // 渲染分页
    renderPagination(total, totalPages);
  }
};

// 库存调整
const adjustInventory = async (adjustData) => {
  const response = await fetch('/api/inventory/adjust', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(adjustData),
  });

  const result = await response.json();

  if (result.code === 200) {
    // 调整成功，刷新页面
    location.reload();
  } else {
    alert(result.message);
  }
};
```

---

## 错误处理

### 常见错误码

| 错误码 | 说明           | 示例场景                                 |
| ------ | -------------- | ---------------------------------------- |
| 400    | 请求参数错误   | 缺少必填字段、数据格式错误、业务逻辑错误 |
| 404    | 资源不存在     | 商品不存在、库存记录不存在、颜色不存在   |
| 409    | 资源冲突       | 商品编码重复、库存记录已存在             |
| 500    | 服务器内部错误 | 数据库连接失败、系统异常                 |

### 错误响应格式

**基本错误响应：**

```json
{
  "code": 400,
  "data": null,
  "message": "商品编码不能为空"
}
```

**字段验证错误：**

```json
{
  "code": 400,
  "data": {
    "errors": [
      {
        "field": "code",
        "message": "商品编码不能为空"
      },
      {
        "field": "colorSizeCombinations",
        "message": "颜色尺寸组合不能为空"
      }
    ]
  },
  "message": "参数验证失败"
}
```

**业务逻辑错误：**

```json
{
  "code": 400,
  "data": null,
  "message": "商品 PROD001 不支持颜色 COLOR999 和尺寸 XXL 的组合"
}
```

### 常见错误场景

#### 1. 商品档案相关错误

**商品编码重复：**

```json
{
  "code": 409,
  "data": null,
  "message": "商品编码 PROD001 已存在"
}
```

**颜色不存在：**

```json
{
  "code": 400,
  "data": null,
  "message": "颜色 COLOR999 不存在"
}
```

**辅料不存在：**

```json
{
  "code": 400,
  "data": null,
  "message": "辅料 acc-invalid-uuid 不存在"
}
```

#### 2. 库存相关错误

**库存记录不存在：**

```json
{
  "code": 404,
  "data": null,
  "message": "库存明细 PROD001-COLOR001-M 不存在"
}
```

**库存不足：**

```json
{
  "code": 400,
  "data": null,
  "message": "库存不足，当前可用数量：10，请求数量：15"
}
```

**颜色尺寸组合无效：**

```json
{
  "code": 400,
  "data": null,
  "message": "商品 PROD001 不支持颜色 COLOR001 和尺寸 XXL 的组合"
}
```

### 前端错误处理建议

```javascript
// 统一错误处理函数
const handleApiError = (result) => {
  switch (result.code) {
    case 400:
      if (result.data && result.data.errors) {
        // 字段验证错误
        const errorMessages = result.data.errors
          .map((error) => `${error.field}: ${error.message}`)
          .join('\n');
        alert(`参数错误：\n${errorMessages}`);
      } else {
        // 业务逻辑错误
        alert(`操作失败：${result.message}`);
      }
      break;
    case 404:
      alert(`资源不存在：${result.message}`);
      break;
    case 409:
      alert(`数据冲突：${result.message}`);
      break;
    case 500:
      alert('系统错误，请稍后重试');
      break;
    default:
      alert(`未知错误：${result.message}`);
  }
};

// API调用示例
const createProduct = async (productData) => {
  try {
    const response = await fetch('/api/products', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(productData),
    });

    const result = await response.json();

    if (result.code === 200) {
      alert('商品创建成功');
      // 跳转到商品详情页或刷新列表
    } else {
      handleApiError(result);
    }
  } catch (error) {
    alert('网络错误，请检查网络连接');
  }
};
```

---

## 重要提示

### 1. 数据关联关系

- ✅ **自动生成**：创建商品时会自动生成库存记录
- ✅ **自动同步**：修改商品颜色尺寸组合时会自动同步库存记录
- ⚠️ **级联删除**：删除商品时相关库存记录会被软删除

### 2. 价格计算逻辑

- **基础价格**：在商品档案中定义（clothingCost, accessoryCost, retailPrice等）
- **颜色调整**：颜色级别的价格调整会叠加到基础价格上
- **最终价格** = 基础价格 + 颜色调整价格

**示例：**

```
基础零售价：¥200
红色调整：+¥20
红色最终价格：¥220
```

### 3. 库存状态判断

- **可用数量** = 库存数量 - 预留数量
- **低库存判断**：可用数量 ≤ 安全库存
- **无库存判断**：库存数量 = 0

### 4. 排序功能详解

- **多级排序**：确保结果稳定性
- **价格排序**：按最新进货成本计算
- **数量排序**：优先按可用数量，其次按总数量

**排序优先级：**

1. 用户指定的排序字段
2. 商品编码（ASC）
3. 颜色编码（ASC）
4. 尺寸（ASC）

### 5. 性能建议

- 📊 **大量数据查询**：建议使用筛选条件减少数据量
- 🔄 **批量操作**：优先使用批量接口（如批量获取库存汇总）
- 🖼️ **图片资源**：建议使用CDN加速
- 📄 **分页查询**：合理设置pageSize，建议10-50条

### 6. 前端展示建议

**商品库存页面布局：**

```
商品：PROD001 - 时尚T恤
总库存：230件 | 可用：215件 | 品牌：知名品牌

┌─ 红色 (COLOR001) - 总计：150件 | 可用：140件 ⚠️有低库存
│  ├─ S码：50件 (可用45件) [A区-01-01]
│  ├─ M码：60件 (可用55件) [A区-01-02]
│  └─ L码：40件 (可用40件) [A区-01-03] ⚠️低库存
│  💰 特殊价格：零售价¥130（+¥10调整）
│  📷 专属图片：red-tshirt-1.jpg
│
└─ 蓝色 (COLOR002) - 总计：80件 | 可用：75件
   ├─ M码：30件 (可用28件) [A区-02-01]
   ├─ L码：35件 (可用32件) [A区-02-02]
   └─ XL码：15件 (可用15件) [A区-02-03]
   💰 标准价格：零售价¥120
```

### 7. 开发注意事项

- 🔐 **权限控制**：库存调整操作建议记录操作人信息
- 📝 **操作日志**：所有库存变动都有完整的操作记录
- 🔄 **事务处理**：涉及多表操作的接口都使用数据库事务
- 🛡️ **数据验证**：前端和后端都要进行数据验证
- 📱 **响应式设计**：考虑移动端的展示效果

### 8. 测试建议

- ✅ **创建商品**：测试各种颜色尺寸组合的自动库存生成
- ✅ **库存调整**：测试SET/ADD/SUBTRACT三种调整类型
- ✅ **排序功能**：测试各种排序字段和方向
- ✅ **筛选功能**：测试各种筛选条件的组合
- ✅ **错误处理**：测试各种异常情况的错误响应

---

## 总结

这份API文档涵盖了：

1. **完整的接口说明** - 包括请求参数、响应格式、示例代码
2. **详细的数据结构** - TypeScript接口定义，便于前端开发
3. **实际业务场景** - 从创建商品到库存管理的完整流程
4. **前端实现示例** - JavaScript代码示例，可直接参考使用
5. **错误处理指南** - 完整的错误码说明和处理建议
6. **性能优化建议** - 帮助前端实现高效的用户体验

**核心特性：**

- ✅ 支持颜色级别的复杂价格和辅料差异
- ✅ 精确到颜色+尺寸级别的库存管理
- ✅ 强大的排序和筛选功能
- ✅ 自动化的库存记录生成和同步
- ✅ 前端友好的数据结构设计

前端开发人员可以直接基于这份文档进行开发，所有接口都已经过测试验证！🎉
