// 使用内置的 fetch (Node.js 18+)

const BASE_URL = 'http://127.0.0.1:8080';

async function testSuppliers() {
  console.log('🚀 开始测试供应商模块...');

  try {
    // 1. 登录获取token
    console.log('\n📝 正在登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userCode: 'husky',
        password: '541888',
      }),
    });

    const loginData = await loginResponse.json();
    if (loginData.code !== 200) {
      throw new Error(`登录失败: ${loginData.message}`);
    }

    const token = loginData.data.accessToken;
    console.log('✅ 登录成功');

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };

    // 2. 创建供应商
    console.log('\n📝 创建供应商...');
    const supplierCode = `SUP${Date.now()}`;
    const createResponse = await fetch(`${BASE_URL}/suppliers`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        code: supplierCode,
        name: '广州服装供应商',
        address: '广州市白云区服装批发市场A区101号',
        contactName: '张三',
        contactPhone: '13800138000',
      }),
    });

    const createData = await createResponse.json();
    console.log('创建供应商响应:', JSON.stringify(createData, null, 2));

    if (createData.code !== 200) {
      throw new Error(`创建供应商失败: ${createData.message}`);
    }
    console.log('✅ 供应商创建成功');

    // 3. 获取供应商详情
    console.log('\n📝 获取供应商详情...');
    const detailResponse = await fetch(
      `${BASE_URL}/suppliers/${supplierCode}`,
      {
        method: 'GET',
        headers,
      },
    );

    const detailData = await detailResponse.json();
    console.log('供应商详情:', JSON.stringify(detailData, null, 2));

    if (detailData.code !== 200) {
      throw new Error(`获取供应商详情失败: ${detailData.message}`);
    }
    console.log('✅ 获取供应商详情成功');

    // 4. 更新供应商
    console.log('\n📝 更新供应商...');
    const updateResponse = await fetch(
      `${BASE_URL}/suppliers/${supplierCode}`,
      {
        method: 'PATCH',
        headers,
        body: JSON.stringify({
          name: '广州服装供应商（已更新）',
          contactName: '李四',
          contactPhone: '13900139000',
        }),
      },
    );

    const updateData = await updateResponse.json();
    console.log('更新供应商响应:', JSON.stringify(updateData, null, 2));

    if (updateData.code !== 200) {
      throw new Error(`更新供应商失败: ${updateData.message}`);
    }
    console.log('✅ 供应商更新成功');

    // 5. 获取供应商列表（分页）
    console.log('\n📝 获取供应商列表（分页）...');
    const listResponse = await fetch(
      `${BASE_URL}/suppliers?page=1&pageSize=10`,
      {
        method: 'GET',
        headers,
      },
    );

    const listData = await listResponse.json();
    console.log('供应商列表:', JSON.stringify(listData, null, 2));

    if (listData.code !== 200) {
      throw new Error(`获取供应商列表失败: ${listData.message}`);
    }
    console.log('✅ 获取供应商列表成功');

    // 6. 模糊搜索供应商
    console.log('\n📝 模糊搜索供应商...');
    const searchResponse = await fetch(
      `${BASE_URL}/suppliers?page=1&pageSize=10&search=广州`,
      {
        method: 'GET',
        headers,
      },
    );

    const searchData = await searchResponse.json();
    console.log('搜索结果:', JSON.stringify(searchData, null, 2));

    if (searchData.code !== 200) {
      throw new Error(`搜索供应商失败: ${searchData.message}`);
    }
    console.log('✅ 模糊搜索成功');

    // 7. 测试不传分页参数（应该返回空数据）
    console.log('\n📝 测试不传分页参数...');
    const noPageResponse = await fetch(`${BASE_URL}/suppliers`, {
      method: 'GET',
      headers,
    });

    const noPageData = await noPageResponse.json();
    console.log('不传分页参数响应:', JSON.stringify(noPageData, null, 2));

    if (noPageData.code !== 200) {
      throw new Error(`测试不传分页参数失败: ${noPageData.message}`);
    }
    console.log('✅ 不传分页参数测试成功');

    // 8. 删除供应商
    console.log('\n📝 删除供应商...');
    const deleteResponse = await fetch(
      `${BASE_URL}/suppliers/${supplierCode}`,
      {
        method: 'DELETE',
        headers,
      },
    );

    const deleteData = await deleteResponse.json();
    console.log('删除供应商响应:', JSON.stringify(deleteData, null, 2));

    if (deleteData.code !== 200) {
      throw new Error(`删除供应商失败: ${deleteData.message}`);
    }
    console.log('✅ 供应商删除成功');

    // 9. 验证删除后无法获取详情
    console.log('\n📝 验证删除后无法获取详情...');
    const deletedDetailResponse = await fetch(
      `${BASE_URL}/suppliers/${supplierCode}`,
      {
        method: 'GET',
        headers,
      },
    );

    const deletedDetailData = await deletedDetailResponse.json();
    console.log(
      '删除后获取详情响应:',
      JSON.stringify(deletedDetailData, null, 2),
    );

    if (deletedDetailResponse.status === 404) {
      console.log('✅ 删除验证成功（供应商已不存在）');
    } else {
      console.log('⚠️ 删除验证异常（供应商仍然存在）');
    }

    console.log('\n🎉 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

testSuppliers();
