#!/bin/bash

# 修复PDF生成问题的脚本

set -e

echo "开始修复PDF生成问题..."

# 1. 检查数据库字段是否存在
echo "1. 检查数据库字段..."
PGPASSWORD=54188 psql -h 43.138.236.92 -U postgres -d manager -c "
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'commission_contracts' 
AND column_name IN ('paymentStatus', 'paidAmount', 'remainingDebtAmount', 'lastPaymentDate')
ORDER BY column_name;
"

# 2. 检查Puppeteer依赖
echo "2. 检查Puppeteer依赖..."
node -e "
try {
  const puppeteer = require('puppeteer');
  console.log('Puppeteer is available');
} catch (e) {
  console.log('Puppeteer not found:', e.message);
}
"

# 3. 安装系统依赖（如果需要）
echo "3. 检查系统依赖..."
if ! command -v google-chrome &> /dev/null && ! command -v chromium &> /dev/null; then
    echo "安装Chrome依赖..."
    yum update -y
    yum install -y \
        alsa-lib.x86_64 \
        atk.x86_64 \
        cups-libs.x86_64 \
        gtk3.x86_64 \
        ipa-gothic-fonts \
        libXcomposite.x86_64 \
        libXcursor.x86_64 \
        libXdamage.x86_64 \
        libXext.x86_64 \
        libXi.x86_64 \
        libXrandr.x86_64 \
        libXScrnSaver.x86_64 \
        libXtst.x86_64 \
        pango.x86_64 \
        xorg-x11-fonts-100dpi \
        xorg-x11-fonts-75dpi \
        xorg-x11-fonts-cyrillic \
        xorg-x11-fonts-misc \
        xorg-x11-fonts-Type1 \
        xorg-x11-utils
        
    # 下载并安装Chrome
    wget https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
    yum localinstall -y google-chrome-stable_current_x86_64.rpm
    rm -f google-chrome-stable_current_x86_64.rpm
fi

# 4. 重新安装Puppeteer（如果需要）
echo "4. 重新安装Puppeteer..."
npm install puppeteer --save

# 5. 测试PDF生成
echo "5. 测试PDF生成..."
node -e "
const puppeteer = require('puppeteer');

(async () => {
  try {
    console.log('启动浏览器...');
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
      ],
    });
    
    console.log('创建页面...');
    const page = await browser.newPage();
    
    console.log('设置内容...');
    await page.setContent('<html><body><h1>测试PDF生成</h1></body></html>');
    
    console.log('生成PDF...');
    const pdf = await page.pdf({ format: 'A4' });
    
    console.log('PDF生成成功，大小:', pdf.length, 'bytes');
    
    await browser.close();
    console.log('测试完成');
  } catch (error) {
    console.error('PDF生成测试失败:', error.message);
    process.exit(1);
  }
})();
"

# 6. 重启服务
echo "6. 重启服务..."
pm2 restart backend

echo "PDF生成问题修复完成！"
