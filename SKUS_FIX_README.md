# SKUs 接口 500 错误修复指南

## 问题描述

服务器上的 SKUs 接口返回 500 错误，主要原因是数据库中缺少 `skus` 表。

## 快速修复

### 方法一：自动修复脚本（推荐）

```bash
# 1. 给脚本添加执行权限
chmod +x scripts/fix-skus-deployment.sh
chmod +x scripts/diagnose-skus-issue.sh

# 2. 运行诊断脚本
bash scripts/diagnose-skus-issue.sh

# 3. 运行修复脚本
bash scripts/fix-skus-deployment.sh
```

### 方法二：手动执行迁移

```bash
# 1. 构建项目
pnpm install
pnpm run build

# 2. 运行迁移
pnpm run migration:run

# 3. 重启应用
pm2 restart all
```

### 方法三：直接执行 SQL

如果 TypeORM 迁移不工作，可以直接执行 SQL：

```bash
# 连接数据库并执行 SQL 脚本
PGPASSWORD=54188 psql -h ************* -U postgres -d manager -f scripts/create-skus-table.sql
```

## 问题原因分析

1. **缺少 SKU 表**：系统中没有创建 `skus` 表的迁移文件
2. **TypeORM 同步关闭**：生产环境中 `DB_SYNCHRONIZE=false`，不会自动创建表
3. **迁移未执行**：新的 SKU 表迁移文件没有在服务器上执行

## 修复内容

### 1. 创建的迁移文件

- `src/migrations/1749365279200-CreateSkusTable.ts`：创建 SKU 表的 TypeORM 迁移

### 2. 创建的脚本文件

- `scripts/fix-skus-deployment.sh`：自动修复脚本
- `scripts/diagnose-skus-issue.sh`：问题诊断脚本  
- `scripts/create-skus-table.sql`：直接创建表的 SQL 脚本

### 3. SKU 表结构

```sql
CREATE TABLE skus (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(100) UNIQUE NOT NULL,           -- SKU编码
    name VARCHAR(200) NOT NULL,                  -- 商品名称
    "manufacturerCode" VARCHAR(100),             -- 厂商编码
    "brandCode" VARCHAR(50) NOT NULL,            -- 品牌编码
    "supplierCode" VARCHAR(50) NOT NULL,         -- 供应商编码
    "categoryCode" VARCHAR(50) NOT NULL,         -- 分类编码
    "colorCode" VARCHAR(50) NOT NULL,            -- 颜色编码
    "craftDescription" VARCHAR(500),             -- 工艺描述
    "clothingCost" DECIMAL(10,2),                -- 服装成本
    "retailPrice" DECIMAL(10,2),                 -- 零售价
    "preOrderPrice" DECIMAL(10,2),               -- 预订价
    "restockPrice" DECIMAL(10,2),                -- 补货价
    "spotPrice" DECIMAL(10,2),                   -- 现货价
    accessories JSONB,                           -- 辅料配置
    images JSONB,                                -- 图片数组
    "isDeleted" BOOLEAN DEFAULT FALSE,           -- 删除标记
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 验证修复

修复完成后，可以通过以下方式验证：

### 1. 检查表是否创建成功

```bash
PGPASSWORD=54188 psql -h ************* -U postgres -d manager -c "\d skus"
```

### 2. 测试 API 接口

```bash
# 测试获取 SKU 列表
curl -X GET "http://your-server:8080/skus"

# 测试分页查询
curl -X GET "http://your-server:8080/skus/paginated?page=1&pageSize=10"
```

### 3. 检查应用日志

```bash
pm2 logs
```

## 注意事项

1. **备份数据库**：在执行任何数据库操作前，建议先备份数据库
2. **依赖表检查**：确保 `brands`、`suppliers`、`product_categories`、`colors`、`accessories` 表存在
3. **权限问题**：确保数据库用户有创建表和索引的权限
4. **应用重启**：修复完成后需要重启应用服务

## 故障排除

如果修复后仍有问题：

1. **查看详细错误日志**：`pm2 logs --lines 100`
2. **检查数据库连接**：确认数据库配置正确
3. **验证表结构**：确认所有字段和索引都正确创建
4. **检查依赖关系**：确认关联表数据完整

## 联系支持

如果问题仍然存在，请提供：
- 诊断脚本输出结果
- 应用错误日志
- 数据库错误信息
