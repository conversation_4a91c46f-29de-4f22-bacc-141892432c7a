const https = require('http');

// 测试登录接口
function testLogin() {
  const data = JSON.stringify({
    userCode: 'husky',
    password: '541888'
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  const req = https.request(options, (res) => {
    console.log(`登录接口状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('登录响应:', JSON.stringify(response, null, 2));
        
        if (response.data && response.data.accessToken) {
          console.log('✅ 登录成功！');
          testCompanyAPIs(response.data.accessToken);
        } else {
          console.log('❌ 登录失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 测试公司接口
function testCompanyAPIs(token) {
  console.log('\n🏢 开始测试公司接口...');
  
  // 1. 创建公司
  testCreateCompany(token, () => {
    // 2. 获取公司列表
    testGetCompanies(token, () => {
      // 3. 获取公司详情
      testGetCompanyDetail(token, () => {
        // 4. 更新公司
        testUpdateCompany(token, () => {
          // 5. 删除公司
          testDeleteCompany(token);
        });
      });
    });
  });
}

// 创建公司
function testCreateCompany(token, callback) {
  const data = JSON.stringify({
    code: 'COMP001',
    name: '北京科技有限公司',
    address: '北京市朝阳区xxx街道xxx号',
    socialCreditCode: '91110000123456789X',
    businessLicenseUrl: 'https://example.com/license.jpg',
    managerCode: 'husky',
    employeeCodes: ['husky', 'user001']
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/companies',
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  const req = https.request(options, (res) => {
    console.log(`\n📝 创建公司状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('创建公司响应:', JSON.stringify(response, null, 2));
        
        if (response.code === 200) {
          console.log('✅ 创建公司成功！');
          if (callback) callback();
        } else {
          console.log('❌ 创建公司失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 获取公司列表
function testGetCompanies(token, callback) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/companies?page=1&pageSize=10',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  const req = https.request(options, (res) => {
    console.log(`\n📋 获取公司列表状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('公司列表响应:', JSON.stringify(response, null, 2));
        
        if (response.code === 200) {
          console.log(`✅ 获取公司列表成功！共 ${response.data.total} 个公司`);
          if (callback) callback();
        } else {
          console.log('❌ 获取公司列表失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

// 获取公司详情
function testGetCompanyDetail(token, callback) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/companies/COMP001',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  const req = https.request(options, (res) => {
    console.log(`\n🔍 获取公司详情状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('公司详情响应:', JSON.stringify(response, null, 2));
        
        if (response.code === 200) {
          console.log('✅ 获取公司详情成功！');
          console.log(`   公司名称: ${response.data.name}`);
          console.log(`   店长: ${response.data.manager ? response.data.manager.nickname : '无'}`);
          console.log(`   员工数量: ${response.data.employees ? response.data.employees.length : 0}`);
          if (callback) callback();
        } else {
          console.log('❌ 获取公司详情失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

// 更新公司
function testUpdateCompany(token, callback) {
  const data = JSON.stringify({
    name: '北京科技有限公司（已更新）',
    address: '北京市海淀区xxx街道xxx号'
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/companies/COMP001',
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  const req = https.request(options, (res) => {
    console.log(`\n✏️ 更新公司状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('更新公司响应:', JSON.stringify(response, null, 2));
        
        if (response.code === 200) {
          console.log('✅ 更新公司成功！');
          if (callback) callback();
        } else {
          console.log('❌ 更新公司失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 删除公司
function testDeleteCompany(token) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/companies/COMP001',
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  const req = https.request(options, (res) => {
    console.log(`\n🗑️ 删除公司状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('删除公司响应:', JSON.stringify(response, null, 2));
        
        if (response.code === 200) {
          console.log('✅ 删除公司成功！');
          console.log('\n🎉 所有公司接口测试完成！');
        } else {
          console.log('❌ 删除公司失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

console.log('🚀 开始测试公司接口...');
testLogin();
