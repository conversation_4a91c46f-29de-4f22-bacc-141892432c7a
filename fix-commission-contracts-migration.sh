#!/bin/bash

# 修复commission-contracts迁移错误的脚本

set -e

echo "开始修复commission-contracts迁移错误..."

# 1. 检查当前迁移状态
echo "1. 检查当前迁移状态..."
npm run migration:show

# 2. 如果迁移失败，先回滚失败的迁移
echo "2. 回滚失败的迁移..."
npm run migration:revert || echo "没有需要回滚的迁移"

# 3. 手动清理可能存在的问题字段
echo "3. 清理可能存在的问题字段..."
psql -h 43.138.236.92 -U postgres -d manager -c "
DO \$\$ 
BEGIN
    -- 删除可能存在的问题字段
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='paymentStatus') THEN
        ALTER TABLE commission_contracts DROP COLUMN \"paymentStatus\";
    END IF;
    
    -- 删除可能存在的枚举类型
    DROP TYPE IF EXISTS commission_contracts_paymentstatus_enum;
    
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error during cleanup: %', SQLERRM;
END \$\$;
" || echo "清理过程中出现错误，继续执行..."

# 4. 重新构建项目
echo "4. 重新构建项目..."
npm run build

# 5. 重新运行迁移
echo "5. 重新运行迁移..."
npm run migration:run

# 6. 重启服务
echo "6. 重启服务..."
pm2 restart backend

# 7. 检查服务状态
echo "7. 检查服务状态..."
pm2 status

echo "修复完成！"
