# 简单测试公司接口返回用户姓名
Write-Host "Testing company API with user names..." -ForegroundColor Green

try {
    # 1. 登录
    $loginBody = @{
        userCode = "husky"
        password = "541888"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.data.accessToken
    Write-Host "Login successful" -ForegroundColor Green

    # 2. 获取公司列表（检查是否包含managerName）
    $headers = @{
        "Authorization" = "Bearer $token"
    }
    
    $listResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies?page=1&pageSize=5" -Method GET -Headers $headers
    Write-Host "Company list response:" -ForegroundColor Yellow
    Write-Host ($listResponse | ConvertTo-Json -Depth 10) -ForegroundColor Cyan

    # 3. 获取第一个公司的详情（如果存在）
    if ($listResponse.data.companies.Count -gt 0) {
        $firstCompanyCode = $listResponse.data.companies[0].code
        Write-Host "Getting details for company: $firstCompanyCode" -ForegroundColor Yellow
        
        $detailResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies/$firstCompanyCode" -Method GET -Headers $headers
        Write-Host "Company detail response:" -ForegroundColor Yellow
        Write-Host ($detailResponse | ConvertTo-Json -Depth 10) -ForegroundColor Cyan
    } else {
        Write-Host "No companies found" -ForegroundColor Red
    }

    Write-Host "Test completed successfully!" -ForegroundColor Magenta
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}
