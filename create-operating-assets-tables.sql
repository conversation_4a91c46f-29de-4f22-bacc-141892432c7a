-- 创建运营资产表
CREATE TABLE IF NOT EXISTS operating_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "totalAmount" DECIMAL(15,2) NOT NULL DEFAULT 0 COMMENT '运营资产总金额',
    "isDeleted" BOOLEAN NOT NULL DEFAULT false COMMENT '是否已删除',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    "deletedAt" TIMESTAMP NULL COMMENT '删除时间'
);

-- 创建运营资产类型枚举
CREATE TYPE operating_asset_type AS ENUM (
    'human_resources',
    'warehouse_logistics', 
    'administrative_consumption',
    'other'
);

-- 创建运营资产明细表
CREATE TABLE IF NOT EXISTS operating_asset_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "operatingAssetId" UUID NOT NULL COMMENT '运营资产ID',
    type operating_asset_type NOT NULL COMMENT '运营资产类型',
    amount DECIMAL(15,2) NOT NULL COMMENT '明细金额（保留两位小数）',
    screenshot VARCHAR(500) NOT NULL COMMENT '截图URL（必填）',
    remark TEXT NULL COMMENT '备注（可选）',
    "isDeleted" BOOLEAN NOT NULL DEFAULT false COMMENT '是否已删除',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    "deletedAt" TIMESTAMP NULL COMMENT '删除时间',
    
    -- 外键约束
    CONSTRAINT fk_operating_asset_details_operating_asset 
        FOREIGN KEY ("operatingAssetId") REFERENCES operating_assets(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_operating_asset_details_operating_asset_id 
    ON operating_asset_details("operatingAssetId");
    
CREATE INDEX IF NOT EXISTS idx_operating_asset_details_type 
    ON operating_asset_details(type);
    
CREATE INDEX IF NOT EXISTS idx_operating_asset_details_created_at 
    ON operating_asset_details("createdAt");
    
CREATE INDEX IF NOT EXISTS idx_operating_asset_details_is_deleted 
    ON operating_asset_details("isDeleted");

-- 添加更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_operating_assets_updated_at 
    BEFORE UPDATE ON operating_assets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入注释（PostgreSQL特定语法）
COMMENT ON TABLE operating_assets IS '运营资产表';
COMMENT ON TABLE operating_asset_details IS '运营资产明细表';

COMMENT ON COLUMN operating_assets.id IS '主键ID';
COMMENT ON COLUMN operating_assets."totalAmount" IS '运营资产总金额';
COMMENT ON COLUMN operating_assets."isDeleted" IS '是否已删除';
COMMENT ON COLUMN operating_assets."createdAt" IS '创建时间';
COMMENT ON COLUMN operating_assets."updatedAt" IS '更新时间';
COMMENT ON COLUMN operating_assets."deletedAt" IS '删除时间';

COMMENT ON COLUMN operating_asset_details.id IS '主键ID';
COMMENT ON COLUMN operating_asset_details."operatingAssetId" IS '运营资产ID';
COMMENT ON COLUMN operating_asset_details.type IS '运营资产类型';
COMMENT ON COLUMN operating_asset_details.amount IS '明细金额（保留两位小数）';
COMMENT ON COLUMN operating_asset_details.screenshot IS '截图URL（必填）';
COMMENT ON COLUMN operating_asset_details.remark IS '备注（可选）';
COMMENT ON COLUMN operating_asset_details."isDeleted" IS '是否已删除';
COMMENT ON COLUMN operating_asset_details."createdAt" IS '创建时间';
COMMENT ON COLUMN operating_asset_details."deletedAt" IS '删除时间';
