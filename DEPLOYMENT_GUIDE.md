# 🚀 生产环境部署指南

## ⚠️ 重要警告

**不要直接上传文件到服务器！** 请按照以下步骤进行安全部署。

## 📋 部署前检查清单

### 1. 环境要求检查

- [ ] Node.js >= 16.x
- [ ] PostgreSQL >= 12.x
- [ ] npm >= 8.x 或 yarn >= 1.22.x
- [ ] 服务器内存 >= 2GB
- [ ] 磁盘空间 >= 10GB

### 2. 数据库准备

#### 2.1 备份现有数据库
```bash
# 创建数据库备份
pg_dump -h localhost -U postgres -d manager > backup_$(date +%Y%m%d_%H%M%S).sql
```

#### 2.2 检查数据库连接
```bash
# 测试数据库连接
psql -h localhost -U postgres -d manager -c "SELECT version();"
```

### 3. 代码部署步骤

#### 3.1 使用Git部署（推荐）
```bash
# 1. 克隆或拉取最新代码
git clone <your-repo-url> /path/to/app
cd /path/to/app

# 2. 切换到正确的分支
git checkout main  # 或您的生产分支

# 3. 安装依赖
npm install --production

# 4. 构建项目
npm run build
```

#### 3.2 文件上传部署（不推荐）
如果必须使用文件上传：
```bash
# 1. 在本地构建
npm run build

# 2. 压缩文件（排除不必要的文件）
tar -czf app.tar.gz \
  --exclude=node_modules \
  --exclude=.git \
  --exclude=*.log \
  --exclude=.env.local \
  .

# 3. 上传到服务器
scp app.tar.gz user@server:/path/to/app/

# 4. 在服务器上解压
cd /path/to/app
tar -xzf app.tar.gz

# 5. 安装依赖
npm install --production
```

### 4. 环境配置

#### 4.1 创建生产环境配置
```bash
# 复制环境配置文件
cp .env.example .env.production

# 编辑生产环境配置
nano .env.production
```

#### 4.2 生产环境配置示例
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password
DB_DATABASE=manager

# 应用配置
NODE_ENV=production
PORT=3000
APP_NAME=Manager System

# JWT配置
JWT_SECRET=your_very_secure_jwt_secret_key_here

# 文件上传配置
UPLOAD_PATH=/var/uploads
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/manager/app.log
```

### 5. 数据库迁移

#### 5.1 检查迁移状态
```bash
# 查看待执行的迁移
npm run migration:show
```

#### 5.2 执行迁移（关键步骤）
```bash
# 执行数据库迁移
npm run migration:run

# 预期输出：
# Migration CreateInventoryTables1704067200000 has been executed successfully.
# Migration MigrateProductsToInventory1704067300000 has been executed successfully.
```

#### 5.3 验证迁移结果
```bash
# 连接数据库验证
psql -h localhost -U postgres -d manager

# 检查新表是否创建成功
\dt inventory*

# 检查库存记录是否生成
SELECT COUNT(*) FROM inventory_details;

# 检查商品和库存的关联
SELECT 
  p.code, 
  p.name, 
  COUNT(i.id) as inventory_count 
FROM products p 
LEFT JOIN inventory_details i ON p.code = i."productCode" 
WHERE p."isDeleted" = false 
GROUP BY p.code, p.name 
LIMIT 10;
```

### 6. 应用启动

#### 6.1 使用PM2管理进程（推荐）
```bash
# 安装PM2
npm install -g pm2

# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'manager-api',
    script: 'dist/main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/manager/error.log',
    out_file: '/var/log/manager/out.log',
    log_file: '/var/log/manager/combined.log',
    time: true
  }]
};
EOF

# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

#### 6.2 使用systemd管理（备选）
```bash
# 创建systemd服务文件
sudo cat > /etc/systemd/system/manager-api.service << EOF
[Unit]
Description=Manager API Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/app
ExecStart=/usr/bin/node dist/main.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl enable manager-api
sudo systemctl start manager-api
```

### 7. 健康检查

#### 7.1 API健康检查
```bash
# 检查应用是否正常启动
curl http://localhost:3000/health

# 检查商品接口
curl http://localhost:3000/products?page=1&pageSize=5

# 检查库存接口
curl http://localhost:3000/inventory?page=1&pageSize=5
```

#### 7.2 数据库连接检查
```bash
# 检查数据库连接池
curl http://localhost:3000/health/db
```

### 8. 反向代理配置（Nginx）

#### 8.1 Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # API代理
    location /api/ {
        proxy_pass http://localhost:3000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 文件上传大小限制
        client_max_body_size 50M;
    }

    # 静态文件服务
    location /uploads/ {
        alias /var/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 9. 监控和日志

#### 9.1 日志配置
```bash
# 创建日志目录
sudo mkdir -p /var/log/manager
sudo chown www-data:www-data /var/log/manager

# 配置日志轮转
sudo cat > /etc/logrotate.d/manager << EOF
/var/log/manager/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload manager-api
    endscript
}
EOF
```

#### 9.2 监控脚本
```bash
# 创建监控脚本
cat > monitor.sh << EOF
#!/bin/bash

# 检查应用状态
if ! curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "$(date): API health check failed" >> /var/log/manager/monitor.log
    pm2 restart manager-api
fi

# 检查数据库连接
if ! psql -h localhost -U postgres -d manager -c "SELECT 1;" > /dev/null 2>&1; then
    echo "$(date): Database connection failed" >> /var/log/manager/monitor.log
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is ${DISK_USAGE}%" >> /var/log/manager/monitor.log
fi
EOF

chmod +x monitor.sh

# 添加到crontab
echo "*/5 * * * * /path/to/monitor.sh" | crontab -
```

## 🚨 故障排除

### 常见问题和解决方案

#### 1. 迁移失败
```bash
# 查看迁移错误
npm run migration:show

# 手动回滚迁移
npm run migration:revert

# 重新执行迁移
npm run migration:run
```

#### 2. 应用启动失败
```bash
# 查看详细错误日志
pm2 logs manager-api

# 检查端口占用
netstat -tlnp | grep :3000

# 检查环境变量
pm2 env 0
```

#### 3. 数据库连接失败
```bash
# 检查数据库服务状态
sudo systemctl status postgresql

# 检查数据库连接
psql -h localhost -U postgres -d manager

# 检查防火墙设置
sudo ufw status
```

#### 4. 权限问题
```bash
# 修复文件权限
sudo chown -R www-data:www-data /path/to/app
sudo chmod -R 755 /path/to/app

# 修复上传目录权限
sudo chown -R www-data:www-data /var/uploads
sudo chmod -R 755 /var/uploads
```

## ✅ 部署完成检查

- [ ] 数据库迁移成功执行
- [ ] 应用正常启动
- [ ] API接口响应正常
- [ ] 库存数据正确生成
- [ ] 日志正常输出
- [ ] 监控脚本运行
- [ ] 反向代理配置正确
- [ ] SSL证书配置（如需要）

## 📞 紧急回滚

如果部署出现问题，可以快速回滚：

```bash
# 1. 停止应用
pm2 stop manager-api

# 2. 回滚数据库迁移
npm run migration:revert
npm run migration:revert

# 3. 恢复数据库备份
psql -h localhost -U postgres -d manager < backup_YYYYMMDD_HHMMSS.sql

# 4. 切换到上一个版本
git checkout previous-stable-tag

# 5. 重新构建和启动
npm run build
pm2 start manager-api
```

记住：**安全第一，测试充分，备份完整！** 🛡️
