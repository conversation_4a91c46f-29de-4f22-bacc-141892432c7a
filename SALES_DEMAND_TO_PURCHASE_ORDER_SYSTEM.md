# 销售需求订单到商品采购订单系统

## 系统概述

本系统实现了从销售需求订单到商品采购订单的完整业务流程，支持销售人员创建需求订单，生产人员合并多个需求订单为采购订单的工作流程。

## 核心功能

### 1. 销售需求订单管理
- ✅ 销售人员创建需求订单
- ✅ 自动计算库存缺口
- ✅ 订单审核流程（草稿 → 已提交 → 已审核）
- ✅ 支持优先级管理（紧急/高/普通/低）
- ✅ 预估金额自动计算

### 2. 商品采购订单管理
- ✅ 合并多个需求订单为采购订单
- ✅ 自动库存缺口分析
- ✅ 按供应商分组采购
- ✅ 采购订单状态管理
- ✅ 入库数量跟踪

### 3. 数据统计支持
- ✅ 完善的索引设计
- ✅ 统计视图支持
- ✅ 冗余字段便于分析
- ✅ 时间维度统计

## 数据库设计

### 核心表结构

#### 1. 销售需求订单表 (sales_demand_orders)
```sql
- id: UUID主键
- order_number: 订单编号（SD + 年月日时分秒）
- customer_code: 客户编码（可选）
- sales_person_code: 销售人员编码
- demand_date: 需求日期
- expected_delivery_date: 期望交货日期
- priority_level: 优先级（urgent/high/normal/low）
- status: 状态（draft/submitted/approved/merged/completed/cancelled）
- total_quantity: 总需求数量（自动计算）
- estimated_total_amount: 预估总金额（自动计算）
```

#### 2. 销售需求订单明细表 (sales_demand_order_details)
```sql
- id: UUID主键
- sales_demand_order_id: 需求订单ID
- product_code: 商品编码
- color_code: 颜色编码
- size_code: 尺寸编码
- sku_code: SKU编码
- demand_quantity: 需求数量
- current_stock: 当前库存（创建时记录）
- shortage_quantity: 缺货数量（需求-库存）
- unit_price: 预估单价
- estimated_amount: 预估小计金额
```

#### 3. 商品采购订单表 (product_purchase_orders)
```sql
- id: UUID主键
- order_number: 采购订单编号（PPO-供应商编码-年月日时分秒）
- supplier_code: 供应商编码
- order_date: 采购订单日期
- total_amount: 合计金额（自动计算）
- total_quantity: 合计数量（自动计算）
- status: 订单状态（draft/confirmed/producing/shipped/received/completed/cancelled）
- merged_demand_order_ids: 合并的需求订单ID列表（JSON格式）
- unique_sku_count: 不同SKU数量（统计字段）
- unique_product_count: 涉及商品数量（统计字段）
```

#### 4. 商品采购订单明细表 (product_purchase_order_details)
```sql
- id: UUID主键
- product_purchase_order_id: 采购订单ID
- sku_code: SKU编码
- purchase_quantity: 采购数量
- unit_price: 采购单价
- total_amount: 小计金额
- source_demand_orders: 来源需求订单（JSON格式）
- total_demand_quantity: 总需求数量
- received_quantity: 已入库数量
- pending_receive_quantity: 待入库数量
```

## API接口

### 销售需求订单接口

#### 创建需求订单
```http
POST /sales-demand-orders
Content-Type: application/json

{
  "customerCode": "CUST001",
  "salesPersonCode": "SALES001",
  "demandDate": "2025-01-19",
  "expectedDeliveryDate": "2025-02-19",
  "priorityLevel": "normal",
  "remark": "春季新品需求",
  "details": [
    {
      "productCode": "PROD001",
      "colorCode": "COLOR001",
      "sizeCode": "M",
      "demandQuantity": 50,
      "unitPrice": 100.00,
      "remark": "客户指定颜色"
    }
  ]
}
```

#### 分页查询需求订单
```http
GET /sales-demand-orders?page=1&pageSize=20&status=approved&salesPersonCode=SALES001
```

#### 提交审核
```http
POST /sales-demand-orders/{id}/submit
```

#### 审核通过
```http
POST /sales-demand-orders/{id}/approve
Content-Type: application/json

{
  "approvedByUserCode": "MANAGER001"
}
```

### 商品采购订单接口

#### 合并需求订单为采购订单
```http
POST /product-purchase-orders/merge-demands
Content-Type: application/json

{
  "demandOrderIds": ["uuid1", "uuid2", "uuid3"],
  "supplierCode": "SUP001",
  "expectedDeliveryDate": "2025-02-19",
  "remark": "合并春季新品需求订单",
  "createdByUserCode": "USER001"
}
```

#### 分析库存缺口
```http
POST /product-purchase-orders/analyze-shortage
Content-Type: application/json

{
  "demandOrderIds": ["uuid1", "uuid2", "uuid3"]
}
```

#### 确认采购订单
```http
POST /product-purchase-orders/{id}/confirm
Content-Type: application/json

{
  "confirmedByUserCode": "MANAGER001"
}
```

#### 更新订单状态
```http
POST /product-purchase-orders/{id}/update-status
Content-Type: application/json

{
  "status": "received",
  "userCode": "WAREHOUSE001"
}
```

## 业务流程

### 1. 销售需求订单流程
```
销售人员创建需求订单 → 提交审核 → 销售主管审核 → 等待合并
```

### 2. 采购订单流程
```
生产人员查看已审核需求 → 选择需求订单合并 → 系统分析库存缺口 → 生成采购订单 → 确认采购 → 执行采购 → 入库更新库存
```

### 3. 状态流转

#### 需求订单状态
- `draft` → `submitted` → `approved` → `merged` → `completed`
- 可取消：任何状态 → `cancelled`

#### 采购订单状态
- `draft` → `confirmed` → `producing` → `shipped` → `received` → `completed`
- 可取消：`draft`/`confirmed` → `cancelled`

## 数据统计功能

### 1. 统计视图

#### 需求订单统计
```sql
SELECT * FROM v_sales_demand_order_stats 
WHERE month = '2025-01-01'::date;
```

#### 采购订单统计
```sql
SELECT * FROM v_product_purchase_order_stats 
WHERE supplier_code = 'SUP001';
```

#### 商品需求统计
```sql
SELECT * FROM v_product_demand_stats 
WHERE brand_code = 'BRAND001';
```

### 2. 常用统计查询

#### 销售人员需求统计
```sql
SELECT 
  sales_person_code,
  COUNT(*) as order_count,
  SUM(total_quantity) as total_quantity,
  SUM(estimated_total_amount) as total_amount
FROM sales_demand_orders 
WHERE demand_date >= '2025-01-01' 
  AND is_deleted = false
GROUP BY sales_person_code;
```

#### 供应商采购统计
```sql
SELECT 
  supplier_code,
  COUNT(*) as order_count,
  SUM(total_amount) as total_amount,
  AVG(total_amount) as avg_amount
FROM product_purchase_orders 
WHERE order_date >= '2025-01-01' 
  AND is_deleted = false
GROUP BY supplier_code;
```

## 部署说明

### 1. 数据库迁移
```bash
# 执行数据库迁移脚本
psql -h localhost -U postgres -d manager -f create-demand-purchase-orders-tables.sql
```

### 2. 启动服务
```bash
# 安装依赖
pnpm install

# 启动开发服务
pnpm run start:dev

# 生产环境启动
pnpm run start:prod
```

### 3. API文档
启动服务后访问：`http://localhost:3000/api` 查看完整的API文档。

## 权限设计

- **销售人员**：可创建、查看自己的需求订单
- **销售主管**：可审核需求订单
- **生产人员**：可查看已审核的需求订单，可合并为采购订单
- **采购人员**：可执行采购订单
- **仓库人员**：可进行入库操作

## 注意事项

1. **数据一致性**：所有涉及多表操作的功能都使用事务处理
2. **软删除**：删除操作为软删除，数据不会物理删除
3. **状态控制**：严格的状态转换验证，防止非法状态变更
4. **库存同步**：需求订单创建时会记录当前库存，用于缺口计算
5. **性能优化**：合理的索引设计，支持高效的查询和统计

## 扩展功能

系统设计时已考虑未来扩展：
- 支持批量操作
- 支持Excel导入导出
- 支持消息通知
- 支持工作流引擎集成
- 支持更复杂的库存策略
