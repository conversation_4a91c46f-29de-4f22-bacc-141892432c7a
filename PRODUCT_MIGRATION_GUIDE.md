# 产品和库存管理系统迁移指南

## 📋 迁移概述

本次迁移将现有的产品管理系统重构为支持SPU/SKU分离的完整库存管理系统。

### 主要变更
- ✅ 产品表添加`categoryCode`字段关联商品分类
- ✅ 库存表添加完整的SKU管理字段（18个新字段）
- ✅ 实现自动SKU编码生成（格式：产品编码-颜色编码-尺寸编码）
- ✅ 支持库存预警、统计和多仓库管理
- ✅ 保留原有字段确保向后兼容

## 🚀 快速部署

### 开发环境
```bash
# 一键部署（包含备份、迁移、验证）
npm run deploy:migration development

# 验证迁移结果
npm run verify:product-migration

# 启动应用
npm run start:dev
```

### 生产环境
```bash
# 一键部署（包含备份、迁移、验证）
npm run deploy:migration:prod

# 验证迁移结果（生产环境）
PGPASSWORD=54188 psql -h ************* -U postgres -d manager -f scripts/verify-product-migration.sql

# 重启应用
pm2 restart all
```

## 📊 数据库变更详情

### 产品表 (products)
```sql
-- 新增字段
ALTER TABLE products ADD COLUMN categoryCode varchar(50);
```

### 库存表 (inventory_details)
```sql
-- 核心SKU字段
ADD COLUMN skuCode varchar(150);        -- SKU编码
ADD COLUMN sizeCode varchar(10);        -- 尺寸编码

-- 库存数量字段
ADD COLUMN totalStock integer DEFAULT 0;     -- 总库存
ADD COLUMN actualStock integer DEFAULT 0;    -- 可用库存
ADD COLUMN reservedStock integer DEFAULT 0;  -- 预留库存
ADD COLUMN damagedStock integer DEFAULT 0;   -- 损坏库存

-- 采购相关字段
ADD COLUMN purchasingStock integer DEFAULT 0;    -- 采购在途
ADD COLUMN needPurchaseStock integer DEFAULT 0;  -- 建议采购

-- 预警字段
ADD COLUMN safetyStock integer DEFAULT 10;   -- 安全库存
ADD COLUMN minStock integer DEFAULT 5;       -- 最小库存
ADD COLUMN maxStock integer DEFAULT 1000;    -- 最大库存

-- 成本字段
ADD COLUMN avgCost decimal(10,2) DEFAULT 0;     -- 平均成本
ADD COLUMN latestCost decimal(10,2) DEFAULT 0;  -- 最新成本

-- 业务字段
ADD COLUMN isActive boolean DEFAULT true;              -- 是否启用
ADD COLUMN warehouseLocation varchar(100) DEFAULT 'A区'; -- 仓库位置

-- 统计字段
ADD COLUMN totalInbound integer DEFAULT 0;      -- 总入库
ADD COLUMN totalOutbound integer DEFAULT 0;     -- 总出库
ADD COLUMN lastInboundDate timestamp;           -- 最后入库时间
ADD COLUMN lastOutboundDate timestamp;          -- 最后出库时间
```

## 🔍 迁移验证

部署后会自动执行以下验证：

### 1. 结构验证
- ✅ 检查新字段是否正确创建
- ✅ 检查索引是否正确建立
- ✅ 检查外键约束是否生效

### 2. 数据验证
- ✅ 检查SKU编码是否正确生成
- ✅ 检查库存数据是否正确迁移
- ✅ 检查产品分类是否正确关联

### 3. 功能验证
- ✅ 测试产品创建API
- ✅ 测试SKU生成功能
- ✅ 测试库存查询API

## 🔄 回滚方案

如果迁移出现问题，提供多种回滚方式：

### 1. 使用备份文件回滚（推荐）
```bash
# 开发环境
npm run rollback:product-migration development ./backups/backup_development_20241217_120000.sql

# 生产环境
./scripts/rollback-product-migration.sh production ./backups/backup_production_20241217_120000.sql
```

### 2. 手动回滚
```bash
# 删除新增字段（谨慎使用）
npm run rollback:product-migration development
```

### 3. TypeORM回滚
```bash
# 回滚最后一次迁移
npm run migration:revert
```

## 📝 API变更说明

### 产品创建API
```javascript
// 新增字段
{
  "categoryCode": "DEFAULT",  // 可选，默认使用DEFAULT分类
  // ... 其他字段保持不变
}

// 响应新增SKU列表
{
  "message": "产品创建成功",
  "skus": [
    {
      "skuCode": "PROD001-RED-M",
      "productCode": "PROD001",
      "colorCode": "RED",
      "sizeCode": "M"
    }
  ]
}
```

### 库存查询API
```javascript
// 新增字段
{
  "skuCode": "PROD001-RED-M",
  "sizeCode": "M",
  "totalStock": 100,
  "actualStock": 80,
  "reservedStock": 15,
  "damagedStock": 5,
  // ... 其他新字段
}
```

## ⚠️ 注意事项

### 1. 字段映射
由于PostgreSQL字段名转换为小写，实体定义中使用了字段映射：
```typescript
@Column({ name: 'skucode' })
skuCode: string;
```

### 2. 向后兼容
- 保留了原有的`size`和`quantity`字段
- 新字段设置为可空或有默认值
- 现有API继续可用

### 3. 性能考虑
- 新增了必要的索引
- 建议部署后执行`ANALYZE`更新统计信息

## 🛠️ 故障排除

### 常见问题

#### 1. 迁移脚本执行失败
```bash
# 检查数据库连接
psql -h localhost -U postgres -d manager -c "SELECT version();"

# 检查迁移状态
npm run migration:show

# 查看详细错误
npm run migration:run 2>&1 | tee migration.log
```

#### 2. SKU编码重复
```sql
-- 检查重复SKU
SELECT skucode, COUNT(*) 
FROM inventory_details 
WHERE skucode IS NOT NULL 
GROUP BY skucode 
HAVING COUNT(*) > 1;
```

#### 3. 应用启动失败
```bash
# 检查实体映射
npm run build

# 检查数据库连接配置
cat src/config/database.config.ts
```

## 📞 支持

如果遇到问题：
1. 查看迁移日志：`migration.log`
2. 运行验证脚本：`npm run verify:product-migration`
3. 检查备份文件是否可用
4. 联系开发团队

---

**重要提醒**: 
- 生产环境部署前请务必在开发环境完整测试
- 确保数据库备份可用且可恢复
- 部署期间建议暂停业务操作
- 迁移脚本已经过充分测试，但仍建议在非高峰期执行
