# Test file upload functionality
Write-Host "Testing file upload functionality..." -ForegroundColor Green

try {
    # 1. Login first
    $loginBody = @{
        userCode = "husky"
        password = "541888"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.data.accessToken
    Write-Host "Login successful" -ForegroundColor Green

    # 2. Create a test image file (1x1 pixel PNG)
    $testImagePath = "test-image.png"
    # Create a minimal PNG file (1x1 pixel transparent)
    $pngBytes = [byte[]](0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00, 0x0B, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00, 0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82)
    [System.IO.File]::WriteAllBytes($testImagePath, $pngBytes)
    Write-Host "Test image created: $testImagePath" -ForegroundColor Yellow

    # 3. Test single image upload
    Write-Host "Testing single image upload..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $token"
    }
    
    # Use Invoke-WebRequest for file upload (better multipart support)
    $form = @{
        file = Get-Item $testImagePath
        folder = "test-uploads"
    }
    
    $uploadResponse = Invoke-WebRequest -Uri "http://127.0.0.1:8080/file-upload/image" -Method POST -Form $form -Headers $headers
    $uploadResult = $uploadResponse.Content | ConvertFrom-Json
    
    Write-Host "Upload response:" -ForegroundColor Cyan
    Write-Host ($uploadResult | ConvertTo-Json -Depth 10) -ForegroundColor White
    
    if ($uploadResult.code -eq 200) {
        Write-Host "✅ Single image upload successful!" -ForegroundColor Green
        Write-Host "Uploaded URL: $($uploadResult.data.url)" -ForegroundColor Green
    } else {
        Write-Host "❌ Single image upload failed" -ForegroundColor Red
    }

    # 4. Clean up test file
    Remove-Item $testImagePath -Force
    Write-Host "Test file cleaned up" -ForegroundColor Yellow

    Write-Host "File upload test completed!" -ForegroundColor Magenta

} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
    
    # Clean up test file if it exists
    if (Test-Path $testImagePath) {
        Remove-Item $testImagePath -Force
    }
}
