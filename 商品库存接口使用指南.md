# 商品档案与库存管理接口使用指南

## 快速概览

### 核心概念
- **商品档案**：管理商品基本信息（名称、价格、规格等）
- **库存管理**：管理具体的库存数量（按商品-颜色-尺寸维度）
- **自动同步**：创建/更新商品时自动生成/同步库存记录

### 接口分工
```
商品档案接口 (/products)     ←→     库存管理接口 (/inventory)
     ↓                                    ↓
  商品基本信息                        库存数量管理
  价格规格管理                        仓储位置管理
  品牌供应商关联                      库存变动记录
```

## 常用接口速查

### 商品档案接口

| 接口 | 方法 | 功能 | 说明 |
|-----|------|------|------|
| `/products` | POST | 创建商品 | 自动生成库存记录 |
| `/products` | GET | 商品列表 | 支持分页和筛选 |
| `/products/{id}` | GET | 商品详情 | 完整商品信息 |
| `/products/{id}` | PATCH | 更新商品 | 自动同步库存 |
| `/products/{id}` | DELETE | 删除商品 | 软删除 |

### 库存管理接口

| 接口 | 方法 | 功能 | 说明 |
|-----|------|------|------|
| `/inventory` | POST | 创建库存明细 | 手动创建 |
| `/inventory` | GET | 库存列表 | 按颜色分组 |
| `/inventory/products/{code}` | GET | 商品库存详情 | 完整库存信息 |
| `/inventory/adjust` | POST | 调整库存 | 支持增减设置 |
| `/inventory/batch` | POST | 批量操作 | 批量创建库存 |

## 典型使用场景

### 场景1：新品上架

```bash
# 1. 创建商品档案（自动生成库存记录）
POST /products
{
  "code": "SHIRT001",
  "name": "商务衬衫",
  "brandCode": "BRAND001",
  "supplierCode": "SUP001",
  "clothingCost": 80.0,
  "retailPrice": 200.0,
  "colorSizeCombinations": [
    {
      "colorCode": "WHITE",
      "sizes": ["S", "M", "L", "XL"]
    }
  ]
}

# 2. 采购入库（调整库存数量）
POST /inventory/adjust
{
  "productCode": "SHIRT001",
  "colorCode": "WHITE",
  "size": "M",
  "adjustmentType": "ADD",
  "quantity": 100,
  "reason": "采购入库"
}
```

### 场景2：查询商品库存

```bash
# 1. 查询商品列表
GET /products?page=1&pageSize=10

# 2. 查询具体商品的库存情况
GET /inventory/products/SHIRT001

# 3. 查询库存明细列表
GET /inventory?page=1&pageSize=10&productCode=SHIRT001
```

### 场景3：商品规格调整

```bash
# 1. 更新商品颜色尺寸组合（自动同步库存）
PATCH /products/{id}
{
  "colorSizeCombinations": [
    {
      "colorCode": "WHITE",
      "sizes": ["S", "M", "L", "XL", "XXL"]  // 新增XXL
    },
    {
      "colorCode": "BLACK",  // 新增黑色
      "sizes": ["M", "L", "XL"]
    }
  ]
}

# 2. 查看同步后的库存记录
GET /inventory/products/SHIRT001
```

## 数据结构说明

### 商品档案核心字段

```json
{
  "code": "商品编码（唯一）",
  "name": "商品名称",
  "brandCode": "品牌编码",
  "supplierCode": "供应商编码",
  "clothingCost": "服装成本",
  "accessoryCost": "辅料成本",
  "retailPrice": "零售价",
  "preOrderPrice": "预订价",
  "restockPrice": "补货价",
  "spotPrice": "现货价",
  "colorSizeCombinations": [
    {
      "colorCode": "颜色编码",
      "sizes": ["尺寸数组"],
      "images": ["图片数组"],
      "accessories": [{"accessoryId": "辅料ID", "quantity": 数量}]
    }
  ]
}
```

### 库存明细核心字段

```json
{
  "productCode": "商品编码",
  "colorCode": "颜色编码",
  "size": "尺寸",
  "quantity": "库存数量",
  "reservedQuantity": "预留数量",
  "availableQuantity": "可用数量",
  "safetyStock": "安全库存",
  "latestCost": "最新成本",
  "warehouseLocation": "仓库位置"
}
```

## 重要业务规则

### 1. 自动同步机制
- 创建商品时，系统自动为每个颜色尺寸组合生成库存记录（初始数量为0）
- 更新商品规格时，系统自动同步库存记录（新增/删除对应记录）
- 删除商品时，系统自动软删除所有相关库存记录

### 2. 库存计算规则
- **可用数量** = 库存数量 - 预留数量
- **低库存判断** = 可用数量 ≤ 安全库存
- **库存调整限制** = 调整后数量不能小于0

### 3. 数据一致性保证
- 所有涉及商品和库存的操作都使用数据库事务
- 外键约束确保数据引用完整性
- 唯一约束防止重复记录

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|-------|------|---------|
| 400 | 商品编码已存在 | 使用不同的商品编码 |
| 400 | 品牌不存在或未激活 | 检查品牌编码和激活状态 |
| 400 | 供应商不存在 | 检查供应商编码 |
| 400 | 颜色编码不存在 | 先创建颜色记录 |
| 400 | 库存数量不能为负 | 检查调整数量 |
| 404 | 商品不存在 | 检查商品ID或编码 |
| 404 | 库存记录不存在 | 检查库存明细ID |

### 错误响应格式

```json
{
  "code": 400,
  "data": null,
  "message": "具体错误信息"
}
```

## 性能优化建议

### 1. 查询优化
- 使用分页参数避免大量数据加载
- 合理使用筛选条件缩小查询范围
- 批量查询时使用批量接口

### 2. 缓存策略
- 商品基本信息可以缓存1小时
- 品牌供应商信息可以缓存24小时
- 库存汇总信息建议缓存5分钟

### 3. 并发控制
- 库存调整操作使用乐观锁
- 避免同时调整同一库存记录
- 大批量操作考虑分批处理

## 监控指标

### 业务指标
- 商品创建成功率
- 库存调整频率
- 低库存商品数量
- 库存周转率

### 技术指标
- 接口响应时间
- 接口成功率
- 数据库连接数
- 缓存命中率

## 最佳实践

### 1. 接口调用顺序
```
创建商品 → 查看自动生成的库存 → 调整库存数量 → 监控库存状态
```

### 2. 数据维护
- 定期检查商品和库存数据一致性
- 及时处理低库存预警
- 定期清理软删除的历史数据

### 3. 权限管理
- 商品档案管理权限与库存管理权限分离
- 库存调整权限需要严格控制
- 重要操作需要审计日志

## 常见问题

### Q: 为什么创建商品后库存数量是0？
A: 系统只自动生成库存记录结构，实际库存数量需要通过采购入库等操作调整。

### Q: 更新商品规格后库存记录会丢失吗？
A: 不会。系统会智能同步库存记录，只对变化的部分进行新增或删除操作。

### Q: 如何批量调整多个商品的库存？
A: 使用 `POST /inventory/batch` 接口进行批量创建，或多次调用调整接口。

### Q: 库存调整失败如何回滚？
A: 系统使用数据库事务，调整失败会自动回滚，无需手动处理。

### Q: 如何查看库存变动历史？
A: 所有库存变动都记录在 `inventory_transactions` 表中，可通过相关接口查询。
