# 测试登录
Write-Host "🚀 开始测试登录..." -ForegroundColor Green

$loginBody = @{
    userCode = "husky"
    password = "541888"
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
Write-Host "登录响应: $($loginResponse | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow

$token = $loginResponse.data.accessToken
Write-Host "✅ 登录成功，token: $($token.Substring(0, [Math]::Min(50, $token.Length)))..." -ForegroundColor Green

# 测试创建公司
Write-Host ""
Write-Host "📝 开始测试创建公司..." -ForegroundColor Green

$timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
$companyCode = "COMP$timestamp"

$companyBody = @{
    code = $companyCode
    name = "测试公司$timestamp"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

Write-Host "请求体: $companyBody" -ForegroundColor Cyan
Write-Host "请求头: $($headers | ConvertTo-Json)" -ForegroundColor Cyan

try {
    $createResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies" -Method POST -Body $companyBody -Headers $headers
    Write-Host "Create company response: $($createResponse | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow
    Write-Host "Create company success!" -ForegroundColor Green
} catch {
    Write-Host "Create company failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "Error details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}
