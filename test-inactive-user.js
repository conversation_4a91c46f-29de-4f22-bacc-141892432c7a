const https = require('http');

// 测试登录接口
function testLogin() {
  const data = JSON.stringify({
    userCode: 'husky',
    password: '541888',
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
    },
  };

  const req = https.request(options, (res) => {
    console.log(`登录接口状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('登录响应:', JSON.stringify(response, null, 2));

        if (response.data && response.data.accessToken) {
          console.log('✅ 登录成功！');
          testCreateInactiveUser(response.data.accessToken);
        } else {
          console.log('❌ 登录失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 创建未激活用户
function testCreateInactiveUser(token) {
  const data = JSON.stringify({
    code: 'inactive002',
    nickname: '未激活用户',
    password: '123456',
    isActive: false,
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/users',
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Content-Length': data.length,
    },
  };

  const req = https.request(options, (res) => {
    console.log(`\n创建未激活用户状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('创建用户响应:', JSON.stringify(response, null, 2));

        if (response.code === 200) {
          console.log('✅ 未激活用户创建成功！');
          testUserListWithoutInactive(token);
        } else {
          console.log('❌ 用户创建失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 测试不包含未激活用户的列表
function testUserListWithoutInactive(token) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/users',
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };

  const req = https.request(options, (res) => {
    console.log(`\n用户列表（不包含未激活）状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('用户列表响应:', JSON.stringify(response, null, 2));

        if (response.code === 200) {
          console.log(`✅ 获取用户列表成功！共 ${response.data.total} 个用户`);
          testUserListWithInactive(token);
        } else {
          console.log('❌ 用户列表获取失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

// 测试包含未激活用户的列表
function testUserListWithInactive(token) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/users?includeInactive=true',
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };

  const req = https.request(options, (res) => {
    console.log(`\n用户列表（包含未激活）状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('用户列表响应:', JSON.stringify(response, null, 2));

        if (response.code === 200) {
          console.log(
            `✅ 获取用户列表成功！共 ${response.data.total} 个用户（包含未激活）`,
          );
        } else {
          console.log('❌ 用户列表获取失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

console.log('🚀 开始测试未激活用户功能...');
testLogin();
