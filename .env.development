# ========================================
# 开发环境配置文件
# ========================================
# 此文件用于本地开发环境
# 复制此文件为 .env 用于本地开发

# 环境配置
NODE_ENV=development
PORT=8080

# 数据库配置 (本地开发)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=54188
DB_DATABASE=manager

# JWT配置 (开发环境可以使用简单密钥)
JWT_SECRET=dev_jwt_secret_key_for_development_only
JWT_EXPIRATION=7d

# Redis配置 (本地开发)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 腾讯云对象存储配置
TENCENT_COS_SECRET_ID=AKIDnRuAC6qPmw6C8ta9XiEYeaL3YptjLlyx
TENCENT_COS_SECRET_KEY=oOkwvhGbidOxLYYM0UmqgIgq37yeAb0M
TENCENT_COS_REGION=ap-guangzhou
TENCENT_COS_BUCKET=vasa-1302219791
TENCENT_COS_BASE_URL=https://vasa-1302219791.cos.ap-guangzhou.myqcloud.com

# 短信验证码配置
SMS_CODE_EXPIRES_IN=300
SMS_CODE_LENGTH=6
ADMIN_PHONE_NUMBER=16604583277

# 开发环境特殊配置
# 开发模式下可以启用更详细的日志
LOG_LEVEL=debug
# 开发模式下可以启用数据库同步
DB_SYNCHRONIZE=true
# 开发模式下启用数据库日志
DB_LOGGING=true
