# 数据库迁移部署指南

## 🚀 自动化部署流程

### 部署脚本功能

我们的部署脚本现在包含完整的数据库迁移功能：

1. **自动备份数据库** - 在迁移前创建备份
2. **执行数据库迁移** - 自动运行所有待执行的迁移
3. **验证迁移结果** - 确保迁移成功执行
4. **回滚支持** - 提供迁移回滚功能

### 部署命令

#### 完整部署（包括数据库创建）
```bash
./deploy-production.sh full
```

#### 更新部署（仅更新代码和迁移）
```bash
./deploy-production.sh update
```

#### 快速部署到服务器
```bash
./quick-deploy.sh
```

## 📋 数据库迁移管理

### 生成新的迁移文件

当您添加新的实体或修改现有实体时：

```bash
# 生成迁移文件
npm run migration:generate -- src/migrations/YourMigrationName

# 示例：为新的表生成迁移
npm run migration:generate -- src/migrations/CreateNewTable
```

### 手动执行迁移

```bash
# 查看迁移状态
npm run migration:show

# 执行迁移
npm run migration:run

# 验证迁移结果
npm run migration:verify
```

### 迁移回滚

```bash
# 回滚最近的迁移
npm run migration:rollback --rollback

# 从备份恢复数据库
npm run migration:rollback --backup

# 查看回滚选项
npm run migration:rollback --help
```

## 🔧 部署流程详解

### 1. 部署前准备
- ✅ 代码提交到版本控制
- ✅ 本地测试通过
- ✅ 迁移文件已生成（如有新表结构）

### 2. 自动化部署流程
```
1. 检查系统依赖
2. 安装项目依赖
3. 构建生产版本
4. 备份当前数据库
5. 执行数据库迁移
6. 验证迁移结果
7. 停止旧服务
8. 启动新服务
9. 验证服务状态
```

### 3. 部署后验证
- ✅ 服务状态正常
- ✅ API 接口响应
- ✅ 数据库连接正常
- ✅ 新表结构存在

## 📁 文件结构

```
├── deploy-production.sh           # 主部署脚本
├── quick-deploy.sh               # 快速部署脚本
├── scripts/
│   ├── verify-migrations.sh      # 迁移验证脚本
│   └── rollback-migration.sh     # 迁移回滚脚本
├── src/
│   ├── migrations/               # 迁移文件目录
│   │   ├── 1749365279193-CreateMemosTable.ts
│   │   └── ...
│   └── config/
│       └── data-source.ts        # 数据源配置
└── backups/                      # 数据库备份目录
    └── manager_backup_*.sql
```

## 🛠️ 常用命令

### 服务管理
```bash
pm2 status                        # 查看服务状态
pm2 logs web-manager-backend      # 查看日志
pm2 restart web-manager-backend   # 重启服务
pm2 stop web-manager-backend      # 停止服务
```

### 数据库管理
```bash
npm run migration:show            # 查看迁移状态
npm run migration:verify          # 验证迁移结果
npm run migration:rollback        # 迁移回滚工具
```

### 数据库连接
```bash
psql -h localhost -U postgres -d manager  # 连接数据库
```

## ⚠️ 注意事项

### 生产环境配置
- `DB_SYNCHRONIZE=false` - 关闭自动同步
- `migrationsRun=true` - 启动时自动运行迁移
- 备份会自动保留最近5个文件

### 安全建议
1. **备份优先** - 每次迁移前都会自动备份
2. **验证机制** - 迁移后自动验证表结构
3. **回滚准备** - 提供快速回滚方案
4. **权限控制** - 确保数据库用户有足够权限

### 故障排除
1. **迁移失败** - 检查数据库连接和权限
2. **表不存在** - 运行 `npm run migration:verify` 检查
3. **服务启动失败** - 查看 PM2 日志
4. **API 无响应** - 检查端口和防火墙设置

## 🎯 最佳实践

### 开发流程
1. 本地开发时使用 `synchronize: true`
2. 生产部署前生成迁移文件
3. 测试环境验证迁移脚本
4. 生产环境使用迁移部署

### 迁移文件管理
1. 迁移文件纳入版本控制
2. 使用描述性的迁移名称
3. 避免直接修改已部署的迁移文件
4. 复杂变更分步骤进行

### 部署策略
1. 使用蓝绿部署减少停机时间
2. 监控部署过程和服务状态
3. 准备快速回滚方案
4. 定期清理旧备份文件

---

## 📞 支持

如果在部署过程中遇到问题：

1. 查看部署日志
2. 运行验证脚本
3. 检查数据库状态
4. 必要时使用回滚功能
