# 测试公司接口返回用户姓名
Write-Host "Testing company API with user names..." -ForegroundColor Green

# 1. 登录
$loginBody = @{
    userCode = "husky"
    password = "541888"
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
$token = $loginResponse.data.accessToken
Write-Host "Login successful" -ForegroundColor Green

# 2. 创建公司（指定管理员为husky）
$timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
$random = Get-Random -Minimum 1000 -Maximum 9999
$companyCode = "COMP$timestamp$random"

$companyBody = @{
    code = $companyCode
    name = "Test Company $timestamp"
    managerCode = "husky"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

$createResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies" -Method POST -Body $companyBody -Headers $headers
Write-Host "Company created successfully: $companyCode" -ForegroundColor Green

# 3. 获取公司详情（检查是否包含managerName）
$detailResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies/$companyCode" -Method GET -Headers @{"Authorization" = "Bearer $token"}
Write-Host "Company detail response:" -ForegroundColor Yellow
Write-Host ($detailResponse | ConvertTo-Json -Depth 10) -ForegroundColor Cyan

# 4. 获取公司列表（检查是否包含managerName）
$listResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies?page=1&pageSize=5" -Method GET -Headers @{"Authorization" = "Bearer $token"}
Write-Host "Company list response:" -ForegroundColor Yellow
Write-Host ($listResponse | ConvertTo-Json -Depth 10) -ForegroundColor Cyan

Write-Host "Test completed!" -ForegroundColor Magenta
