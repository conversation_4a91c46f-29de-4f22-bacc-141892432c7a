const https = require('http');

// 测试登录接口
function testLogin() {
  const data = JSON.stringify({
    userCode: 'husky',
    password: '541888',
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
    },
  };

  const req = https.request(options, (res) => {
    console.log(`登录接口状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('登录响应:', JSON.stringify(response, null, 2));

        if (response.data && response.data.accessToken) {
          console.log('✅ 登录成功！');
          testUserList(response.data.accessToken);
        } else {
          console.log('❌ 登录失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 测试用户列表接口
function testUserList(token) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/users',
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };

  const req = https.request(options, (res) => {
    console.log(`\n用户列表接口状态码: ${res.statusCode}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('用户列表响应:', JSON.stringify(response, null, 2));

        if (response.code === 200) {
          console.log('✅ 用户列表获取成功！');
        } else {
          console.log('❌ 用户列表获取失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

console.log('🚀 开始测试API...');
testLogin();
