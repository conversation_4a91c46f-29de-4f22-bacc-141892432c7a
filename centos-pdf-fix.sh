#!/bin/bash

# CentOS/TencentOS专用PDF修复脚本

set -e

echo "=========================================="
echo "开始CentOS PDF修复..."
echo "=========================================="

# 1. 停止服务
echo "1. 停止当前服务..."
pm2 stop backend || echo "服务未运行"

# 2. 清理旧的Puppeteer安装
echo "2. 清理旧的Puppeteer安装..."
rm -rf node_modules/puppeteer
rm -rf ~/.cache/puppeteer

# 3. 安装系统依赖 (CentOS/TencentOS版本)
echo "3. 安装系统依赖..."
yum install -y \
    alsa-lib \
    atk \
    cups-libs \
    gtk3 \
    libXcomposite \
    libXcursor \
    libXdamage \
    libXext \
    libXi \
    libXrandr \
    libXScrnSaver \
    libXtst \
    pango \
    xorg-x11-fonts-100dpi \
    xorg-x11-fonts-75dpi \
    xorg-x11-fonts-cyrillic \
    xorg-x11-fonts-misc \
    xorg-x11-fonts-Type1 \
    xorg-x11-utils \
    nss \
    nspr \
    libdrm \
    libxkbcommon \
    wget \
    which

# 4. 安装Chrome
echo "4. 安装Chrome..."
if ! command -v google-chrome &> /dev/null; then
    echo "下载Chrome..."
    cd /tmp
    wget -q --no-check-certificate https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
    echo "安装Chrome..."
    yum localinstall -y google-chrome-stable_current_x86_64.rpm
    rm -f google-chrome-stable_current_x86_64.rpm
    cd -
else
    echo "Chrome已安装: $(google-chrome --version)"
fi

# 5. 重新安装Puppeteer
echo "5. 重新安装Puppeteer..."
npm install puppeteer --save

# 6. 测试Chrome
echo "6. 测试Chrome..."
if google-chrome --version; then
    echo "✅ Chrome安装成功"
else
    echo "❌ Chrome安装失败"
    exit 1
fi

# 7. 测试Puppeteer基本功能
echo "7. 测试Puppeteer..."
node << 'EOF'
const puppeteer = require('puppeteer');

(async () => {
  let browser;
  try {
    console.log('启动浏览器...');
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu'
      ]
    });
    
    console.log('创建页面...');
    const page = await browser.newPage();
    
    console.log('设置内容...');
    await page.setContent('<html><body><h1>测试</h1></body></html>');
    
    console.log('生成PDF...');
    const pdf = await page.pdf({ format: 'A4' });
    
    console.log('✅ Puppeteer测试成功，PDF大小:', pdf.length, 'bytes');
    
  } catch (error) {
    console.error('❌ Puppeteer测试失败:', error.message);
    process.exit(1);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
})();
EOF

# 8. 重新构建项目
echo "8. 重新构建项目..."
npm run build

# 9. 启动服务
echo "9. 启动服务..."
pm2 start backend

# 10. 等待服务启动
echo "10. 等待服务启动..."
sleep 15

# 11. 检查服务状态
echo "11. 检查服务状态..."
pm2 status

# 12. 测试API
echo "12. 测试API..."
if curl -s http://localhost:8080/api/commission-contracts > /dev/null; then
    echo "✅ API服务正常"
else
    echo "❌ API服务异常，检查日志:"
    pm2 logs backend --lines 10
fi

echo ""
echo "=========================================="
echo "PDF修复完成！"
echo "=========================================="
echo ""
echo "现在可以测试PDF生成功能了。"
echo "如果还有问题，请运行: pm2 logs backend"
