// 测试供应商Excel导入导出功能
// 使用内置的 fetch (Node.js 18+)
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://127.0.0.1:8080';

async function testSuppliersExcel() {
  console.log('🚀 开始测试供应商Excel导入导出功能...');

  try {
    // 1. 登录获取token
    console.log('\n📝 正在登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userCode: 'husky',
        password: '541888',
      }),
    });

    const loginData = await loginResponse.json();
    if (loginData.code !== 200) {
      throw new Error(`登录失败: ${loginData.message}`);
    }

    const token = loginData.data.accessToken;
    console.log('✅ 登录成功');

    const headers = {
      'Authorization': `Bearer ${token}`,
    };

    // 2. 下载导入模板
    console.log('\n📝 下载导入模板...');
    const templateResponse = await fetch(`${BASE_URL}/suppliers/import/template`, {
      method: 'GET',
      headers,
    });

    if (!templateResponse.ok) {
      throw new Error(`下载模板失败: ${templateResponse.status}`);
    }

    const templateBuffer = await templateResponse.arrayBuffer();
    const templatePath = path.join(__dirname, 'supplier_import_template.xlsx');
    fs.writeFileSync(templatePath, Buffer.from(templateBuffer));
    console.log(`✅ 导入模板下载成功: ${templatePath}`);

    // 3. 创建一些测试供应商数据
    console.log('\n📝 创建测试供应商数据...');
    const testSuppliers = [
      {
        code: `SUP${Date.now()}1`,
        name: '广州服装供应商',
        address: '广州市白云区服装批发市场A区101号',
        contactName: '张三',
        contactPhone: '13800138000',
      },
      {
        code: `SUP${Date.now()}2`,
        name: '深圳电子供应商',
        address: '深圳市南山区科技园',
        contactName: '李四',
        contactPhone: '13900139000',
      },
    ];

    for (const supplier of testSuppliers) {
      const createResponse = await fetch(`${BASE_URL}/suppliers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(supplier),
      });

      const createData = await createResponse.json();
      if (createData.code !== 200) {
        console.log(`⚠️ 创建供应商 ${supplier.code} 失败: ${createData.message}`);
      } else {
        console.log(`✅ 创建供应商 ${supplier.code} 成功`);
      }
    }

    // 4. 导出供应商数据为Excel
    console.log('\n📝 导出供应商数据为Excel...');
    const exportResponse = await fetch(`${BASE_URL}/suppliers/export/excel`, {
      method: 'GET',
      headers,
    });

    if (!exportResponse.ok) {
      throw new Error(`导出Excel失败: ${exportResponse.status}`);
    }

    const exportBuffer = await exportResponse.arrayBuffer();
    const exportPath = path.join(__dirname, 'suppliers_export.xlsx');
    fs.writeFileSync(exportPath, Buffer.from(exportBuffer));
    console.log(`✅ 供应商数据导出成功: ${exportPath}`);

    // 5. 测试Excel导入功能（使用模板文件）
    console.log('\n📝 测试Excel导入功能...');
    
    // 检查模板文件是否存在
    if (!fs.existsSync(templatePath)) {
      throw new Error('模板文件不存在');
    }

    // 创建FormData
    const formData = new FormData();
    const fileBuffer = fs.readFileSync(templatePath);
    const blob = new Blob([fileBuffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    formData.append('file', blob, 'supplier_import_template.xlsx');

    const importResponse = await fetch(`${BASE_URL}/suppliers/import/excel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    const importData = await importResponse.json();
    console.log('导入结果:', JSON.stringify(importData, null, 2));

    if (importData.code === 200) {
      console.log('✅ Excel导入测试成功');
      console.log(`📊 导入统计: 成功 ${importData.data.successCount} 条，失败 ${importData.data.failureCount} 条`);
      
      if (importData.data.errors.length > 0) {
        console.log('⚠️ 导入错误详情:');
        importData.data.errors.forEach(error => console.log(`   - ${error}`));
      }
    } else {
      console.log('⚠️ Excel导入测试失败:', importData.message);
    }

    // 6. 清理测试文件
    console.log('\n📝 清理测试文件...');
    try {
      if (fs.existsSync(templatePath)) {
        fs.unlinkSync(templatePath);
        console.log('✅ 模板文件已删除');
      }
      if (fs.existsSync(exportPath)) {
        fs.unlinkSync(exportPath);
        console.log('✅ 导出文件已删除');
      }
    } catch (error) {
      console.log('⚠️ 清理文件时出错:', error.message);
    }

    // 7. 清理测试数据
    console.log('\n📝 清理测试数据...');
    for (const supplier of testSuppliers) {
      try {
        const deleteResponse = await fetch(`${BASE_URL}/suppliers/${supplier.code}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        const deleteData = await deleteResponse.json();
        if (deleteData.code === 200) {
          console.log(`✅ 删除供应商 ${supplier.code} 成功`);
        }
      } catch (error) {
        console.log(`⚠️ 删除供应商 ${supplier.code} 失败:`, error.message);
      }
    }

    console.log('\n🎉 Excel导入导出功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

testSuppliersExcel();
