# 完整测试公司接口
Write-Host "🚀 开始完整测试公司接口..." -ForegroundColor Green

# 1. 登录
$loginBody = @{
    userCode = "husky"
    password = "541888"
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
$token = $loginResponse.data.accessToken
Write-Host "✅ 登录成功" -ForegroundColor Green

# 2. 创建公司
$timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
$companyCode = "COMP$timestamp"

$companyBody = @{
    code = $companyCode
    name = "测试公司$timestamp"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

$createResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies" -Method POST -Body $companyBody -Headers $headers
Write-Host "✅ 创建公司成功: $companyCode" -ForegroundColor Green

# 3. 获取公司详情
$detailResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies/$companyCode" -Method GET -Headers @{"Authorization" = "Bearer $token"}
Write-Host "✅ 获取公司详情成功" -ForegroundColor Green
Write-Host "公司详情: $($detailResponse | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow

# 4. 更新公司
$updateBody = @{
    name = "更新后的公司名称$timestamp"
    address = "北京市朝阳区测试地址"
    socialCreditCode = "91110000123456789X"
    businessLicenseUrl = "https://example.com/license.jpg"
} | ConvertTo-Json

$updateResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies/$companyCode" -Method PATCH -Body $updateBody -Headers $headers
Write-Host "✅ 更新公司成功" -ForegroundColor Green

# 5. 再次获取公司详情验证更新
$updatedDetailResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies/$companyCode" -Method GET -Headers @{"Authorization" = "Bearer $token"}
Write-Host "✅ 验证更新成功" -ForegroundColor Green
Write-Host "更新后的公司详情: $($updatedDetailResponse | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow

# 6. 获取公司列表
$listResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies?page=1&pageSize=10" -Method GET -Headers @{"Authorization" = "Bearer $token"}
Write-Host "✅ 获取公司列表成功" -ForegroundColor Green
Write-Host "公司列表: $($listResponse | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow

# 7. 软删除公司
$deleteResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies/$companyCode" -Method DELETE -Headers @{"Authorization" = "Bearer $token"}
Write-Host "✅ 软删除公司成功" -ForegroundColor Green

Write-Host "All tests completed!" -ForegroundColor Magenta
