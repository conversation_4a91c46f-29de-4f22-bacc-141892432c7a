#!/bin/bash

# ========================================
# 紧急修复脚本 - 资产模块500错误
# ========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "🚨 开始紧急修复资产模块500错误..."

# 1. 修复数据库字段名
log_info "修复数据库字段名..."
PGPASSWORD=54188 psql -h localhost -U postgres -d manager -f emergency-fix-assets.sql

if [ $? -eq 0 ]; then
    log_success "数据库字段名修复完成"
else
    log_error "数据库字段名修复失败"
    exit 1
fi

# 2. 重新构建应用
log_info "重新构建应用..."
npm run build

if [ $? -eq 0 ]; then
    log_success "应用构建完成"
else
    log_error "应用构建失败"
    exit 1
fi

# 3. 重启服务
log_info "重启服务..."
pm2 restart web-manager-backend

if [ $? -eq 0 ]; then
    log_success "服务重启完成"
else
    log_error "服务重启失败，尝试停止并重新启动..."
    pm2 stop web-manager-backend
    pm2 start ecosystem.config.js --env production
fi

# 4. 等待服务启动
log_info "等待服务启动..."
sleep 10

# 5. 检查服务状态
log_info "检查服务状态..."
if pm2 status | grep -q "online"; then
    log_success "✅ 服务修复完成！服务正在正常运行"
    
    # 测试API
    log_info "测试API..."
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/operating-assets/details?page=1&pageSize=10)
    if [ "$response" = "200" ]; then
        log_success "✅ API测试通过！"
    else
        log_error "❌ API测试失败，返回状态码: $response"
    fi
else
    log_error "❌ 服务启动失败"
    log_info "查看错误日志:"
    pm2 logs web-manager-backend --lines 20
    exit 1
fi

log_success "🎉 紧急修复完成！"
