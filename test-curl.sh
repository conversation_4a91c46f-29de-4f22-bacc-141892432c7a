#!/bin/bash

# 测试登录
echo "🚀 开始测试登录..."
LOGIN_RESPONSE=$(curl -s -X POST http://127.0.0.1:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"userCode":"husky","password":"541888"}')

echo "登录响应: $LOGIN_RESPONSE"

# 提取token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ 登录失败，无法获取token"
  exit 1
fi

echo "✅ 登录成功，token: ${TOKEN:0:50}..."

# 测试创建公司
echo ""
echo "📝 开始测试创建公司..."
TIMESTAMP=$(date +%s)
COMPANY_CODE="COMP$TIMESTAMP"

CREATE_RESPONSE=$(curl -s -X POST http://127.0.0.1:8080/companies \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"code\":\"$COMPANY_CODE\",\"name\":\"测试公司$TIMESTAMP\"}")

echo "创建公司响应: $CREATE_RESPONSE"

# 检查响应
if echo "$CREATE_RESPONSE" | grep -q '"code":200'; then
  echo "✅ 创建公司成功！"
  
  # 测试获取公司详情
  echo ""
  echo "🔍 开始测试获取公司详情..."
  DETAIL_RESPONSE=$(curl -s -X GET "http://127.0.0.1:8080/companies/$COMPANY_CODE" \
    -H "Authorization: Bearer $TOKEN")
  
  echo "公司详情响应: $DETAIL_RESPONSE"
  
  if echo "$DETAIL_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 获取公司详情成功！"
    echo "🎉 所有测试完成！"
  else
    echo "❌ 获取公司详情失败"
  fi
else
  echo "❌ 创建公司失败"
fi
