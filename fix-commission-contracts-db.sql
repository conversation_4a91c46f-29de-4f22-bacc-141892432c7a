-- 修复commission_contracts表结构的SQL脚本
-- 此脚本将安全地添加缺失的字段

-- 开始事务
BEGIN;

-- 1. 清理可能存在的问题字段和类型
DO $$ 
BEGIN
    -- 删除可能存在的问题字段
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='paymentStatus') THEN
        ALTER TABLE commission_contracts DROP COLUMN "paymentStatus";
    END IF;
    
    -- 删除可能存在的枚举类型
    DROP TYPE IF EXISTS commission_contracts_paymentstatus_enum;
    
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error during cleanup: %', SQLERRM;
END $$;

-- 2. 创建枚举类型
CREATE TYPE commission_contracts_paymentstatus_enum AS ENUM('unpaid', 'partial_paid', 'fully_paid', 'overdue');

-- 3. 添加 paymentStatus 字段
ALTER TABLE commission_contracts 
ADD COLUMN "paymentStatus" commission_contracts_paymentstatus_enum NOT NULL DEFAULT 'unpaid';

-- 4. 添加 paidAmount 字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='paidAmount') THEN
        ALTER TABLE commission_contracts 
        ADD COLUMN "paidAmount" numeric(15,2) NOT NULL DEFAULT 0;
    END IF;
END $$;

-- 5. 添加 remainingDebtAmount 字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='remainingDebtAmount') THEN
        ALTER TABLE commission_contracts 
        ADD COLUMN "remainingDebtAmount" numeric(15,2) NOT NULL DEFAULT 0;
    END IF;
END $$;

-- 6. 添加 lastPaymentDate 字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='commission_contracts' AND column_name='lastPaymentDate') THEN
        ALTER TABLE commission_contracts 
        ADD COLUMN "lastPaymentDate" date;
    END IF;
END $$;

-- 7. 添加字段注释
COMMENT ON COLUMN commission_contracts."paymentStatus" IS '还款状态';
COMMENT ON COLUMN commission_contracts."paidAmount" IS '已还款金额';
COMMENT ON COLUMN commission_contracts."remainingDebtAmount" IS '剩余欠款金额';
COMMENT ON COLUMN commission_contracts."lastPaymentDate" IS '最后还款日期';

-- 8. 初始化 remainingDebtAmount 字段为 requestedDebtAmount 的值
UPDATE commission_contracts 
SET "remainingDebtAmount" = "requestedDebtAmount" 
WHERE "remainingDebtAmount" = 0 AND "requestedDebtAmount" > 0;

-- 提交事务
COMMIT;

-- 验证表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'commission_contracts' 
AND column_name IN ('paymentStatus', 'paidAmount', 'remainingDebtAmount', 'lastPaymentDate')
ORDER BY column_name;
