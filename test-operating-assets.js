// 测试运营资产API功能
const fs = require('fs');
const BASE_URL = 'http://127.0.0.1:8080';

// 运营资产类型
const OPERATING_ASSET_TYPES = {
  HUMAN_RESOURCES: 'human_resources',
  WAREHOUSE_LOGISTICS: 'warehouse_logistics', 
  ADMINISTRATIVE_CONSUMPTION: 'administrative_consumption',
  OTHER: 'other'
};

// 运营资产类型中文名称
const TYPE_LABELS = {
  [OPERATING_ASSET_TYPES.HUMAN_RESOURCES]: '人力资产',
  [OPERATING_ASSET_TYPES.WAREHOUSE_LOGISTICS]: '仓储物流',
  [OPERATING_ASSET_TYPES.ADMINISTRATIVE_CONSUMPTION]: '行政消耗',
  [OPERATING_ASSET_TYPES.OTHER]: '其他'
};

async function testOperatingAssets() {
  console.log('🚀 测试运营资产API功能...');

  try {
    // 1. 登录获取token
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userCode: 'husky',
        password: '541888',
      }),
    });

    const loginData = await loginResponse.json();
    const token = loginData.data.accessToken;

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };

    console.log('✅ 登录成功');

    // 2. 创建不同类型的运营资产明细
    console.log('\n📝 测试创建运营资产明细...');
    
    const testDetails = [
      {
        type: OPERATING_ASSET_TYPES.HUMAN_RESOURCES,
        amount: 15000.50,
        screenshot: 'https://example.com/hr-screenshot.jpg',
        remark: '员工工资支出'
      },
      {
        type: OPERATING_ASSET_TYPES.WAREHOUSE_LOGISTICS,
        amount: 8500.00,
        screenshot: 'https://example.com/warehouse-screenshot.jpg',
        remark: '仓库租金'
      },
      {
        type: OPERATING_ASSET_TYPES.ADMINISTRATIVE_CONSUMPTION,
        amount: 3200.75,
        screenshot: 'https://example.com/admin-screenshot.jpg',
        remark: '办公用品采购'
      },
      {
        type: OPERATING_ASSET_TYPES.OTHER,
        amount: 1200.00,
        screenshot: 'https://example.com/other-screenshot.jpg',
        remark: '其他费用'
      }
    ];

    const createdDetailIds = [];

    for (const detail of testDetails) {
      const createResponse = await fetch(`${BASE_URL}/operating-assets/details`, {
        method: 'POST',
        headers,
        body: JSON.stringify(detail),
      });

      const createData = await createResponse.json();
      console.log(`✅ 创建${TYPE_LABELS[detail.type]}明细成功: ${detail.amount}元`);
    }

    // 3. 测试分页查询（不带筛选）
    console.log('\n📋 测试分页查询运营资产明细...');
    const listResponse = await fetch(`${BASE_URL}/operating-assets/details?page=1&pageSize=10`, {
      method: 'GET',
      headers,
    });

    const listData = await listResponse.json();
    console.log(`✅ 查询成功，共${listData.data.total}条记录`);
    console.log(`💰 系统总金额: ¥${listData.data.systemTotalAmount}`);
    console.log(`💰 查询结果总金额: ¥${listData.data.totalAmount}`);
    
    if (listData.data.details.length > 0) {
      console.log('📄 明细列表:');
      listData.data.details.forEach((detail, index) => {
        console.log(`  ${index + 1}. [${detail.typeLabel}] ¥${detail.amount} - ${detail.remark || '无备注'}`);
      });
      
      // 保存第一个明细ID用于后续测试
      const firstDetailId = listData.data.details[0].id;
      createdDetailIds.push(firstDetailId);
    }

    // 4. 测试类型筛选查询
    console.log('\n🔍 测试类型筛选查询（人力资产）...');
    const typeFilterResponse = await fetch(
      `${BASE_URL}/operating-assets/details?page=1&pageSize=10&type=${OPERATING_ASSET_TYPES.HUMAN_RESOURCES}`,
      {
        method: 'GET',
        headers,
      }
    );

    const typeFilterData = await typeFilterResponse.json();
    console.log(`✅ 人力资产筛选查询成功，共${typeFilterData.data.total}条记录`);
    typeFilterData.data.details.forEach((detail, index) => {
      console.log(`  ${index + 1}. [${detail.typeLabel}] ¥${detail.amount} - ${detail.remark || '无备注'}`);
    });

    // 5. 测试搜索功能
    console.log('\n🔍 测试搜索功能（搜索"工资"）...');
    const searchResponse = await fetch(
      `${BASE_URL}/operating-assets/details?page=1&pageSize=10&search=工资`,
      {
        method: 'GET',
        headers,
      }
    );

    const searchData = await searchResponse.json();
    console.log(`✅ 搜索成功，共${searchData.data.total}条记录`);

    // 6. 测试获取明细详情
    if (createdDetailIds.length > 0) {
      console.log('\n📄 测试获取明细详情...');
      const detailResponse = await fetch(`${BASE_URL}/operating-assets/details/${createdDetailIds[0]}`, {
        method: 'GET',
        headers,
      });

      const detailData = await detailResponse.json();
      console.log(`✅ 获取明细详情成功:`);
      console.log(`  ID: ${detailData.data.id}`);
      console.log(`  类型: ${detailData.data.typeLabel} (${detailData.data.type})`);
      console.log(`  金额: ¥${detailData.data.amount}`);
      console.log(`  备注: ${detailData.data.remark || '无备注'}`);
      console.log(`  创建时间: ${new Date(detailData.data.createdAt).toLocaleString('zh-CN')}`);
    }

    // 7. 测试Excel导出功能
    console.log('\n📊 测试Excel导出功能...');
    
    // 导出所有数据
    const exportAllResponse = await fetch(`${BASE_URL}/operating-assets/export/excel`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (exportAllResponse.ok) {
      const excelBuffer = await exportAllResponse.arrayBuffer();
      const fileName = `operating-assets-all-${Date.now()}.xlsx`;
      fs.writeFileSync(fileName, Buffer.from(excelBuffer));
      console.log(`✅ 导出所有数据成功: ${fileName} (${excelBuffer.byteLength} 字节)`);
    }

    // 导出特定类型数据
    const exportTypeResponse = await fetch(
      `${BASE_URL}/operating-assets/export/excel?type=${OPERATING_ASSET_TYPES.HUMAN_RESOURCES}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      }
    );

    if (exportTypeResponse.ok) {
      const excelBuffer = await exportTypeResponse.arrayBuffer();
      const fileName = `operating-assets-hr-${Date.now()}.xlsx`;
      fs.writeFileSync(fileName, Buffer.from(excelBuffer));
      console.log(`✅ 导出人力资产数据成功: ${fileName} (${excelBuffer.byteLength} 字节)`);
    }

    console.log('\n🎉 运营资产API测试完成！');
    console.log('\n📋 功能特点总结:');
    console.log('✅ 支持4种运营资产类型: 人力资产、仓储物流、行政消耗、其他');
    console.log('✅ 返回列表时包含类型中文名称 (typeLabel)');
    console.log('✅ 支持按类型筛选查询');
    console.log('✅ 支持备注模糊搜索');
    console.log('✅ 支持时间范围筛选');
    console.log('✅ 支持Excel导出（包含图片嵌入）');
    console.log('✅ 完全模仿固定资产接口设计');
    console.log('✅ 支持分页查询和全量查询');
    console.log('✅ 自动计算总金额和查询结果金额');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testOperatingAssets();
