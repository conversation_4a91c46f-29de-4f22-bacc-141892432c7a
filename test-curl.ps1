# 测试登录
Write-Host "🚀 开始测试登录..." -ForegroundColor Green

$loginBody = @{
    userCode = "husky"
    password = "541888"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "登录响应: $($loginResponse | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow
    
    if ($loginResponse.data.accessToken) {
        $token = $loginResponse.data.accessToken
        Write-Host "✅ 登录成功，token: $($token.Substring(0, [Math]::Min(50, $token.Length)))..." -ForegroundColor Green
        
        # 测试创建公司
        Write-Host ""
        Write-Host "📝 开始测试创建公司..." -ForegroundColor Green
        
        $timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
        $companyCode = "COMP$timestamp"
        
        $companyBody = @{
            code = $companyCode
            name = "测试公司$timestamp"
        } | ConvertTo-Json
        
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        try {
            $createResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies" -Method POST -Body $companyBody -Headers $headers
            Write-Host "创建公司响应: $($createResponse | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow
            
            if ($createResponse.code -eq 200) {
                Write-Host "✅ 创建公司成功！" -ForegroundColor Green
                
                # 测试获取公司详情
                Write-Host ""
                Write-Host "🔍 开始测试获取公司详情..." -ForegroundColor Green
                
                try {
                    $detailResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies/$companyCode" -Method GET -Headers @{"Authorization" = "Bearer $token"}
                    Write-Host "公司详情响应: $($detailResponse | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow
                    
                    if ($detailResponse.code -eq 200) {
                        Write-Host "✅ 获取公司详情成功！" -ForegroundColor Green
                        Write-Host "🎉 所有测试完成！" -ForegroundColor Magenta
                    } else {
                        Write-Host "❌ 获取公司详情失败" -ForegroundColor Red
                    }
                } catch {
                    Write-Host "❌ 获取公司详情请求失败: $($_.Exception.Message)" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ 创建公司失败" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ 创建公司请求失败: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 登录失败，无法获取token" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
