#!/bin/bash

# ========================================
# 紧急服务器修复脚本
# ========================================
# 修复生产环境数据库字段名问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 紧急修复数据库
emergency_fix_database() {
    log_info "🚨 开始紧急修复数据库..."
    
    # 直接执行SQL修复
    PGPASSWORD=54188 psql -h localhost -U postgres -d manager << 'EOF'
-- 紧急修复字段名
\echo '🚨 开始紧急修复数据库字段名问题...'

-- 1. 修复 expenses 表
\echo '修复 expenses 表字段名...'
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expenses' AND column_name = 'create_date') THEN
        ALTER TABLE expenses RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'expenses.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'expenses.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 2. 修复 expense_details 表
\echo '修复 expense_details 表字段名...'
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expense_details' AND column_name = 'create_date') THEN
        ALTER TABLE expense_details RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'expense_details.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'expense_details.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 3. 修复 incomes 表
\echo '修复 incomes 表字段名...'
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'incomes' AND column_name = 'create_date') THEN
        ALTER TABLE incomes RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'incomes.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'incomes.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 4. 修复 income_details 表
\echo '修复 income_details 表字段名...'
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'income_details' AND column_name = 'create_date') THEN
        ALTER TABLE income_details RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'income_details.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'income_details.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 5. 验证修复结果
\echo '验证修复结果...'
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public'
    AND table_name IN ('expenses', 'expense_details', 'incomes', 'income_details')
    AND column_name IN ('createDate', 'create_date')
ORDER BY table_name, column_name;

\echo '✅ 紧急修复完成！'
EOF

    if [ $? -eq 0 ]; then
        log_success "数据库修复完成"
    else
        log_error "数据库修复失败"
        exit 1
    fi
}

# 重启服务
restart_service() {
    log_info "重启应用服务..."
    
    # 重启PM2服务
    pm2 restart web-manager-backend || {
        log_warning "PM2重启失败，尝试停止并启动"
        pm2 stop web-manager-backend
        pm2 start ecosystem.config.js --env production
    }
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    if pm2 status | grep -q "online"; then
        log_success "服务重启成功"
    else
        log_error "服务重启失败"
        pm2 logs web-manager-backend --lines 20
        exit 1
    fi
}

# 测试API
test_api() {
    log_info "测试API接口..."
    
    # 测试基础API
    if curl -f http://localhost:3000/api >/dev/null 2>&1; then
        log_success "基础API测试通过"
    else
        log_warning "基础API测试失败"
    fi
    
    # 测试支出接口
    if curl -f "http://localhost:3000/expenses/details?page=1&pageSize=10" >/dev/null 2>&1; then
        log_success "支出接口测试通过"
    else
        log_warning "支出接口仍有问题"
    fi
    
    # 测试收入接口
    if curl -f "http://localhost:3000/incomes/details?page=1&pageSize=10" >/dev/null 2>&1; then
        log_success "收入接口测试通过"
    else
        log_warning "收入接口仍有问题"
    fi
}

# 主函数
main() {
    log_info "🚨 开始紧急修复服务器问题..."
    echo
    
    emergency_fix_database
    echo
    
    restart_service
    echo
    
    test_api
    echo
    
    log_success "🎉 紧急修复完成！"
    echo
    echo "=========================================="
    echo "📋 修复总结"
    echo "=========================================="
    echo "✅ 数据库字段名已修复"
    echo "✅ 应用服务已重启"
    echo "✅ API接口已测试"
    echo "=========================================="
    echo
    echo "📝 后续建议："
    echo "1. 监控应用日志: pm2 logs web-manager-backend"
    echo "2. 检查API状态: curl http://localhost:3000/api"
    echo "3. 测试前端功能确保正常"
    echo "=========================================="
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
