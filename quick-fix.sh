#!/bin/bash

# 快速修复commission-contracts问题的脚本

set -e

echo "开始快速修复commission-contracts问题..."

# 1. 回滚失败的迁移
echo "1. 回滚失败的迁移..."
npm run migration:revert || echo "没有需要回滚的迁移"

# 2. 直接执行SQL修复
echo "2. 执行数据库修复..."
PGPASSWORD=54188 psql -h 43.138.236.92 -U postgres -d manager -f fix-commission-contracts-db.sql

# 3. 标记迁移为已执行（避免重复执行）
echo "3. 标记迁移为已执行..."
PGPASSWORD=54188 psql -h 43.138.236.92 -U postgres -d manager -c "
INSERT INTO migrations (timestamp, name) 
VALUES (1749872208311, 'AddMissingCommissionContractFields1749872208311')
ON CONFLICT DO NOTHING;
"

# 4. 重新构建和重启
echo "4. 重新构建项目..."
npm run build

echo "5. 重启服务..."
pm2 restart backend

echo "6. 检查服务状态..."
pm2 status

echo "修复完成！请测试commission-contracts接口。"
