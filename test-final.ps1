# Test company API with user names
Write-Host "Testing company API..." -ForegroundColor Green

# 1. Login
$loginBody = @{
    userCode = "husky"
    password = "541888"
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
$token = $loginResponse.data.accessToken
Write-Host "Login successful" -ForegroundColor Green

# 2. Get company list
$headers = @{
    Authorization = "Bearer $token"
}

$listResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies?page=1&pageSize=5" -Method GET -Headers $headers
Write-Host "Company list response:" -ForegroundColor Yellow
Write-Host ($listResponse | ConvertTo-Json -Depth 10) -ForegroundColor Cyan

# 3. Get company detail if exists
if ($listResponse.data.companies.Count -gt 0) {
    $firstCompanyCode = $listResponse.data.companies[0].code
    Write-Host "Getting details for company: $firstCompanyCode" -ForegroundColor Yellow
    
    $detailResponse = Invoke-RestMethod -Uri "http://127.0.0.1:8080/companies/$firstCompanyCode" -Method GET -Headers $headers
    Write-Host "Company detail response:" -ForegroundColor Yellow
    Write-Host ($detailResponse | ConvertTo-Json -Depth 10) -ForegroundColor Cyan
}

Write-Host "Test completed!" -ForegroundColor Magenta
