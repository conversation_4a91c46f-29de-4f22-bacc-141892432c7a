// 测试供应商空手机号更新功能
// 使用内置的 fetch (Node.js 18+)

const BASE_URL = 'http://127.0.0.1:8080';

async function testSuppliersEmptyPhone() {
  console.log('🚀 开始测试供应商空手机号更新功能...');

  try {
    // 1. 登录获取token
    console.log('\n📝 正在登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userCode: 'husky',
        password: '541888',
      }),
    });

    const loginData = await loginResponse.json();
    if (loginData.code !== 200) {
      throw new Error(`登录失败: ${loginData.message}`);
    }

    const token = loginData.data.accessToken;
    console.log('✅ 登录成功');

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };

    // 2. 创建供应商（带手机号）
    console.log('\n📝 创建供应商（带手机号）...');
    const supplierCode = `SUP${Date.now()}`;
    const createResponse = await fetch(`${BASE_URL}/suppliers`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        code: supplierCode,
        name: '测试供应商',
        address: '测试地址',
        contactName: '张三',
        contactPhone: '13800138000',
      }),
    });

    const createData = await createResponse.json();
    console.log('创建供应商响应:', JSON.stringify(createData, null, 2));

    if (createData.code !== 200) {
      throw new Error(`创建供应商失败: ${createData.message}`);
    }
    console.log('✅ 供应商创建成功');

    // 3. 更新供应商（清空手机号）
    console.log('\n📝 更新供应商（清空手机号）...');
    const updateResponse = await fetch(`${BASE_URL}/suppliers/${supplierCode}`, {
      method: 'PATCH',
      headers,
      body: JSON.stringify({
        name: '广州服装供应商',
        address: '广州市白云区服装批发市场A区101号',
        contactName: '张三',
        contactPhone: '',
      }),
    });

    const updateData = await updateResponse.json();
    console.log('更新供应商响应:', JSON.stringify(updateData, null, 2));

    if (updateData.code !== 200) {
      throw new Error(`更新供应商失败: ${updateData.message}`);
    }
    console.log('✅ 供应商更新成功（手机号已清空）');

    // 4. 获取供应商详情验证
    console.log('\n📝 获取供应商详情验证...');
    const detailResponse = await fetch(`${BASE_URL}/suppliers/${supplierCode}`, {
      method: 'GET',
      headers,
    });

    const detailData = await detailResponse.json();
    console.log('供应商详情:', JSON.stringify(detailData, null, 2));

    if (detailData.code !== 200) {
      throw new Error(`获取供应商详情失败: ${detailData.message}`);
    }

    // 验证手机号是否为空
    if (detailData.data.contactPhone === '') {
      console.log('✅ 手机号已成功清空');
    } else {
      console.log('⚠️ 手机号未清空，当前值:', detailData.data.contactPhone);
    }

    // 5. 测试不传分页参数
    console.log('\n📝 测试不传分页参数...');
    const noPageResponse = await fetch(`${BASE_URL}/suppliers`, {
      method: 'GET',
      headers,
    });

    const noPageData = await noPageResponse.json();
    console.log('不传分页参数响应:', JSON.stringify(noPageData, null, 2));

    if (noPageData.code !== 200) {
      throw new Error(`测试不传分页参数失败: ${noPageData.message}`);
    }

    // 验证是否返回空数据
    if (noPageData.data.suppliers.length === 0 && noPageData.data.total === 0) {
      console.log('✅ 不传分页参数正确返回空数据');
    } else {
      console.log('⚠️ 不传分页参数仍然返回了数据，需要修复');
    }

    // 6. 清理测试数据
    console.log('\n📝 清理测试数据...');
    const deleteResponse = await fetch(`${BASE_URL}/suppliers/${supplierCode}`, {
      method: 'DELETE',
      headers,
    });

    const deleteData = await deleteResponse.json();
    if (deleteData.code === 200) {
      console.log('✅ 测试数据清理成功');
    }

    console.log('\n🎉 空手机号更新测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

testSuppliersEmptyPhone();
