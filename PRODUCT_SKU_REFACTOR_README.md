# 产品和库存管理系统重构文档

## 概述

本次重构实现了完整的SPU/SKU分离设计，支持基于颜色选择的自动SKU生成，并建立了完善的库存管理体系，为后续的采购入库和销售开单功能奠定了基础。

## 主要改进

### 1. 产品档案优化
- ✅ 添加了`categoryCode`字段关联商品分类
- ✅ 移除了库存相关字段，专注于SPU信息管理
- ✅ 支持基于颜色选择的产品创建流程

### 2. 库存管理重构
- ✅ 添加了`skuCode`字段（格式：SPU编码-颜色编码-尺寸编码）
- ✅ 实现了完整的库存数量管理字段
- ✅ 支持库存预警和成本核算
- ✅ 添加了业务状态和统计字段

### 3. SKU自动生成
- ✅ 根据选择的颜色和商品分类的尺寸自动生成SKU
- ✅ 每个SKU自动创建对应的库存记录
- ✅ 支持颜色级别的价格调整和辅料配置

## 数据库结构变更

### 产品表 (products)
```sql
-- 新增字段
ALTER TABLE products ADD COLUMN categoryCode varchar(50);
ALTER TABLE products ADD CONSTRAINT FK_products_categoryCode 
  FOREIGN KEY (categoryCode) REFERENCES product_categories(code);
```

### 库存详情表 (inventory_details)
```sql
-- 新增SKU编码
ALTER TABLE inventory_details ADD COLUMN skuCode varchar(150) UNIQUE;
ALTER TABLE inventory_details RENAME COLUMN size TO sizeCode;

-- 库存数量字段
ALTER TABLE inventory_details ADD COLUMN totalStock integer DEFAULT 0;
ALTER TABLE inventory_details ADD COLUMN actualStock integer DEFAULT 0;
ALTER TABLE inventory_details ADD COLUMN reservedStock integer DEFAULT 0;
ALTER TABLE inventory_details ADD COLUMN damagedStock integer DEFAULT 0;
ALTER TABLE inventory_details ADD COLUMN purchasingStock integer DEFAULT 0;
ALTER TABLE inventory_details ADD COLUMN needPurchaseStock integer DEFAULT 0;

-- 库存预警字段
ALTER TABLE inventory_details ADD COLUMN safetyStock integer DEFAULT 10;
ALTER TABLE inventory_details ADD COLUMN minStock integer DEFAULT 5;
ALTER TABLE inventory_details ADD COLUMN maxStock integer DEFAULT 1000;

-- 成本字段
ALTER TABLE inventory_details ADD COLUMN avgCost decimal(10,2) DEFAULT 0;
ALTER TABLE inventory_details ADD COLUMN latestCost decimal(10,2) DEFAULT 0;

-- 业务状态字段
ALTER TABLE inventory_details ADD COLUMN isActive boolean DEFAULT true;
ALTER TABLE inventory_details ADD COLUMN warehouseLocation varchar(100) DEFAULT 'A区';

-- 统计字段
ALTER TABLE inventory_details ADD COLUMN totalInbound integer DEFAULT 0;
ALTER TABLE inventory_details ADD COLUMN totalOutbound integer DEFAULT 0;
ALTER TABLE inventory_details ADD COLUMN lastInboundDate timestamp;
ALTER TABLE inventory_details ADD COLUMN lastOutboundDate timestamp;
```

## API接口变更

### 1. 创建商品接口
**路径**: `POST /products`

**请求结构**:
```typescript
{
  code: "PROD001",
  name: "时尚T恤",
  categoryCode: "CAT001", // 新增：商品分类编码
  brandCode: "BRAND001",
  supplierCode: "SUP001",
  // 基础价格...
  selectedColors: [ // 新增：选择的颜色列表
    {
      colorCode: "COLOR001",
      images: ["red-front.jpg"],
      priceAdjustments: {
        retailPriceAdjustment: 20.0
      },
      accessories: [],
      remark: "红色款式"
    }
  ]
}
```

**响应结构**:
```typescript
{
  code: 200,
  data: {
    spu: { /* SPU信息 */ },
    skus: [ /* 生成的SKU列表 */ ],
    message: "成功创建商品档案，生成6个SKU"
  },
  message: "商品档案创建成功"
}
```

### 2. 获取SKU列表接口
**路径**: `GET /products/:id/skus`

**响应**: 返回指定商品的所有SKU库存信息

## 业务流程

### 1. 产品创建流程
```
1. 前端选择商品分类 → 获取该分类的可用尺寸
2. 前端选择颜色 → 配置颜色图片、价格调整等
3. 提交到后端
4. 后端验证数据（品牌、供应商、分类、颜色）
5. 创建SPU记录
6. 根据 颜色 × 尺寸 自动生成SKU
7. 为每个SKU创建库存记录（初始库存为0）
8. 返回SPU和SKU列表
```

### 2. 库存字段关系
```
totalStock = actualStock + reservedStock + damagedStock
actualStock: 可销售库存
reservedStock: 已下单未发货的预留库存
damagedStock: 损坏不可销售库存
purchasingStock: 采购在途库存
needPurchaseStock: 建议采购数量（系统计算）
```

## 数据迁移

### 执行迁移
```bash
# 1. 执行数据库结构迁移
npm run migration:run

# 2. 执行数据迁移（为现有产品生成SKU）
# 迁移脚本会自动处理现有数据
```

### 迁移内容
- 为现有产品添加默认分类
- 根据现有颜色尺寸组合生成SKU记录
- 迁移现有库存数据到新字段

## 测试

### 运行测试脚本
```bash
# 测试产品创建功能
npx ts-node src/scripts/test-product-creation.ts
```

### 测试前准备
1. 确保数据库迁移已执行
2. 创建测试数据：
   - 品牌（激活状态）
   - 供应商
   - 商品分类（配置尺寸）
   - 颜色

## 后续扩展

这个设计为以下功能提供了基础：

### 1. 采购入库服务
```typescript
// 采购入库时更新库存
async purchaseInbound(skuCode: string, quantity: number, cost: number) {
  // 更新 actualStock, totalStock, latestCost, avgCost
  // 减少 purchasingStock
  // 更新 totalInbound, lastInboundDate
}
```

### 2. 销售开单服务
```typescript
// 销售时预留库存
async reserveStock(skuCode: string, quantity: number) {
  // actualStock -> reservedStock
}

// 确认发货时扣减库存
async confirmShipment(skuCode: string, quantity: number) {
  // reservedStock -> 实际出库
  // 更新 totalOutbound, lastOutboundDate
  // 检查是否需要补货
}
```

### 3. 库存预警
- 基于 `safetyStock` 的库存预警
- 自动计算 `needPurchaseStock`
- 库存报表和分析

## 注意事项

1. **数据一致性**: 所有库存操作都应该在事务中进行
2. **并发控制**: 库存扣减需要考虑并发安全
3. **性能优化**: 大量SKU的查询需要合适的索引
4. **业务规则**: 不同业务场景的库存计算逻辑可能不同

## 总结

本次重构实现了：
- ✅ 清晰的SPU/SKU分离
- ✅ 完整的库存管理体系
- ✅ 自动化的SKU生成
- ✅ 为后续业务功能奠定基础
- ✅ 完善的数据迁移方案

这个设计能够很好地支持服装行业的复杂业务需求，为后续的采购、销售、库存管理等功能提供了坚实的基础。
