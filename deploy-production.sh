#!/bin/bash

# ========================================
# 生产环境部署脚本
# ========================================
# 用法: ./deploy-production.sh [full|update]
# full: 完整部署（包括数据库创建）
# update: 更新部署（仅更新代码）

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        log_warning "检测到root用户，建议使用普通用户部署"
    fi
}

# 检查必要的工具
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm 未安装，尝试安装..."
        npm install -g pnpm
    fi
    
    # 检查PostgreSQL
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL 未安装，请先安装 PostgreSQL"
        exit 1
    fi
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 未安装，尝试安装..."
        npm install -g pm2
    fi
    
    log_success "依赖检查完成"
}

# 创建数据库
create_database() {
    log_info "创建数据库 'manager'..."
    
    # 检查数据库是否已存在
    DB_EXISTS=$(sudo -u postgres psql -tAc "SELECT 1 FROM pg_database WHERE datname='manager'")
    
    if [ "$DB_EXISTS" = "1" ]; then
        log_warning "数据库 'manager' 已存在"
        read -p "是否要删除现有数据库并重新创建？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "删除现有数据库..."
            sudo -u postgres psql -c "DROP DATABASE IF EXISTS manager;"
            sudo -u postgres psql -c "CREATE DATABASE manager;"
            log_success "数据库重新创建完成"
        else
            log_info "保留现有数据库"
        fi
    else
        sudo -u postgres psql -c "CREATE DATABASE manager;"
        log_success "数据库 'manager' 创建完成"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    pnpm install --frozen-lockfile
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建生产版本..."

    # 复制生产环境配置
    cp .env.production .env
    log_info "已复制生产环境配置"

    # 构建项目
    pnpm run build
    log_success "项目构建完成"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."

    local backup_dir="./backups"
    local backup_file="$backup_dir/manager_backup_$(date +%Y%m%d_%H%M%S).sql"

    # 创建备份目录
    mkdir -p "$backup_dir"

    # 执行备份
    if pg_dump -h localhost -U postgres -d manager > "$backup_file" 2>/dev/null; then
        log_success "数据库备份完成: $backup_file"

        # 保留最近5个备份文件
        ls -t "$backup_dir"/manager_backup_*.sql 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true
    else
        log_warning "数据库备份失败，但继续执行迁移"
    fi
}

# 执行数据库迁移
run_migrations() {
    log_info "执行数据库迁移..."

    # 确保数据库连接正常
    log_info "测试数据库连接..."
    if ! psql -h localhost -U postgres -d manager -c "SELECT 1;" >/dev/null 2>&1; then
        log_error "数据库连接失败，请检查数据库服务状态"
        exit 1
    fi
    log_success "数据库连接正常"

    # 确保 uuid-ossp 扩展已启用
    log_info "确保 uuid-ossp 扩展已启用..."
    psql -h localhost -U postgres -d manager -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";" >/dev/null 2>&1 || true

    # 检查迁移状态
    log_info "检查待执行的迁移..."
    if pnpm run migration:show 2>/dev/null; then
        log_info "迁移状态检查完成"
    else
        log_warning "无法检查迁移状态，但继续执行迁移"
    fi

    # 执行迁移
    log_info "执行数据库迁移..."
    if pnpm run migration:run; then
        log_success "数据库迁移完成"

        # 显示迁移后的状态
        log_info "显示迁移后的状态..."
        pnpm run migration:show 2>/dev/null || true

        # 验证迁移结果
        log_info "验证迁移结果..."
        if [ -f "scripts/verify-migrations.sh" ]; then
            chmod +x scripts/verify-migrations.sh
            if ./scripts/verify-migrations.sh; then
                log_success "迁移验证通过"
            else
                log_warning "迁移验证失败，但继续部署"
            fi
        else
            log_warning "迁移验证脚本不存在，跳过验证"
        fi
    else
        log_error "数据库迁移失败"
        log_info "尝试查看迁移状态..."
        pnpm run migration:show 2>/dev/null || true
        log_info "尝试查看数据库日志..."
        tail -n 20 /var/log/postgresql/postgresql-*.log 2>/dev/null || log_warning "无法查看数据库日志"
        exit 1
    fi
}

# 停止现有服务
stop_service() {
    log_info "停止现有服务..."
    
    # 停止PM2进程
    pm2 stop web-manager-backend 2>/dev/null || log_warning "PM2进程未运行"
    pm2 delete web-manager-backend 2>/dev/null || log_warning "PM2进程不存在"
    
    # 停止可能运行的Node.js进程
    pkill -f "node.*main.js" 2>/dev/null || log_warning "没有找到运行中的Node.js进程"
    
    log_success "服务停止完成"
}

# 启动服务
start_service() {
    log_info "启动生产服务..."
    
    # 使用PM2启动服务
    pm2 start ecosystem.config.js --env production
    
    # 保存PM2配置
    pm2 save
    
    # 设置PM2开机自启
    pm2 startup
    
    log_success "服务启动完成"
}

# 创建PM2配置文件
create_pm2_config() {
    log_info "创建PM2配置文件..."
    
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'web-manager-backend',
    script: 'start-production.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF
    
    # 创建日志目录
    mkdir -p logs
    
    log_success "PM2配置文件创建完成"
}

# 检查服务状态
check_service() {
    log_info "检查服务状态..."
    
    sleep 3
    
    # 检查PM2状态
    pm2 status
    
    # 检查端口是否监听
    if netstat -tuln | grep -q ":3000 "; then
        log_success "服务正在端口3000上运行"
    else
        log_error "服务未在端口3000上运行"
        pm2 logs web-manager-backend --lines 20
        exit 1
    fi
    
    # 测试API
    log_info "测试API连接..."
    if curl -f http://localhost:3000/api >/dev/null 2>&1; then
        log_success "API测试成功"

        # 测试数据库相关的API
        log_info "测试数据库相关API..."

        # 测试用户登录接口（不需要认证）
        if curl -f http://localhost:3000/api/auth/login -X POST \
           -H "Content-Type: application/json" \
           -d '{"username":"test","password":"test"}' >/dev/null 2>&1; then
            log_info "认证接口响应正常"
        else
            log_info "认证接口测试完成（预期的认证失败）"
        fi

        log_success "API功能测试完成"
    else
        log_warning "API测试失败，请检查服务状态"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=========================================="
    echo "🎉 部署信息"
    echo "=========================================="
    echo "应用名称: web-manager-backend"
    echo "运行环境: production"
    echo "监听端口: 3000"
    echo "数据库: manager"
    echo "API文档: http://*************:3000/api"
    echo "=========================================="
    echo
    echo "常用命令:"
    echo "查看日志: pm2 logs web-manager-backend"
    echo "重启服务: pm2 restart web-manager-backend"
    echo "停止服务: pm2 stop web-manager-backend"
    echo "查看状态: pm2 status"
    echo ""
    echo "数据库迁移命令:"
    echo "查看迁移状态: npm run migration:show"
    echo "验证迁移结果: npm run migration:verify"
    echo "回滚迁移: npm run migration:rollback"
    echo "生成新迁移: npm run migration:generate -- src/migrations/YourMigrationName"
    echo "=========================================="
}

# 主函数
main() {
    local deploy_type=${1:-full}
    
    log_info "开始部署 web-manager-backend (模式: $deploy_type)"
    
    check_root
    check_dependencies
    
    if [ "$deploy_type" = "full" ]; then
        create_database
    fi
    
    install_dependencies
    build_project
    backup_database
    run_migrations
    stop_service
    create_pm2_config
    start_service
    check_service
    show_deployment_info
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
