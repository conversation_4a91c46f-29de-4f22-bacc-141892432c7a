#!/bin/bash

# 服务器PDF生成问题诊断和修复脚本

set -e

echo "开始诊断和修复服务器PDF生成问题..."

# 1. 检查当前系统信息
echo "1. 检查系统信息..."
echo "操作系统: $(cat /etc/os-release | grep PRETTY_NAME)"
echo "内核版本: $(uname -r)"
echo "内存使用: $(free -h)"

# 2. 检查Node.js和npm版本
echo "2. 检查Node.js环境..."
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 3. 检查Puppeteer安装
echo "3. 检查Puppeteer安装..."
if npm list puppeteer 2>/dev/null; then
    echo "Puppeteer已安装"
else
    echo "Puppeteer未安装，正在安装..."
    npm install puppeteer --save
fi

# 4. 检查Chrome/Chromium
echo "4. 检查Chrome/Chromium..."
if command -v google-chrome &> /dev/null; then
    echo "Google Chrome已安装: $(google-chrome --version)"
elif command -v chromium &> /dev/null; then
    echo "Chromium已安装: $(chromium --version)"
else
    echo "Chrome/Chromium未安装，正在安装..."
    
    # 安装必要的系统依赖
    yum update -y
    yum install -y \
        alsa-lib.x86_64 \
        atk.x86_64 \
        cups-libs.x86_64 \
        gtk3.x86_64 \
        ipa-gothic-fonts \
        libXcomposite.x86_64 \
        libXcursor.x86_64 \
        libXdamage.x86_64 \
        libXext.x86_64 \
        libXi.x86_64 \
        libXrandr.x86_64 \
        libXScrnSaver.x86_64 \
        libXtst.x86_64 \
        pango.x86_64 \
        xorg-x11-fonts-100dpi \
        xorg-x11-fonts-75dpi \
        xorg-x11-fonts-cyrillic \
        xorg-x11-fonts-misc \
        xorg-x11-fonts-Type1 \
        xorg-x11-utils \
        wget
    
    # 下载并安装Chrome
    cd /tmp
    wget https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
    yum localinstall -y google-chrome-stable_current_x86_64.rpm
    rm -f google-chrome-stable_current_x86_64.rpm
    cd -
fi

# 5. 测试Puppeteer基本功能
echo "5. 测试Puppeteer基本功能..."
node << 'EOF'
const puppeteer = require('puppeteer');

(async () => {
  try {
    console.log('启动浏览器...');
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ],
    });
    
    console.log('创建页面...');
    const page = await browser.newPage();
    
    console.log('设置内容...');
    await page.setContent(`
      <html>
        <head>
          <meta charset="UTF-8">
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            h1 { color: #333; }
          </style>
        </head>
        <body>
          <h1>PDF生成测试</h1>
          <p>这是一个测试PDF文档。</p>
          <p>当前时间: ${new Date().toLocaleString()}</p>
        </body>
      </html>
    `, { waitUntil: 'networkidle0', timeout: 10000 });
    
    console.log('生成PDF...');
    const pdf = await page.pdf({
      format: 'A4',
      margin: { top: '20mm', right: '20mm', bottom: '20mm', left: '20mm' },
      printBackground: true,
    });
    
    console.log('PDF生成成功，大小:', pdf.length, 'bytes');
    
    await browser.close();
    console.log('测试完成 - Puppeteer工作正常');
  } catch (error) {
    console.error('Puppeteer测试失败:', error.message);
    console.error('错误详情:', error.stack);
    process.exit(1);
  }
})();
EOF

# 6. 检查应用日志
echo "6. 检查应用日志..."
echo "最近的应用日志:"
pm2 logs backend --lines 20 --nostream || echo "无法获取PM2日志"

# 7. 测试commission-contracts API
echo "7. 测试commission-contracts API..."
echo "检查服务是否运行..."
if curl -s http://localhost:8080/api/commission-contracts > /dev/null; then
    echo "API服务正常运行"
else
    echo "API服务可能有问题"
fi

# 8. 重新构建和重启
echo "8. 重新构建和重启服务..."
npm run build
pm2 restart backend

# 9. 等待服务启动
echo "9. 等待服务启动..."
sleep 10

# 10. 最终测试
echo "10. 进行最终测试..."
node << 'EOF'
const http = require('http');

// 测试API是否可访问
const options = {
  hostname: 'localhost',
  port: 8080,
  path: '/api/commission-contracts',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
};

const req = http.request(options, (res) => {
  console.log(`API状态码: ${res.statusCode}`);
  if (res.statusCode === 200) {
    console.log('API服务正常');
  } else {
    console.log('API服务可能有问题');
  }
});

req.on('error', (e) => {
  console.error(`API请求失败: ${e.message}`);
});

req.end();
EOF

echo ""
echo "诊断和修复完成！"
echo ""
echo "如果PDF生成仍然失败，请检查以下几点："
echo "1. 查看PM2日志: pm2 logs backend"
echo "2. 检查系统资源: free -h && df -h"
echo "3. 确认防火墙设置"
echo "4. 检查SELinux状态: getenforce"
echo ""
echo "常见解决方案："
echo "- 如果内存不足，增加swap空间"
echo "- 如果是权限问题，检查文件权限"
echo "- 如果是字体问题，安装中文字体包"
