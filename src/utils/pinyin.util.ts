import pinyin from 'pinyin';

/**
 * 拼音码生成工具
 */
export class PinyinUtil {
  /**
   * 将中文名称转换为拼音码
   * @param name 中文名称
   * @returns 拼音码（大写字母）
   */
  static generatePinyinCode(name: string): string {
    if (!name || name.trim() === '') {
      return '';
    }

    try {
      // 使用pinyin库将中文转换为拼音
      const pinyinArray = pinyin(name.trim(), {
        style: pinyin.STYLE_FIRST_LETTER, // 只取首字母
        heteronym: false, // 不启用多音字模式
      });

      // 提取首字母并转换为大写
      const code = pinyinArray
        .map((item: string[]) => item[0])
        .join('')
        .toUpperCase()
        .replace(/[^A-Z]/g, ''); // 只保留字母

      return code;
    } catch (error) {
      console.error('生成拼音码失败:', error);
      return '';
    }
  }

  /**
   * 生成带数字后缀的唯一拼音码
   * @param name 中文名称
   * @param existingCodes 已存在的拼音码列表
   * @returns 唯一的拼音码
   */
  static generateUniquePinyinCode(
    name: string,
    existingCodes: string[] = [],
  ): string {
    const baseCode = this.generatePinyinCode(name);

    if (!baseCode) {
      return '';
    }

    // 如果基础拼音码不存在，直接返回
    if (!existingCodes.includes(baseCode)) {
      return baseCode;
    }

    // 如果存在重复，添加数字后缀
    let counter = 1;
    let uniqueCode = `${baseCode}${counter}`;

    while (existingCodes.includes(uniqueCode)) {
      counter++;
      uniqueCode = `${baseCode}${counter}`;
    }

    return uniqueCode;
  }

  /**
   * 验证拼音码格式
   * @param code 拼音码
   * @returns 是否有效
   */
  static isValidPinyinCode(code: string): boolean {
    if (!code || code.trim() === '') {
      return false;
    }

    // 拼音码应该只包含大写字母和数字，长度在1-10之间
    const regex = /^[A-Z][A-Z0-9]{0,9}$/;
    return regex.test(code.trim());
  }
}
