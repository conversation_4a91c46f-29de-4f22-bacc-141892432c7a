import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { RentalAssetDetail } from './rental-asset-detail.entity';

@Entity('rental_assets')
export class RentalAsset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '租赁资产总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalAmount: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '创建日期（字符串格式，必填）',
    name: 'createdAt',
  })
  createDate: string;

  // 关联租赁资产明细
  @OneToMany(() => RentalAssetDetail, (detail) => detail.rentalAsset)
  details: RentalAssetDetail[];
}
