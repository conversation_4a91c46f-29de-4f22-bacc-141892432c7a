import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RentalAssetsService } from './rental-assets.service';
import { RentalAssetsController } from './rental-assets.controller';
import { RentalAsset } from './entities/rental-asset.entity';
import { RentalAssetDetail } from './entities/rental-asset-detail.entity';

@Module({
  imports: [TypeOrmModule.forFeature([RentalAsset, RentalAssetDetail])],
  controllers: [RentalAssetsController],
  providers: [RentalAssetsService],
  exports: [RentalAssetsService],
})
export class RentalAssetsModule {}
