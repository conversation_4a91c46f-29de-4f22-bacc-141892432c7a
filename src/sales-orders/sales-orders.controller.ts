import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { SalesOrdersService } from './sales-orders.service';
import { CreateSalesOrderDto } from './dto/create-sales-order.dto';
import {
  UpdateSalesOrderDto,
  UpdateSalesOrderStatusDto,
} from './dto/update-sales-order.dto';
import { QuerySalesOrdersDto } from './dto/query-sales-orders.dto';
import {
  SalesOrderResponseDto,
  SalesOrderListResponseDto,
} from './dto/sales-order-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('销售订单管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('sales-orders')
export class SalesOrdersController {
  private readonly logger = new Logger(SalesOrdersController.name);

  constructor(private readonly salesOrdersService: SalesOrdersService) {}

  @Post()
  @ApiOperation({ summary: '创建销售订单' })
  @ApiResponse({
    status: 200,
    description: '销售订单创建成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '销售订单创建成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 404,
    description: '相关资源不存在',
  })
  async create(@Body() createDto: CreateSalesOrderDto) {
    this.logger.log(
      `创建销售订单: 销售人员=${createDto.salesPersonCode}, 客户=${createDto.customerCode}, 明细数量=${createDto.details.length}`,
    );

    await this.salesOrdersService.create(createDto);

    return {
      code: 200,
      data: null,
      message: '销售订单创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '分页查询销售订单列表' })
  @ApiResponse({
    status: 200,
    description: '获取销售订单列表成功',
    type: SalesOrderListResponseDto,
  })
  async findAll(@Query() queryDto: QuerySalesOrdersDto) {
    this.logger.log(
      `查询销售订单列表: page=${queryDto.page}, pageSize=${queryDto.pageSize}`,
    );

    const result = await this.salesOrdersService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取销售订单列表成功',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询销售订单详情' })
  @ApiParam({
    name: 'id',
    description: '销售订单ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '获取销售订单详情成功',
    type: SalesOrderResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '销售订单不存在',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    this.logger.log(`查询销售订单详情: ${id}`);

    const result = await this.salesOrdersService.findOne(id);

    return {
      code: 200,
      data: result,
      message: '获取销售订单详情成功',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新销售订单基本信息' })
  @ApiParam({
    name: 'id',
    description: '销售订单ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '销售订单更新成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '销售订单更新成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误或订单状态不允许编辑',
  })
  @ApiResponse({
    status: 404,
    description: '销售订单不存在',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateSalesOrderDto,
  ) {
    this.logger.log(`更新销售订单: ${id}`);

    await this.salesOrdersService.update(id, updateDto);

    return {
      code: 200,
      data: null,
      message: '销售订单更新成功',
    };
  }

  @Patch(':id/status')
  @ApiOperation({ summary: '更新销售订单状态' })
  @ApiParam({
    name: 'id',
    description: '销售订单ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '销售订单状态更新成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '销售订单状态更新成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '状态转换不合法',
  })
  @ApiResponse({
    status: 404,
    description: '销售订单不存在',
  })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateStatusDto: UpdateSalesOrderStatusDto,
  ) {
    this.logger.log(
      `更新销售订单状态: ${id}, 新状态: ${updateStatusDto.status}`,
    );

    await this.salesOrdersService.updateStatus(id, updateStatusDto);

    return {
      code: 200,
      data: null,
      message: '销售订单状态更新成功',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除销售订单' })
  @ApiParam({
    name: 'id',
    description: '销售订单ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '销售订单删除成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '销售订单删除成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '订单状态不允许删除',
  })
  @ApiResponse({
    status: 404,
    description: '销售订单不存在',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    this.logger.log(`删除销售订单: ${id}`);

    await this.salesOrdersService.remove(id);

    return {
      code: 200,
      data: null,
      message: '销售订单删除成功',
    };
  }
}
