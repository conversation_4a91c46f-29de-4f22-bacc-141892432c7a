# 销售订单管理系统

## 系统概述

销售订单管理系统实现了完整的销售开单流程，支持库存验证、状态管理、物流配置等功能。

## 核心功能

### 1. 销售订单管理

- ✅ 创建销售订单（支持多商品明细）
- ✅ 订单状态管理（草稿 → 待发货 → 完成 → 已取消）
- ✅ 库存验证（库存为0不允许开单）
- ✅ 自动订单编号生成
- ✅ 总金额自动计算

### 2. 业务特性

- ✅ 单个客户限制（每个订单只能指定一个主客户）
- ✅ 代发模式（支持指定代发客户）
- ✅ 物流方式（到付/寄付）
- ✅ 运费计算（寄付模式需要运费金额）
- ✅ 放单标记
- ✅ 发货人指定（库房发货人）

### 3. 状态流转规则

- `草稿` → `待发货` → `完成`
- `草稿` → `已取消`
- `待发货` → `已取消`
- `完成` 状态不能转换
- `已取消` 状态不能转换

## 数据库设计

### 核心表结构

#### 1. 销售订单主表 (sales_orders)

```sql
- id: UUID主键
- orderNumber: 订单编号（SO + 年月日 + 4位序号）
- salesPersonCode: 销售人员编码（必填）
- orderDate: 订单日期
- expectedDeliveryDate: 期望交货日期
- priority: 订单优先级（urgent/high/normal/low）
- status: 订单状态（draft/pending_shipment/completed/cancelled）
- isDropShipping: 是否代发
- isReleased: 是否放单
- shippingMethod: 物流方式（collect到付/prepaid寄付）
- shippingFee: 运费金额
- shippingCompany: 物流公司
- trackingNumber: 物流单号
- shipperUserCode: 发货人编码
- customerCode: 主客户编码
- dropShipCustomerCode: 代发客户编码
- totalQuantity: 总商品数量（自动计算）
- totalAmount: 商品总金额（自动计算）
- grandTotal: 订单总金额（商品总金额 + 运费）
```

#### 2. 销售订单明细表 (sales_order_details)

```sql
- id: UUID主键
- salesOrderId: 销售订单ID
- inventoryId: 库存记录ID（关联skus_inventory表的id字段，精确到SKU+尺码）
- productCode: 商品编码（冗余存储）
- colorCode: 颜色编码
- sizeCode: 尺寸编码
- skuCode: SKU编码
- quantity: 销售数量
- priceType: 价格类型（retail/pre_order/restock/spot）
- unitPrice: 单价
- totalAmount: 小计金额
- discountAmount: 折扣金额
- actualAmount: 实际金额
- costPrice: 成本价
- totalCost: 成本总额
- grossProfit: 毛利润
```

#### 3. 销售订单客户关联表 (sales_order_customers)

```sql
- id: UUID主键
- salesOrderId: 销售订单ID
- customerCode: 客户编码
- allocatedQuantity: 分配总数量
- allocatedAmount: 分配总金额
- allocatedShippingFee: 分配运费
- totalPayable: 客户应付总额
- status: 客户订单状态
- receiverName: 收货人姓名
- receiverPhone: 收货人电话
- shippingAddress: 收货地址
```

## API接口

### 销售订单接口

#### 创建销售订单

```http
POST /sales-orders
Content-Type: application/json

{
  "salesPersonCode": "user001",
  "orderDate": "2025-01-19",
  "expectedDeliveryDate": "2025-01-25",
  "priority": "normal",
  "isDropShipping": false,
  "isReleased": false,
  "shippingMethod": "collect",
  "shippingFee": 15.00,
  "shippingCompany": "顺丰快递",
  "shipperUserCode": "user003",
  "customerCode": "CUS001",
  "dropShipCustomerCode": null,
  "remark": "客户要求加急处理",
  "details": [
    {
      "inventoryId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "quantity": 10,
      "priceType": "retail",
      "unitPrice": 50.00,
      "discountAmount": 0.00,
      "expectedShipDate": "2025-01-20",
      "remark": "客户指定颜色"
    }
  ]
}
```

#### 分页查询销售订单

```http
GET /sales-orders?page=1&pageSize=10&status=draft&includeDetails=true
```

#### 查询订单详情

```http
GET /sales-orders/{id}
```

#### 更新订单状态

```http
PATCH /sales-orders/{id}/status
Content-Type: application/json

{
  "status": "pending_shipment",
  "confirmedByUserCode": "user002",
  "remark": "订单已确认，准备发货"
}
```

#### 更新订单信息

```http
PATCH /sales-orders/{id}
Content-Type: application/json

{
  "expectedDeliveryDate": "2025-01-26",
  "shippingFee": 20.00,
  "shipperUserCode": "user004",
  "remark": "更新运费和发货人"
}
```

#### 删除订单

```http
DELETE /sales-orders/{id}
```

## 业务规则

### 1. 库存验证

- 创建订单时必须验证库存是否足够
- 库存为0的商品不允许开销售订单
- 下单成功后自动扣减库存

### 2. 订单编号生成

- 格式：SO + 年月日 + 4位序号
- 示例：SO202501190001
- 按日期自动递增序号

### 3. 金额计算

- 明细金额 = 单价 × 数量 - 折扣金额
- 商品总金额 = 所有明细实际金额之和
- 订单总金额 = 商品总金额 + 运费

### 4. 状态管理

- 只有草稿状态的订单可以编辑基本信息
- 状态转换需要验证合法性
- 完成状态的订单不能删除

### 5. 代发模式

- 代发模式下必须指定代发客户
- 代发客户必须存在且未删除

### 6. 物流配置

- 寄付模式需要指定运费金额
- 可选择指定发货人（库房人员）
- 支持物流公司和物流单号

## 权限设计

- **销售人员**：可创建、查看自己的销售订单
- **销售主管**：可确认订单状态
- **库房人员**：可作为发货人
- **管理员**：拥有所有权限

## 重要说明

### inventoryId 字段说明

- **inventoryId** 指的是 `skus_inventory` 表的主键 `id`（UUID）
- 不是 `skuId`，也不是 `skuCode`
- 每个库存记录对应一个 SKU + 尺码的组合
- 例如：同一个SKU的不同尺码（S、M、L）会有不同的库存记录ID

#### 如何获取正确的 inventoryId

```sql
-- 查询某个SKU的所有库存记录
SELECT
  si.id as inventoryId,           -- 这个就是我们需要的 inventoryId
  si.skuId,                       -- SKU的ID
  s.code as skuCode,              -- SKU编码
  si.size,                        -- 尺码
  si.currentStock                 -- 当前库存
FROM skus_inventory si
LEFT JOIN skus s ON si.skuId = s.id
WHERE s.code = 'SKU001'           -- 根据SKU编码查询
  AND si.isDeleted = false;

-- 结果示例：
-- inventoryId: a1b2c3d4-e5f6-7890-abcd-ef1234567890 (S码库存记录)
-- inventoryId: b2c3d4e5-f6g7-8901-bcde-f12345678901 (M码库存记录)
-- inventoryId: c3d4e5f6-g7h8-9012-cdef-123456789012 (L码库存记录)
```

### 数据库迁移说明

- 迁移文件只会执行一次
- TypeORM 会在 `migrations` 表中记录已执行的迁移
- 首次部署时会清除现有 sales-orders 相关表并重建
- 后续部署不会重复执行该迁移

## 注意事项

1. **数据一致性**：所有涉及多表操作的功能都使用事务处理
2. **库存同步**：下单时自动扣减库存，确保库存准确性
3. **状态控制**：严格的状态转换验证，防止非法状态变更
4. **软删除**：删除操作为软删除，数据不会物理删除
5. **性能优化**：合理的索引设计，支持高效的查询和统计

## 扩展功能

系统设计时已考虑未来扩展：

- 支持批量操作
- 支持Excel导入导出
- 支持消息通知
- 支持工作流引擎集成
- 支持更复杂的定价策略
