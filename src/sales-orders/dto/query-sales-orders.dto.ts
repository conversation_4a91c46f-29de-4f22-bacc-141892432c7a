import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsDateString,
  IsNumber,
  Min,
  Max,
  IsBoolean,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  SalesOrderStatus,
  SalesOrderPriority,
  ShippingMethod,
} from '../entities/sales-order.entity';

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum SortField {
  ORDER_DATE = 'orderDate',
  CREATED_AT = 'createdAt',
  TOTAL_AMOUNT = 'totalAmount',
  GRAND_TOTAL = 'grandTotal',
  ORDER_NUMBER = 'orderNumber',
}

export class QuerySalesOrdersDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能超过100' })
  pageSize?: number = 10;

  @ApiProperty({
    description: '排序字段',
    enum: SortField,
    example: SortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortField, { message: '排序字段无效' })
  sortField?: SortField = SortField.CREATED_AT;

  @ApiProperty({
    description: '排序方向',
    enum: SortOrder,
    example: SortOrder.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, { message: '排序方向无效' })
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiProperty({
    description: '订单编号（模糊搜索）',
    example: 'SO202501',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '订单编号必须是字符串' })
  orderNumber?: string;

  @ApiProperty({
    description: '销售人员编码',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '销售人员编码必须是字符串' })
  salesPersonCode?: string;

  @ApiProperty({
    description: '主客户编码',
    example: 'CUS001',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '主客户编码必须是字符串' })
  customerCode?: string;

  @ApiProperty({
    description: '订单状态',
    enum: SalesOrderStatus,
    example: SalesOrderStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(SalesOrderStatus, { message: '订单状态无效' })
  status?: SalesOrderStatus;

  @ApiProperty({
    description: '订单优先级',
    enum: SalesOrderPriority,
    example: SalesOrderPriority.NORMAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(SalesOrderPriority, { message: '订单优先级无效' })
  priority?: SalesOrderPriority;

  @ApiProperty({
    description: '物流方式',
    enum: ShippingMethod,
    example: ShippingMethod.COLLECT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShippingMethod, { message: '物流方式无效' })
  shippingMethod?: ShippingMethod;

  @ApiProperty({
    description: '是否代发',
    example: false,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: '是否代发必须是布尔值' })
  isDropShipping?: boolean;

  @ApiProperty({
    description: '是否放单',
    example: false,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: '是否放单必须是布尔值' })
  isReleased?: boolean;

  @ApiProperty({
    description: '订单日期开始',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '订单日期开始格式无效' })
  orderDateStart?: string;

  @ApiProperty({
    description: '订单日期结束',
    example: '2025-01-31',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '订单日期结束格式无效' })
  orderDateEnd?: string;

  @ApiProperty({
    description: '创建时间开始',
    example: '2025-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '创建时间开始格式无效' })
  createdAtStart?: string;

  @ApiProperty({
    description: '创建时间结束',
    example: '2025-01-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '创建时间结束格式无效' })
  createdAtEnd?: string;

  @ApiProperty({
    description: '总金额最小值',
    example: 1000.00,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '总金额最小值必须是数字' })
  @Min(0, { message: '总金额最小值不能小于0' })
  totalAmountMin?: number;

  @ApiProperty({
    description: '总金额最大值',
    example: 10000.00,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '总金额最大值必须是数字' })
  @Min(0, { message: '总金额最大值不能小于0' })
  totalAmountMax?: number;

  @ApiProperty({
    description: '物流公司（模糊搜索）',
    example: '顺丰',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '物流公司必须是字符串' })
  shippingCompany?: string;

  @ApiProperty({
    description: '物流单号（模糊搜索）',
    example: 'SF123',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '物流单号必须是字符串' })
  trackingNumber?: string;

  @ApiProperty({
    description: '是否包含明细信息',
    example: false,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: '是否包含明细信息必须是布尔值' })
  includeDetails?: boolean = false;

  @ApiProperty({
    description: '是否包含客户关联信息',
    example: false,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: '是否包含客户关联信息必须是布尔值' })
  includeCustomers?: boolean = false;
}
