import { ApiProperty } from '@nestjs/swagger';
import {
  SalesOrderStatus,
  SalesOrderPriority,
  ShippingMethod,
} from '../entities/sales-order.entity';
import { PriceType } from '../entities/sales-order-detail.entity';
import { CustomerOrderStatus } from '../entities/sales-order-customer.entity';

export class SalesOrderDetailResponseDto {
  @ApiProperty({
    description: '销售订单明细ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: '库存记录ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  inventoryId: string;

  @ApiProperty({
    description: '商品编码',
    example: 'PROD001',
  })
  productCode: string;

  @ApiProperty({
    description: '颜色编码',
    example: 'COLOR001',
  })
  colorCode: string;

  @ApiProperty({
    description: '尺寸编码',
    example: 'M',
  })
  sizeCode: string;

  @ApiProperty({
    description: 'SKU编码',
    example: 'SKU001-COLOR001-M',
  })
  skuCode: string;

  @ApiProperty({
    description: '商品名称',
    example: '春季新款T恤',
    nullable: true,
  })
  productName: string | null;

  @ApiProperty({
    description: '颜色名称',
    example: '天蓝色',
    nullable: true,
  })
  colorName: string | null;

  @ApiProperty({
    description: '销售数量',
    example: 10,
  })
  quantity: number;

  @ApiProperty({
    description: '已发货数量',
    example: 0,
  })
  shippedQuantity: number;

  @ApiProperty({
    description: '待发货数量',
    example: 10,
  })
  pendingShipQuantity: number;

  @ApiProperty({
    description: '库存预留数量',
    example: 10,
  })
  reservedQuantity: number;

  @ApiProperty({
    description: '下单时库存数量',
    example: 50,
  })
  stockAtOrder: number;

  @ApiProperty({
    description: '价格类型',
    enum: PriceType,
    example: PriceType.RETAIL,
  })
  priceType: PriceType;

  @ApiProperty({
    description: '单价',
    example: 50.0,
  })
  unitPrice: number;

  @ApiProperty({
    description: '小计金额',
    example: 500.0,
  })
  totalAmount: number;

  @ApiProperty({
    description: '折扣金额',
    example: 0.0,
  })
  discountAmount: number;

  @ApiProperty({
    description: '实际金额',
    example: 500.0,
  })
  actualAmount: number;

  @ApiProperty({
    description: '成本价',
    example: 30.0,
  })
  costPrice: number;

  @ApiProperty({
    description: '成本总额',
    example: 300.0,
  })
  totalCost: number;

  @ApiProperty({
    description: '毛利润',
    example: 200.0,
  })
  grossProfit: number;

  @ApiProperty({
    description: '商品分类编码',
    example: 'CAT001',
    nullable: true,
  })
  categoryCode: string | null;

  @ApiProperty({
    description: '品牌编码',
    example: 'BRAND001',
    nullable: true,
  })
  brandCode: string | null;

  @ApiProperty({
    description: '供应商编码',
    example: 'SUP001',
    nullable: true,
  })
  supplierCode: string | null;

  @ApiProperty({
    description: '期望发货日期',
    example: '2025-01-20',
    nullable: true,
  })
  expectedShipDate: Date | null;

  @ApiProperty({
    description: '明细备注',
    example: '客户指定颜色',
    nullable: true,
  })
  remark: string | null;

  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;
}

export class SalesOrderCustomerResponseDto {
  @ApiProperty({
    description: '销售订单客户关联ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: '客户编码',
    example: 'CUS001',
  })
  customerCode: string;

  @ApiProperty({
    description: '客户名称',
    example: '张三',
    nullable: true,
  })
  customerName: string | null;

  @ApiProperty({
    description: '分配总数量',
    example: 50,
  })
  allocatedQuantity: number;

  @ApiProperty({
    description: '分配总金额',
    example: 2500.0,
  })
  allocatedAmount: number;

  @ApiProperty({
    description: '分配运费',
    example: 15.0,
  })
  allocatedShippingFee: number;

  @ApiProperty({
    description: '客户应付总额',
    example: 2515.0,
  })
  totalPayable: number;

  @ApiProperty({
    description: '客户订单状态',
    enum: CustomerOrderStatus,
    example: CustomerOrderStatus.PENDING,
  })
  status: CustomerOrderStatus;

  @ApiProperty({
    description: '收货人姓名',
    example: '李四',
    nullable: true,
  })
  receiverName: string | null;

  @ApiProperty({
    description: '收货人电话',
    example: '13800138000',
    nullable: true,
  })
  receiverPhone: string | null;

  @ApiProperty({
    description: '收货地址',
    example: '北京市朝阳区xxx街道xxx号',
    nullable: true,
  })
  shippingAddress: string | null;

  @ApiProperty({
    description: '省份代码',
    example: 11,
    nullable: true,
  })
  provinceCode: number | null;

  @ApiProperty({
    description: '城市',
    example: '北京市',
    nullable: true,
  })
  city: string | null;

  @ApiProperty({
    description: '区县',
    example: '朝阳区',
    nullable: true,
  })
  district: string | null;

  @ApiProperty({
    description: '物流公司',
    example: '顺丰快递',
    nullable: true,
  })
  shippingCompany: string | null;

  @ApiProperty({
    description: '物流单号',
    example: 'SF1234567890',
    nullable: true,
  })
  trackingNumber: string | null;

  @ApiProperty({
    description: '发货时间',
    example: '2025-01-20T10:00:00.000Z',
    nullable: true,
  })
  shippedAt: Date | null;

  @ApiProperty({
    description: '送达时间',
    example: '2025-01-22T14:30:00.000Z',
    nullable: true,
  })
  deliveredAt: Date | null;

  @ApiProperty({
    description: '客户优先级',
    example: 0,
  })
  priority: number;

  @ApiProperty({
    description: '是否加急',
    example: false,
  })
  isUrgent: boolean;

  @ApiProperty({
    description: '特殊要求',
    example: '需要特殊包装',
    nullable: true,
  })
  specialRequirements: string | null;

  @ApiProperty({
    description: '客户备注',
    example: '客户要求分批发货',
    nullable: true,
  })
  remark: string | null;

  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;
}

export class SalesOrderResponseDto {
  @ApiProperty({
    description: '销售订单ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: '销售订单编号',
    example: 'SO202501190001',
  })
  orderNumber: string;

  @ApiProperty({
    description: '销售人员编码',
    example: 'user001',
  })
  salesPersonCode: string;

  @ApiProperty({
    description: '销售人员昵称',
    example: '张三',
    nullable: true,
  })
  salesPersonName: string | null;

  @ApiProperty({
    description: '订单日期',
    example: '2025-01-19',
  })
  orderDate: Date;

  @ApiProperty({
    description: '期望交货日期',
    example: '2025-01-25',
    nullable: true,
  })
  expectedDeliveryDate: Date | null;

  @ApiProperty({
    description: '订单优先级',
    enum: SalesOrderPriority,
    example: SalesOrderPriority.NORMAL,
  })
  priority: SalesOrderPriority;

  @ApiProperty({
    description: '订单状态',
    enum: SalesOrderStatus,
    example: SalesOrderStatus.DRAFT,
  })
  status: SalesOrderStatus;

  @ApiProperty({
    description: '是否代发',
    example: false,
  })
  isDropShipping: boolean;

  @ApiProperty({
    description: '是否放单',
    example: false,
  })
  isReleased: boolean;

  @ApiProperty({
    description: '物流方式',
    enum: ShippingMethod,
    example: ShippingMethod.COLLECT,
  })
  shippingMethod: ShippingMethod;

  @ApiProperty({
    description: '运费金额',
    example: 15.0,
  })
  shippingFee: number;

  @ApiProperty({
    description: '物流公司名称',
    example: '顺丰快递',
    nullable: true,
  })
  shippingCompany: string | null;

  @ApiProperty({
    description: '物流单号',
    example: 'SF1234567890',
    nullable: true,
  })
  trackingNumber: string | null;

  @ApiProperty({
    description: '发货人编码',
    example: 'user003',
    nullable: true,
  })
  shipperUserCode: string | null;

  @ApiProperty({
    description: '发货人姓名',
    example: '王五',
    nullable: true,
  })
  shipperUserName: string | null;

  @ApiProperty({
    description: '主客户编码',
    example: 'CUS001',
  })
  customerCode: string;

  @ApiProperty({
    description: '主客户名称',
    example: '张三',
    nullable: true,
  })
  customerName: string | null;

  @ApiProperty({
    description: '代发客户编码',
    example: 'CUS002',
    nullable: true,
  })
  dropShipCustomerCode: string | null;

  @ApiProperty({
    description: '代发客户名称',
    example: '李四',
    nullable: true,
  })
  dropShipCustomerName: string | null;

  @ApiProperty({
    description: '总商品数量',
    example: 100,
  })
  totalQuantity: number;

  @ApiProperty({
    description: '商品总金额',
    example: 5000.0,
  })
  totalAmount: number;

  @ApiProperty({
    description: '订单总金额',
    example: 5015.0,
  })
  grandTotal: number;

  @ApiProperty({
    description: '创建人编码',
    example: 'user001',
    nullable: true,
  })
  createdByUserCode: string | null;

  @ApiProperty({
    description: '确认人编码',
    example: 'user002',
    nullable: true,
  })
  confirmedByUserCode: string | null;

  @ApiProperty({
    description: '确认时间',
    example: '2025-01-19T10:30:00.000Z',
    nullable: true,
  })
  confirmedAt: Date | null;

  @ApiProperty({
    description: '订单备注',
    example: '客户要求加急处理',
    nullable: true,
  })
  remark: string | null;

  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: '销售订单明细列表',
    type: [SalesOrderDetailResponseDto],
    required: false,
  })
  details?: SalesOrderDetailResponseDto[];

  @ApiProperty({
    description: '销售订单客户关联列表',
    type: [SalesOrderCustomerResponseDto],
    required: false,
  })
  customers?: SalesOrderCustomerResponseDto[];
}

export class SalesOrderListResponseDto {
  @ApiProperty({
    description: '销售订单列表',
    type: [SalesOrderResponseDto],
  })
  data: SalesOrderResponseDto[];

  @ApiProperty({
    description: '总记录数',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: '当前页码',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: '每页数量',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: '总页数',
    example: 10,
  })
  totalPages: number;
}
