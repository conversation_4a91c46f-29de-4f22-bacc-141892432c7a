import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsNumber,
  IsDateString,
  Min,
  IsUUID,
} from 'class-validator';
import { Transform } from 'class-transformer';
import {
  SalesOrderStatus,
  SalesOrderPriority,
  ShippingMethod,
} from '../entities/sales-order.entity';

export class UpdateSalesOrderDto {
  @ApiProperty({
    description: '期望交货日期',
    example: '2025-01-25',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '期望交货日期格式无效' })
  expectedDeliveryDate?: string;

  @ApiProperty({
    description: '订单优先级',
    enum: SalesOrderPriority,
    example: SalesOrderPriority.NORMAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(SalesOrderPriority, { message: '订单优先级无效' })
  priority?: SalesOrderPriority;

  @ApiProperty({
    description: '是否代发',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '是否代发必须是布尔值' })
  isDropShipping?: boolean;

  @ApiProperty({
    description: '是否放单',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '是否放单必须是布尔值' })
  isReleased?: boolean;

  @ApiProperty({
    description: '物流方式',
    enum: ShippingMethod,
    example: ShippingMethod.COLLECT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShippingMethod, { message: '物流方式无效' })
  shippingMethod?: ShippingMethod;

  @ApiProperty({
    description: '运费金额',
    example: 15.0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '运费金额必须是数字' })
  @Min(0, { message: '运费金额不能小于0' })
  @Transform(({ value }) => parseFloat(value))
  shippingFee?: number;

  @ApiProperty({
    description: '物流公司名称',
    example: '顺丰快递',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '物流公司名称必须是字符串' })
  shippingCompany?: string;

  @ApiProperty({
    description: '物流单号',
    example: 'SF1234567890',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '物流单号必须是字符串' })
  trackingNumber?: string;

  @ApiProperty({
    description: '发货人编码（库房发货人）',
    example: 'user003',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '发货人编码必须是字符串' })
  shipperUserCode?: string;

  @ApiProperty({
    description: '代发客户编码（代发模式下必填）',
    example: 'CUS002',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '代发客户编码必须是字符串' })
  dropShipCustomerCode?: string;

  @ApiProperty({
    description: '订单备注',
    example: '客户要求加急处理',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '订单备注必须是字符串' })
  remark?: string;
}

export class UpdateSalesOrderStatusDto {
  @ApiProperty({
    description: '订单状态',
    enum: SalesOrderStatus,
    example: SalesOrderStatus.PENDING_SHIPMENT,
  })
  @IsEnum(SalesOrderStatus, { message: '订单状态无效' })
  status: SalesOrderStatus;

  @ApiProperty({
    description: '确认人编码（状态变更为待发货时必填）',
    example: 'user002',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '确认人编码必须是字符串' })
  confirmedByUserCode?: string;

  @ApiProperty({
    description: '状态变更备注',
    example: '订单已确认，准备发货',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '状态变更备注必须是字符串' })
  remark?: string;
}

export class UpdateSalesOrderDetailDto {
  @ApiProperty({
    description: '销售订单明细ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @IsUUID('4', { message: '销售订单明细ID必须是有效的UUID' })
  id: string;

  @ApiProperty({
    description: '销售数量',
    example: 10,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '销售数量必须是数字' })
  @Min(1, { message: '销售数量不能小于1' })
  quantity?: number;

  @ApiProperty({
    description: '单价',
    example: 50.0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '单价必须是数字' })
  @Min(0, { message: '单价不能小于0' })
  @Transform(({ value }) => parseFloat(value))
  unitPrice?: number;

  @ApiProperty({
    description: '折扣金额',
    example: 0.0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '折扣金额必须是数字' })
  @Min(0, { message: '折扣金额不能小于0' })
  @Transform(({ value }) => parseFloat(value))
  discountAmount?: number;

  @ApiProperty({
    description: '期望发货日期',
    example: '2025-01-20',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '期望发货日期格式无效' })
  expectedShipDate?: string;

  @ApiProperty({
    description: '明细备注',
    example: '客户指定颜色',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '明细备注必须是字符串' })
  remark?: string;
}

export class BatchUpdateSalesOrderDetailsDto {
  @ApiProperty({
    description: '销售订单ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @IsUUID('4', { message: '销售订单ID必须是有效的UUID' })
  salesOrderId: string;

  @ApiProperty({
    description: '要更新的明细列表',
    type: [UpdateSalesOrderDetailDto],
  })
  details: UpdateSalesOrderDetailDto[];
}
