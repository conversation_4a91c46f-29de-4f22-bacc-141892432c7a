import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsNumber,
  IsArray,
  ValidateNested,
  IsUUID,
  IsDateString,
  Min,
  IsPositive,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import {
  SalesOrderPriority,
  ShippingMethod,
} from '../entities/sales-order.entity';
import { PriceType } from '../entities/sales-order-detail.entity';

export class CreateSalesOrderDetailDto {
  @ApiProperty({
    description: '库存记录ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @IsUUID('4', { message: '库存记录ID必须是有效的UUID' })
  @IsNotEmpty({ message: '库存记录ID不能为空' })
  inventoryId: string;

  @ApiProperty({
    description: '销售数量',
    example: 10,
    minimum: 1,
  })
  @IsNumber({}, { message: '销售数量必须是数字' })
  @IsPositive({ message: '销售数量必须大于0' })
  @Min(1, { message: '销售数量不能小于1' })
  quantity: number;

  @ApiProperty({
    description: '价格类型',
    enum: PriceType,
    example: PriceType.RETAIL,
  })
  @IsEnum(PriceType, { message: '价格类型无效' })
  priceType: PriceType;

  @ApiProperty({
    description: '单价',
    example: 50.0,
    minimum: 0,
  })
  @IsNumber({}, { message: '单价必须是数字' })
  @Min(0, { message: '单价不能小于0' })
  @Transform(({ value }) => parseFloat(value))
  unitPrice: number;

  @ApiProperty({
    description: '折扣金额',
    example: 0.0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '折扣金额必须是数字' })
  @Min(0, { message: '折扣金额不能小于0' })
  @Transform(({ value }) => parseFloat(value))
  discountAmount?: number;

  @ApiProperty({
    description: '期望发货日期',
    example: '2025-01-20',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '期望发货日期格式无效' })
  expectedShipDate?: string;

  @ApiProperty({
    description: '明细备注',
    example: '客户指定颜色',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '明细备注必须是字符串' })
  remark?: string;
}

export class CreateSalesOrderCustomerDto {
  @ApiProperty({
    description: '客户编码',
    example: 'CUS001',
  })
  @IsString({ message: '客户编码必须是字符串' })
  @IsNotEmpty({ message: '客户编码不能为空' })
  customerCode: string;

  @ApiProperty({
    description: '分配总数量',
    example: 50,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '分配总数量必须是数字' })
  @Min(0, { message: '分配总数量不能小于0' })
  allocatedQuantity?: number;

  @ApiProperty({
    description: '分配总金额',
    example: 2500.0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '分配总金额必须是数字' })
  @Min(0, { message: '分配总金额不能小于0' })
  @Transform(({ value }) => parseFloat(value))
  allocatedAmount?: number;

  @ApiProperty({
    description: '分配运费',
    example: 15.0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '分配运费必须是数字' })
  @Min(0, { message: '分配运费不能小于0' })
  @Transform(({ value }) => parseFloat(value))
  allocatedShippingFee?: number;

  @ApiProperty({
    description: '收货人姓名',
    example: '李四',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '收货人姓名必须是字符串' })
  receiverName?: string;

  @ApiProperty({
    description: '收货人电话',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '收货人电话必须是字符串' })
  receiverPhone?: string;

  @ApiProperty({
    description: '收货地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '收货地址必须是字符串' })
  shippingAddress?: string;

  @ApiProperty({
    description: '省份代码',
    example: 11,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '省份代码必须是数字' })
  provinceCode?: number;

  @ApiProperty({
    description: '城市',
    example: '北京市',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '城市必须是字符串' })
  city?: string;

  @ApiProperty({
    description: '区县',
    example: '朝阳区',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '区县必须是字符串' })
  district?: string;

  @ApiProperty({
    description: '客户优先级（数字越大优先级越高）',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '客户优先级必须是数字' })
  priority?: number;

  @ApiProperty({
    description: '是否加急',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '是否加急必须是布尔值' })
  isUrgent?: boolean;

  @ApiProperty({
    description: '特殊要求',
    example: '需要特殊包装',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '特殊要求必须是字符串' })
  specialRequirements?: string;

  @ApiProperty({
    description: '客户备注',
    example: '客户要求分批发货',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '客户备注必须是字符串' })
  remark?: string;
}

export class CreateSalesOrderDto {
  @ApiProperty({
    description: '销售人员编码',
    example: 'user001',
  })
  @IsString({ message: '销售人员编码必须是字符串' })
  @IsNotEmpty({ message: '销售人员编码不能为空' })
  salesPersonCode: string;

  @ApiProperty({
    description: '订单日期',
    example: '2025-01-19',
  })
  @IsDateString({}, { message: '订单日期格式无效' })
  @IsNotEmpty({ message: '订单日期不能为空' })
  orderDate: string;

  @ApiProperty({
    description: '期望交货日期',
    example: '2025-01-25',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '期望交货日期格式无效' })
  expectedDeliveryDate?: string;

  @ApiProperty({
    description: '订单优先级',
    enum: SalesOrderPriority,
    example: SalesOrderPriority.NORMAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(SalesOrderPriority, { message: '订单优先级无效' })
  priority?: SalesOrderPriority;

  @ApiProperty({
    description: '是否代发',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '是否代发必须是布尔值' })
  isDropShipping?: boolean;

  @ApiProperty({
    description: '是否放单',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '是否放单必须是布尔值' })
  isReleased?: boolean;

  @ApiProperty({
    description: '物流方式',
    enum: ShippingMethod,
    example: ShippingMethod.COLLECT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShippingMethod, { message: '物流方式无效' })
  shippingMethod?: ShippingMethod;

  @ApiProperty({
    description: '运费金额',
    example: 15.0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '运费金额必须是数字' })
  @Min(0, { message: '运费金额不能小于0' })
  @Transform(({ value }) => parseFloat(value))
  shippingFee?: number;

  @ApiProperty({
    description: '物流公司名称',
    example: '顺丰快递',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '物流公司名称必须是字符串' })
  shippingCompany?: string;

  @ApiProperty({
    description: '发货人编码（库房发货人）',
    example: 'user003',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '发货人编码必须是字符串' })
  shipperUserCode?: string;

  @ApiProperty({
    description: '主客户编码',
    example: 'CUS001',
  })
  @IsString({ message: '主客户编码必须是字符串' })
  @IsNotEmpty({ message: '主客户编码不能为空' })
  customerCode: string;

  @ApiProperty({
    description: '代发客户编码（代发模式下必填）',
    example: 'CUS002',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '代发客户编码必须是字符串' })
  dropShipCustomerCode?: string;

  @ApiProperty({
    description: '订单备注',
    example: '客户要求加急处理',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '订单备注必须是字符串' })
  remark?: string;

  @ApiProperty({
    description: '销售订单明细列表',
    type: [CreateSalesOrderDetailDto],
  })
  @IsArray({ message: '销售订单明细必须是数组' })
  @ValidateNested({ each: true })
  @Type(() => CreateSalesOrderDetailDto)
  details: CreateSalesOrderDetailDto[];

  @ApiProperty({
    description: '销售订单客户关联列表（可选，用于多客户分配）',
    type: [CreateSalesOrderCustomerDto],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: '销售订单客户关联必须是数组' })
  @ValidateNested({ each: true })
  @Type(() => CreateSalesOrderCustomerDto)
  customers?: CreateSalesOrderCustomerDto[];
}
