import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@/users/entities/user.entity';
import { Customer } from '@/customers/entities/customer.entity';

export enum SalesOrderStatus {
  DRAFT = 'draft',
  PENDING_SHIPMENT = 'pending_shipment',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum SalesOrderPriority {
  URGENT = 'urgent',
  HIGH = 'high',
  NORMAL = 'normal',
  LOW = 'low',
}

export enum ShippingMethod {
  COLLECT = 'collect', // 到付
  PREPAID = 'prepaid', // 寄付
}

@Entity('sales_orders')
@Index(['orderNumber'], { unique: true })
@Index(['salesPersonCode', 'orderDate'])
@Index(['status', 'orderDate'])
export class SalesOrder {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '销售订单ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: '销售订单编号',
  })
  @ApiProperty({
    description: '销售订单编号（自动生成）',
    example: 'SO202501190001',
  })
  orderNumber: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '销售人员编码',
  })
  @ApiProperty({
    description: '销售人员编码',
    example: 'user001',
  })
  salesPersonCode: string;

  // 销售人员关联
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'salesPersonCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '销售人员信息',
    type: () => User,
    required: false,
  })
  salesPerson: User | null;

  @Column({
    type: 'date',
    comment: '订单日期',
  })
  @ApiProperty({
    description: '订单日期',
    example: '2025-01-19',
  })
  orderDate: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: '期望交货日期',
  })
  @ApiProperty({
    description: '期望交货日期',
    example: '2025-01-25',
    required: false,
  })
  expectedDeliveryDate: Date | null;

  @Column({
    type: 'enum',
    enum: SalesOrderPriority,
    default: SalesOrderPriority.NORMAL,
    comment: '订单优先级',
  })
  @ApiProperty({
    description: '订单优先级',
    enum: SalesOrderPriority,
    example: SalesOrderPriority.NORMAL,
  })
  priority: SalesOrderPriority;

  @Column({
    type: 'enum',
    enum: SalesOrderStatus,
    default: SalesOrderStatus.DRAFT,
    comment: '订单状态',
  })
  @ApiProperty({
    description: '订单状态',
    enum: SalesOrderStatus,
    example: SalesOrderStatus.DRAFT,
  })
  status: SalesOrderStatus;

  // 业务选项字段
  @Column({
    type: 'boolean',
    default: false,
    comment: '是否代发',
  })
  @ApiProperty({
    description: '是否代发',
    example: false,
  })
  isDropShipping: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否放单',
  })
  @ApiProperty({
    description: '是否放单',
    example: false,
  })
  isReleased: boolean;

  // 物流相关字段
  @Column({
    type: 'enum',
    enum: ShippingMethod,
    default: ShippingMethod.COLLECT,
    comment: '物流方式',
  })
  @ApiProperty({
    description: '物流方式',
    enum: ShippingMethod,
    example: ShippingMethod.COLLECT,
  })
  shippingMethod: ShippingMethod;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '运费金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '运费金额',
    example: 15.0,
  })
  shippingFee: number;

  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: '物流公司',
  })
  @ApiProperty({
    description: '物流公司名称',
    example: '顺丰快递',
    required: false,
  })
  shippingCompany: string | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '物流单号',
  })
  @ApiProperty({
    description: '物流单号',
    example: 'SF1234567890',
    required: false,
  })
  trackingNumber: string | null;

  // 发货人信息
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '发货人编码',
  })
  @ApiProperty({
    description: '发货人编码（库房发货人）',
    example: 'user003',
    required: false,
  })
  shipperUserCode: string | null;

  // 发货人关联
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'shipperUserCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '发货人信息',
    type: () => User,
    required: false,
  })
  shipper: User | null;

  // 主客户信息
  @Column({
    type: 'varchar',
    length: 50,
    comment: '主客户编码',
  })
  @ApiProperty({
    description: '主客户编码',
    example: 'CUS001',
  })
  customerCode: string;

  // 主客户关联
  @ManyToOne(() => Customer, { nullable: true })
  @JoinColumn({ name: 'customerCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '主客户信息',
    type: () => Customer,
    required: false,
  })
  customer: Customer | null;

  // 代发客户信息（仅在代发模式下使用）
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '代发客户编码',
  })
  @ApiProperty({
    description: '代发客户编码（代发模式下必填）',
    example: 'CUS002',
    required: false,
  })
  dropShipCustomerCode: string | null;

  // 代发客户关联
  @ManyToOne(() => Customer, { nullable: true })
  @JoinColumn({ name: 'dropShipCustomerCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '代发客户信息',
    type: () => Customer,
    required: false,
  })
  dropShipCustomer: Customer | null;

  // 金额统计字段
  @Column({
    type: 'int',
    default: 0,
    comment: '总商品数量',
  })
  @ApiProperty({
    description: '总商品数量（自动计算）',
    example: 100,
  })
  totalQuantity: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '商品总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '商品总金额（自动计算）',
    example: 5000.0,
  })
  totalAmount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '订单总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '订单总金额（商品总金额 + 运费）',
    example: 5015.0,
  })
  grandTotal: number;

  // 审核相关字段
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '创建人编码',
  })
  @ApiProperty({
    description: '创建人编码',
    example: 'user001',
    required: false,
  })
  createdByUserCode: string | null;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '确认人编码',
  })
  @ApiProperty({
    description: '确认人编码',
    example: 'user002',
    required: false,
  })
  confirmedByUserCode: string | null;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '确认时间',
  })
  @ApiProperty({
    description: '确认时间',
    example: '2025-01-19T10:30:00.000Z',
    required: false,
  })
  confirmedAt: Date | null;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  @ApiProperty({
    description: '订单备注',
    example: '客户要求加急处理',
    required: false,
  })
  remark: string | null;

  // 系统字段
  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联关系
  @OneToMany('SalesOrderDetail', 'salesOrder')
  @ApiProperty({
    description: '销售订单明细列表',
    type: () => Array,
  })
  details: any[];

  @OneToMany('SalesOrderCustomer', 'salesOrder')
  @ApiProperty({
    description: '销售订单客户关联列表',
    type: () => Array,
  })
  customers: any[];
}
