import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

import { SkuInventory } from '@/skus-inventory/entities/sku-inventory.entity';

export enum PriceType {
  RETAIL = 'retail',
  PRE_ORDER = 'pre_order',
  RESTOCK = 'restock',
  SPOT = 'spot',
}

@Entity('sales_order_details')
@Index(['salesOrderId', 'skuCode'])
@Index(['productCode', 'colorCode', 'sizeCode'])
export class SalesOrderDetail {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '销售订单明细ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Column({
    type: 'uuid',
    comment: '销售订单ID',
  })
  @ApiProperty({
    description: '销售订单ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  salesOrderId: string;

  // 销售订单关联
  @ManyToOne('SalesOrder', 'details', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'salesOrderId' })
  @ApiProperty({
    description: '销售订单信息',
    type: () => Object,
  })
  salesOrder: any;

  // 库存记录关联
  @Column({
    type: 'uuid',
    comment: '库存记录ID',
  })
  @ApiProperty({
    description: '库存记录ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  inventoryId: string;

  @ManyToOne(() => SkuInventory, { nullable: true })
  @JoinColumn({ name: 'inventoryId' })
  @ApiProperty({
    description: '库存记录信息',
    type: () => SkuInventory,
    required: false,
  })
  inventory: SkuInventory | null;

  // 商品信息字段（冗余存储，便于查询）
  @Column({
    type: 'varchar',
    length: 100,
    comment: '商品编码',
  })
  @ApiProperty({
    description: '商品编码',
    example: 'PROD001',
  })
  productCode: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '颜色编码',
  })
  @ApiProperty({
    description: '颜色编码',
    example: 'COLOR001',
  })
  colorCode: string;

  @Column({
    type: 'varchar',
    length: 10,
    comment: '尺寸编码',
  })
  @ApiProperty({
    description: '尺寸编码',
    example: 'M',
  })
  sizeCode: string;

  @Column({
    type: 'varchar',
    length: 150,
    comment: 'SKU编码',
  })
  @ApiProperty({
    description: 'SKU编码',
    example: 'SKU001-COLOR001-M',
  })
  skuCode: string;

  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: '商品名称',
  })
  @ApiProperty({
    description: '商品名称',
    example: '春季新款T恤',
    required: false,
  })
  productName: string | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '颜色名称',
  })
  @ApiProperty({
    description: '颜色名称',
    example: '天蓝色',
    required: false,
  })
  colorName: string | null;

  // 数量相关字段
  @Column({
    type: 'int',
    comment: '销售数量',
  })
  @ApiProperty({
    description: '销售数量',
    example: 10,
  })
  quantity: number;

  @Column({
    type: 'int',
    default: 0,
    comment: '已发货数量',
  })
  @ApiProperty({
    description: '已发货数量',
    example: 0,
  })
  shippedQuantity: number;

  @Column({
    type: 'int',
    default: 0,
    comment: '待发货数量',
  })
  @ApiProperty({
    description: '待发货数量',
    example: 10,
  })
  pendingShipQuantity: number;

  @Column({
    type: 'int',
    default: 0,
    comment: '库存预留数量',
  })
  @ApiProperty({
    description: '库存预留数量',
    example: 10,
  })
  reservedQuantity: number;

  @Column({
    type: 'int',
    default: 0,
    comment: '下单时库存数量',
  })
  @ApiProperty({
    description: '下单时库存数量',
    example: 50,
  })
  stockAtOrder: number;

  // 价格相关字段
  @Column({
    type: 'enum',
    enum: PriceType,
    default: PriceType.RETAIL,
    comment: '价格类型',
  })
  @ApiProperty({
    description: '价格类型',
    enum: PriceType,
    example: PriceType.RETAIL,
  })
  priceType: PriceType;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '单价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '单价',
    example: 50.0,
  })
  unitPrice: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '小计金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '小计金额（单价 × 数量）',
    example: 500.0,
  })
  totalAmount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '折扣金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '折扣金额',
    example: 0.0,
  })
  discountAmount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '实际金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '实际金额（小计金额 - 折扣金额）',
    example: 500.0,
  })
  actualAmount: number;

  // 成本相关字段
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '成本价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '成本价',
    example: 30.0,
  })
  costPrice: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '成本总额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '成本总额（成本价 × 数量）',
    example: 300.0,
  })
  totalCost: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '毛利润',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '毛利润（实际金额 - 成本总额）',
    example: 200.0,
  })
  grossProfit: number;

  // 统计字段（冗余存储，便于统计分析）
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '商品分类编码',
  })
  @ApiProperty({
    description: '商品分类编码',
    example: 'CAT001',
    required: false,
  })
  categoryCode: string | null;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '品牌编码',
  })
  @ApiProperty({
    description: '品牌编码',
    example: 'BRAND001',
    required: false,
  })
  brandCode: string | null;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '供应商编码',
  })
  @ApiProperty({
    description: '供应商编码',
    example: 'SUP001',
    required: false,
  })
  supplierCode: string | null;

  // 业务字段
  @Column({
    type: 'date',
    nullable: true,
    comment: '期望发货日期',
  })
  @ApiProperty({
    description: '期望发货日期',
    example: '2025-01-20',
    required: false,
  })
  expectedShipDate: Date | null;

  @Column({
    type: 'text',
    nullable: true,
    comment: '明细备注',
  })
  @ApiProperty({
    description: '明细备注',
    example: '客户指定颜色',
    required: false,
  })
  remark: string | null;

  // 系统字段
  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;
}
