import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

import { Customer } from '@/customers/entities/customer.entity';

export enum CustomerOrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('sales_order_customers')
@Index(['salesOrderId', 'customerCode'])
@Index(['customerCode', 'status'])
export class SalesOrderCustomer {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '销售订单客户关联ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Column({
    type: 'uuid',
    comment: '销售订单ID',
  })
  @ApiProperty({
    description: '销售订单ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  salesOrderId: string;

  // 销售订单关联
  @ManyToOne('SalesOrder', 'customers', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'salesOrderId' })
  @ApiProperty({
    description: '销售订单信息',
    type: () => Object,
  })
  salesOrder: any;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '客户编码',
  })
  @ApiProperty({
    description: '客户编码',
    example: 'CUS001',
  })
  customerCode: string;

  // 客户关联
  @ManyToOne(() => Customer, { nullable: true })
  @JoinColumn({ name: 'customerCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '客户信息',
    type: () => Customer,
    required: false,
  })
  customer: Customer | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '客户名称',
  })
  @ApiProperty({
    description: '客户名称',
    example: '张三',
    required: false,
  })
  customerName: string | null;

  // 分配数量和金额
  @Column({
    type: 'int',
    default: 0,
    comment: '分配总数量',
  })
  @ApiProperty({
    description: '分配总数量',
    example: 50,
  })
  allocatedQuantity: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '分配总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '分配总金额',
    example: 2500.0,
  })
  allocatedAmount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '分配运费',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '分配运费',
    example: 15.0,
  })
  allocatedShippingFee: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '客户应付总额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '客户应付总额（分配金额 + 分配运费）',
    example: 2515.0,
  })
  totalPayable: number;

  // 客户状态
  @Column({
    type: 'enum',
    enum: CustomerOrderStatus,
    default: CustomerOrderStatus.PENDING,
    comment: '客户订单状态',
  })
  @ApiProperty({
    description: '客户订单状态',
    enum: CustomerOrderStatus,
    example: CustomerOrderStatus.PENDING,
  })
  status: CustomerOrderStatus;

  // 收货信息
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '收货人姓名',
  })
  @ApiProperty({
    description: '收货人姓名',
    example: '李四',
    required: false,
  })
  receiverName: string | null;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '收货人电话',
  })
  @ApiProperty({
    description: '收货人电话',
    example: '13800138000',
    required: false,
  })
  receiverPhone: string | null;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '收货地址',
  })
  @ApiProperty({
    description: '收货地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  shippingAddress: string | null;

  @Column({
    type: 'int',
    nullable: true,
    comment: '省份代码',
  })
  @ApiProperty({
    description: '省份代码',
    example: 11,
    required: false,
  })
  provinceCode: number | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '城市',
  })
  @ApiProperty({
    description: '城市',
    example: '北京市',
    required: false,
  })
  city: string | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '区县',
  })
  @ApiProperty({
    description: '区县',
    example: '朝阳区',
    required: false,
  })
  district: string | null;

  // 物流信息
  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: '物流公司',
  })
  @ApiProperty({
    description: '物流公司',
    example: '顺丰快递',
    required: false,
  })
  shippingCompany: string | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '物流单号',
  })
  @ApiProperty({
    description: '物流单号',
    example: 'SF1234567890',
    required: false,
  })
  trackingNumber: string | null;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '发货时间',
  })
  @ApiProperty({
    description: '发货时间',
    example: '2025-01-20T10:00:00.000Z',
    required: false,
  })
  shippedAt: Date | null;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '送达时间',
  })
  @ApiProperty({
    description: '送达时间',
    example: '2025-01-22T14:30:00.000Z',
    required: false,
  })
  deliveredAt: Date | null;

  // 优先级和特殊要求
  @Column({
    type: 'int',
    default: 0,
    comment: '客户优先级',
  })
  @ApiProperty({
    description: '客户优先级（数字越大优先级越高）',
    example: 0,
  })
  priority: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否加急',
  })
  @ApiProperty({
    description: '是否加急',
    example: false,
  })
  isUrgent: boolean;

  @Column({
    type: 'text',
    nullable: true,
    comment: '特殊要求',
  })
  @ApiProperty({
    description: '特殊要求',
    example: '需要特殊包装',
    required: false,
  })
  specialRequirements: string | null;

  // 商品明细分配（JSON格式存储）
  @Column({
    type: 'jsonb',
    nullable: true,
    comment: '商品明细分配',
  })
  @ApiProperty({
    description: '商品明细分配（JSON格式）',
    example: [
      {
        detailId: 'detail-uuid-1',
        quantity: 20,
        amount: 1000.0,
      },
      {
        detailId: 'detail-uuid-2',
        quantity: 30,
        amount: 1500.0,
      },
    ],
    required: false,
  })
  itemAllocations: any[] | null;

  @Column({
    type: 'text',
    nullable: true,
    comment: '客户备注',
  })
  @ApiProperty({
    description: '客户备注',
    example: '客户要求分批发货',
    required: false,
  })
  remark: string | null;

  // 系统字段
  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;
}
