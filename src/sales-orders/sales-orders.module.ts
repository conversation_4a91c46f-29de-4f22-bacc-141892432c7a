import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SalesOrdersService } from './sales-orders.service';
import { SalesOrdersController } from './sales-orders.controller';
import { SalesOrder } from './entities/sales-order.entity';

import { SkuInventory } from '@/skus-inventory/entities/sku-inventory.entity';
import { User } from '@/users/entities/user.entity';
import { Customer } from '@/customers/entities/customer.entity';
import { ExpensesModule } from '@/expenses/expenses.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SalesOrder, SkuInventory, User, Customer]),
    ExpensesModule,
  ],
  controllers: [SalesOrdersController],
  providers: [SalesOrdersService],
  exports: [SalesOrdersService],
})
export class SalesOrdersModule {}
