import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  <PERSON>s,
  Req,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { FixedAssetsService } from './fixed-assets.service';
import { CreateFixedAssetDetailDto } from './dto/create-fixed-asset-detail.dto';
import { UpdateFixedAssetDetailDto } from './dto/update-fixed-asset-detail.dto';
import { QueryFixedAssetDetailsDto } from './dto/query-fixed-asset-details.dto';

@ApiTags('fixed-assets')
@Controller('fixed-assets')
export class FixedAssetsController {
  private readonly logger = new Logger(FixedAssetsController.name);

  constructor(private readonly fixedAssetsService: FixedAssetsService) {}

  @Post('details')
  @ApiOperation({ summary: '新增固定资产明细' })
  @ApiResponse({
    status: 200,
    description: '固定资产明细创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async createDetail(@Body() createDetailDto: CreateFixedAssetDetailDto) {
    this.logger.log(
      `Creating fixed asset detail with amount: ${createDetailDto.amount}`,
    );

    await this.fixedAssetsService.createDetail(createDetailDto);

    return {
      code: 200,
      data: null,
      message: '固定资产明细创建成功',
    };
  }

  @Get('details')
  @ApiOperation({ summary: '分页查询固定资产明细' })
  @ApiQuery({
    name: 'page',
    description: '页码（0表示不分页）',
    example: 1,
    required: false,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量（0表示不分页）',
    example: 10,
    required: false,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词（备注）',
    example: '办公设备',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: '排序字段',
    enum: ['createDate', 'amount'],
    example: 'createDate',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: '排序方向',
    enum: ['ASC', 'DESC'],
    example: 'DESC',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllDetails(@Query() queryDto: QueryFixedAssetDetailsDto) {
    this.logger.log(
      `Querying fixed asset details with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.fixedAssetsService.findAllDetails(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('details/:id')
  @ApiOperation({ summary: '获取固定资产明细详情' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async findDetailById(@Param('id') id: string) {
    this.logger.log(`Finding fixed asset detail by id: ${id}`);

    const detail = await this.fixedAssetsService.findDetailById(id);

    return {
      code: 200,
      data: detail,
      message: '查询成功',
    };
  }

  @Patch('details/:id')
  @ApiOperation({ summary: '更新固定资产明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async updateDetail(
    @Param('id') id: string,
    @Body() updateDetailDto: UpdateFixedAssetDetailDto,
  ) {
    this.logger.log(`Updating fixed asset detail: ${id}`);

    await this.fixedAssetsService.updateDetail(id, updateDetailDto);

    return {
      code: 200,
      data: null,
      message: '更新成功',
    };
  }

  @Delete('details/:id')
  @ApiOperation({ summary: '删除固定资产明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async removeDetail(@Param('id') id: string) {
    this.logger.log(`Removing fixed asset detail: ${id}`);

    await this.fixedAssetsService.removeDetail(id);

    return {
      code: 200,
      data: null,
      message: '删除成功',
    };
  }

  @Get('export/excel')
  @ApiOperation({ summary: '导出固定资产明细为Excel' })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'detailIds',
    description: '选择的明细项目ID列表（逗号分隔）',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '导出Excel成功',
  })
  async exportToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Exporting fixed asset details to Excel`);

    try {
      // 从原始请求中获取查询参数，绕过验证
      const { startTime, endTime, detailIds } = req.query;

      // 构建导出参数
      const exportParams: any = {};

      if (startTime) {
        exportParams.startTime = startTime;
      }

      if (endTime) {
        exportParams.endTime = endTime;
      }

      if (detailIds) {
        // 处理detailIds参数（逗号分隔的字符串转换为数组）
        // 先解码URL编码的字符串，然后分割
        const decodedDetailIds = decodeURIComponent(detailIds);
        exportParams.detailIds = decodedDetailIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);
      }

      const excelBuffer =
        await this.fixedAssetsService.exportToExcel(exportParams);

      res.set({
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="fixed-assets-${new Date().toISOString().split('T')[0]}.xlsx"`,
        'Content-Length': excelBuffer.length,
      });

      res.status(HttpStatus.OK).send(excelBuffer);
    } catch (error) {
      this.logger.error('Failed to export Excel', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: '导出Excel失败',
      });
    }
  }
}
