import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import { FixedAsset } from './entities/fixed-asset.entity';
import { FixedAssetDetail } from './entities/fixed-asset-detail.entity';
import { CreateFixedAssetDetailDto } from './dto/create-fixed-asset-detail.dto';
import { UpdateFixedAssetDetailDto } from './dto/update-fixed-asset-detail.dto';
import { QueryFixedAssetDetailsDto } from './dto/query-fixed-asset-details.dto';

// 内部处理接口
interface ProcessedExportDto {
  startTime?: string;
  endTime?: string;
  detailIds?: string[];
}

@Injectable()
export class FixedAssetsService {
  private readonly logger = new Logger(FixedAssetsService.name);

  constructor(
    @InjectRepository(FixedAsset)
    private readonly fixedAssetRepository: Repository<FixedAsset>,
    @InjectRepository(FixedAssetDetail)
    private readonly fixedAssetDetailRepository: Repository<FixedAssetDetail>,
    private readonly dataSource: DataSource,
  ) {}

  // 获取或创建固定资产记录（系统只有一条记录）
  private async getOrCreateFixedAsset(): Promise<FixedAsset> {
    let fixedAsset = await this.fixedAssetRepository.findOne({
      where: { isDeleted: false },
    });

    if (!fixedAsset) {
      fixedAsset = this.fixedAssetRepository.create({
        totalAmount: 0,
        createDate: new Date().toISOString().split('T')[0],
      });
      await this.fixedAssetRepository.save(fixedAsset);
      this.logger.log('Created new fixed asset record');
    }

    return fixedAsset;
  }

  // 新增固定资产明细
  async createDetail(
    createDetailDto: CreateFixedAssetDetailDto,
  ): Promise<void> {
    const { amount, screenshot, remark, createDate } = createDetailDto;

    this.logger.log(`Creating fixed asset detail with amount: ${amount}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建固定资产记录
      const fixedAsset = await this.getOrCreateFixedAsset();

      // 如果没有提供 createDate，使用当前日期
      const finalCreateDate =
        createDate || new Date().toISOString().split('T')[0];

      // 创建明细记录
      const detail = queryRunner.manager.create(FixedAssetDetail, {
        fixedAssetId: fixedAsset.id,
        amount,
        screenshot,
        remark,
        createDate: finalCreateDate,
      });

      await queryRunner.manager.save(detail);

      // 更新总金额
      await queryRunner.manager.update(
        FixedAsset,
        { id: fixedAsset.id },
        {
          totalAmount: () => `"totalAmount" + ${amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(
        `Fixed asset detail created successfully with amount: ${amount}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询固定资产明细
  async findAllDetails(queryDto: QueryFixedAssetDetailsDto) {
    const {
      page = 1,
      pageSize = 10,
      startTime,
      endTime,
      search,
      sortBy = 'createDate',
      sortOrder = 'DESC',
    } = queryDto;

    this.logger.log(
      `Querying fixed asset details: page=${page}, pageSize=${pageSize}`,
    );

    // 获取固定资产总金额
    const fixedAsset = await this.getOrCreateFixedAsset();

    // 处理时间参数 - 由于createDate是字符串类型，需要特殊处理
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime) {
      // 对于日期字符串，我们需要确保包含开始日期的所有时间
      if (startTime.length === 10) {
        processedStartTime = startTime; // 保持原始日期格式用于字符串比较
      }
    }

    if (endTime) {
      // 对于结束日期，我们需要确保包含结束日期的所有时间
      if (endTime.length === 10) {
        processedEndTime = endTime; // 保持原始日期格式用于字符串比较
      }
    }

    // 构建查询条件
    const queryBuilder = this.fixedAssetDetailRepository
      .createQueryBuilder('detail')
      .where('detail.isDeleted = false');

    // 添加时间范围过滤 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (processedStartTime) {
      // 将字符串日期转换为日期类型进行比较，确保包含开始日期的所有数据
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: processedStartTime,
        },
      );
    }

    if (processedEndTime) {
      // 将字符串日期转换为日期类型进行比较，确保包含结束日期的所有数据
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: processedEndTime,
        },
      );
    }

    // 添加搜索过滤
    if (search) {
      queryBuilder.andWhere('detail.remark ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // 排序
    queryBuilder.orderBy(`detail.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const details = await queryBuilder.getMany();

      // 计算查询结果的总金额
      const queryResultTotalAmount = details.reduce(
        (sum, detail) => sum + detail.amount,
        0,
      );

      return {
        details,
        total: details.length,
        page: 0,
        pageSize: 0,
        totalAmount: queryResultTotalAmount,
        systemTotalAmount: fixedAsset.totalAmount,
        startTime: processedStartTime || null,
        endTime: processedEndTime || null,
      };
    }

    // 正常分页查询
    const [details, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    // 计算所有查询条件下的总金额（不分页）
    const allMatchingDetails = await queryBuilder
      .skip(0)
      .take(undefined)
      .getMany();

    const queryResultTotalAmount = allMatchingDetails.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    return {
      details,
      total,
      page,
      pageSize,
      totalAmount: queryResultTotalAmount,
      systemTotalAmount: fixedAsset.totalAmount,
      startTime: processedStartTime || null,
      endTime: processedEndTime || null,
    };
  }

  // 获取固定资产明细详情
  async findDetailById(id: string): Promise<FixedAssetDetail> {
    this.logger.log(`Finding fixed asset detail by id: ${id}`);

    const detail = await this.fixedAssetDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException('固定资产明细不存在');
    }

    return detail;
  }

  // 更新固定资产明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateFixedAssetDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating fixed asset detail: ${id}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找现有明细
      const existingDetail = await queryRunner.manager.findOne(
        FixedAssetDetail,
        {
          where: { id, isDeleted: false },
        },
      );

      if (!existingDetail) {
        throw new NotFoundException('固定资产明细不存在');
      }

      const oldAmount = existingDetail.amount;
      const newAmount = updateDetailDto.amount || oldAmount;
      const amountDifference = newAmount - oldAmount;

      // 更新明细
      await queryRunner.manager.update(
        FixedAssetDetail,
        { id },
        updateDetailDto,
      );

      // 如果金额有变化，更新总金额
      if (amountDifference !== 0) {
        const fixedAsset = await this.getOrCreateFixedAsset();
        await queryRunner.manager.update(
          FixedAsset,
          { id: fixedAsset.id },
          {
            totalAmount: () => `"totalAmount" + ${amountDifference}`,
          },
        );
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Fixed asset detail updated successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除固定资产明细（软删除）
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing fixed asset detail: ${id}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找现有明细
      const existingDetail = await queryRunner.manager.findOne(
        FixedAssetDetail,
        {
          where: { id, isDeleted: false },
        },
      );

      if (!existingDetail) {
        throw new NotFoundException('固定资产明细不存在');
      }

      // 软删除明细
      await queryRunner.manager.update(
        FixedAssetDetail,
        { id },
        {
          isDeleted: true,
        },
      );

      // 更新总金额（减去删除的金额）
      const fixedAsset = await this.getOrCreateFixedAsset();
      await queryRunner.manager.update(
        FixedAsset,
        { id: fixedAsset.id },
        {
          totalAmount: () => `"totalAmount" - ${existingDetail.amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Fixed asset detail removed successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出Excel
  async exportToExcel(exportDto?: ProcessedExportDto): Promise<Buffer> {
    this.logger.log('Exporting fixed asset details to Excel');

    // 验证：如果没有提供明细ID，必须提供时间范围
    if (!exportDto?.detailIds || exportDto.detailIds.length === 0) {
      if (!exportDto?.startTime || !exportDto?.endTime) {
        throw new BadRequestException(
          '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
        );
      }
    }

    const fixedAsset = await this.getOrCreateFixedAsset();
    let queryBuilder = this.fixedAssetDetailRepository
      .createQueryBuilder('detail')
      .where('detail.isDeleted = false')
      .orderBy('detail.createDate', 'DESC');

    // 根据导出条件过滤
    if (exportDto) {
      const { startTime, endTime, detailIds } = exportDto;

      // 时间范围过滤 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
      if (startTime) {
        queryBuilder.andWhere(
          'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
          {
            startTime: startTime,
          },
        );
      }

      if (endTime) {
        queryBuilder.andWhere(
          'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
          {
            endTime: endTime,
          },
        );
      }

      // 特定明细ID过滤
      if (detailIds && detailIds.length > 0) {
        queryBuilder.andWhere('detail.id IN (:...detailIds)', { detailIds });
      }
    }

    const details = await queryBuilder.getMany();

    // 计算选中明细的总金额
    const selectedTotalAmount = details.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    // 生成Excel（支持图片插入）
    try {
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('固定资产明细');

      // 设置列宽
      worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '金额', key: 'amount', width: 15 },
        { header: '创建时间', key: 'createdAt', width: 20 },
        { header: '截图', key: 'screenshot', width: 30 },
        { header: '备注', key: 'remark', width: 30 },
      ];

      // 添加标题
      worksheet.mergeCells('A1:E1');
      const titleCell = worksheet.getCell('A1');
      titleCell.value = '固定资产报表(包含软件维护费用以及长期固定设备费用等)';
      titleCell.font = { bold: true, size: 16 };
      titleCell.alignment = { horizontal: 'center' };

      // 添加概况信息
      let currentRow = 3;
      worksheet.getCell(`A${currentRow}`).value = '资产概况';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '固定资产总金额';
      worksheet.getCell(`B${currentRow}`).value =
        `¥${fixedAsset.totalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出明细总金额';
      worksheet.getCell(`B${currentRow}`).value =
        `¥${selectedTotalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出明细数量';
      worksheet.getCell(`B${currentRow}`).value = `${details.length}条`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出时间';
      worksheet.getCell(`B${currentRow}`).value = new Date().toLocaleString(
        'zh-CN',
      );
      currentRow += 2;

      // 添加明细记录标题
      worksheet.getCell(`A${currentRow}`).value = '明细记录';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      // 添加表头
      const headerRow = worksheet.getRow(currentRow);
      headerRow.values = ['序号', '金额', '创建时间', '截图', '备注'];
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
      currentRow++;

      // 添加明细数据和图片
      for (let i = 0; i < details.length; i++) {
        const detail = details[i];
        const row = worksheet.getRow(currentRow);

        // 设置行高以容纳图片
        row.height = 120;

        // 添加基本数据
        row.values = [
          i + 1,
          `¥${detail.amount.toFixed(2)}`,
          detail.createDate,
          '', // 截图列留空，用于插入图片
          detail.remark || '',
        ];

        // 下载并插入图片
        try {
          if (detail.screenshot) {
            this.logger.log(`Downloading image: ${detail.screenshot}`);

            const imageResponse = await axios.get(detail.screenshot, {
              responseType: 'arraybuffer',
              timeout: 10000,
            });

            const imageBuffer = Buffer.from(imageResponse.data);

            // 添加图片到工作簿
            const imageId = workbook.addImage({
              buffer: imageBuffer,
              extension: 'jpeg',
            });

            // 在截图列插入图片
            worksheet.addImage(imageId, {
              tl: { col: 3, row: currentRow - 1 }, // 从截图列开始
              ext: { width: 200, height: 100 }, // 图片大小
            });

            this.logger.log(`Image inserted successfully for detail ${i + 1}`);
          }
        } catch (imageError) {
          this.logger.warn(
            `Failed to load image for detail ${i + 1}: ${detail.screenshot}`,
            imageError.message,
          );
          // 如果图片加载失败，在截图列显示链接
          row.getCell(4).value = detail.screenshot;
        }

        currentRow++;
      }

      // 设置边框
      const borderStyle = {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const },
      };

      // 为表格添加边框
      const tableStartRow = currentRow - details.length - 1;
      const tableEndRow = currentRow - 1;
      for (let row = tableStartRow; row <= tableEndRow; row++) {
        for (let col = 1; col <= 5; col++) {
          worksheet.getCell(row, col).border = borderStyle;
        }
      }

      // 生成Excel缓冲区
      const excelBuffer = await workbook.xlsx.writeBuffer();
      return Buffer.from(excelBuffer);
    } catch (error) {
      this.logger.error('Excel generation failed', error);
      throw new BadRequestException('Excel生成失败');
    }
  }
}
