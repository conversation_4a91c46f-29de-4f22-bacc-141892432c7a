import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { FixedAssetDetail } from './fixed-asset-detail.entity';

@Entity('fixed_assets')
export class FixedAsset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '固定资产总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalAmount: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '创建日期（字符串格式，必填）',
    name: 'createdAt',
  })
  createDate: string;

  // 关联固定资产明细
  @OneToMany(() => FixedAssetDetail, (detail) => detail.fixedAsset)
  details: FixedAssetDetail[];
}
