import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  Join<PERSON>olumn,
} from 'typeorm';
import { FixedAsset } from './fixed-asset.entity';

@Entity('fixed_asset_details')
export class FixedAssetDetail {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', comment: '固定资产ID' })
  fixedAssetId: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '明细金额（保留两位小数）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 500,
    comment: '截图URL（必填）',
  })
  screenshot: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注（可选）',
  })
  remark: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '创建日期（字符串格式，必填）',
    name: 'createdAt',
  })
  createDate: string;

  // 关联固定资产
  @ManyToOne(() => FixedAsset, (asset) => asset.details)
  @JoinColumn({ name: 'fixedAssetId' })
  fixedAsset: FixedAsset;
}
