import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  <PERSON>s,
  Req,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { RdCostsService } from './rd-costs.service';
import { CreateRdCostDetailDto } from './dto/create-rd-cost-detail.dto';
import { UpdateRdCostDetailDto } from './dto/update-rd-cost-detail.dto';
import { QueryRdCostDetailsDto } from './dto/query-rd-cost-details.dto';

@ApiTags('rd-costs')
@Controller('rd-costs')
export class RdCostsController {
  private readonly logger = new Logger(RdCostsController.name);

  constructor(private readonly rdCostsService: RdCostsService) {}

  @Post('details')
  @ApiOperation({ summary: '新增研发成本明细' })
  @ApiResponse({
    status: 200,
    description: '研发成本明细创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async createDetail(@Body() createDetailDto: CreateRdCostDetailDto) {
    this.logger.log(
      `Creating rd cost detail with amount: ${createDetailDto.amount}`,
    );

    await this.rdCostsService.createDetail(createDetailDto);

    return {
      code: 200,
      data: null,
      message: '研发成本明细创建成功',
    };
  }

  @Get('details')
  @ApiOperation({ summary: '分页查询研发成本明细' })
  @ApiQuery({
    name: 'page',
    description: '页码',
    example: 1,
    required: false,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量',
    example: 10,
    required: false,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词（备注）',
    example: '样衣',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: '排序字段',
    enum: ['createDate', 'amount'],
    example: 'createDate',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: '排序方向',
    enum: ['ASC', 'DESC'],
    example: 'DESC',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllDetails(@Query() queryDto: QueryRdCostDetailsDto) {
    this.logger.log(
      `Querying rd cost details with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.rdCostsService.findAllDetails(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('details/:id')
  @ApiOperation({ summary: '获取研发成本明细详情' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async findDetailById(@Param('id') id: string) {
    this.logger.log(`Finding rd cost detail by id: ${id}`);

    const result = await this.rdCostsService.findDetailById(id);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Patch('details/:id')
  @ApiOperation({ summary: '更新研发成本明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '研发成本明细更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async updateDetail(
    @Param('id') id: string,
    @Body() updateDetailDto: UpdateRdCostDetailDto,
  ) {
    this.logger.log(`Updating rd cost detail: ${id}`);

    await this.rdCostsService.updateDetail(id, updateDetailDto);

    return {
      code: 200,
      data: null,
      message: '研发成本明细更新成功',
    };
  }

  @Delete('details/:id')
  @ApiOperation({ summary: '删除研发成本明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '研发成本明细删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async removeDetail(@Param('id') id: string) {
    this.logger.log(`Removing rd cost detail: ${id}`);

    await this.rdCostsService.removeDetail(id);

    return {
      code: 200,
      data: null,
      message: '研发成本明细删除成功',
    };
  }

  @Get('export/excel')
  @ApiOperation({ summary: '导出研发成本明细Excel' })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'detailIds',
    description: '选择的明细项目ID列表（逗号分隔）',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  async exportToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log('Exporting rd cost details to Excel');

    try {
      // 从查询参数中获取导出参数
      const { startTime, endTime, detailIds } = req.query;

      // 构建导出参数
      const exportParams: any = {};

      if (startTime) {
        exportParams.startTime = startTime;
      }

      if (endTime) {
        exportParams.endTime = endTime;
      }

      if (detailIds) {
        // 处理detailIds参数（逗号分隔的字符串转换为数组）
        const decodedDetailIds = decodeURIComponent(detailIds);
        exportParams.detailIds = decodedDetailIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);
      }

      const excelBuffer = await this.rdCostsService.exportToExcel(exportParams);

      // 设置响应头
      const filename = `研发成本明细_${new Date().toISOString().split('T')[0]}.xlsx`;
      res.set({
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
        'Content-Length': excelBuffer.length,
      });

      res.status(HttpStatus.OK).send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }
}
