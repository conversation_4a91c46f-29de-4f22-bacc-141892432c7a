import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { RdCostDetail } from './rd-cost-detail.entity';

@Entity('rd_costs')
export class RdCost {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '研发成本总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalAmount: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '创建日期（字符串格式，必填）',
    name: 'createdAt',
  })
  createDate: string;

  // 关联研发成本明细
  @OneToMany(() => RdCostDetail, (detail) => detail.rdCost)
  details: RdCostDetail[];
}
