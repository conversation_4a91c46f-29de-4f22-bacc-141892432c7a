import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RdCostsService } from './rd-costs.service';
import { RdCostsController } from './rd-costs.controller';
import { RdCost } from './entities/rd-cost.entity';
import { RdCostDetail } from './entities/rd-cost-detail.entity';

@Module({
  imports: [TypeOrmModule.forFeature([RdCost, RdCostDetail])],
  controllers: [RdCostsController],
  providers: [RdCostsService],
  exports: [RdCostsService],
})
export class RdCostsModule {}
