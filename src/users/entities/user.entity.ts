import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

// 权限路由接口定义
export interface RoutePermission {
  [routePath: string]: {
    create?: boolean; // 新增
    read?: boolean; // 查看
    update?: boolean; // 修改
    delete?: boolean; // 删除
    export?: boolean; // 导出
    import?: boolean; // 导入
  };
}

@Entity('users')
export class User {
  @PrimaryColumn({ type: 'varchar', length: 50 })
  @ApiProperty({ description: '用户编码，作为主键', example: 'husky' })
  code: string;

  @Column({ type: 'varchar', length: 100 })
  @ApiProperty({ description: '用户昵称（必填）', example: '胡斯阔' })
  nickname: string;

  @Column()
  @Exclude()
  @ApiProperty({ description: '用户密码（加密存储）' })
  password: string;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({
    description:
      '权限路由配置，JSON格式存储各路由的增删改查导出导入权限。不传则拥有全部权限',
    example: {
      '/users': {
        create: true,
        read: true,
        update: true,
        delete: true,
        export: true,
        import: true,
      },
      '/products': {
        create: false,
        read: true,
        update: false,
        delete: false,
        export: true,
        import: false,
      },
    },
    required: false,
  })
  routePermissions: RoutePermission | null;

  @Column({ default: false })
  @ApiProperty({ description: '是否为超级管理员（全局唯一）', example: false })
  isSuperAdmin: boolean;

  @Column({ default: false })
  @ApiProperty({
    description: '是否为公司管理员（每个公司可以有多个）',
    example: false,
  })
  isCompanyAdmin: boolean;

  // 公司关联（多对一）
  @ManyToOne('Company', { nullable: true })
  @JoinColumn({ name: 'companyCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '所属公司信息',
    required: false,
  })
  company: any;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @ApiProperty({
    description: '所属公司编码',
    example: 'COMP001',
    required: false,
  })
  companyCode: string | null;

  @Column({ default: true })
  @ApiProperty({
    description: '是否激活（激活状态的用户可以被搜索）',
    example: true,
  })
  isActive: boolean;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 软删除字段
  @Column({ default: false })
  @ApiProperty({ description: '是否已删除（软删除标记）' })
  isDeleted: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  @ApiProperty({ description: '删除时间', required: false })
  deletedAt: Date | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @ApiProperty({
    description: '开户银行账户名',
    example: '张三',
    required: false,
  })
  bankAccountName: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @ApiProperty({
    description: '开户银行卡号',
    example: '6222021234567890123',
    required: false,
  })
  bankAccountNumber: string | null;
}
