# 用户和公司关系管理系统

## 设计概述

本系统重新设计了用户和公司的关系模型，采用更清晰的层级管理结构，支持多级管理员权限。

## 关系设计

### 数据库关系
- **User → Company**: 多对一关系（一个用户隶属于一个公司）
- **Company → User**: 一对多关系（一个公司可以有多个用户）
- **Company.manager**: 公司负责人（一对一关系）

### 管理员层级
1. **超级管理员 (Super Admin)**
   - 全局唯一（系统中只能有一个）
   - 拥有所有权限，不受公司限制
   - 可以管理所有公司和用户

2. **公司管理员 (Company Admin)**
   - 每个公司可以有多个
   - 只能管理本公司的数据和用户
   - 可以设置同公司的其他用户为公司管理员

3. **普通用户 (Regular User)**
   - 隶属于某个公司的普通员工
   - 只能访问自己的数据

## 数据库结构

### User表新增字段
```sql
-- 是否为公司管理员
is_company_admin BOOLEAN DEFAULT FALSE

-- 所属公司编码
company_code VARCHAR(50) NULL

-- 外键约束
FOREIGN KEY (company_code) REFERENCES companies(code)
```

### Company表变更
- 删除了多对多关系表 `company_employees`
- 保留 `manager_code` 字段作为公司负责人

## API接口

### 用户管理接口
- `POST /users` - 创建用户（支持设置公司管理员）
- `GET /users` - 查询用户列表（包含公司信息）
- `GET /users/:code` - 获取用户详情
- `PATCH /users/:code` - 更新用户信息
- `DELETE /users/:code` - 删除用户

### 管理员管理接口
- `POST /users/:code/set-super-admin` - 设置超级管理员
- `POST /users/:code/remove-super-admin` - 取消超级管理员
- `POST /users/:code/set-company-admin` - 设置公司管理员
- `POST /users/:code/remove-company-admin` - 取消公司管理员

### 公司管理接口
- `GET /companies/:code` - 获取公司详情（包含员工列表）

## 权限控制

### 权限守卫
1. **SuperAdminGuard**: 只允许超级管理员
2. **CompanyAdminGuard**: 允许超级管理员或公司管理员
3. **FlexibleAdminGuard**: 支持多种权限组合

### 权限装饰器
```typescript
// 只允许超级管理员
@SuperAdminOnly()

// 允许超级管理员或公司管理员
@AdminOnly()

// 允许超级管理员、公司管理员或同公司用户
@CompanyMembersOnly()

// 自定义权限组合
@RequireAdminLevels(AdminLevel.SUPER_ADMIN, AdminLevel.COMPANY_ADMIN)
```

## 业务逻辑

### 超级管理员管理
- 系统中只能有一个超级管理员
- 设置新的超级管理员时，会自动取消当前超级管理员的权限
- 超级管理员不能取消自己的权限

### 公司管理员管理
- 每个公司可以有多个管理员
- 超级管理员可以设置任何用户为公司管理员
- 公司管理员只能管理同公司的用户

### 用户公司关联
- 用户创建时可以指定所属公司
- 用户可以转移到其他公司
- 用户离职时设置 companyCode 为 null

## 使用示例

### 创建公司管理员
```typescript
// 创建用户并设置为公司管理员
POST /users
{
  "code": "admin001",
  "nickname": "张三",
  "password": "123456",
  "isCompanyAdmin": true,
  "companyCode": "COMP001"
}

// 或者先创建用户，再设置为管理员
POST /users/admin001/set-company-admin?companyCode=COMP001
```

### 设置超级管理员
```typescript
// 设置超级管理员（会自动取消当前超级管理员）
POST /users/admin001/set-super-admin
```

### 查询公司员工
```typescript
// 获取公司详情，包含员工列表
GET /companies/COMP001

// 返回数据包含员工的管理员状态
{
  "employees": [
    {
      "code": "user001",
      "name": "张三",
      "isCompanyAdmin": true
    },
    {
      "code": "user002", 
      "name": "李四",
      "isCompanyAdmin": false
    }
  ]
}
```

## 数据迁移

执行 `src/migrations/update-user-company-relationship.sql` 文件来更新数据库结构：

```sql
-- 添加新字段
ALTER TABLE users ADD COLUMN is_company_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN company_code VARCHAR(50) NULL;

-- 删除旧的关联表
DROP TABLE IF EXISTS company_employees;

-- 创建索引
CREATE INDEX idx_users_company_code ON users(company_code);
CREATE INDEX idx_users_is_company_admin ON users(is_company_admin);
```

## 注意事项

1. **数据一致性**: 确保用户的 companyCode 对应的公司存在
2. **权限验证**: 所有管理员操作都需要验证操作者权限
3. **超级管理员唯一性**: 通过应用层逻辑保证全局唯一
4. **公司负责人**: Company.manager 字段独立于管理员系统，可以是普通用户
5. **软删除**: 用户删除时不影响公司数据，但需要处理管理员权限

## 优势

1. **清晰的层级结构**: 超级管理员 → 公司管理员 → 普通用户
2. **灵活的权限控制**: 支持多种权限组合和验证方式
3. **简化的数据模型**: 避免了复杂的多对多关系
4. **易于扩展**: 可以轻松添加新的管理员级别或权限类型
5. **数据隔离**: 公司间数据天然隔离，安全性更高
