import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUsersDto } from './dto/query-users.dto';
import { UserResponseDto, UserListResponseDto } from './dto/user-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';
import { SuperAdminGuard } from '@/auth/guards/super-admin.guard';

@ApiTags('users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  private readonly logger = new Logger(UsersController.name);

  constructor(private readonly usersService: UsersService) {}

  @Post()
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '创建用户（仅超级管理员）' })
  @ApiResponse({
    status: 200,
    description: '用户创建成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '用户创建成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '用户编码已存在或权限不足',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async create(@Request() req: any, @Body() createUserDto: CreateUserDto) {
    this.logger.log(
      `Creating user ${createUserDto.code} by operator ${req.user.code}`,
    );

    await this.usersService.create(createUserDto, req.user.code);

    return {
      code: 200,
      data: null,
      message: '用户创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取用户列表（支持模糊搜索和激活状态筛选）' })
  @ApiResponse({
    status: 200,
    description: '获取用户列表成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '获取用户列表成功' },
        data: {
          type: 'object',
          properties: {
            users: {
              type: 'array',
              items: { $ref: '#/components/schemas/UserResponseDto' },
            },
            total: { type: 'number', example: 100 },
            page: { type: 'number', example: 1 },
            pageSize: { type: 'number', example: 10 },
            totalPages: { type: 'number', example: 10 },
          },
        },
      },
    },
  })
  async findAll(@Query() queryDto: QueryUsersDto) {
    this.logger.log(`Querying users with params: ${JSON.stringify(queryDto)}`);

    const result = await this.usersService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取用户列表成功',
    };
  }

  @Get(':code')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '根据用户编码获取用户详情' })
  @ApiParam({
    name: 'code',
    description: '用户编码',
    example: 'husky',
  })
  @ApiResponse({
    status: 200,
    description: '获取用户详情成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '获取用户详情成功' },
        data: { $ref: '#/components/schemas/UserResponseDto' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async findOne(@Param('code') code: string) {
    this.logger.log(`Getting user details for code: ${code}`);

    const user = await this.usersService.findOne(code);

    // 移除密码字段
    const { password, ...userResponse } = user;

    return {
      code: 200,
      data: userResponse,
      message: '获取用户详情成功',
    };
  }

  @Patch(':code')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '更新用户信息' })
  @ApiParam({
    name: 'code',
    description: '用户编码',
    example: 'husky',
  })
  @ApiResponse({
    status: 200,
    description: '用户更新成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '用户更新成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async update(
    @Request() req: any,
    @Param('code') code: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    this.logger.log(`Updating user ${code} by operator ${req.user.code}`);

    await this.usersService.update(code, updateUserDto, req.user.code);

    return {
      code: 200,
      data: null,
      message: '用户更新成功',
    };
  }

  @Delete(':code')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '删除用户（软删除）' })
  @ApiParam({
    name: 'code',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '用户删除成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '用户删除成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  @ApiResponse({
    status: 400,
    description: '不能删除自己',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async remove(@Request() req: any, @Param('code') code: string) {
    this.logger.log(`Deleting user ${code} by operator ${req.user.code}`);

    await this.usersService.remove(code, req.user.code);

    return {
      code: 200,
      data: null,
      message: '用户删除成功',
    };
  }

  // 管理员管理相关接口

  @Post(':code/set-super-admin')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '设置超级管理员（全局唯一）' })
  @ApiParam({
    name: 'code',
    description: '目标用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '超级管理员设置成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '超级管理员设置成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '权限不足或用户不存在',
  })
  async setSuperAdmin(@Request() req: any, @Param('code') code: string) {
    this.logger.log(`Setting super admin ${code} by operator ${req.user.code}`);

    await this.usersService.setSuperAdmin(code, req.user.code);

    return {
      code: 200,
      data: null,
      message: '超级管理员设置成功',
    };
  }

  @Post(':code/remove-super-admin')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '取消超级管理员权限' })
  @ApiParam({
    name: 'code',
    description: '目标用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '超级管理员权限取消成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '超级管理员权限取消成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '权限不足或不能取消自己的权限',
  })
  async removeSuperAdmin(@Request() req: any, @Param('code') code: string) {
    this.logger.log(
      `Removing super admin ${code} by operator ${req.user.code}`,
    );

    await this.usersService.removeSuperAdmin(code, req.user.code);

    return {
      code: 200,
      data: null,
      message: '超级管理员权限取消成功',
    };
  }

  @Post(':code/set-company-admin')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '设置公司管理员' })
  @ApiParam({
    name: 'code',
    description: '目标用户编码',
    example: 'user001',
  })
  @ApiQuery({
    name: 'companyCode',
    description: '公司编码',
    example: 'COMP001',
  })
  @ApiResponse({
    status: 200,
    description: '公司管理员设置成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '公司管理员设置成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '权限不足或用户不存在',
  })
  async setCompanyAdmin(
    @Request() req: any,
    @Param('code') code: string,
    @Query('companyCode') companyCode: string,
  ) {
    this.logger.log(
      `Setting company admin ${code} for company ${companyCode} by operator ${req.user.code}`,
    );

    await this.usersService.setCompanyAdmin(code, companyCode, req.user.code);

    return {
      code: 200,
      data: null,
      message: '公司管理员设置成功',
    };
  }

  @Post(':code/remove-company-admin')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '取消公司管理员权限' })
  @ApiParam({
    name: 'code',
    description: '目标用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '公司管理员权限取消成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '公司管理员权限取消成功' },
        data: { type: 'null' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '权限不足或用户不存在',
  })
  async removeCompanyAdmin(@Request() req: any, @Param('code') code: string) {
    this.logger.log(
      `Removing company admin ${code} by operator ${req.user.code}`,
    );

    await this.usersService.removeCompanyAdmin(code, req.user.code);

    return {
      code: 200,
      data: null,
      message: '公司管理员权限取消成功',
    };
  }
}
