import {
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  Min,
  IsBoolean,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';

export class QueryUsersDto {
  @ApiProperty({
    description: '页码（必填，0表示获取所有数据）',
    example: 1,
    required: true,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(0, { message: '页码必须大于等于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填，0表示获取所有数据）',
    example: 10,
    required: true,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(0, { message: '每页数量必须大于等于0' })
  pageSize: number;

  @ApiProperty({
    description: '搜索关键词（模糊搜索用户编码和昵称）',
    example: 'husky',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '是否包含未激活的用户（默认false，只查询激活用户）',
    example: false,
    required: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return false;
  })
  @IsBoolean()
  includeInactive?: boolean = false;
}
