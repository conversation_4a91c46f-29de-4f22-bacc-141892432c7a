import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsObject,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RoutePermission } from '../entities/user.entity';

export class CreateUserDto {
  @ApiProperty({
    description: '用户编码（主键）',
    example: 'user001',
  })
  @IsString()
  @IsNotEmpty({ message: '用户编码不能为空' })
  code: string;

  @ApiProperty({
    description: '用户昵称（必填）',
    example: '张三',
  })
  @IsString()
  @IsNotEmpty({ message: '用户昵称不能为空' })
  nickname: string;

  @ApiProperty({
    description: '用户密码',
    example: '123456',
  })
  @IsString()
  @IsNotEmpty({ message: '密码不能为空' })
  password: string;

  @ApiProperty({
    description: '权限路由配置，不传则拥有全部权限',
    example: {
      '/users': {
        create: true,
        read: true,
        update: true,
        delete: true,
        export: true,
        import: true,
      },
      '/products': {
        create: false,
        read: true,
        update: false,
        delete: false,
        export: true,
        import: false,
      },
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  routePermissions?: RoutePermission;

  @ApiProperty({
    description: '是否为公司管理员',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isCompanyAdmin?: boolean;

  @ApiProperty({
    description: '所属公司编码',
    example: 'COMP001',
    required: false,
  })
  @IsOptional()
  @IsString()
  companyCode?: string;

  @ApiProperty({
    description: '是否激活',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: '开户银行账户名',
    example: '张三',
    required: false,
  })
  @IsOptional()
  @IsString()
  bankAccountName?: string;

  @ApiProperty({
    description: '开户银行卡号',
    example: '6222021234567890123',
    required: false,
  })
  @IsOptional()
  @IsString()
  bankAccountNumber?: string;
}
