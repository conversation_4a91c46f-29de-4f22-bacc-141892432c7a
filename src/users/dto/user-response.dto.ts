import { ApiProperty } from '@nestjs/swagger';
import { RoutePermission } from '../entities/user.entity';

export class UserResponseDto {
  @ApiProperty({ description: '用户编码', example: 'husky' })
  code: string;

  @ApiProperty({ description: '用户昵称', example: '胡斯阔' })
  nickname: string;

  @ApiProperty({
    description: '权限路由配置',
    example: {
      '/users': {
        create: true,
        read: true,
        update: true,
        delete: true,
        export: true,
        import: true,
      },
    },
    required: false,
  })
  routePermissions: RoutePermission | null;

  @ApiProperty({ description: '是否为超级管理员', example: false })
  isSuperAdmin: boolean;

  @ApiProperty({ description: '是否为公司管理员', example: false })
  isCompanyAdmin: boolean;

  @ApiProperty({
    description: '所属公司编码',
    example: 'COMP001',
    required: false,
  })
  companyCode: string | null;

  @ApiProperty({
    description: '所属公司信息',
    required: false,
  })
  company: any;

  @ApiProperty({ description: '是否激活', example: true })
  isActive: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({
    description: '开户银行账户名',
    example: '张三',
    required: false,
  })
  bankAccountName: string | null;

  @ApiProperty({
    description: '开户银行卡号',
    example: '6222021234567890123',
    required: false,
  })
  bankAccountNumber: string | null;
}

export class UserListResponseDto {
  @ApiProperty({ description: '用户列表', type: [UserResponseDto] })
  users: UserResponseDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}
