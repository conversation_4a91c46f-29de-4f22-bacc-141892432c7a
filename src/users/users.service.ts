import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUsersDto } from './dto/query-users.dto';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  /**
   * 创建用户
   */
  async create(
    createUserDto: CreateUserDto,
    operatorCode: string,
  ): Promise<User> {
    this.logger.log(`Creating user with code: ${createUserDto.code}`);

    // 检查操作者是否为超级管理员
    const operator = await this.findByCode(operatorCode);
    if (!operator || !operator.isSuperAdmin) {
      throw new BadRequestException('只有超级管理员可以创建用户');
    }

    // 检查用户编码是否已存在
    const existingUser = await this.usersRepository.findOne({
      where: { code: createUserDto.code, isDeleted: false },
    });

    if (existingUser) {
      throw new BadRequestException('用户编码已存在');
    }

    // 如果要设置为公司管理员，验证公司是否存在
    if (createUserDto.isCompanyAdmin && createUserDto.companyCode) {
      // 这里可以添加公司存在性验证
      // const company = await this.companyService.findByCode(createUserDto.companyCode);
      // if (!company) {
      //   throw new BadRequestException('指定的公司不存在');
      // }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // 创建用户
    const user = this.usersRepository.create({
      ...createUserDto,
      password: hashedPassword,
      isActive: createUserDto.isActive ?? true,
      isSuperAdmin: false, // 新创建的用户默认不是超级管理员
      isCompanyAdmin: createUserDto.isCompanyAdmin ?? false,
      companyCode: createUserDto.companyCode || null,
    });

    return await this.usersRepository.save(user);
  }

  /**
   * 分页查询用户列表（支持模糊搜索）
   */
  async findAll(queryDto: QueryUsersDto) {
    this.logger.log(`Querying users with params: ${JSON.stringify(queryDto)}`);

    const { page, pageSize, search, includeInactive = false } = queryDto;

    // page和pageSize是必须参数
    if (page === undefined || pageSize === undefined) {
      return {
        users: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // 处理分页参数：page=0&pageSize=0表示获取所有数据
    const isGetAll = page === 0 && pageSize === 0;
    const finalPage = isGetAll ? 1 : page;
    const finalPageSize = isGetAll ? undefined : pageSize;
    const skip = isGetAll ? 0 : (page - 1) * pageSize;

    const queryBuilder = this.usersRepository
      .createQueryBuilder('user')
      .where('user.isDeleted = :isDeleted', { isDeleted: false });

    // 根据参数决定是否包含未激活用户
    if (!includeInactive) {
      queryBuilder.andWhere('user.isActive = :isActive', { isActive: true });
    }

    // 模糊搜索用户编码和昵称
    if (search) {
      queryBuilder.andWhere(
        '(user.code ILIKE :search OR user.nickname ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const users = await queryBuilder
      .leftJoinAndSelect('user.company', 'company')
      .select([
        'user.code',
        'user.nickname',
        'user.routePermissions',
        'user.isSuperAdmin',
        'user.isCompanyAdmin',
        'user.companyCode',
        'user.isActive',
        'user.createdAt',
        'user.updatedAt',
        'user.bankAccountName',
        'user.bankAccountNumber',
        'company.code',
        'company.name',
      ])
      .orderBy('user.createdAt', 'DESC')
      .skip(skip)
      .take(finalPageSize)
      .getMany();

    return {
      users,
      total,
      page: isGetAll ? 0 : finalPage,
      pageSize: isGetAll ? 0 : finalPageSize!,
      totalPages: isGetAll ? 1 : Math.ceil(total / finalPageSize!),
    };
  }

  /**
   * 根据用户编码查找用户
   */
  async findByCode(code: string): Promise<User | null> {
    return await this.usersRepository.findOne({
      where: { code, isDeleted: false },
    });
  }

  /**
   * 根据用户编码获取用户详情
   */
  async findOne(code: string): Promise<User> {
    const user = await this.findByCode(code);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }
    return user;
  }

  /**
   * 更新用户
   */
  async update(
    code: string,
    updateUserDto: UpdateUserDto,
    operatorCode: string,
  ): Promise<User> {
    this.logger.log(`Updating user ${code} by operator ${operatorCode}`);

    // 检查操作者是否为超级管理员
    const operator = await this.findByCode(operatorCode);
    if (!operator || !operator.isSuperAdmin) {
      throw new BadRequestException('只有超级管理员可以更新用户');
    }

    // 查找要更新的用户
    const user = await this.findOne(code);

    // 如果要更新密码，需要加密
    if (updateUserDto.password) {
      updateUserDto.password = await bcrypt.hash(updateUserDto.password, 10);
    }

    // 更新用户信息
    Object.assign(user, updateUserDto);
    return await this.usersRepository.save(user);
  }

  /**
   * 软删除用户
   */
  async remove(code: string, operatorCode: string): Promise<void> {
    this.logger.log(`Soft deleting user ${code} by operator ${operatorCode}`);

    // 检查操作者是否为超级管理员
    const operator = await this.findByCode(operatorCode);
    if (!operator || !operator.isSuperAdmin) {
      throw new BadRequestException('只有超级管理员可以删除用户');
    }

    // 不能删除自己
    if (code === operatorCode) {
      throw new BadRequestException('不能删除自己');
    }

    // 查找要删除的用户
    const user = await this.findOne(code);

    // 软删除
    user.isDeleted = true;
    user.deletedAt = new Date();
    await this.usersRepository.save(user);
  }

  /**
   * 验证密码
   */
  async verifyPassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  /**
   * 检查用户是否为超级管理员
   */
  async isSuperAdmin(code: string): Promise<boolean> {
    const user = await this.findByCode(code);
    return user ? user.isSuperAdmin : false;
  }

  /**
   * 检查用户是否为公司管理员
   */
  async isCompanyAdmin(code: string): Promise<boolean> {
    const user = await this.findByCode(code);
    return user ? user.isCompanyAdmin : false;
  }

  /**
   * 设置超级管理员（只能有一个）
   */
  async setSuperAdmin(targetCode: string, operatorCode: string): Promise<void> {
    this.logger.log(
      `Setting super admin: ${targetCode} by operator: ${operatorCode}`,
    );

    // 检查操作者是否为超级管理员
    const operator = await this.findByCode(operatorCode);
    if (!operator || !operator.isSuperAdmin) {
      throw new BadRequestException('只有超级管理员可以设置超级管理员');
    }

    // 检查目标用户是否存在
    const targetUser = await this.findOne(targetCode);

    // 如果目标用户已经是超级管理员，直接返回
    if (targetUser.isSuperAdmin) {
      return;
    }

    // 先取消当前所有超级管理员的权限
    await this.usersRepository.update(
      { isSuperAdmin: true, isDeleted: false },
      { isSuperAdmin: false },
    );

    // 设置新的超级管理员
    targetUser.isSuperAdmin = true;
    await this.usersRepository.save(targetUser);

    this.logger.log(`Super admin set successfully: ${targetCode}`);
  }

  /**
   * 取消超级管理员权限
   */
  async removeSuperAdmin(
    targetCode: string,
    operatorCode: string,
  ): Promise<void> {
    this.logger.log(
      `Removing super admin: ${targetCode} by operator: ${operatorCode}`,
    );

    // 检查操作者是否为超级管理员
    const operator = await this.findByCode(operatorCode);
    if (!operator || !operator.isSuperAdmin) {
      throw new BadRequestException('只有超级管理员可以取消超级管理员权限');
    }

    // 不能取消自己的超级管理员权限
    if (targetCode === operatorCode) {
      throw new BadRequestException('不能取消自己的超级管理员权限');
    }

    // 检查目标用户是否存在
    const targetUser = await this.findOne(targetCode);

    // 取消超级管理员权限
    targetUser.isSuperAdmin = false;
    await this.usersRepository.save(targetUser);

    this.logger.log(`Super admin removed successfully: ${targetCode}`);
  }

  /**
   * 设置公司管理员
   */
  async setCompanyAdmin(
    targetCode: string,
    companyCode: string,
    operatorCode: string,
  ): Promise<void> {
    this.logger.log(
      `Setting company admin: ${targetCode} for company: ${companyCode} by operator: ${operatorCode}`,
    );

    // 检查操作者权限（超级管理员或同公司的管理员）
    const operator = await this.findByCode(operatorCode);
    if (!operator) {
      throw new BadRequestException('操作者不存在');
    }

    if (
      !operator.isSuperAdmin &&
      (!operator.isCompanyAdmin || operator.companyCode !== companyCode)
    ) {
      throw new BadRequestException(
        '只有超级管理员或同公司的管理员可以设置公司管理员',
      );
    }

    // 检查目标用户是否存在
    const targetUser = await this.findOne(targetCode);

    // 设置公司管理员
    targetUser.isCompanyAdmin = true;
    targetUser.companyCode = companyCode;
    await this.usersRepository.save(targetUser);

    this.logger.log(`Company admin set successfully: ${targetCode}`);
  }

  /**
   * 取消公司管理员权限
   */
  async removeCompanyAdmin(
    targetCode: string,
    operatorCode: string,
  ): Promise<void> {
    this.logger.log(
      `Removing company admin: ${targetCode} by operator: ${operatorCode}`,
    );

    // 检查目标用户是否存在
    const targetUser = await this.findOne(targetCode);

    // 检查操作者权限
    const operator = await this.findByCode(operatorCode);
    if (!operator) {
      throw new BadRequestException('操作者不存在');
    }

    if (
      !operator.isSuperAdmin &&
      (!operator.isCompanyAdmin ||
        operator.companyCode !== targetUser.companyCode)
    ) {
      throw new BadRequestException(
        '只有超级管理员或同公司的管理员可以取消公司管理员权限',
      );
    }

    // 取消公司管理员权限
    targetUser.isCompanyAdmin = false;
    await this.usersRepository.save(targetUser);

    this.logger.log(`Company admin removed successfully: ${targetCode}`);
  }

  /**
   * 统计用户数量
   */
  async countUsers(): Promise<number> {
    return await this.usersRepository.count({
      where: { isDeleted: false },
    });
  }

  /**
   * 创建用户（内部方法，用于初始化和认证服务）
   */
  async createUser(
    code: string,
    password: string,
    nickname: string,
    routePermissions?: any,
    isSuperAdmin: boolean = false,
    isActive: boolean = true,
  ): Promise<User> {
    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);

    const user = this.usersRepository.create({
      code,
      nickname,
      password: hashedPassword,
      routePermissions,
      isSuperAdmin,
      isActive,
      isDeleted: false,
    });

    return await this.usersRepository.save(user);
  }

  /**
   * 根据ID查找用户（用于JWT策略）
   */
  async findById(id: string): Promise<User | null> {
    // 由于我们使用code作为主键，这里实际上是根据code查找
    return await this.findByCode(id);
  }

  /**
   * 更新用户（管理员操作）
   */
  async adminUpdateUser(
    adminCode: string,
    targetCode: string,
    updateData: any,
  ): Promise<User> {
    // 检查操作者是否为超级管理员
    const admin = await this.findByCode(adminCode);
    if (!admin || !admin.isSuperAdmin) {
      throw new BadRequestException('只有超级管理员可以更新用户');
    }

    // 查找目标用户
    const user = await this.findOne(targetCode);

    // 如果要更新密码，需要加密
    if (updateData.password) {
      updateData.password = await bcrypt.hash(updateData.password, 10);
    }

    // 更新用户信息
    Object.assign(user, updateData);
    return await this.usersRepository.save(user);
  }
}
