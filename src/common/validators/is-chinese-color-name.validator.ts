import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

export function IsChineseColorName(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isChineseColorName',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') return false;
          
          // 检查是否为中文字符且不超过6个字符
          const chineseRegex = /^[\u4e00-\u9fa5]{1,6}$/;
          if (!chineseRegex.test(value)) {
            return false;
          }
          
          // 检查是否以"色"结尾
          if (!value.endsWith('色')) {
            return false;
          }
          
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a Chinese color name with maximum 6 characters and ending with '色'`;
        },
      },
    });
  };
}
