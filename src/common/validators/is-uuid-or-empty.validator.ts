import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
  isUUID,
} from 'class-validator';

export function IsUUIDOrEmpty(
  version?: '3' | '4' | '5' | 'all',
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isUUIDOrEmpty',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          // 如果值为空、null、undefined或空字符串，则通过验证
          if (value === null || value === undefined || value === '') {
            return true;
          }
          // 如果有值，则必须是有效的UUID
          return typeof value === 'string' && isUUID(value, version);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid UUID or empty`;
        },
      },
    });
  };
}
