import { IsString, IsNotEmpty, IsOptional, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCompanyDto {
  @ApiProperty({
    description: '公司编码（主键）',
    example: 'COMP001',
  })
  @IsString()
  @IsNotEmpty({ message: '公司编码不能为空' })
  code: string;

  @ApiProperty({
    description: '公司名称',
    example: '北京科技有限公司',
  })
  @IsString()
  @IsNotEmpty({ message: '公司名称不能为空' })
  name: string;

  @ApiProperty({
    description: '公司地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: '社会信用代码',
    example: '91110000123456789X',
    required: false,
  })
  @IsOptional()
  @IsString()
  socialCreditCode?: string;

  @ApiProperty({
    description: '营业资质图片URL',
    example: 'https://example.com/license.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  businessLicenseUrl?: string;

  @ApiProperty({
    description: '公司负责人编码',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  managerCode?: string;
}
