import { ApiProperty } from '@nestjs/swagger';
import { UserResponseDto } from '@/users/dto/user-response.dto';

export class CompanyListItemDto {
  @ApiProperty({ description: '公司编码', example: 'COMP001' })
  code: string;

  @ApiProperty({ description: '公司名称', example: '北京科技有限公司' })
  name: string;

  @ApiProperty({ description: '公司地址', example: '北京市朝阳区xxx街道xxx号' })
  address: string;

  @ApiProperty({ description: '社会信用代码', example: '91110000123456789X' })
  socialCreditCode: string | null;

  @ApiProperty({
    description: '营业资质图片URL',
    example: 'https://example.com/license.jpg',
    required: false,
  })
  businessLicenseUrl: string | null;

  @ApiProperty({
    description: '店长编码',
    example: 'user001',
    required: false,
  })
  managerCode: string | null;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class CompanyDetailDto {
  @ApiProperty({ description: '公司编码', example: 'COMP001' })
  code: string;

  @ApiProperty({ description: '公司名称', example: '北京科技有限公司' })
  name: string;

  @ApiProperty({ description: '公司地址', example: '北京市朝阳区xxx街道xxx号' })
  address: string;

  @ApiProperty({ description: '社会信用代码', example: '91110000123456789X' })
  socialCreditCode: string;

  @ApiProperty({
    description: '营业资质图片URL',
    example: 'https://example.com/license.jpg',
    required: false,
  })
  businessLicenseUrl: string | null;

  @ApiProperty({
    description: '店长信息',
    type: UserResponseDto,
    required: false,
  })
  manager: UserResponseDto | null;

  @ApiProperty({
    description: '公司人员列表',
    type: [UserResponseDto],
  })
  employees: UserResponseDto[];

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class CompanyListResponseDto {
  @ApiProperty({ description: '状态码', example: 200 })
  code: number;

  @ApiProperty({
    description: '公司列表数据',
    type: 'object',
    properties: {
      companies: {
        type: 'array',
        items: { $ref: '#/components/schemas/CompanyListItemDto' },
      },
      total: { type: 'number', description: '总数量' },
      page: { type: 'number', description: '当前页码' },
      pageSize: { type: 'number', description: '每页数量' },
      totalPages: { type: 'number', description: '总页数' },
    },
  })
  data: {
    companies: CompanyListItemDto[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };

  @ApiProperty({ description: '响应消息', example: '获取公司列表成功' })
  message: string;
}

export class CompanyDetailResponseDto {
  @ApiProperty({ description: '状态码', example: 200 })
  code: number;

  @ApiProperty({
    description: '公司详情数据',
    type: CompanyDetailDto,
  })
  data: CompanyDetailDto;

  @ApiProperty({ description: '响应消息', example: '获取公司详情成功' })
  message: string;
}
