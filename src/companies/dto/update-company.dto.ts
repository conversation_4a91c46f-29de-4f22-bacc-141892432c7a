import { IsString, IsOptional, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCompanyDto {
  @ApiProperty({
    description: '公司名称',
    example: '北京科技有限公司',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: '公司地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: '社会信用代码',
    example: '91110000123456789X',
    required: false,
  })
  @IsOptional()
  @IsString()
  socialCreditCode?: string;

  @ApiProperty({
    description: '营业资质图片URL',
    example: 'https://example.com/license.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  businessLicenseUrl?: string;

  @ApiProperty({
    description: '公司负责人编码',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  managerCode?: string;
}
