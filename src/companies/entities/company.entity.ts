import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('companies')
export class Company {
  @PrimaryColumn({ type: 'varchar', length: 50 })
  @ApiProperty({ description: '公司编码，作为主键', example: 'COMP001' })
  code: string;

  @Column({ type: 'varchar', length: 200 })
  @ApiProperty({ description: '公司名称', example: '北京科技有限公司' })
  name: string;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: '公司地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  address: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @ApiProperty({
    description: '社会信用代码',
    example: '91110000123456789X',
    required: false,
  })
  socialCreditCode: string | null;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @ApiProperty({
    description: '营业资质图片URL',
    example: 'https://example.com/license.jpg',
    required: false,
  })
  businessLicenseUrl: string | null;

  // 公司负责人关联（一对一）
  @ManyToOne('User', { nullable: true })
  @JoinColumn({ name: 'managerCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '公司负责人信息',
    required: false,
  })
  manager: any;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @ApiProperty({
    description: '公司负责人编码',
    example: 'user001',
    required: false,
  })
  managerCode: string | null;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 软删除字段
  @Column({ default: false })
  @ApiProperty({ description: '是否已删除（软删除标记）' })
  isDeleted: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  @ApiProperty({ description: '删除时间', required: false })
  deletedAt: Date | null;
}
