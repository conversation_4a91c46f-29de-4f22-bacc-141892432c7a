import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Company } from './entities/company.entity';
import { User } from '@/users/entities/user.entity';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { QueryCompaniesDto } from './dto/query-companies.dto';

@Injectable()
export class CompaniesService {
  private readonly logger = new Logger(CompaniesService.name);

  constructor(
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  /**
   * 创建公司
   */
  async create(createCompanyDto: CreateCompanyDto): Promise<void> {
    this.logger.log(`Creating company with code: ${createCompanyDto.code}`);

    // 检查公司编码是否已存在
    const existingCompany = await this.companiesRepository.findOne({
      where: { code: createCompanyDto.code, isDeleted: false },
    });

    if (existingCompany) {
      throw new BadRequestException('公司编码已存在');
    }

    // 验证店长是否存在
    let manager: User | null = null;
    if (createCompanyDto.managerCode) {
      manager = await this.usersRepository.findOne({
        where: { code: createCompanyDto.managerCode, isDeleted: false },
      });
      if (!manager) {
        throw new BadRequestException(
          `店长用户 ${createCompanyDto.managerCode} 不存在`,
        );
      }
    }

    // 员工关系已移除，现在通过用户表的companyCode字段管理

    // 创建公司
    const company = this.companiesRepository.create({
      code: createCompanyDto.code,
      name: createCompanyDto.name,
      address: createCompanyDto.address || null,
      socialCreditCode: createCompanyDto.socialCreditCode || null,
      businessLicenseUrl: createCompanyDto.businessLicenseUrl || null,
      managerCode: createCompanyDto.managerCode || null,
    });

    // 设置关联关系
    if (manager) {
      company.manager = manager;
    }

    await this.companiesRepository.save(company);
    this.logger.log(`Company ${createCompanyDto.code} created successfully`);
  }

  /**
   * 分页查询公司列表（支持模糊搜索）
   */
  async findAll(queryDto: QueryCompaniesDto) {
    this.logger.log(
      `Querying companies with params: ${JSON.stringify(queryDto)}`,
    );

    const { page, pageSize, search } = queryDto;

    // page和pageSize是必须参数
    if (page === undefined || pageSize === undefined) {
      return {
        companies: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // 处理分页参数：page=0&pageSize=0表示获取所有数据
    const isGetAll = page === 0 && pageSize === 0;
    const finalPage = isGetAll ? 1 : page;
    const finalPageSize = isGetAll ? undefined : pageSize;
    const skip = isGetAll ? 0 : (page - 1) * pageSize;

    const queryBuilder = this.companiesRepository
      .createQueryBuilder('company')
      .leftJoinAndSelect('company.manager', 'manager')
      .where('company.isDeleted = :isDeleted', { isDeleted: false });

    // 模糊搜索
    if (search) {
      queryBuilder.andWhere(
        '(company.code ILIKE :search OR company.name ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const companies = await queryBuilder
      .select([
        'company.code',
        'company.name',
        'company.address',
        'company.socialCreditCode',
        'company.businessLicenseUrl',
        'company.managerCode',
        'company.createdAt',
        'company.updatedAt',
        'manager.code',
        'manager.nickname',
      ])
      .orderBy('company.createdAt', 'DESC')
      .skip(skip)
      .take(finalPageSize)
      .getMany();

    // 格式化返回数据，添加 managerName
    const formattedCompanies = companies.map((company) => ({
      code: company.code,
      name: company.name,
      address: company.address,
      socialCreditCode: company.socialCreditCode,
      businessLicenseUrl: company.businessLicenseUrl,
      managerCode: company.managerCode,
      managerName: company.manager?.nickname || null,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
    }));

    return {
      companies: formattedCompanies,
      total,
      page: isGetAll ? 0 : finalPage,
      pageSize: isGetAll ? 0 : finalPageSize!,
      totalPages: isGetAll ? 1 : Math.ceil(total / finalPageSize!),
    };
  }

  /**
   * 根据公司编码查找公司
   */
  async findByCode(code: string): Promise<Company | null> {
    return await this.companiesRepository.findOne({
      where: { code, isDeleted: false },
      relations: ['manager'],
    });
  }

  /**
   * 根据公司编码获取公司详情（内部使用，返回实体）
   */
  async findOne(code: string): Promise<Company> {
    const company = await this.findByCode(code);
    if (!company) {
      throw new NotFoundException('公司不存在');
    }
    return company;
  }

  /**
   * 根据公司编码获取公司详情（API使用，格式化返回）
   */
  async findOneFormatted(code: string) {
    const company = await this.findByCode(code);
    if (!company) {
      throw new NotFoundException('公司不存在');
    }

    // 获取公司员工（通过用户表查询）
    const employees = await this.usersRepository.find({
      where: { companyCode: code, isDeleted: false },
      select: ['code', 'nickname', 'isCompanyAdmin'],
    });

    // 格式化返回数据
    return {
      code: company.code,
      name: company.name,
      address: company.address,
      socialCreditCode: company.socialCreditCode,
      businessLicenseUrl: company.businessLicenseUrl,
      managerCode: company.managerCode,
      managerName: company.manager?.nickname || null,
      employees: employees.map((emp) => ({
        code: emp.code,
        name: emp.nickname,
        isCompanyAdmin: emp.isCompanyAdmin,
      })),
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
    };
  }

  /**
   * 更新公司
   */
  async update(
    code: string,
    updateCompanyDto: UpdateCompanyDto,
  ): Promise<void> {
    this.logger.log(`Updating company ${code}`);

    // 查找要更新的公司
    const company = await this.findOne(code);

    // 验证店长是否存在
    if (updateCompanyDto.managerCode !== undefined) {
      if (updateCompanyDto.managerCode) {
        const manager = await this.usersRepository.findOne({
          where: { code: updateCompanyDto.managerCode, isDeleted: false },
        });
        if (!manager) {
          throw new BadRequestException(
            `店长用户 ${updateCompanyDto.managerCode} 不存在`,
          );
        }
        company.manager = manager;
        company.managerCode = updateCompanyDto.managerCode;
      } else {
        company.manager = null;
        company.managerCode = null;
      }
    }

    // 员工关系现在通过用户表的companyCode字段管理，不在此处处理

    // 更新其他字段
    if (updateCompanyDto.name !== undefined) {
      company.name = updateCompanyDto.name;
    }
    if (updateCompanyDto.address !== undefined) {
      company.address = updateCompanyDto.address;
    }
    if (updateCompanyDto.socialCreditCode !== undefined) {
      company.socialCreditCode = updateCompanyDto.socialCreditCode;
    }
    if (updateCompanyDto.businessLicenseUrl !== undefined) {
      company.businessLicenseUrl = updateCompanyDto.businessLicenseUrl;
    }

    await this.companiesRepository.save(company);
    this.logger.log(`Company ${code} updated successfully`);
  }

  /**
   * 软删除公司
   */
  async remove(code: string): Promise<void> {
    this.logger.log(`Soft deleting company ${code}`);

    // 查找要删除的公司
    const company = await this.findOne(code);

    // 软删除
    company.isDeleted = true;
    company.deletedAt = new Date();
    await this.companiesRepository.save(company);
    this.logger.log(`Company ${code} soft deleted successfully`);
  }
}
