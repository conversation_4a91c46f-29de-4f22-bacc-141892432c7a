import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CompaniesService } from './companies.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { QueryCompaniesDto } from './dto/query-companies.dto';
import {
  CompanyListResponseDto,
  CompanyDetailResponseDto,
} from './dto/company-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';
import { SuperAdminGuard } from '@/auth/guards/super-admin.guard';

@ApiTags('公司管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, SuperAdminGuard)
@Controller('companies')
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '创建公司' })
  @ApiResponse({
    status: 200,
    description: '创建公司成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        data: { type: 'object', example: {} },
        message: { type: 'string', example: '创建公司成功' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误或公司编码已存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async create(@Body() createCompanyDto: CreateCompanyDto) {
    await this.companiesService.create(createCompanyDto);
    return {
      code: 200,
      data: {},
      message: '创建公司成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取公司列表（支持分页和模糊搜索）' })
  @ApiResponse({
    status: 200,
    description: '获取公司列表成功',
    type: CompanyListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async findAll(@Query() queryDto: QueryCompaniesDto) {
    const result = await this.companiesService.findAll(queryDto);
    return {
      code: 200,
      data: result,
      message: '获取公司列表成功',
    };
  }

  @Get(':code')
  @ApiOperation({ summary: '根据编码获取公司详情' })
  @ApiResponse({
    status: 200,
    description: '获取公司详情成功',
    type: CompanyDetailResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '公司不存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async findOne(@Param('code') code: string) {
    const company = await this.companiesService.findOneFormatted(code);
    return {
      code: 200,
      data: company,
      message: '获取公司详情成功',
    };
  }

  @Patch(':code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '更新公司信息' })
  @ApiResponse({
    status: 200,
    description: '更新公司成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        data: { type: 'object', example: {} },
        message: { type: 'string', example: '更新公司成功' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 404,
    description: '公司不存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async update(
    @Param('code') code: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
  ) {
    await this.companiesService.update(code, updateCompanyDto);
    return {
      code: 200,
      data: {},
      message: '更新公司成功',
    };
  }

  @Delete(':code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '删除公司（软删除）' })
  @ApiResponse({
    status: 200,
    description: '删除公司成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        data: { type: 'object', example: {} },
        message: { type: 'string', example: '删除公司成功' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: '公司不存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async remove(@Param('code') code: string) {
    await this.companiesService.remove(code);
    return {
      code: 200,
      data: {},
      message: '删除公司成功',
    };
  }
}
