# 支出和收入管理系统

## 功能概述

支出和收入管理系统为服装批发商提供完整的财务流水管理功能，支持支出和收入的详细记录、查询、统计和导出。

## 模块设计

### 支出模块 (Expenses)
- **关联实体**: 供应商 (Suppliers)
- **主要功能**: 记录向供应商的各种支出
- **数据结构**: 主表 + 明细表设计

### 收入模块 (Incomes)  
- **关联实体**: 客户 (Customers) + 负责人 (Users)
- **主要功能**: 记录来自客户的各种收入
- **数据结构**: 主表 + 明细表设计

## 数据库结构

### 支出相关表

#### expenses (支出主表)
- `id` - UUID主键
- `total_amount` - 支出总金额 (DECIMAL 15,2)
- `is_deleted` - 软删除标识
- `created_at/updated_at/deleted_at` - 时间戳

#### expense_details (支出明细表)
- `id` - UUID主键
- `expense_id` - 关联支出主表
- `supplier_code` - 供应商编码 (必填)
- `supplier_name` - 供应商名称 (冗余字段)
- `amount` - 支出金额 (必填，DECIMAL 15,2)
- `screenshot` - 截图URL (选填)
- `remark` - 备注 (选填)
- 时间戳和软删除字段

### 收入相关表

#### incomes (收入主表)
- `id` - UUID主键
- `total_amount` - 收入总金额 (DECIMAL 15,2)
- `is_deleted` - 软删除标识
- `created_at/updated_at/deleted_at` - 时间戳

#### income_details (收入明细表)
- `id` - UUID主键
- `income_id` - 关联收入主表
- `customer_code` - 客户编码 (必填)
- `customer_name` - 客户名称 (冗余字段)
- `responsible_user_code` - 负责人编码 (必填)
- `responsible_user_name` - 负责人姓名 (冗余字段)
- `amount` - 收入金额 (必填，DECIMAL 15,2)
- `screenshot` - 截图URL (选填)
- `remark` - 备注 (选填)
- 时间戳和软删除字段

## API接口

### 支出接口

#### 明细管理
- `POST /expenses/details` - 新增支出明细
- `GET /expenses/details` - 分页查询支出明细列表
- `GET /expenses/details/:id` - 获取支出明细详情
- `PATCH /expenses/details/:id` - 更新支出明细
- `DELETE /expenses/details/:id` - 删除支出明细

#### 导出功能
- `GET /expenses/export` - 导出支出明细Excel

### 收入接口

#### 明细管理
- `POST /incomes/details` - 新增收入明细
- `GET /incomes/details` - 分页查询收入明细列表
- `GET /incomes/details/:id` - 获取收入明细详情
- `PATCH /incomes/details/:id` - 更新收入明细
- `DELETE /incomes/details/:id` - 删除收入明细

#### 导出功能
- `GET /incomes/export` - 导出收入明细Excel

## 查询功能

### 支出查询参数
- `page` - 页码 (必填)
- `pageSize` - 每页数量 (必填)
- `startTime` - 起始时间 (选填)
- `endTime` - 结束时间 (选填)
- `supplierSearch` - 供应商搜索 (模糊搜索编码和名称)
- `remarkSearch` - 备注搜索

### 收入查询参数
- `page` - 页码 (必填)
- `pageSize` - 每页数量 (必填)
- `startTime` - 起始时间 (选填)
- `endTime` - 结束时间 (选填)
- `customerSearch` - 客户搜索 (模糊搜索编码和名称)
- `responsibleUserSearch` - 负责人搜索 (模糊搜索编码和姓名)
- `remarkSearch` - 备注搜索

## 导出功能

### 支出导出参数
- `startTime` - 起始时间
- `endTime` - 结束时间
- `detailIds` - 明细ID列表 (逗号分隔，用于选择性导出)
- `supplierSearch` - 供应商搜索

### 收入导出参数
- `startTime` - 起始时间
- `endTime` - 结束时间
- `detailIds` - 明细ID列表 (逗号分隔，用于选择性导出)
- `customerSearch` - 客户搜索
- `responsibleUserSearch` - 负责人搜索

### Excel导出特色
- 包含完整的明细数据
- 末尾显示选择性统计汇总
- 支持按条件筛选导出
- 自动计算总金额和明细数量

## 业务逻辑

### 数据验证
- 支出必须关联有效的供应商
- 收入必须关联有效的客户和负责人
- 金额必须大于0且保留两位小数
- 支持截图和备注的可选填写

### 自动统计
- 新增明细时自动更新主表总金额
- 更新明细时自动调整总金额差额
- 删除明细时自动扣减总金额
- 查询时返回系统总金额和查询结果总金额

### 软删除机制
- 所有删除操作都是软删除
- 保留删除时间戳
- 查询时自动过滤已删除数据

## 使用示例

### 新增支出明细
```json
POST /expenses/details
{
  "supplierCode": "SUP001",
  "amount": 15000.50,
  "screenshot": "https://example.com/receipt.jpg",
  "remark": "采购办公用品"
}
```

### 新增收入明细
```json
POST /incomes/details
{
  "customerCode": "CUS001",
  "responsibleUserCode": "user001",
  "amount": 25000.00,
  "screenshot": "https://example.com/payment.jpg",
  "remark": "销售收入"
}
```

### 分页查询支出
```
GET /expenses/details?page=1&pageSize=10&startTime=2023-01-01&endTime=2023-12-31&supplierSearch=SUP001
```

### 分页查询收入
```
GET /incomes/details?page=1&pageSize=10&customerSearch=CUS001&responsibleUserSearch=user001
```

### 选择性导出
```
GET /expenses/export?detailIds=uuid1,uuid2,uuid3&startTime=2023-01-01&endTime=2023-12-31
```

## 数据迁移

执行 `src/migrations/create-expenses-incomes-tables.sql` 文件来创建数据库表结构：

```sql
-- 创建支出和收入相关表
-- 包含主表、明细表、索引、约束等完整结构
```

## 注意事项

1. **关联验证**: 确保供应商、客户、用户编码的有效性
2. **金额精度**: 所有金额字段保留两位小数
3. **性能优化**: 已为常用查询字段创建索引
4. **数据一致性**: 使用事务确保主表和明细表数据一致
5. **选择性统计**: 导出功能支持基于选中明细的精确统计计算

## 扩展建议

1. **审批流程**: 可以添加审批状态字段
2. **分类管理**: 可以添加支出/收入分类
3. **预算控制**: 可以添加预算限制功能
4. **报表分析**: 可以添加月度/年度统计报表
5. **权限控制**: 可以基于用户角色限制操作权限
