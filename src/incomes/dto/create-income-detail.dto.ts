import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateIncomeDetailDto {
  @ApiProperty({
    description: '客户编码（必填）',
    example: 'CUS001',
  })
  @IsString()
  @IsNotEmpty({ message: '客户编码不能为空' })
  customerCode: string;

  @ApiProperty({
    description: '创建日期（字符串格式，必填）',
    example: '2024-01-15',
  })
  @IsString()
  @IsNotEmpty({ message: '创建日期不能为空' })
  createDate: string;

  @ApiProperty({
    description: '负责人编码（必填）',
    example: 'user001',
  })
  @IsString()
  @IsNotEmpty({ message: '负责人编码不能为空' })
  responsibleUserCode: string;

  @ApiProperty({
    description: '收入金额（保留两位小数）',
    example: 25000.5,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '金额必须大于0' })
  amount: number;

  @ApiProperty({
    description: '截图URL（选填）',
    example: 'https://example.com/screenshot.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  screenshot?: string;

  @ApiProperty({
    description: '备注（选填）',
    example: '销售收入',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;
}
