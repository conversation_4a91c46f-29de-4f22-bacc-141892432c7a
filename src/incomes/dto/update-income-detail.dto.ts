import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateIncomeDetailDto {
  @ApiProperty({
    description: '客户编码',
    example: 'CUS001',
    required: false,
  })
  @IsOptional()
  @IsString()
  customerCode?: string;

  @ApiProperty({
    description: '创建日期（字符串格式）',
    example: '2024-01-15',
    required: false,
  })
  @IsOptional()
  @IsString()
  createDate?: string;

  @ApiProperty({
    description: '负责人编码',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  responsibleUserCode?: string;

  @ApiProperty({
    description: '收入金额（保留两位小数）',
    example: 25000.5,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '金额必须大于0' })
  amount?: number;

  @ApiProperty({
    description: '截图URL',
    example: 'https://example.com/screenshot.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  screenshot?: string;

  @ApiProperty({
    description: '备注',
    example: '销售收入',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;
}
