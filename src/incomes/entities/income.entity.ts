import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { IncomeDetail } from './income-detail.entity';

@Entity('incomes')
export class Income {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '收入总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalAmount: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    comment: '创建日期（字符串格式，必填）',
  })
  createDate: string;

  // 关联收入明细
  @OneToMany(() => IncomeDetail, (detail) => detail.income)
  details: IncomeDetail[];
}
