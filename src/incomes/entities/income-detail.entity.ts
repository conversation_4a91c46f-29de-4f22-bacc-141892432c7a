import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Jo<PERSON><PERSON><PERSON>umn,
} from 'typeorm';
import { Income } from './income.entity';

@Entity('income_details')
export class IncomeDetail {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', comment: '收入ID' })
  incomeId: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '客户编码（必填）',
  })
  customerCode: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '客户名称（冗余字段，便于查询）',
  })
  customerName: string | null;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '负责人编码（必填）',
  })
  responsibleUserCode: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '负责人姓名（冗余字段，便于查询）',
  })
  responsibleUserName: string | null;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '收入金额（保留两位小数）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '截图URL（选填）',
  })
  screenshot: string | null;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注（选填）',
  })
  remark: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    comment: '创建日期（字符串格式，必填）',
  })
  createDate: string;

  // 关联收入主表
  @ManyToOne(() => Income, (income) => income.details)
  @JoinColumn({ name: 'incomeId' })
  income: Income;
}
