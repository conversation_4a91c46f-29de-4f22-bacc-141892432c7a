import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import { Income } from './entities/income.entity';
import { IncomeDetail } from './entities/income-detail.entity';
import { CreateIncomeDetailDto } from './dto/create-income-detail.dto';
import { UpdateIncomeDetailDto } from './dto/update-income-detail.dto';
import { QueryIncomeDetailsDto } from './dto/query-income-details.dto';
import { ProcessedExportIncomeDto } from './dto/export-income-details.dto';
import { CustomersService } from '@/customers/customers.service';
import { UsersService } from '@/users/users.service';

@Injectable()
export class IncomesService {
  private readonly logger = new Logger(IncomesService.name);

  constructor(
    @InjectRepository(Income)
    private readonly incomeRepository: Repository<Income>,
    @InjectRepository(IncomeDetail)
    private readonly incomeDetailRepository: Repository<IncomeDetail>,
    private readonly dataSource: DataSource,
    private readonly customersService: CustomersService,
    private readonly usersService: UsersService,
  ) {}

  // 获取或创建收入记录（系统只有一条记录）
  private async getOrCreateIncome(): Promise<Income> {
    let income = await this.incomeRepository.findOne({
      where: { isDeleted: false },
    });

    if (!income) {
      income = this.incomeRepository.create({
        totalAmount: 0,
        createDate: new Date().toISOString().split('T')[0], // 格式: YYYY-MM-DD
      });
      await this.incomeRepository.save(income);
      this.logger.log('Created new income record');
    }

    return income;
  }

  // 验证客户是否存在
  private async validateCustomer(
    customerCode: string,
  ): Promise<{ code: string; name: string }> {
    if (!customerCode || customerCode.trim() === '') {
      throw new BadRequestException('客户编码不能为空');
    }

    const customer = await this.customersService.findByCode(customerCode);
    if (!customer) {
      throw new BadRequestException(`客户 ${customerCode} 不存在`);
    }

    return { code: customer.code, name: customer.name };
  }

  // 验证负责人是否存在
  private async validateUser(
    userCode: string,
  ): Promise<{ code: string; name: string }> {
    if (!userCode || userCode.trim() === '') {
      throw new BadRequestException('负责人编码不能为空');
    }

    const user = await this.usersService.findByCode(userCode);
    if (!user) {
      throw new BadRequestException(`用户 ${userCode} 不存在`);
    }

    return { code: user.code, name: user.nickname };
  }

  // 新增收入明细
  async createDetail(createDetailDto: CreateIncomeDetailDto): Promise<void> {
    const {
      customerCode,
      responsibleUserCode,
      amount,
      screenshot,
      remark,
      createDate,
    } = createDetailDto;

    this.logger.log(
      `Creating income detail for customer: ${customerCode}, amount: ${amount}`,
    );

    // 验证客户和负责人
    const customer = await this.validateCustomer(customerCode);
    const user = await this.validateUser(responsibleUserCode);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建收入记录
      const income = await this.getOrCreateIncome();

      // 创建明细记录
      const detail = queryRunner.manager.create(IncomeDetail, {
        incomeId: income.id,
        customerCode: customer.code,
        customerName: customer.name,
        responsibleUserCode: user.code,
        responsibleUserName: user.name,
        amount,
        screenshot: screenshot || null,
        remark: remark || null,
        createDate,
      });

      await queryRunner.manager.save(detail);

      // 更新总金额
      await queryRunner.manager.update(
        Income,
        { id: income.id },
        {
          totalAmount: () => `"totalAmount" + ${amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(
        `Income detail created successfully for customer: ${customerCode}, amount: ${amount}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询收入明细
  async findAllDetails(queryDto: QueryIncomeDetailsDto) {
    const {
      page,
      pageSize,
      startTime,
      endTime,
      customerSearch,
      responsibleUserSearch,
      remarkSearch,
      sortBy = 'createDate',
      sortOrder = 'DESC',
    } = queryDto;

    this.logger.log(
      `Querying income details: page=${page}, pageSize=${pageSize}`,
    );

    // 获取收入总金额
    const income = await this.getOrCreateIncome();

    // 处理时间参数 - 由于createDate是字符串类型，需要特殊处理
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime) {
      if (startTime.length === 10) {
        // 对于日期字符串，我们需要确保包含开始日期的所有时间
        processedStartTime = startTime; // 保持原始日期格式用于字符串比较
      }
    }

    if (endTime) {
      if (endTime.length === 10) {
        // 对于结束日期，我们需要确保包含结束日期的所有时间
        processedEndTime = endTime; // 保持原始日期格式用于字符串比较
      }
    }

    const queryBuilder = this.incomeDetailRepository
      .createQueryBuilder('detail')
      .where('detail.isDeleted = false')
      .orderBy(`detail.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // 添加时间范围过滤 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (processedStartTime) {
      // 将字符串日期转换为日期类型进行比较，确保包含开始日期的所有数据
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: processedStartTime,
        },
      );
    }

    if (processedEndTime) {
      // 将字符串日期转换为日期类型进行比较，确保包含结束日期的所有数据
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: processedEndTime,
        },
      );
    }

    // 添加客户搜索过滤
    if (customerSearch) {
      queryBuilder.andWhere(
        '(detail.customerCode ILIKE :customerSearch OR detail.customerName ILIKE :customerSearch)',
        { customerSearch: `%${customerSearch}%` },
      );
    }

    // 添加负责人搜索过滤
    if (responsibleUserSearch) {
      queryBuilder.andWhere(
        '(detail.responsibleUserCode ILIKE :responsibleUserSearch OR detail.responsibleUserName ILIKE :responsibleUserSearch)',
        { responsibleUserSearch: `%${responsibleUserSearch}%` },
      );
    }

    // 添加备注搜索过滤
    if (remarkSearch) {
      queryBuilder.andWhere('detail.remark ILIKE :remarkSearch', {
        remarkSearch: `%${remarkSearch}%`,
      });
    }

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const details = await queryBuilder.getMany();

      // 计算查询结果的总金额
      const queryResultTotalAmount = details.reduce(
        (sum, detail) => sum + detail.amount,
        0,
      );

      return {
        details,
        total: details.length,
        page: 0,
        pageSize: 0,
        totalAmount: queryResultTotalAmount,
        systemTotalAmount: income.totalAmount,
        startTime: processedStartTime || null,
        endTime: processedEndTime || null,
      };
    }

    // 正常分页查询
    const [details, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    // 计算所有查询条件下的总金额（不分页）
    const allMatchingDetails = await queryBuilder
      .skip(0)
      .take(undefined)
      .getMany();

    const queryResultTotalAmount = allMatchingDetails.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    return {
      details,
      total,
      page,
      pageSize,
      totalAmount: queryResultTotalAmount,
      systemTotalAmount: income.totalAmount,
      startTime: processedStartTime || null,
      endTime: processedEndTime || null,
    };
  }

  // 获取收入明细详情
  async findDetailById(id: string): Promise<IncomeDetail> {
    this.logger.log(`Finding income detail by id: ${id}`);

    const detail = await this.incomeDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException('收入明细不存在');
    }

    return detail;
  }

  // 更新收入明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateIncomeDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating income detail ${id}`);

    const detail = await this.findDetailById(id);
    const oldAmount = detail.amount;

    // 如果更新了客户，需要验证
    if (
      updateDetailDto.customerCode &&
      updateDetailDto.customerCode !== detail.customerCode
    ) {
      const customer = await this.validateCustomer(
        updateDetailDto.customerCode,
      );
      detail.customerCode = customer.code;
      detail.customerName = customer.name;
    }

    // 如果更新了负责人，需要验证
    if (
      updateDetailDto.responsibleUserCode &&
      updateDetailDto.responsibleUserCode !== detail.responsibleUserCode
    ) {
      const user = await this.validateUser(updateDetailDto.responsibleUserCode);
      detail.responsibleUserCode = user.code;
      detail.responsibleUserName = user.name;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 更新明细
      Object.assign(detail, updateDetailDto);
      await queryRunner.manager.save(detail);

      // 如果金额发生变化，更新总金额
      if (
        updateDetailDto.amount !== undefined &&
        updateDetailDto.amount !== oldAmount
      ) {
        const amountDiff = updateDetailDto.amount - oldAmount;
        const income = await this.getOrCreateIncome();

        await queryRunner.manager.update(
          Income,
          { id: income.id },
          {
            totalAmount: () => `"totalAmount" + ${amountDiff}`,
          },
        );
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Income detail ${id} updated successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除收入明细
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing income detail ${id}`);

    const detail = await this.findDetailById(id);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 软删除明细
      detail.isDeleted = true;
      await queryRunner.manager.save(detail);

      // 更新总金额
      const income = await this.getOrCreateIncome();
      await queryRunner.manager.update(
        Income,
        { id: income.id },
        {
          totalAmount: () => `"totalAmount" - ${detail.amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Income detail ${id} removed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出收入明细Excel
  async exportDetailsToExcel(
    exportDto: ProcessedExportIncomeDto,
  ): Promise<Buffer> {
    this.logger.log('Exporting income details to Excel');

    // 验证：如果没有提供明细ID，必须提供时间范围
    if (!exportDto.detailIds || exportDto.detailIds.length === 0) {
      if (!exportDto.startTime || !exportDto.endTime) {
        throw new BadRequestException(
          '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
        );
      }
    }

    let queryBuilder = this.incomeDetailRepository
      .createQueryBuilder('detail')
      .where('detail.isDeleted = false')
      .orderBy('detail.createDate', 'DESC');

    // 明细ID过滤（可选）
    if (exportDto.detailIds && exportDto.detailIds.length > 0) {
      queryBuilder.andWhere('detail.id IN (:...detailIds)', {
        detailIds: exportDto.detailIds,
      });
    }

    // 时间范围过滤 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (exportDto.startTime) {
      const processedStartTime =
        exportDto.startTime.length === 10
          ? exportDto.startTime
          : exportDto.startTime;
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: processedStartTime,
        },
      );
    }

    if (exportDto.endTime) {
      const processedEndTime =
        exportDto.endTime.length === 10 ? exportDto.endTime : exportDto.endTime;
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: processedEndTime,
        },
      );
    }

    // 客户搜索过滤
    if (exportDto.customerSearch) {
      queryBuilder.andWhere(
        '(detail.customerCode ILIKE :customerSearch OR detail.customerName ILIKE :customerSearch)',
        { customerSearch: `%${exportDto.customerSearch}%` },
      );
    }

    // 负责人搜索过滤
    if (exportDto.responsibleUserSearch) {
      queryBuilder.andWhere(
        '(detail.responsibleUserCode ILIKE :responsibleUserSearch OR detail.responsibleUserName ILIKE :responsibleUserSearch)',
        { responsibleUserSearch: `%${exportDto.responsibleUserSearch}%` },
      );
    }

    const details = await queryBuilder.getMany();

    // 验证是否找到了明细
    if (details.length === 0) {
      throw new BadRequestException('未找到符合条件的收入明细');
    }

    // 计算选择性统计数据
    const totalAmount = details.reduce((sum, detail) => sum + detail.amount, 0);

    // 生成Excel（支持图片插入）
    try {
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('收入明细');

      // 设置列宽
      worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '客户编码', key: 'customerCode', width: 15 },
        { header: '客户名称', key: 'customerName', width: 20 },
        { header: '负责人编码', key: 'responsibleUserCode', width: 15 },
        { header: '负责人姓名', key: 'responsibleUserName', width: 20 },
        { header: '收入金额', key: 'amount', width: 15 },
        { header: '截图', key: 'screenshot', width: 30 },
        { header: '备注', key: 'remark', width: 30 },
        { header: '创建日期', key: 'createDate', width: 20 },
      ];

      // 设置标题行样式
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };

      let currentRow = 2; // 从第二行开始（第一行是标题）

      // 添加数据行并处理图片
      for (let i = 0; i < details.length; i++) {
        const detail = details[i];

        // 设置行高以容纳图片
        worksheet.getRow(currentRow).height = 80;

        const row = worksheet.addRow([
          i + 1,
          detail.customerCode,
          detail.customerName || '',
          detail.responsibleUserCode,
          detail.responsibleUserName || '',
          detail.amount,
          '', // 截图列先留空，后面插入图片
          detail.remark || '',
          detail.createDate,
        ]);

        // 下载并插入图片
        try {
          if (detail.screenshot) {
            this.logger.log(`Downloading image: ${detail.screenshot}`);

            const imageResponse = await axios.get(detail.screenshot, {
              responseType: 'arraybuffer',
              timeout: 10000,
            });

            const imageBuffer = Buffer.from(imageResponse.data);

            // 添加图片到工作簿
            const imageId = workbook.addImage({
              buffer: imageBuffer,
              extension: 'jpeg',
            });

            // 在截图列插入图片
            worksheet.addImage(imageId, {
              tl: { col: 6, row: currentRow - 1 }, // 从截图列开始（第7列，索引6）
              ext: { width: 200, height: 100 }, // 图片大小
            });

            this.logger.log(`Image inserted successfully for detail ${i + 1}`);
          }
        } catch (imageError) {
          this.logger.warn(
            `Failed to load image for detail ${i + 1}: ${detail.screenshot}`,
            imageError.message,
          );
          // 如果图片加载失败，在截图列显示链接
          row.getCell(7).value = detail.screenshot;
        }

        currentRow++;
      }

      // 添加统计数据行
      worksheet.addRow([]); // 空行
      currentRow++;

      const summaryTitleRow = worksheet.addRow([
        '统计汇总（仅针对选中的明细）',
      ]);
      summaryTitleRow.font = { bold: true, size: 12 };
      summaryTitleRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFCCCCCC' },
      };
      currentRow++;

      const totalAmountRow = worksheet.addRow(['总收入金额', totalAmount]);
      totalAmountRow.font = { bold: true };
      totalAmountRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF0F0F0' },
      };
      currentRow++;

      const countRow = worksheet.addRow(['明细数量', details.length]);
      countRow.font = { bold: true };
      countRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF0F0F0' },
      };

      // 设置边框
      const borderStyle = {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const },
      };

      // 为表格添加边框
      const tableStartRow = 1;
      const tableEndRow = details.length + 1;
      for (let row = tableStartRow; row <= tableEndRow; row++) {
        for (let col = 1; col <= 9; col++) {
          worksheet.getCell(row, col).border = borderStyle;
        }
      }

      // 生成Excel缓冲区
      const excelBuffer = await workbook.xlsx.writeBuffer();
      return Buffer.from(excelBuffer);
    } catch (error) {
      this.logger.error('Excel generation failed', error);
      throw new BadRequestException('Excel生成失败');
    }
  }
}
