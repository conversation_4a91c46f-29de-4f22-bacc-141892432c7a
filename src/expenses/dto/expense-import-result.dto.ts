import { ApiProperty } from '@nestjs/swagger';

export class ExpenseImportResult {
  @ApiProperty({ description: '成功导入的数量', example: 10 })
  successCount: number;

  @ApiProperty({ description: '导入失败的数量', example: 2 })
  failureCount: number;

  @ApiProperty({
    description: '错误信息列表',
    example: ['第3行：供应商编码不能为空', '第5行：金额必须大于0'],
    type: [String],
  })
  errors: string[];

  @ApiProperty({
    description: '成功导入的供应商编码列表',
    example: ['SUP001', 'SUP002'],
    type: [String],
  })
  successSupplierCodes: string[];
}
