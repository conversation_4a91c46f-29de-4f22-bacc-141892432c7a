import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateExpenseDetailDto {
  @ApiProperty({
    description: '供应商编码',
    example: 'SUP001',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierCode?: string;

  @ApiProperty({
    description: '创建日期（字符串格式）',
    example: '2024-01-15',
    required: false,
  })
  @IsOptional()
  @IsString()
  createDate?: string;

  @ApiProperty({
    description: '支出金额（保留两位小数）',
    example: 15000.5,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '金额必须大于0' })
  amount?: number;

  @ApiProperty({
    description: '截图URL',
    example: 'https://example.com/screenshot.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  screenshot?: string;

  @ApiProperty({
    description: '备注',
    example: '采购办公用品',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;
}
