import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class ExportExpenseDetailsDto {
  @ApiProperty({
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsString()
  startTime?: string;

  @ApiProperty({
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsString()
  endTime?: string;

  @ApiProperty({
    description:
      '明细ID列表（逗号分隔，用于选择性导出）。如果不提供，则必须提供起始时间和终止时间进行时间范围导出',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @IsOptional()
  @IsString()
  detailIds?: string;

  @ApiProperty({
    description: '供应商搜索',
    example: 'SUP001',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierSearch?: string;
}

// 内部处理接口
export interface ProcessedExportExpenseDto {
  startTime?: string;
  endTime?: string;
  detailIds?: string[];
  supplierSearch?: string;
}
