import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  Res,
  Req,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { ExpensesService } from './expenses.service';
import { CreateExpenseDetailDto } from './dto/create-expense-detail.dto';
import { UpdateExpenseDetailDto } from './dto/update-expense-detail.dto';
import { QueryExpenseDetailsDto } from './dto/query-expense-details.dto';
import { ExpenseImportResult } from './dto/expense-import-result.dto';

@ApiTags('expenses')
@Controller('expenses')
export class ExpensesController {
  private readonly logger = new Logger(ExpensesController.name);

  constructor(private readonly expensesService: ExpensesService) {}

  @Post('details')
  @ApiOperation({ summary: '新增支出明细' })
  @ApiResponse({
    status: 200,
    description: '支出明细创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误或供应商不存在',
  })
  async createDetail(@Body() createDetailDto: CreateExpenseDetailDto) {
    this.logger.log(
      `Creating expense detail for supplier: ${createDetailDto.supplierCode}, amount: ${createDetailDto.amount}`,
    );

    await this.expensesService.createDetail(createDetailDto);

    return {
      code: 200,
      data: null,
      message: '支出明细创建成功',
    };
  }

  @Get('details')
  @ApiOperation({ summary: '分页查询支出明细列表' })
  @ApiQuery({
    name: 'page',
    description: '页码（必填）',
    example: 1,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量（必填）',
    example: 10,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始时间',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '结束时间',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'supplierSearch',
    description: '供应商搜索（模糊搜索供应商编码和名称）',
    example: 'SUP001',
    required: false,
  })
  @ApiQuery({
    name: 'remarkSearch',
    description: '备注搜索',
    example: '办公用品',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: '排序字段',
    enum: ['createDate', 'amount'],
    example: 'createDate',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: '排序方向',
    enum: ['ASC', 'DESC'],
    example: 'DESC',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllDetails(@Query() queryDto: QueryExpenseDetailsDto) {
    this.logger.log(
      `Querying expense details with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.expensesService.findAllDetails(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('details/:id')
  @ApiOperation({ summary: '获取支出明细详情' })
  @ApiParam({
    name: 'id',
    description: '支出明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '支出明细不存在',
  })
  async findDetailById(@Param('id') id: string) {
    this.logger.log(`Finding expense detail by id: ${id}`);

    const detail = await this.expensesService.findDetailById(id);

    return {
      code: 200,
      data: detail,
      message: '查询成功',
    };
  }

  @Patch('details/:id')
  @ApiOperation({ summary: '更新支出明细' })
  @ApiParam({
    name: 'id',
    description: '支出明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '支出明细不存在',
  })
  async updateDetail(
    @Param('id') id: string,
    @Body() updateDetailDto: UpdateExpenseDetailDto,
  ) {
    this.logger.log(`Updating expense detail ${id}`);

    await this.expensesService.updateDetail(id, updateDetailDto);

    return {
      code: 200,
      data: null,
      message: '支出明细更新成功',
    };
  }

  @Delete('details/:id')
  @ApiOperation({ summary: '删除支出明细' })
  @ApiParam({
    name: 'id',
    description: '支出明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '支出明细不存在',
  })
  async removeDetail(@Param('id') id: string) {
    this.logger.log(`Removing expense detail ${id}`);

    await this.expensesService.removeDetail(id);

    return {
      code: 200,
      data: null,
      message: '支出明细删除成功',
    };
  }

  @Get('export')
  @ApiOperation({ summary: '导出支出明细Excel' })
  @ApiQuery({
    name: 'startTime',
    description: '起始时间',
    example: '2023-01-01',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '结束时间',
    example: '2023-12-31',
    required: false,
  })
  @ApiQuery({
    name: 'detailIds',
    description:
      '明细ID列表（逗号分隔，可选）。如果不提供，则必须提供起始时间和终止时间进行时间范围导出',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiQuery({
    name: 'supplierSearch',
    description: '供应商搜索',
    example: 'SUP001',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  @ApiResponse({
    status: 400,
    description: '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
  })
  async exportDetailsToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Exporting expense details to Excel`);

    try {
      // 从原始请求中获取查询参数
      const { startTime, endTime, detailIds, supplierSearch } = req.query;

      // 验证：如果没有提供明细ID，必须提供时间范围
      if (!detailIds || detailIds.trim() === '') {
        if (!startTime || !endTime) {
          this.logger.warn('Export attempt without details or time range');
          return res.status(HttpStatus.BAD_REQUEST).json({
            code: 400,
            data: null,
            message:
              '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
          });
        }
      }

      // 构建导出参数
      const exportParams: any = {};

      if (startTime) {
        exportParams.startTime = decodeURIComponent(startTime);
      }

      if (endTime) {
        exportParams.endTime = decodeURIComponent(endTime);
      }

      // 处理明细ID列表（如果提供了的话）
      if (detailIds && detailIds.trim() !== '') {
        const decodedDetailIds = decodeURIComponent(detailIds);
        const detailIdArray = decodedDetailIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);

        // 验证处理后的ID数组
        if (detailIdArray.length === 0) {
          this.logger.warn(
            'Export attempt with empty detail IDs after processing',
          );
          return res.status(HttpStatus.BAD_REQUEST).json({
            code: 400,
            data: null,
            message: '明细ID格式不正确',
          });
        }

        exportParams.detailIds = detailIdArray;
        this.logger.log(
          `Exporting ${detailIdArray.length} selected expense details`,
        );
      } else {
        this.logger.log(
          `Exporting expense details by time range: ${startTime} to ${endTime}`,
        );
      }

      if (supplierSearch) {
        exportParams.supplierSearch = decodeURIComponent(supplierSearch);
      }

      const excelBuffer =
        await this.expensesService.exportDetailsToExcel(exportParams);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="expense-details-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }

  @Get('import/template')
  @ApiOperation({ summary: '下载支出明细导入模板' })
  @ApiResponse({
    status: 200,
    description: '模板文件下载成功',
  })
  async downloadImportTemplate(@Res() res: Response) {
    this.logger.log('Generating expense details import template');

    try {
      const templateBuffer =
        await this.expensesService.generateImportTemplate();

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        'attachment; filename="expense-details-import-template.xlsx"',
      );

      // 发送Excel文件
      res.send(templateBuffer);
    } catch (error) {
      this.logger.error('Template download failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: '模板下载失败',
      });
    }
  }

  @Post('import/excel')
  @ApiOperation({ summary: '从Excel导入支出明细数据' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: '导入完成',
    type: ExpenseImportResult,
  })
  @ApiResponse({
    status: 400,
    description: '文件格式错误或数据验证失败',
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (_req, file, callback) => {
        if (!file.originalname.match(/\.(xlsx|xls)$/)) {
          return callback(new Error('只支持Excel文件格式'), false);
        }
        callback(null, true);
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  async importFromExcel(@UploadedFile() file: Express.Multer.File) {
    this.logger.log(
      `Importing expense details from Excel: ${file?.originalname}`,
    );

    if (!file) {
      return {
        code: 400,
        data: null,
        message: '请选择要导入的Excel文件',
      };
    }

    try {
      const result = await this.expensesService.importFromExcel(file);

      return {
        code: 200,
        data: result,
        message: `导入完成：成功 ${result.successCount} 条，失败 ${result.failureCount} 条`,
      };
    } catch (error) {
      this.logger.error('Excel import failed', error);
      return {
        code: 400,
        data: null,
        message: `导入失败：${error.message}`,
      };
    }
  }
}
