import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Expense } from './expense.entity';

@Entity('expense_details')
export class ExpenseDetail {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', comment: '支出ID' })
  expenseId: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '供应商编码（必填）',
  })
  supplierCode: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '供应商名称（冗余字段，便于查询）',
  })
  supplierName: string | null;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '支出金额（保留两位小数）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '截图URL（选填）',
  })
  screenshot: string | null;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注（选填）',
  })
  remark: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    comment: '创建日期（字符串格式，必填）',
  })
  createDate: string;

  // 关联支出主表
  @ManyToOne(() => Expense, (expense) => expense.details)
  @JoinColumn({ name: 'expenseId' })
  expense: Expense;
}
