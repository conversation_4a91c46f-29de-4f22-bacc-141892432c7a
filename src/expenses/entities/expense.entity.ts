import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { ExpenseDetail } from './expense-detail.entity';

@Entity('expenses')
export class Expense {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '支出总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalAmount: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @Column({
    type: 'varchar',
    length: 20,
    comment: '创建日期（字符串格式，必填）',
  })
  createDate: string;

  // 关联支出明细
  @OneToMany(() => ExpenseDetail, (detail) => detail.expense)
  details: ExpenseDetail[];
}
