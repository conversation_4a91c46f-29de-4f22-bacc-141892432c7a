import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from '@/users/entities/user.entity';

@Injectable()
export class InitService implements OnModuleInit {
  private readonly logger = new Logger(InitService.name);

  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  async onModuleInit() {
    this.logger.log('Initializing application...');
    await this.createSuperAdmin();
    this.logger.log('Application initialization completed.');
  }

  private async createSuperAdmin() {
    const superAdminCode = 'husky';
    const password = '541888';
    const nickname = 'husky';

    try {
      // 检查超级管理员是否已存在
      const existingUser = await this.usersRepository.findOne({
        where: { code: superAdminCode, isDeleted: false },
      });

      if (existingUser) {
        this.logger.log('Super admin already exists, updating password...');
        // 更新密码
        const hashedPassword = await bcrypt.hash(password, 10);
        existingUser.password = hashedPassword;
        await this.usersRepository.save(existingUser);
        this.logger.log('Super admin password updated successfully.');
        return;
      }

      // 创建超级管理员用户
      this.logger.log('Creating super admin user...');

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 10);

      const superAdmin = this.usersRepository.create({
        code: superAdminCode,
        nickname: nickname,
        password: hashedPassword,
        routePermissions: null, // null表示拥有全部权限
        isSuperAdmin: true,
        isActive: true,
        isDeleted: false,
      });

      await this.usersRepository.save(superAdmin);

      this.logger.log(
        `Super admin user created successfully with code: ${superAdminCode}`,
      );
    } catch (error) {
      this.logger.error(`Failed to create super admin: ${error.message}`);
      throw error;
    }
  }
}
