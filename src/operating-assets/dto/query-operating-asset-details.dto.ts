import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import {
  OperatingAssetType,
  WarehouseLogisticsType,
} from '../entities/operating-asset-detail.entity';

export enum SortBy {
  CREATE_DATE = 'createDate',
  AMOUNT = 'amount',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class QueryOperatingAssetDetailsDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  pageSize?: number = 10;

  @ApiProperty({
    description: '运营资产类型筛选',
    enum: OperatingAssetType,
    example: OperatingAssetType.HUMAN_RESOURCES,
    required: false,
  })
  @IsOptional()
  @IsEnum(OperatingAssetType, { message: '运营资产类型无效' })
  type?: OperatingAssetType;

  @ApiProperty({
    description: '仓储物流收支类型筛选（仅当type为warehouse_logistics时有效）',
    enum: WarehouseLogisticsType,
    example: WarehouseLogisticsType.INCOME,
    required: false,
  })
  @IsOptional()
  @IsEnum(WarehouseLogisticsType, { message: '仓储物流收支类型无效' })
  warehouseLogisticsType?: WarehouseLogisticsType;

  @ApiProperty({
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiProperty({
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiProperty({
    description: '搜索关键词（备注）',
    example: '员工工资',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '排序字段',
    enum: SortBy,
    example: SortBy.CREATE_DATE,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortBy, { message: '排序字段无效' })
  sortBy?: SortBy = SortBy.CREATE_DATE;

  @ApiProperty({
    description: '排序方向',
    enum: SortOrder,
    example: SortOrder.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, { message: '排序方向无效' })
  sortOrder?: SortOrder = SortOrder.DESC;
}
