import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  <PERSON>s,
  Req,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { OperatingAssetsService } from './operating-assets.service';
import { CreateOperatingAssetDetailDto } from './dto/create-operating-asset-detail.dto';
import { UpdateOperatingAssetDetailDto } from './dto/update-operating-asset-detail.dto';
import { QueryOperatingAssetDetailsDto } from './dto/query-operating-asset-details.dto';
import {
  OperatingAssetType,
  WarehouseLogisticsType,
} from './entities/operating-asset-detail.entity';

@ApiTags('operating-assets')
@Controller('operating-assets')
export class OperatingAssetsController {
  private readonly logger = new Logger(OperatingAssetsController.name);

  constructor(
    private readonly operatingAssetsService: OperatingAssetsService,
  ) {}

  @Post('details')
  @ApiOperation({ summary: '新增运营资产明细' })
  @ApiResponse({
    status: 200,
    description: '运营资产明细创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async createDetail(@Body() createDetailDto: CreateOperatingAssetDetailDto) {
    this.logger.log(
      `Creating operating asset detail with type: ${createDetailDto.type}, amount: ${createDetailDto.amount}`,
    );

    await this.operatingAssetsService.createDetail(createDetailDto);

    return {
      code: 200,
      data: null,
      message: '运营资产明细创建成功',
    };
  }

  @Get('details')
  @ApiOperation({ summary: '分页查询运营资产明细' })
  @ApiQuery({
    name: 'page',
    description: '页码',
    example: 1,
    required: false,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量',
    example: 10,
    required: false,
  })
  @ApiQuery({
    name: 'type',
    description: '运营资产类型筛选',
    enum: OperatingAssetType,
    example: OperatingAssetType.HUMAN_RESOURCES,
    required: false,
  })
  @ApiQuery({
    name: 'warehouseLogisticsType',
    description: '仓储物流收支类型筛选（仅当type为warehouse_logistics时有效）',
    enum: WarehouseLogisticsType,
    example: WarehouseLogisticsType.INCOME,
    required: false,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词（备注）',
    example: '员工工资',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: '排序字段',
    enum: ['createDate', 'amount'],
    example: 'createDate',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: '排序方向',
    enum: ['ASC', 'DESC'],
    example: 'DESC',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllDetails(@Query() queryDto: QueryOperatingAssetDetailsDto) {
    this.logger.log(
      `Querying operating asset details with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.operatingAssetsService.findAllDetails(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('details/:id')
  @ApiOperation({ summary: '获取运营资产明细详情' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async findDetailById(@Param('id') id: string) {
    this.logger.log(`Finding operating asset detail by id: ${id}`);

    const detail = await this.operatingAssetsService.findDetailById(id);

    return {
      code: 200,
      data: detail,
      message: '查询成功',
    };
  }

  @Patch('details/:id')
  @ApiOperation({ summary: '更新运营资产明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '运营资产明细更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async updateDetail(
    @Param('id') id: string,
    @Body() updateDetailDto: UpdateOperatingAssetDetailDto,
  ) {
    this.logger.log(`Updating operating asset detail: ${id}`);

    await this.operatingAssetsService.updateDetail(id, updateDetailDto);

    return {
      code: 200,
      data: null,
      message: '运营资产明细更新成功',
    };
  }

  @Delete('details/:id')
  @ApiOperation({ summary: '删除运营资产明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '运营资产明细删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async removeDetail(@Param('id') id: string) {
    this.logger.log(`Removing operating asset detail: ${id}`);

    await this.operatingAssetsService.removeDetail(id);

    return {
      code: 200,
      data: null,
      message: '运营资产明细删除成功',
    };
  }

  @Get('export/excel')
  @ApiOperation({ summary: '导出运营资产明细为Excel' })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'type',
    description: '运营资产类型筛选',
    enum: OperatingAssetType,
    example: OperatingAssetType.HUMAN_RESOURCES,
    required: false,
  })
  @ApiQuery({
    name: 'warehouseLogisticsType',
    description: '仓储物流收支类型筛选（仅当type为warehouse_logistics时有效）',
    enum: WarehouseLogisticsType,
    example: WarehouseLogisticsType.INCOME,
    required: false,
  })
  @ApiQuery({
    name: 'detailIds',
    description: '选择的明细项目ID列表（逗号分隔）',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  async exportToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log('Exporting operating asset details to Excel');

    const { startTime, endTime, type, warehouseLogisticsType, detailIds } =
      req.query;

    // 处理导出参数
    const exportParams: any = {};

    if (startTime) {
      exportParams.startTime = startTime;
    }

    if (endTime) {
      exportParams.endTime = endTime;
    }

    if (type) {
      exportParams.type = type;
    }

    if (warehouseLogisticsType) {
      exportParams.warehouseLogisticsType = warehouseLogisticsType;
    }

    if (detailIds) {
      // 处理detailIds参数（逗号分隔的字符串转换为数组）
      // 先解码URL编码的字符串，然后分割
      const decodedDetailIds = decodeURIComponent(detailIds);
      exportParams.detailIds = decodedDetailIds
        .split(',')
        .map((id: string) => id.trim())
        .filter((id: string) => id.length > 0);
    }

    try {
      const excelBuffer =
        await this.operatingAssetsService.exportToExcel(exportParams);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="operating-assets-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }
}
