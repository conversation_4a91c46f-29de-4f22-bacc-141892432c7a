import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OperatingAssetsService } from './operating-assets.service';
import { OperatingAssetsController } from './operating-assets.controller';
import { OperatingAsset } from './entities/operating-asset.entity';
import { OperatingAssetDetail } from './entities/operating-asset-detail.entity';

@Module({
  imports: [TypeOrmModule.forFeature([OperatingAsset, OperatingAssetDetail])],
  controllers: [OperatingAssetsController],
  providers: [OperatingAssetsService],
  exports: [OperatingAssetsService],
})
export class OperatingAssetsModule {}
