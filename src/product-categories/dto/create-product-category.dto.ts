import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsArray } from 'class-validator';

export class CreateProductCategoryDto {
  @ApiProperty({
    description: '商品分类编码（必填，唯一）',
    example: 'CAT001',
  })
  @IsString()
  @IsNotEmpty({ message: '商品分类编码不能为空' })
  code: string;

  @ApiProperty({
    description: '商品分类名称（必填）',
    example: '上衣',
  })
  @IsString()
  @IsNotEmpty({ message: '商品分类名称不能为空' })
  name: string;

  @ApiProperty({
    description: '尺码数组（可选）',
    example: ['S', 'M', 'L', 'XL'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: '尺码必须是数组格式' })
  @IsString({ each: true, message: '尺码数组中的每个元素必须是字符串' })
  sizes?: string[];
}
