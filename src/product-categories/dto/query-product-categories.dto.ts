import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, Min, IsOptional, IsString } from 'class-validator';

export class QueryProductCategoriesDto {
  @ApiProperty({
    description: '页码（必填，从1开始，page=0&pageSize=0表示获取所有数据）',
    example: 1,
  })
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(0, { message: '页码不能小于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填，page=0&pageSize=0表示获取所有数据）',
    example: 10,
  })
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(0, { message: '每页数量不能小于0' })
  pageSize: number;

  @ApiProperty({
    description: '搜索关键词（可选，模糊搜索编码和名称）',
    example: '上衣',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}
