import { ApiProperty } from '@nestjs/swagger';

export class ProductCategoryDto {
  @ApiProperty({ description: '商品分类ID', example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' })
  id: string;

  @ApiProperty({ description: '商品分类编码', example: 'CAT001' })
  code: string;

  @ApiProperty({ description: '商品分类名称', example: '上衣' })
  name: string;

  @ApiProperty({ 
    description: '尺码数组', 
    example: ['S', 'M', 'L', 'XL'],
    required: false,
    type: [String]
  })
  sizes: string[] | null;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' })
  updatedAt: Date;
}

export class ProductCategoryListResponseDto {
  @ApiProperty({ 
    description: '商品分类列表', 
    type: [ProductCategoryDto] 
  })
  categories: ProductCategoryDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}
