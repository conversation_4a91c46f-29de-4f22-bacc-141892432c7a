import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ProductCategoriesService } from './product-categories.service';
import { CreateProductCategoryDto } from './dto/create-product-category.dto';
import { UpdateProductCategoryDto } from './dto/update-product-category.dto';
import { QueryProductCategoriesDto } from './dto/query-product-categories.dto';
import { ProductCategoryListResponseDto } from './dto/product-category-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('product-categories')
@Controller('product-categories')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProductCategoriesController {
  private readonly logger = new Logger(ProductCategoriesController.name);

  constructor(private readonly productCategoriesService: ProductCategoriesService) {}

  @Post()
  @ApiOperation({ summary: '创建商品分类' })
  @ApiResponse({
    status: 200,
    description: '商品分类创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '商品分类编码已存在',
  })
  async create(@Body() createProductCategoryDto: CreateProductCategoryDto) {
    this.logger.log(`Creating product category ${createProductCategoryDto.code}`);

    await this.productCategoriesService.create(createProductCategoryDto);

    return {
      code: 200,
      data: null,
      message: '商品分类创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取商品分类列表（支持模糊搜索）' })
  @ApiResponse({
    status: 200,
    description: '获取商品分类列表成功',
    type: ProductCategoryListResponseDto,
  })
  async findAll(@Query() queryDto: QueryProductCategoriesDto) {
    this.logger.log(`Querying product categories with params: ${JSON.stringify(queryDto)}`);

    const result = await this.productCategoriesService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取商品分类列表成功',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取商品分类详情' })
  @ApiParam({
    name: 'id',
    description: '商品分类ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '获取商品分类详情成功',
  })
  @ApiResponse({
    status: 404,
    description: '商品分类不存在',
  })
  async findOne(@Param('id') id: string) {
    this.logger.log(`Getting product category details for id: ${id}`);

    const category = await this.productCategoriesService.findOne(id);

    return {
      code: 200,
      data: {
        id: category.id,
        code: category.code,
        name: category.name,
        sizes: category.sizes,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt,
      },
      message: '获取商品分类详情成功',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新商品分类信息' })
  @ApiParam({
    name: 'id',
    description: '商品分类ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '商品分类更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '商品分类不存在',
  })
  async update(
    @Param('id') id: string,
    @Body() updateProductCategoryDto: UpdateProductCategoryDto,
  ) {
    this.logger.log(`Updating product category ${id}`);

    await this.productCategoriesService.update(id, updateProductCategoryDto);

    return {
      code: 200,
      data: null,
      message: '商品分类更新成功',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除商品分类（软删除）' })
  @ApiParam({
    name: 'id',
    description: '商品分类ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '商品分类删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '商品分类不存在',
  })
  async remove(@Param('id') id: string) {
    this.logger.log(`Deleting product category ${id}`);

    await this.productCategoriesService.remove(id);

    return {
      code: 200,
      data: null,
      message: '商品分类删除成功',
    };
  }
}
