import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductCategory } from './entities/product-category.entity';
import { CreateProductCategoryDto } from './dto/create-product-category.dto';
import { UpdateProductCategoryDto } from './dto/update-product-category.dto';
import { QueryProductCategoriesDto } from './dto/query-product-categories.dto';

@Injectable()
export class ProductCategoriesService {
  private readonly logger = new Logger(ProductCategoriesService.name);

  constructor(
    @InjectRepository(ProductCategory)
    private productCategoriesRepository: Repository<ProductCategory>,
  ) {}

  /**
   * 创建商品分类
   */
  async create(createProductCategoryDto: CreateProductCategoryDto): Promise<ProductCategory> {
    this.logger.log(`Creating product category with code: ${createProductCategoryDto.code}`);

    // 检查商品分类编码是否已存在
    const existingCategory = await this.productCategoriesRepository.findOne({
      where: { code: createProductCategoryDto.code, isDeleted: false },
    });

    if (existingCategory) {
      throw new BadRequestException('商品分类编码已存在');
    }

    // 创建商品分类
    const category = this.productCategoriesRepository.create({
      ...createProductCategoryDto,
      sizes: createProductCategoryDto.sizes || null,
      isDeleted: false,
    });

    return await this.productCategoriesRepository.save(category);
  }

  /**
   * 分页查询商品分类列表（支持模糊搜索）
   */
  async findAll(queryDto: QueryProductCategoriesDto) {
    this.logger.log(`Querying product categories with params: ${JSON.stringify(queryDto)}`);

    const { page, pageSize, search } = queryDto;

    // page和pageSize是必须参数
    if (page === undefined || pageSize === undefined) {
      return {
        categories: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // 处理分页参数：page=0&pageSize=0表示获取所有数据
    const isGetAll = page === 0 && pageSize === 0;
    const finalPage = isGetAll ? 1 : page;
    const finalPageSize = isGetAll ? undefined : pageSize;
    const skip = isGetAll ? 0 : (page - 1) * pageSize;

    const queryBuilder = this.productCategoriesRepository
      .createQueryBuilder('category')
      .where('category.isDeleted = :isDeleted', { isDeleted: false });

    // 模糊搜索商品分类编码和名称
    if (search) {
      queryBuilder.andWhere(
        '(category.code ILIKE :search OR category.name ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const categories = await queryBuilder
      .select([
        'category.id',
        'category.code',
        'category.name',
        'category.sizes',
        'category.createdAt',
        'category.updatedAt',
      ])
      .orderBy('category.createdAt', 'DESC')
      .skip(skip)
      .take(finalPageSize)
      .getMany();

    return {
      categories,
      total,
      page: isGetAll ? 0 : finalPage,
      pageSize: isGetAll ? 0 : finalPageSize!,
      totalPages: isGetAll ? 1 : Math.ceil(total / finalPageSize!),
    };
  }

  /**
   * 根据ID查找商品分类
   */
  async findById(id: string): Promise<ProductCategory | null> {
    return await this.productCategoriesRepository.findOne({
      where: { id, isDeleted: false },
    });
  }

  /**
   * 根据ID获取商品分类详情
   */
  async findOne(id: string): Promise<ProductCategory> {
    const category = await this.findById(id);
    if (!category) {
      throw new NotFoundException('商品分类不存在');
    }
    return category;
  }

  /**
   * 更新商品分类
   */
  async update(id: string, updateProductCategoryDto: UpdateProductCategoryDto): Promise<ProductCategory> {
    this.logger.log(`Updating product category ${id}`);

    // 查找要更新的商品分类
    const category = await this.findOne(id);

    // 更新字段
    if (updateProductCategoryDto.name !== undefined) {
      category.name = updateProductCategoryDto.name;
    }
    if (updateProductCategoryDto.sizes !== undefined) {
      category.sizes = updateProductCategoryDto.sizes || null;
    }

    return await this.productCategoriesRepository.save(category);
  }

  /**
   * 软删除商品分类
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`Soft deleting product category ${id}`);

    // 查找要删除的商品分类
    const category = await this.findOne(id);

    // 软删除
    category.isDeleted = true;
    category.deletedAt = new Date();
    await this.productCategoriesRepository.save(category);
  }
}
