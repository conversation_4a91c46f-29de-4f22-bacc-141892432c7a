import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('product_categories')
export class ProductCategory {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '商品分类ID（UUID）', example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' })
  id: string;

  @Column({ type: 'varchar', length: 50, unique: true })
  @ApiProperty({ description: '商品分类编码（唯一）', example: 'CAT001' })
  code: string;

  @Column({ type: 'varchar', length: 100 })
  @ApiProperty({ description: '商品分类名称', example: '上衣' })
  name: string;

  @Column({ type: 'text', array: true, nullable: true })
  @ApiProperty({ 
    description: '尺码数组（可选）', 
    example: ['S', 'M', 'L', 'XL'],
    required: false,
    type: [String]
  })
  sizes: string[] | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;
}
