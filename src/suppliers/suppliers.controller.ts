import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Logger,
  UseInterceptors,
  UploadedFile,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { SuppliersService } from './suppliers.service';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { QuerySuppliersDto } from './dto/query-suppliers.dto';
import {
  SupplierResponseDto,
  SupplierListResponseDto,
} from './dto/supplier-response.dto';
import { SupplierImportResult } from './dto/import-supplier.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('suppliers')
@Controller('suppliers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SuppliersController {
  private readonly logger = new Logger(SuppliersController.name);

  constructor(private readonly suppliersService: SuppliersService) {}

  @Post()
  @ApiOperation({ summary: '创建供应商' })
  @ApiResponse({
    status: 200,
    description: '供应商创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '供应商编码已存在',
  })
  async create(@Body() createSupplierDto: CreateSupplierDto) {
    this.logger.log(`Creating supplier ${createSupplierDto.code}`);

    await this.suppliersService.create(createSupplierDto);

    return {
      code: 200,
      data: null,
      message: '供应商创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取供应商列表（支持模糊搜索）' })
  @ApiResponse({
    status: 200,
    description: '获取供应商列表成功',
    type: SupplierListResponseDto,
  })
  async findAll(@Query() queryDto: QuerySuppliersDto) {
    this.logger.log(
      `Querying suppliers with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.suppliersService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取供应商列表成功',
    };
  }

  @Get(':code')
  @ApiOperation({ summary: '根据供应商编码获取供应商详情' })
  @ApiParam({
    name: 'code',
    description: '供应商编码',
    example: 'SUP001',
  })
  @ApiResponse({
    status: 200,
    description: '获取供应商详情成功',
    type: SupplierResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '供应商不存在',
  })
  async findOne(@Param('code') code: string) {
    this.logger.log(`Getting supplier details for code: ${code}`);

    const supplier = await this.suppliersService.findOne(code);

    return {
      code: 200,
      data: supplier,
      message: '获取供应商详情成功',
    };
  }

  @Patch(':code')
  @ApiOperation({ summary: '更新供应商信息' })
  @ApiParam({
    name: 'code',
    description: '供应商编码',
    example: 'SUP001',
  })
  @ApiResponse({
    status: 200,
    description: '供应商更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '供应商不存在',
  })
  async update(
    @Param('code') code: string,
    @Body() updateSupplierDto: UpdateSupplierDto,
  ) {
    this.logger.log(`Updating supplier ${code}`);

    await this.suppliersService.update(code, updateSupplierDto);

    return {
      code: 200,
      data: null,
      message: '供应商更新成功',
    };
  }

  @Delete(':code')
  @ApiOperation({ summary: '删除供应商（软删除）' })
  @ApiParam({
    name: 'code',
    description: '供应商编码',
    example: 'SUP001',
  })
  @ApiResponse({
    status: 200,
    description: '供应商删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '供应商不存在',
  })
  async remove(@Param('code') code: string) {
    this.logger.log(`Deleting supplier ${code}`);

    await this.suppliersService.remove(code);

    return {
      code: 200,
      data: null,
      message: '供应商删除成功',
    };
  }

  @Get('export/excel')
  @ApiOperation({ summary: '导出供应商数据为Excel' })
  @ApiResponse({
    status: 200,
    description: '导出成功',
    headers: {
      'Content-Type': {
        description:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
      'Content-Disposition': {
        description: 'attachment; filename="suppliers.xlsx"',
      },
    },
  })
  async exportToExcel(@Res() res: Response) {
    this.logger.log('Exporting suppliers to Excel');

    const excelBuffer = await this.suppliersService.exportToExcel();

    const filename = `suppliers_${new Date().toISOString().split('T')[0]}.xlsx`;

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Content-Length': excelBuffer.length.toString(),
    });

    res.send(excelBuffer);
  }

  @Get('import/template')
  @ApiOperation({ summary: '下载供应商导入模板' })
  @ApiResponse({
    status: 200,
    description: '模板下载成功',
    headers: {
      'Content-Type': {
        description:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
      'Content-Disposition': {
        description: 'attachment; filename="supplier_import_template.xlsx"',
      },
    },
  })
  async downloadImportTemplate(@Res() res: Response) {
    this.logger.log('Generating import template');

    const templateBuffer = await this.suppliersService.generateImportTemplate();

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition':
        'attachment; filename="supplier_import_template.xlsx"',
      'Content-Length': templateBuffer.length.toString(),
    });

    res.send(templateBuffer);
  }

  @Post('import/excel')
  @ApiOperation({ summary: '从Excel导入供应商数据' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: '导入完成',
    type: SupplierImportResult,
  })
  @ApiResponse({
    status: 400,
    description: '文件格式错误或数据验证失败',
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (_req, file, callback) => {
        if (!file.originalname.match(/\.(xlsx|xls)$/)) {
          return callback(new Error('只支持Excel文件格式'), false);
        }
        callback(null, true);
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  async importFromExcel(@UploadedFile() file: Express.Multer.File) {
    this.logger.log(`Importing suppliers from Excel: ${file?.originalname}`);

    if (!file) {
      return {
        code: 400,
        data: null,
        message: '请选择要导入的Excel文件',
      };
    }

    const result = await this.suppliersService.importFromExcel(file);

    return {
      code: 200,
      data: result,
      message: `导入完成：成功 ${result.successCount} 条，失败 ${result.failureCount} 条`,
    };
  }
}
