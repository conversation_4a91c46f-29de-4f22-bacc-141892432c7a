import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('suppliers')
export class Supplier {
  @PrimaryColumn({ type: 'varchar', length: 50 })
  @ApiProperty({ description: '供应商编码，作为主键', example: 'SUP001' })
  code: string;

  @Column({ type: 'varchar', length: 100 })
  @ApiProperty({ description: '供应商名称（必填）', example: '广州服装供应商' })
  name: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  @ApiProperty({ 
    description: '供应商地址', 
    example: '广州市白云区服装批发市场A区101号',
    required: false 
  })
  address: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @ApiProperty({ 
    description: '联系人姓名', 
    example: '张三',
    required: false 
  })
  contactName: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true })
  @ApiProperty({ 
    description: '联系人手机号', 
    example: '13800138000',
    required: false 
  })
  contactPhone: string | null;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 软删除字段
  @Column({ default: false })
  @ApiProperty({ description: '是否已删除（软删除标记）' })
  isDeleted: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  @ApiProperty({ description: '删除时间', required: false })
  deletedAt: Date | null;
}
