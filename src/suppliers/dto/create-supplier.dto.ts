import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsMobilePhone,
  ValidateIf,
} from 'class-validator';

export class CreateSupplierDto {
  @ApiProperty({
    description: '供应商编码（主键）',
    example: 'SUP001',
  })
  @IsString()
  @IsNotEmpty({ message: '供应商编码不能为空' })
  code: string;

  @ApiProperty({
    description: '供应商名称（必填）',
    example: '广州服装供应商',
  })
  @IsString()
  @IsNotEmpty({ message: '供应商名称不能为空' })
  name: string;

  @ApiProperty({
    description: '供应商地址',
    example: '广州市白云区服装批发市场A区101号',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: '联系人姓名',
    example: '张三',
    required: false,
  })
  @IsOptional()
  @IsString()
  contactName?: string;

  @ApiProperty({
    description: '联系人手机号',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.contactPhone && o.contactPhone.trim() !== '')
  @IsMobilePhone('zh-CN', {}, { message: '请输入有效的手机号码' })
  contactPhone?: string;
}
