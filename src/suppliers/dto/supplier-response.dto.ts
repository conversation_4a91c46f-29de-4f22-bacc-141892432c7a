import { ApiProperty } from '@nestjs/swagger';

export class SupplierResponseDto {
  @ApiProperty({ description: '供应商编码', example: 'SUP001' })
  code: string;

  @ApiProperty({ description: '供应商名称', example: '广州服装供应商' })
  name: string;

  @ApiProperty({ 
    description: '供应商地址', 
    example: '广州市白云区服装批发市场A区101号',
    required: false 
  })
  address: string | null;

  @ApiProperty({ 
    description: '联系人姓名', 
    example: '张三',
    required: false 
  })
  contactName: string | null;

  @ApiProperty({ 
    description: '联系人手机号', 
    example: '13800138000',
    required: false 
  })
  contactPhone: string | null;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class SupplierListResponseDto {
  @ApiProperty({ 
    description: '供应商列表', 
    type: [SupplierResponseDto] 
  })
  suppliers: SupplierResponseDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}
