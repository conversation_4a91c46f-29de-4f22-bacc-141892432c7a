import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class UpdateSupplierDto {
  @ApiProperty({
    description: '供应商名称',
    example: '广州服装供应商',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: '供应商地址',
    example: '广州市白云区服装批发市场A区101号',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: '联系人姓名',
    example: '张三',
    required: false,
  })
  @IsOptional()
  @IsString()
  contactName?: string;

  @ApiProperty({
    description: '联系人手机号',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  @IsString()
  contactPhone?: string;
}
