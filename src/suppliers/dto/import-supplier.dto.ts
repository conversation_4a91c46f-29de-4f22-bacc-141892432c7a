import { ApiProperty } from '@nestjs/swagger';

export class ImportSupplierDto {
  @ApiProperty({
    description: 'Excel文件',
    type: 'string',
    format: 'binary',
  })
  file: Express.Multer.File;
}

export class SupplierImportResult {
  @ApiProperty({ description: '成功导入的数量' })
  successCount: number;

  @ApiProperty({ description: '失败的数量' })
  failureCount: number;

  @ApiProperty({ description: '错误详情', type: [String] })
  errors: string[];

  @ApiProperty({ description: '成功导入的供应商编码', type: [String] })
  successCodes: string[];
}

export class SupplierExcelRow {
  @ApiProperty({ description: '供应商编码（必填）' })
  code: string;

  @ApiProperty({ description: '供应商名称（必填）' })
  name: string;

  @ApiProperty({ description: '供应商地址（可选）' })
  address?: string;

  @ApiProperty({ description: '联系人姓名（可选）' })
  contactName?: string;

  @ApiProperty({ description: '联系人手机号（可选）' })
  contactPhone?: string;
}
