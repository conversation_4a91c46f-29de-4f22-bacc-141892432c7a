import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class QuerySuppliersDto {
  @ApiProperty({
    description: '页码（必填，0表示获取所有数据）',
    example: 1,
    required: true,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(0, { message: '页码必须大于等于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填，0表示获取所有数据）',
    example: 10,
    required: true,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(0, { message: '每页数量必须大于等于0' })
  pageSize: number;

  @ApiProperty({
    description: '搜索关键词（模糊搜索供应商编码和名称）',
    example: 'SUP001',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}
