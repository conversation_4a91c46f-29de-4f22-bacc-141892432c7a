import {
  Injectable,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as XLSX from 'xlsx';
import { Supplier } from './entities/supplier.entity';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { QuerySuppliersDto } from './dto/query-suppliers.dto';
import { SupplierImportResult } from './dto/import-supplier.dto';

@Injectable()
export class SuppliersService {
  private readonly logger = new Logger(SuppliersService.name);

  constructor(
    @InjectRepository(Supplier)
    private suppliersRepository: Repository<Supplier>,
  ) {}

  /**
   * 创建供应商
   */
  async create(createSupplierDto: CreateSupplierDto): Promise<Supplier> {
    this.logger.log(`Creating supplier with code: ${createSupplierDto.code}`);

    // 检查供应商编码是否已存在
    const existingSupplier = await this.suppliersRepository.findOne({
      where: { code: createSupplierDto.code, isDeleted: false },
    });

    if (existingSupplier) {
      throw new BadRequestException('供应商编码已存在');
    }

    // 创建供应商
    const supplier = this.suppliersRepository.create({
      ...createSupplierDto,
      isDeleted: false,
    });

    return await this.suppliersRepository.save(supplier);
  }

  /**
   * 分页查询供应商列表（支持模糊搜索）
   */
  async findAll(queryDto: QuerySuppliersDto) {
    this.logger.log(
      `Querying suppliers with params: ${JSON.stringify(queryDto)}`,
    );

    const { page, pageSize, search } = queryDto;

    // page和pageSize是必须参数
    if (page === undefined || pageSize === undefined) {
      return {
        suppliers: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // 处理分页参数：page=0&pageSize=0表示获取所有数据
    const isGetAll = page === 0 && pageSize === 0;
    const finalPage = isGetAll ? 1 : page;
    const finalPageSize = isGetAll ? undefined : pageSize;
    const skip = isGetAll ? 0 : (page - 1) * pageSize;

    const queryBuilder = this.suppliersRepository
      .createQueryBuilder('supplier')
      .where('supplier.isDeleted = :isDeleted', { isDeleted: false });

    // 模糊搜索供应商编码和名称
    if (search) {
      queryBuilder.andWhere(
        '(supplier.code ILIKE :search OR supplier.name ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const suppliers = await queryBuilder
      .select([
        'supplier.code',
        'supplier.name',
        'supplier.address',
        'supplier.contactName',
        'supplier.contactPhone',
        'supplier.createdAt',
        'supplier.updatedAt',
      ])
      .orderBy('supplier.createdAt', 'DESC')
      .skip(skip)
      .take(finalPageSize)
      .getMany();

    return {
      suppliers,
      total,
      page: isGetAll ? 0 : finalPage,
      pageSize: isGetAll ? 0 : finalPageSize!,
      totalPages: isGetAll ? 1 : Math.ceil(total / finalPageSize!),
    };
  }

  /**
   * 根据供应商编码查找供应商
   */
  async findByCode(code: string): Promise<Supplier | null> {
    return await this.suppliersRepository.findOne({
      where: { code, isDeleted: false },
    });
  }

  /**
   * 根据供应商编码获取供应商详情
   */
  async findOne(code: string): Promise<Supplier> {
    const supplier = await this.findByCode(code);
    if (!supplier) {
      throw new NotFoundException('供应商不存在');
    }
    return supplier;
  }

  /**
   * 更新供应商
   */
  async update(
    code: string,
    updateSupplierDto: UpdateSupplierDto,
  ): Promise<Supplier> {
    this.logger.log(`Updating supplier ${code}`);

    // 查找要更新的供应商
    const supplier = await this.findOne(code);

    // 更新供应商信息
    Object.assign(supplier, updateSupplierDto);
    return await this.suppliersRepository.save(supplier);
  }

  /**
   * 软删除供应商
   */
  async remove(code: string): Promise<void> {
    this.logger.log(`Soft deleting supplier ${code}`);

    // 查找要删除的供应商
    const supplier = await this.findOne(code);

    // 软删除
    supplier.isDeleted = true;
    supplier.deletedAt = new Date();
    await this.suppliersRepository.save(supplier);
  }

  /**
   * 统计供应商数量
   */
  async countSuppliers(): Promise<number> {
    return await this.suppliersRepository.count({
      where: { isDeleted: false },
    });
  }

  /**
   * 导出供应商数据为Excel
   */
  async exportToExcel(): Promise<Buffer> {
    this.logger.log('Exporting suppliers to Excel');

    // 获取所有供应商数据
    const suppliers = await this.suppliersRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });

    // 准备Excel数据
    const excelData = suppliers.map((supplier) => ({
      供应商编码: supplier.code,
      供应商名称: supplier.name,
      供应商地址: supplier.address || '',
      联系人姓名: supplier.contactName || '',
      联系人手机号: supplier.contactPhone || '',
      创建时间: supplier.createdAt.toISOString().split('T')[0],
    }));

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 供应商编码
      { wch: 20 }, // 供应商名称
      { wch: 30 }, // 供应商地址
      { wch: 15 }, // 联系人姓名
      { wch: 15 }, // 联系人手机号
      { wch: 12 }, // 创建时间
    ];
    worksheet['!cols'] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '供应商列表');

    // 生成Excel文件Buffer
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });
    return excelBuffer;
  }

  /**
   * 生成导入示例Excel文件
   */
  async generateImportTemplate(): Promise<Buffer> {
    this.logger.log('Generating import template Excel');

    // 示例数据
    const templateData = [
      {
        供应商编码: 'SUP001',
        供应商名称: '广州服装供应商',
        供应商地址: '广州市白云区服装批发市场A区101号',
        联系人姓名: '张三',
        联系人手机号: '13800138000',
      },
      {
        供应商编码: 'SUP002',
        供应商名称: '深圳电子供应商',
        供应商地址: '深圳市南山区科技园',
        联系人姓名: '李四',
        联系人手机号: '13900139000',
      },
    ];

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(templateData);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 供应商编码
      { wch: 20 }, // 供应商名称
      { wch: 30 }, // 供应商地址
      { wch: 15 }, // 联系人姓名
      { wch: 15 }, // 联系人手机号
    ];
    worksheet['!cols'] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '供应商导入模板');

    // 生成Excel文件Buffer
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });
    return excelBuffer;
  }

  /**
   * 从Excel导入供应商数据
   */
  async importFromExcel(
    file: Express.Multer.File,
  ): Promise<SupplierImportResult> {
    this.logger.log(`Importing suppliers from Excel: ${file.originalname}`);

    const result: SupplierImportResult = {
      successCount: 0,
      failureCount: 0,
      errors: [],
      successCodes: [],
    };

    try {
      // 读取Excel文件
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 将工作表转换为JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (!jsonData || jsonData.length === 0) {
        throw new BadRequestException('Excel文件为空或格式不正确');
      }

      // 处理每一行数据
      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i] as any;
        const rowNumber = i + 2; // Excel行号（从第2行开始，第1行是标题）

        try {
          // 验证必填字段
          const code = this.getExcelCellValue(row, ['供应商编码', 'code']);
          const name = this.getExcelCellValue(row, ['供应商名称', 'name']);

          if (!code || !name) {
            result.errors.push(`第${rowNumber}行：供应商编码和名称为必填项`);
            result.failureCount++;
            continue;
          }

          // 检查编码是否已存在
          const existingSupplier = await this.suppliersRepository.findOne({
            where: { code: code.toString(), isDeleted: false },
          });

          if (existingSupplier) {
            result.errors.push(`第${rowNumber}行：供应商编码 ${code} 已存在`);
            result.failureCount++;
            continue;
          }

          // 获取可选字段
          const address = this.getExcelCellValue(row, [
            '供应商地址',
            'address',
          ]);
          const contactName = this.getExcelCellValue(row, [
            '联系人姓名',
            'contactName',
          ]);
          const contactPhone = this.getExcelCellValue(row, [
            '联系人手机号',
            'contactPhone',
          ]);

          // 创建供应商
          const supplier = this.suppliersRepository.create({
            code: code.toString(),
            name: name.toString(),
            address: address ? address.toString() : null,
            contactName: contactName ? contactName.toString() : null,
            contactPhone: contactPhone ? contactPhone.toString() : null,
            isDeleted: false,
          });

          await this.suppliersRepository.save(supplier);

          result.successCount++;
          result.successCodes.push(code.toString());
        } catch (error) {
          result.errors.push(`第${rowNumber}行：${error.message}`);
          result.failureCount++;
        }
      }

      this.logger.log(
        `Import completed: ${result.successCount} success, ${result.failureCount} failures`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Import failed: ${error.message}`);
      throw new BadRequestException(`导入失败: ${error.message}`);
    }
  }

  /**
   * 从Excel行中获取单元格值（支持多种列名）
   */
  private getExcelCellValue(row: any, possibleKeys: string[]): any {
    for (const key of possibleKeys) {
      if (row[key] !== undefined && row[key] !== null && row[key] !== '') {
        return row[key];
      }
    }
    return null;
  }
}
