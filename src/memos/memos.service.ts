import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between } from 'typeorm';
import { Memo } from './entities/memo.entity';
import { CreateMemoDto } from './dto/create-memo.dto';
import { UpdateMemoDto } from './dto/update-memo.dto';
import { QueryMemosDto } from './dto/query-memos.dto';
import { MemoListResponseDto } from './dto/memo-response.dto';

@Injectable()
export class MemosService {
  private readonly logger = new Logger(MemosService.name);

  constructor(
    @InjectRepository(Memo)
    private memosRepository: Repository<Memo>,
  ) {}

  /**
   * 创建备忘录
   */
  async create(createMemoDto: CreateMemoDto): Promise<Memo> {
    this.logger.log(`Creating memo with title: ${createMemoDto.title}`);

    const memo = this.memosRepository.create({
      title: createMemoDto.title,
      details: createMemoDto.details || null,
      image: createMemoDto.image || null,
      file: createMemoDto.file || null,
      isDeleted: false,
    });

    const savedMemo = await this.memosRepository.save(memo);
    this.logger.log(`Memo created successfully with ID: ${savedMemo.id}`);

    return savedMemo;
  }

  /**
   * 分页查询备忘录列表
   */
  async findAll(queryDto: QueryMemosDto): Promise<MemoListResponseDto> {
    this.logger.log(`Querying memos with params: ${JSON.stringify(queryDto)}`);

    const {
      page = 1,
      pageSize = 10,
      title,
      startDate,
      endDate,
      sortOrder = 'DESC',
    } = queryDto;

    // 处理排序参数，空字符串时使用默认降序
    const finalSortOrder = sortOrder === '' ? 'DESC' : sortOrder;

    // 构建查询条件
    const where: any = {
      isDeleted: false, // 只查询未删除的记录
    };

    // 标题模糊搜索
    if (title) {
      where.title = Like(`%${title}%`);
    }

    // 日期范围过滤
    if (startDate && endDate) {
      where.createdAt = Between(
        new Date(startDate + ' 00:00:00'),
        new Date(endDate + ' 23:59:59'),
      );
    } else if (startDate) {
      where.createdAt = Between(
        new Date(startDate + ' 00:00:00'),
        new Date('9999-12-31 23:59:59'),
      );
    } else if (endDate) {
      where.createdAt = Between(
        new Date('1900-01-01 00:00:00'),
        new Date(endDate + ' 23:59:59'),
      );
    }

    // 执行分页查询
    const [items, total] = await this.memosRepository.findAndCount({
      where,
      order: { createdAt: finalSortOrder },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    const totalPages = Math.ceil(total / pageSize);

    // 截断详情字段为前10个字符
    const processedItems = items.map((item) => ({
      ...item,
      details: item.details
        ? item.details.length > 10
          ? item.details.substring(0, 10) + '...'
          : item.details
        : null,
    }));

    this.logger.log(
      `Found ${total} memos, returning page ${page}/${totalPages}`,
    );

    return {
      items: processedItems,
      total,
      page,
      pageSize,
      totalPages,
    };
  }

  /**
   * 根据ID查找备忘录
   */
  async findOne(id: string): Promise<Memo> {
    this.logger.log(`Finding memo with ID: ${id}`);

    const memo = await this.memosRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!memo) {
      throw new NotFoundException(`备忘录不存在 (ID: ${id})`);
    }

    return memo;
  }

  /**
   * 更新备忘录
   */
  async update(id: string, updateMemoDto: UpdateMemoDto): Promise<Memo> {
    this.logger.log(
      `Updating memo ${id} with data: ${JSON.stringify(updateMemoDto)}`,
    );

    // 先查找备忘录是否存在
    const memo = await this.findOne(id);

    // 更新字段
    if (updateMemoDto.title !== undefined) {
      memo.title = updateMemoDto.title;
    }
    if (updateMemoDto.details !== undefined) {
      memo.details = updateMemoDto.details || null;
    }
    if (updateMemoDto.image !== undefined) {
      memo.image = updateMemoDto.image || null;
    }
    if (updateMemoDto.file !== undefined) {
      memo.file = updateMemoDto.file || null;
    }

    const updatedMemo = await this.memosRepository.save(memo);
    this.logger.log(`Memo updated successfully: ${id}`);

    return updatedMemo;
  }

  /**
   * 删除备忘录（软删除）
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`Soft deleting memo: ${id}`);

    // 先查找备忘录是否存在
    const memo = await this.findOne(id);

    // 执行软删除
    memo.isDeleted = true;
    memo.deletedAt = new Date();
    await this.memosRepository.save(memo);

    this.logger.log(`Memo soft deleted successfully: ${id}`);
  }
}
