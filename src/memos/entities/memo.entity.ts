import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('memos')
export class Memo {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '备忘录UUID，作为主键',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @ApiProperty({
    description: '备忘录标题（必填）',
    example: '重要会议安排',
  })
  title: string;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: '备忘录详细内容（可选）',
    example: '明天下午3点在会议室A召开季度总结会议',
    required: false,
  })
  details: string | null;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @ApiProperty({
    description: '图片链接（可选）',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  image: string | null;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @ApiProperty({
    description: '文件链接（可选）',
    example: 'https://example.com/document.pdf',
    required: false,
  })
  file: string | null;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 软删除字段
  @Column({ default: false })
  @ApiProperty({ description: '是否已删除（软删除标记）' })
  isDeleted: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  @ApiProperty({ description: '删除时间', required: false })
  deletedAt: Date | null;
}
