import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsDateString,
  IsInt,
  Min,
  IsIn,
} from 'class-validator';
import { Type } from 'class-transformer';

export class QueryMemosDto {
  @ApiProperty({
    description: '页码（从1开始）',
    example: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码必须大于0' })
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量必须大于0' })
  pageSize?: number = 10;

  @ApiProperty({
    description: '标题搜索关键词（模糊搜索）',
    example: '会议',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: '开始日期（YYYY-MM-DD格式）',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '开始日期格式不正确，请使用YYYY-MM-DD格式' })
  startDate?: string;

  @ApiProperty({
    description: '结束日期（YYYY-MM-DD格式）',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '结束日期格式不正确，请使用YYYY-MM-DD格式' })
  endDate?: string;

  @ApiProperty({
    description: '创建日期排序方式（可选，空字符串表示使用默认排序）',
    example: 'DESC',
    required: false,
    default: 'DESC',
    enum: ['ASC', 'DESC', ''],
  })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC', ''], {
    message: '排序方式只能是 ASC（升序）、DESC（降序）或空字符串',
  })
  sortOrder?: 'ASC' | 'DESC' | '' = 'DESC';
}
