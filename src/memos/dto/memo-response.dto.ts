import { ApiProperty } from '@nestjs/swagger';

export class MemoResponseDto {
  @ApiProperty({ 
    description: '备忘录UUID', 
    example: '550e8400-e29b-41d4-a716-************' 
  })
  id: string;

  @ApiProperty({ 
    description: '备忘录标题', 
    example: '重要会议安排' 
  })
  title: string;

  @ApiProperty({ 
    description: '备忘录详细内容', 
    example: '明天下午3点在会议室A召开季度总结会议',
    required: false 
  })
  details: string | null;

  @ApiProperty({ 
    description: '图片链接', 
    example: 'https://example.com/image.jpg',
    required: false 
  })
  image: string | null;

  @ApiProperty({ 
    description: '文件链接', 
    example: 'https://example.com/document.pdf',
    required: false 
  })
  file: string | null;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class MemoListResponseDto {
  @ApiProperty({
    description: '备忘录列表',
    type: [MemoResponseDto],
  })
  items: MemoResponseDto[];

  @ApiProperty({
    description: '总数量',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: '当前页码',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: '每页数量',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: '总页数',
    example: 10,
  })
  totalPages: number;
}
