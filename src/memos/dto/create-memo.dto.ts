import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';

export class CreateMemoDto {
  @ApiProperty({
    description: '备忘录标题（必填）',
    example: '重要会议安排',
  })
  @IsString()
  @IsNotEmpty({ message: '标题不能为空' })
  @MaxLength(255, { message: '标题长度不能超过255个字符' })
  title: string;

  @ApiProperty({
    description: '备忘录详细内容（可选）',
    example: '明天下午3点在会议室A召开季度总结会议',
    required: false,
  })
  @IsOptional()
  @IsString()
  details?: string;

  @ApiProperty({
    description: '图片链接（可选）',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: '图片链接长度不能超过500个字符' })
  image?: string;

  @ApiProperty({
    description: '文件链接（可选）',
    example: 'https://example.com/document.pdf',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: '文件链接长度不能超过500个字符' })
  file?: string;
}
