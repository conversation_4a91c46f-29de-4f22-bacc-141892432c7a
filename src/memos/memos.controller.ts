import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { MemosService } from './memos.service';
import { CreateMemoDto } from './dto/create-memo.dto';
import { UpdateMemoDto } from './dto/update-memo.dto';
import { QueryMemosDto } from './dto/query-memos.dto';
import { MemoResponseDto, MemoListResponseDto } from './dto/memo-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';
import { SuperAdminGuard } from '@/auth/guards/super-admin.guard';

@ApiTags('备忘录管理')
@ApiBearerAuth()
@Controller('memos')
@UseGuards(JwtAuthGuard)
export class MemosController {
  private readonly logger = new Logger(MemosController.name);

  constructor(private readonly memosService: MemosService) {}

  @Post()
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '创建备忘录' })
  @ApiResponse({
    status: 200,
    description: '备忘录创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async create(@Body() createMemoDto: CreateMemoDto) {
    this.logger.log(`Creating memo with title: ${createMemoDto.title}`);

    await this.memosService.create(createMemoDto);

    return {
      code: 200,
      data: null,
      message: '备忘录创建成功',
    };
  }

  @Get()
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '获取备忘录列表（支持分页和筛选）' })
  @ApiResponse({
    status: 200,
    description: '获取备忘录列表成功',
    type: MemoListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async findAll(@Query() queryDto: QueryMemosDto) {
    this.logger.log(`Querying memos with params: ${JSON.stringify(queryDto)}`);

    const result = await this.memosService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取备忘录列表成功',
    };
  }

  @Get(':id')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '根据ID获取备忘录详情' })
  @ApiParam({
    name: 'id',
    description: '备忘录UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: '获取备忘录详情成功',
    type: MemoResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '备忘录不存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async findOne(@Param('id') id: string) {
    this.logger.log(`Getting memo details for ID: ${id}`);

    const memo = await this.memosService.findOne(id);

    return {
      code: 200,
      data: memo,
      message: '获取备忘录详情成功',
    };
  }

  @Patch(':id')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '更新备忘录' })
  @ApiParam({
    name: 'id',
    description: '备忘录UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: '备忘录更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '备忘录不存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async update(@Param('id') id: string, @Body() updateMemoDto: UpdateMemoDto) {
    this.logger.log(`Updating memo ${id} with data: ${JSON.stringify(updateMemoDto)}`);

    await this.memosService.update(id, updateMemoDto);

    return {
      code: 200,
      data: null,
      message: '备忘录更新成功',
    };
  }

  @Delete(':id')
  @UseGuards(SuperAdminGuard)
  @ApiOperation({ summary: '删除备忘录' })
  @ApiParam({
    name: 'id',
    description: '备忘录UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: '备忘录删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '备忘录不存在',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，需要超级管理员权限',
  })
  async remove(@Param('id') id: string) {
    this.logger.log(`Deleting memo: ${id}`);

    await this.memosService.remove(id);

    return {
      code: 200,
      data: null,
      message: '备忘录删除成功',
    };
  }
}
