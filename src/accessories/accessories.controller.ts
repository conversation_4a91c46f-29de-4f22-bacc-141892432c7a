import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { AccessoriesService } from './accessories.service';
import { CreateAccessoryDto } from './dto/create-accessory.dto';
import { UpdateAccessoryDto } from './dto/update-accessory.dto';
import { QueryAccessoriesDto } from './dto/query-accessories.dto';
import { AccessoryListResponseDto } from './dto/accessory-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('accessories')
@Controller('accessories')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AccessoriesController {
  private readonly logger = new Logger(AccessoriesController.name);

  constructor(private readonly accessoriesService: AccessoriesService) {}

  @Post()
  @ApiOperation({ summary: '创建辅料' })
  @ApiResponse({
    status: 200,
    description: '辅料创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '供应商不存在或参数错误',
  })
  async create(@Body() createAccessoryDto: CreateAccessoryDto) {
    this.logger.log(`Creating accessory ${createAccessoryDto.articleNumber}`);

    await this.accessoriesService.create(createAccessoryDto);

    return {
      code: 200,
      data: null,
      message: '辅料创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取辅料列表（支持多种搜索条件）' })
  @ApiResponse({
    status: 200,
    description: '获取辅料列表成功',
    type: AccessoryListResponseDto,
  })
  async findAll(@Query() queryDto: QueryAccessoriesDto) {
    this.logger.log(
      `Querying accessories with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.accessoriesService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取辅料列表成功',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取辅料详情' })
  @ApiParam({
    name: 'id',
    description: '辅料ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '获取辅料详情成功',
  })
  @ApiResponse({
    status: 404,
    description: '辅料不存在',
  })
  async findOne(@Param('id') id: string) {
    this.logger.log(`Getting accessory details for id: ${id}`);

    const accessory = await this.accessoriesService.findOne(id);

    return {
      code: 200,
      data: {
        id: accessory.id,
        articleNumber: accessory.articleNumber,
        name: accessory.name,
        brandName: accessory.brandName,
        supplierStyleNumber: accessory.supplierStyleNumber,
        color: accessory.color,
        size: accessory.size,
        costPrice: accessory.costPrice,
        supplierCode: accessory.supplierCode,
        supplierName: accessory.supplier?.name || '',
        imageUrl: accessory.imageUrl,
        createdAt: accessory.createdAt,
        updatedAt: accessory.updatedAt,
      },
      message: '获取辅料详情成功',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新辅料信息' })
  @ApiParam({
    name: 'id',
    description: '辅料ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '辅料更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '辅料不存在',
  })
  @ApiResponse({
    status: 400,
    description: '供应商不存在或参数错误',
  })
  async update(
    @Param('id') id: string,
    @Body() updateAccessoryDto: UpdateAccessoryDto,
  ) {
    this.logger.log(`Updating accessory ${id}`);

    await this.accessoriesService.update(id, updateAccessoryDto);

    return {
      code: 200,
      data: null,
      message: '辅料更新成功',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除辅料（软删除）' })
  @ApiParam({
    name: 'id',
    description: '辅料ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '辅料删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '辅料不存在',
  })
  async remove(@Param('id') id: string) {
    this.logger.log(`Deleting accessory ${id}`);

    await this.accessoriesService.remove(id);

    return {
      code: 200,
      data: null,
      message: '辅料删除成功',
    };
  }
}
