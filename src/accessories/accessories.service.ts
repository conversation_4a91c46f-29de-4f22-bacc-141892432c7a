import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Accessory } from './entities/accessory.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { CreateAccessoryDto } from './dto/create-accessory.dto';
import { UpdateAccessoryDto } from './dto/update-accessory.dto';
import { QueryAccessoriesDto } from './dto/query-accessories.dto';

@Injectable()
export class AccessoriesService {
  private readonly logger = new Logger(AccessoriesService.name);

  constructor(
    @InjectRepository(Accessory)
    private accessoriesRepository: Repository<Accessory>,
    @InjectRepository(Supplier)
    private suppliersRepository: Repository<Supplier>,
  ) {}

  /**
   * 创建辅料
   */
  async create(createAccessoryDto: CreateAccessoryDto): Promise<Accessory> {
    this.logger.log(
      `Creating accessory with article number: ${createAccessoryDto.articleNumber}`,
    );

    // 验证供应商是否存在
    const supplier = await this.suppliersRepository.findOne({
      where: { code: createAccessoryDto.supplierCode, isDeleted: false },
    });

    if (!supplier) {
      throw new BadRequestException(
        `供应商 ${createAccessoryDto.supplierCode} 不存在`,
      );
    }

    // 创建辅料
    const accessory = this.accessoriesRepository.create({
      ...createAccessoryDto,
      brandName: createAccessoryDto.brandName || null,
      supplierStyleNumber: createAccessoryDto.supplierStyleNumber || null,
      imageUrl: createAccessoryDto.imageUrl || null,
      isDeleted: false,
    });

    return await this.accessoriesRepository.save(accessory);
  }

  /**
   * 分页查询辅料列表（支持多种搜索条件）
   */
  async findAll(queryDto: QueryAccessoriesDto) {
    this.logger.log(
      `Querying accessories with params: ${JSON.stringify(queryDto)}`,
    );

    const {
      page,
      pageSize,
      nameSearch,
      articleNumberSearch,
      supplierCodeSearch,
    } = queryDto;

    // page和pageSize是必须参数
    if (page === undefined || pageSize === undefined) {
      return {
        accessories: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // 处理分页参数：page=0&pageSize=0表示获取所有数据
    const isGetAll = page === 0 && pageSize === 0;
    const finalPage = isGetAll ? 1 : page;
    const finalPageSize = isGetAll ? undefined : pageSize;
    const skip = isGetAll ? 0 : (page - 1) * pageSize;

    const queryBuilder = this.accessoriesRepository
      .createQueryBuilder('accessory')
      .leftJoinAndSelect('accessory.supplier', 'supplier')
      .where('accessory.isDeleted = :isDeleted', { isDeleted: false });

    // 名称搜索
    if (nameSearch) {
      queryBuilder.andWhere('accessory.name ILIKE :nameSearch', {
        nameSearch: `%${nameSearch}%`,
      });
    }

    // 货号搜索
    if (articleNumberSearch) {
      queryBuilder.andWhere(
        'accessory.articleNumber ILIKE :articleNumberSearch',
        {
          articleNumberSearch: `%${articleNumberSearch}%`,
        },
      );
    }

    // 供应商编码搜索
    if (supplierCodeSearch) {
      queryBuilder.andWhere('accessory.supplierCode = :supplierCodeSearch', {
        supplierCodeSearch,
      });
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const accessories = await queryBuilder
      .select([
        'accessory.id',
        'accessory.articleNumber',
        'accessory.name',
        'accessory.brandName',
        'accessory.supplierStyleNumber',
        'accessory.color',
        'accessory.size',
        'accessory.costPrice',
        'accessory.supplierCode',
        'accessory.imageUrl',
        'accessory.createdAt',
        'accessory.updatedAt',
        'supplier.name',
      ])
      .orderBy('accessory.createdAt', 'DESC')
      .skip(skip)
      .take(finalPageSize)
      .getMany();

    // 格式化返回数据
    const formattedAccessories = accessories.map((accessory) => ({
      id: accessory.id,
      articleNumber: accessory.articleNumber,
      name: accessory.name,
      brandName: accessory.brandName,
      supplierStyleNumber: accessory.supplierStyleNumber,
      color: accessory.color,
      size: accessory.size,
      costPrice: accessory.costPrice,
      supplierCode: accessory.supplierCode,
      supplierName: accessory.supplier?.name || '',
      imageUrl: accessory.imageUrl,
      createdAt: accessory.createdAt,
      updatedAt: accessory.updatedAt,
    }));

    return {
      accessories: formattedAccessories,
      total,
      page: isGetAll ? 0 : finalPage,
      pageSize: isGetAll ? 0 : finalPageSize!,
      totalPages: isGetAll ? 1 : Math.ceil(total / finalPageSize!),
    };
  }

  /**
   * 根据ID查找辅料
   */
  async findById(id: string): Promise<Accessory | null> {
    return await this.accessoriesRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['supplier'],
    });
  }

  /**
   * 根据ID获取辅料详情
   */
  async findOne(id: string): Promise<Accessory> {
    const accessory = await this.findById(id);
    if (!accessory) {
      throw new NotFoundException('辅料不存在');
    }
    return accessory;
  }

  /**
   * 更新辅料
   */
  async update(
    id: string,
    updateAccessoryDto: UpdateAccessoryDto,
  ): Promise<Accessory> {
    this.logger.log(`Updating accessory ${id}`);

    // 查找要更新的辅料
    const accessory = await this.findOne(id);

    // 如果更新供应商编码，需要验证供应商是否存在
    if (
      updateAccessoryDto.supplierCode &&
      updateAccessoryDto.supplierCode !== accessory.supplierCode
    ) {
      const supplier = await this.suppliersRepository.findOne({
        where: { code: updateAccessoryDto.supplierCode, isDeleted: false },
      });

      if (!supplier) {
        throw new BadRequestException(
          `供应商 ${updateAccessoryDto.supplierCode} 不存在`,
        );
      }
    }

    // 更新字段
    Object.assign(accessory, updateAccessoryDto);

    return await this.accessoriesRepository.save(accessory);
  }

  /**
   * 软删除辅料
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`Soft deleting accessory ${id}`);

    // 查找要删除的辅料
    const accessory = await this.findOne(id);

    // 软删除
    accessory.isDeleted = true;
    accessory.deletedAt = new Date();
    await this.accessoriesRepository.save(accessory);
  }
}
