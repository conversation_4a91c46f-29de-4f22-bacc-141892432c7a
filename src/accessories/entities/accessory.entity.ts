import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Supplier } from '@/suppliers/entities/supplier.entity';

@Entity('accessories')
export class Accessory {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '辅料ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Column({ type: 'varchar', length: 100 })
  @ApiProperty({ description: '货号（必填）', example: 'ACC001' })
  articleNumber: string;

  @Column({ type: 'varchar', length: 200 })
  @ApiProperty({ description: '辅料名称（必填）', example: '拉链' })
  name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @ApiProperty({
    description: '品牌名（选填，字符串）',
    example: 'YKK',
    required: false,
  })
  brandName: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @ApiProperty({
    description: '供应商款号（选填）',
    example: 'SUP-STYLE-001',
    required: false,
  })
  supplierStyleNumber: string | null;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({ description: '颜色（必填，字符串）', example: '黑色' })
  color: string;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({ description: '尺码（必填，字符串）', example: '5cm' })
  size: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '成本价（必填）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '成本价（必填）', example: 12.5 })
  costPrice: number;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @ApiProperty({
    description: '辅料图片URL（选填）',
    example: 'https://example.com/accessory.jpg',
    required: false,
  })
  imageUrl: string | null;

  // 供应商关联（多对一）
  @ManyToOne(() => Supplier)
  @JoinColumn({ name: 'supplierCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '供应商信息',
    type: () => Supplier,
  })
  supplier: Supplier;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({
    description: '供应商编码（必填，关联supplier表）',
    example: 'SUP001',
  })
  supplierCode: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;
}
