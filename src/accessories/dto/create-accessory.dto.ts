import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateAccessoryDto {
  @ApiProperty({
    description: '货号（必填）',
    example: 'ACC001',
  })
  @IsString()
  @IsNotEmpty({ message: '货号不能为空' })
  articleNumber: string;

  @ApiProperty({
    description: '辅料名称（必填）',
    example: '拉链',
  })
  @IsString()
  @IsNotEmpty({ message: '辅料名称不能为空' })
  name: string;

  @ApiProperty({
    description: '品牌名（选填，字符串）',
    example: 'YKK',
    required: false,
  })
  @IsOptional()
  @IsString()
  brandName?: string;

  @ApiProperty({
    description: '供应商款号（选填）',
    example: 'SUP-STYLE-001',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierStyleNumber?: string;

  @ApiProperty({
    description: '颜色（必填，字符串）',
    example: '黑色',
  })
  @IsString()
  @IsNotEmpty({ message: '颜色不能为空' })
  color: string;

  @ApiProperty({
    description: '尺码（必填，字符串）',
    example: '5cm',
  })
  @IsString()
  @IsNotEmpty({ message: '尺码不能为空' })
  size: string;

  @ApiProperty({
    description: '成本价（必填，保留两位小数）',
    example: 12.5,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '成本价必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '成本价不能小于0' })
  costPrice: number;

  @ApiProperty({
    description: '供应商编码（必填，关联supplier表）',
    example: 'SUP001',
  })
  @IsString()
  @IsNotEmpty({ message: '供应商编码不能为空' })
  supplierCode: string;

  @ApiProperty({
    description: '辅料图片URL（选填）',
    example: 'https://example.com/accessory.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageUrl?: string;
}
