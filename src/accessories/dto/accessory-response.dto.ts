import { ApiProperty } from '@nestjs/swagger';

export class AccessoryDto {
  @ApiProperty({
    description: '辅料ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({ description: '货号', example: 'ACC001' })
  articleNumber: string;

  @ApiProperty({ description: '辅料名称', example: '拉链' })
  name: string;

  @ApiProperty({
    description: '品牌名',
    example: 'YKK',
    required: false,
  })
  brandName: string | null;

  @ApiProperty({
    description: '供应商款号',
    example: 'SUP-STYLE-001',
    required: false,
  })
  supplierStyleNumber: string | null;

  @ApiProperty({ description: '颜色', example: '黑色' })
  color: string;

  @ApiProperty({ description: '尺码', example: '5cm' })
  size: string;

  @ApiProperty({ description: '成本价', example: 12.5 })
  costPrice: number;

  @ApiProperty({ description: '供应商编码', example: 'SUP001' })
  supplierCode: string;

  @ApiProperty({ description: '供应商名称', example: '广州服装供应商' })
  supplierName: string;

  @ApiProperty({
    description: '辅料图片URL',
    example: 'https://example.com/accessory.jpg',
    required: false,
  })
  imageUrl: string | null;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' })
  updatedAt: Date;
}

export class AccessoryListResponseDto {
  @ApiProperty({
    description: '辅料列表',
    type: [AccessoryDto],
  })
  accessories: AccessoryDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}
