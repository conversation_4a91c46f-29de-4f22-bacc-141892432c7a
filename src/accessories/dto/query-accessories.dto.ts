import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, Min, IsOptional, IsString } from 'class-validator';

export class QueryAccessoriesDto {
  @ApiProperty({
    description: '页码（必填，从1开始，page=0&pageSize=0表示获取所有数据）',
    example: 1,
  })
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(0, { message: '页码不能小于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填，page=0&pageSize=0表示获取所有数据）',
    example: 10,
  })
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(0, { message: '每页数量不能小于0' })
  pageSize: number;

  @ApiProperty({
    description: '名称搜索（可选，模糊搜索辅料名称）',
    example: '拉链',
    required: false,
  })
  @IsOptional()
  @IsString()
  nameSearch?: string;

  @ApiProperty({
    description: '货号搜索（可选，模糊搜索货号）',
    example: 'ACC001',
    required: false,
  })
  @IsOptional()
  @IsString()
  articleNumberSearch?: string;

  @ApiProperty({
    description: '供应商编码搜索（可选，精确搜索供应商编码）',
    example: 'SUP001',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierCodeSearch?: string;
}
