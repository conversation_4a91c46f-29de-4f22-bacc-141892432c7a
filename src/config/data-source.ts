import { DataSource, DataSourceOptions } from 'typeorm';
import { config } from 'dotenv';

// Load environment variables from .env file
config();

export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || '54188',
  database: process.env.DB_DATABASE || 'manager',
  entities: ['dist/**/*.entity.js'],
  migrations: ['dist/migrations/*.js'],
  synchronize:
    process.env.DB_SYNCHRONIZE === 'true' ||
    process.env.NODE_ENV !== 'production',
  logging:
    process.env.DB_LOGGING === 'true' || process.env.NODE_ENV !== 'production',
  migrationsRun: process.env.NODE_ENV === 'production',
};

const dataSource = new DataSource(dataSourceOptions);
export default dataSource;
