# 资产实体数据库迁移指南

## 概述

本迁移将四个资产实体（运营资产、研发成本、租赁资产、固定资产）的数据库表结构更新为与支出收入模块一致的模式。

## 迁移内容

### 主要更改
1. **移除自动时间戳字段**：`created_at`, `updated_at`, `deleted_at`
2. **添加前端指定日期字段**：`create_date` (VARCHAR(20))
3. **数据迁移**：将现有 `created_at` 数据转换为 `create_date` 格式
4. **性能优化**：为新的 `create_date` 字段创建索引

### 影响的表
- `operating_assets` (运营资产主表)
- `operating_asset_details` (运营资产明细表)
- `rd_costs` (研发成本主表)
- `rd_cost_details` (研发成本明细表)
- `rental_assets` (租赁资产主表)
- `rental_asset_details` (租赁资产明细表)
- `fixed_assets` (固定资产主表)
- `fixed_asset_details` (固定资产明细表)

## 执行步骤

### 1. 备份数据库
```bash
# PostgreSQL 备份命令
pg_dump -h 43.138.236.92 -U your_username -d manager > backup_before_asset_migration_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 执行迁移脚本
```bash
# 连接到数据库并执行迁移
psql -h 43.138.236.92 -U your_username -d manager -f src/migrations/update-asset-entities-to-expense-income-pattern.sql
```

### 3. 验证迁移结果
```sql
-- 检查表结构
\d operating_assets
\d operating_asset_details
\d rd_costs
\d rd_cost_details
\d rental_assets
\d rental_asset_details
\d fixed_assets
\d fixed_asset_details

-- 检查数据完整性
SELECT COUNT(*) FROM operating_assets WHERE create_date IS NULL;
SELECT COUNT(*) FROM operating_asset_details WHERE create_date IS NULL;
-- 重复检查其他表...

-- 检查索引
\di *create_date*
```

### 4. 测试应用程序
- 启动应用程序
- 测试资产模块的 CRUD 操作
- 测试分页查询功能
- 测试导出功能
- 验证日期过滤功能

## 回滚方案

如果迁移出现问题，可以使用回滚脚本：

```bash
# 执行回滚脚本
psql -h 43.138.236.92 -U your_username -d manager -f src/migrations/rollback-asset-entities-to-original-pattern.sql
```

**注意**：回滚将丢失 `create_date` 字段中的数据，并且需要恢复代码中的相关更改。

## 迁移后的数据格式

### 原始格式
```sql
created_at: 2024-01-15 10:30:45.123456
updated_at: 2024-01-16 14:20:30.789012
deleted_at: NULL
```

### 迁移后格式
```sql
create_date: '2024-01-15'
```

## 注意事项

1. **数据精度**：原始的时间戳精确到毫秒，迁移后只保留日期部分
2. **前端适配**：新记录需要前端提供 `create_date` 字段
3. **API 兼容性**：确保前端调用的 API 参数格式正确
4. **性能影响**：新增的索引将提高查询性能
5. **时区考虑**：迁移使用服务器本地时区进行日期转换

## 验证清单

- [ ] 数据库备份已完成
- [ ] 迁移脚本执行成功
- [ ] 所有表都包含 `create_date` 字段
- [ ] 所有表都移除了自动时间戳字段
- [ ] 索引创建成功
- [ ] 应用程序启动正常
- [ ] 资产模块功能测试通过
- [ ] 分页和导出功能正常
- [ ] 日期过滤功能正常

## 故障排除

### 常见问题

1. **字段不存在错误**
   - 检查表结构是否正确迁移
   - 确认应用程序代码已更新

2. **数据类型错误**
   - 确认 `create_date` 字段为 VARCHAR(20) 类型
   - 检查前端传递的日期格式

3. **索引创建失败**
   - 检查数据库权限
   - 确认表名和字段名正确

4. **性能问题**
   - 检查索引是否正确创建
   - 分析查询执行计划

## 联系信息

如有问题，请联系开发团队或查看相关文档。
