import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSkuCodeFormat1749872208315 implements MigrationInterface {
  name = 'UpdateSkuCodeFormat1749872208315';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('开始更新SKU编码格式...');

    // 查询所有现有的SKU记录
    const existingSkus = await queryRunner.query(`
      SELECT id, code, "brandCode", "supplierCode", "categoryCode", "colorCode"
      FROM skus 
      WHERE "isDeleted" = false
      ORDER BY "createdAt" ASC
    `);

    console.log(`找到 ${existingSkus.length} 个现有SKU记录`);

    if (existingSkus.length === 0) {
      console.log('没有现有SKU记录，跳过迁移');
      return;
    }

    // 按基础编码分组，为每组分配序列号
    const codeGroups = new Map<string, any[]>();
    
    for (const sku of existingSkus) {
      // 生成基础编码（品牌+供应商+分类+颜色）
      const brandCodePart = sku.brandCode.padStart(3, '0').substring(0, 3);
      const supplierCodePart = sku.supplierCode.padStart(3, '0').substring(0, 3);
      const categoryCodePart = sku.categoryCode.padStart(2, '0').substring(0, 2);
      const colorCodePart = sku.colorCode.padStart(2, '0').substring(0, 2);
      
      const baseCode = `${brandCodePart}${supplierCodePart}${categoryCodePart}${colorCodePart}`;
      
      if (!codeGroups.has(baseCode)) {
        codeGroups.set(baseCode, []);
      }
      codeGroups.get(baseCode)!.push(sku);
    }

    console.log(`发现 ${codeGroups.size} 个不同的基础编码组合`);

    // 为每个组分配序列号并更新编码
    let totalUpdated = 0;
    
    for (const [baseCode, skus] of codeGroups) {
      let sequence = 1;
      
      for (const sku of skus) {
        // 跳过包含数字4的序列号
        while (this.containsDigit4(sequence)) {
          sequence++;
        }
        
        const sequenceStr = sequence.toString().padStart(3, '0');
        const newCode = `${baseCode}${sequenceStr}`;
        
        // 更新SKU编码
        await queryRunner.query(`
          UPDATE skus 
          SET code = $1 
          WHERE id = $2
        `, [newCode, sku.id]);
        
        console.log(`更新SKU ${sku.id}: ${sku.code} -> ${newCode}`);
        
        sequence++;
        totalUpdated++;
      }
    }

    console.log(`SKU编码格式更新完成，共更新 ${totalUpdated} 条记录`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('回滚SKU编码格式更新...');
    
    // 查询所有13位编码的SKU记录
    const skusToRevert = await queryRunner.query(`
      SELECT id, code, "brandCode", "supplierCode", "categoryCode", "colorCode"
      FROM skus 
      WHERE "isDeleted" = false AND LENGTH(code) = 13
      ORDER BY "createdAt" ASC
    `);

    console.log(`找到 ${skusToRevert.length} 个需要回滚的SKU记录`);

    for (const sku of skusToRevert) {
      // 提取基础编码（去掉后3位序列号）
      const baseCode = sku.code.substring(0, 10);
      
      // 检查基础编码是否已存在
      const existingCount = await queryRunner.query(`
        SELECT COUNT(*) as count 
        FROM skus 
        WHERE code = $1 AND "isDeleted" = false
      `, [baseCode]);

      if (existingCount[0].count === 0) {
        // 如果基础编码不存在，则回滚到基础编码
        await queryRunner.query(`
          UPDATE skus 
          SET code = $1 
          WHERE id = $2
        `, [baseCode, sku.id]);
        
        console.log(`回滚SKU ${sku.id}: ${sku.code} -> ${baseCode}`);
      } else {
        console.log(`跳过SKU ${sku.id}，基础编码 ${baseCode} 已存在`);
      }
    }

    console.log('SKU编码格式回滚完成');
  }

  /**
   * 检查数字是否包含数字4
   */
  private containsDigit4(num: number): boolean {
    return num.toString().includes('4');
  }
}
