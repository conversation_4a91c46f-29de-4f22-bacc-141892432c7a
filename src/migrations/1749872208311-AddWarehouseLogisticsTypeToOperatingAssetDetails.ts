import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddWarehouseLogisticsTypeToOperatingAssetDetails1749872208311
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建仓储物流收支类型枚举
    await queryRunner.query(`
      CREATE TYPE "warehouse_logistics_type_enum" AS ENUM('income', 'expense')
    `);

    // 添加仓储物流收支类型字段到运营资产明细表
    await queryRunner.query(`
      ALTER TABLE "operating_asset_details" 
      ADD COLUMN "warehouseLogisticsType" "warehouse_logistics_type_enum" NULL
    `);

    // 添加字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN "operating_asset_details"."warehouseLogisticsType" 
      IS '仓储物流收支类型（仅仓储物流类型需要）'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除字段
    await queryRunner.query(`
      ALTER TABLE "operating_asset_details" 
      DROP COLUMN "warehouseLogisticsType"
    `);

    // 删除枚举类型
    await queryRunner.query(`
      DROP TYPE "warehouse_logistics_type_enum"
    `);
  }
}
