-- 为供应商档案表添加新字段的SQL迁移脚本
-- 执行前请备份数据库

-- 添加总支付金额字段
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS total_payment_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总支付金额';

-- 添加计算型统计字段
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS defect_rate DECIMAL(5,2) DEFAULT 0 COMMENT '瑕疵率（%）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS repair_rate DECIMAL(5,2) DEFAULT 0 COMMENT '返修率（%）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS repair_success_rate DECIMAL(5,2) DEFAULT 0 COMMENT '修复成功率（%）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS order_completion_rate DECIMAL(5,2) DEFAULT 0 COMMENT '订单完成率（%）';

-- 添加业务关键字段
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS average_unit_price DECIMAL(10,2) DEFAULT 0 COMMENT '平均单价';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS cooperation_start_date TIMESTAMP NULL COMMENT '合作开始时间';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS last_transaction_date TIMESTAMP NULL COMMENT '最后交易时间';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS credit_level VARCHAR(10) DEFAULT 'C' COMMENT '信用等级（A/B/C/D）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS payment_cycle INTEGER DEFAULT 0 COMMENT '平均付款周期（天）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS delivery_cycle INTEGER DEFAULT 0 COMMENT '平均交货周期（天）';

-- 添加质量管控字段
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS quality_score DECIMAL(3,1) DEFAULT 5.0 COMMENT '质量评分（1-10分）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS return_count INTEGER DEFAULT 0 COMMENT '退货次数';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS return_amount DECIMAL(15,2) DEFAULT 0 COMMENT '退货金额';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS return_rate DECIMAL(5,2) DEFAULT 0 COMMENT '退货率（%）';

-- 添加财务相关字段
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS unsettled_amount DECIMAL(15,2) DEFAULT 0 COMMENT '未结算金额';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS prepayment_balance DECIMAL(15,2) DEFAULT 0 COMMENT '预付款余额';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS average_payment_period INTEGER DEFAULT 0 COMMENT '平均账期（天）';

-- 添加供应商表现字段
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS on_time_delivery_rate DECIMAL(5,2) DEFAULT 0 COMMENT '准时交货率（%）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS response_speed_score DECIMAL(3,1) DEFAULT 5.0 COMMENT '响应速度评分（1-10分）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS service_attitude_score DECIMAL(3,1) DEFAULT 5.0 COMMENT '服务态度评分（1-10分）';
ALTER TABLE supplier_archives ADD COLUMN IF NOT EXISTS overall_score DECIMAL(3,1) DEFAULT 5.0 COMMENT '综合评分（1-10分）';

-- 为现有记录设置合作开始时间为创建时间
UPDATE supplier_archives
SET cooperation_start_date = created_at
WHERE cooperation_start_date IS NULL;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_supplier_archives_credit_level ON supplier_archives(credit_level);
CREATE INDEX IF NOT EXISTS idx_supplier_archives_overall_score ON supplier_archives(overall_score);
CREATE INDEX IF NOT EXISTS idx_supplier_archives_defect_rate ON supplier_archives(defect_rate);
CREATE INDEX IF NOT EXISTS idx_supplier_archives_last_transaction_date ON supplier_archives(last_transaction_date);

-- 添加约束检查
ALTER TABLE supplier_archives ADD CONSTRAINT chk_credit_level CHECK (credit_level IN ('A', 'B', 'C', 'D'));
ALTER TABLE supplier_archives ADD CONSTRAINT chk_quality_score CHECK (quality_score >= 1.0 AND quality_score <= 10.0);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_response_speed_score CHECK (response_speed_score >= 1.0 AND response_speed_score <= 10.0);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_service_attitude_score CHECK (service_attitude_score >= 1.0 AND service_attitude_score <= 10.0);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_overall_score CHECK (overall_score >= 1.0 AND overall_score <= 10.0);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_defect_rate CHECK (defect_rate >= 0 AND defect_rate <= 100);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_repair_rate CHECK (repair_rate >= 0 AND repair_rate <= 100);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_repair_success_rate CHECK (repair_success_rate >= 0 AND repair_success_rate <= 100);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_order_completion_rate CHECK (order_completion_rate >= 0 AND order_completion_rate <= 100);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_return_rate CHECK (return_rate >= 0 AND return_rate <= 100);
ALTER TABLE supplier_archives ADD CONSTRAINT chk_on_time_delivery_rate CHECK (on_time_delivery_rate >= 0 AND on_time_delivery_rate <= 100);

-- 为供应商档案明细表添加新字段
ALTER TABLE supplier_archive_details ADD COLUMN IF NOT EXISTS payment_amount DECIMAL(15,2) NULL COMMENT '支付金额（支付类型必填）';
ALTER TABLE supplier_archive_details ADD COLUMN IF NOT EXISTS image_url VARCHAR(500) NULL COMMENT '相关图片URL（所有类型选填）';

-- 更新枚举类型以包含支付类型
-- 注意：PostgreSQL需要先删除约束再重新添加，MySQL可以直接修改
-- ALTER TABLE supplier_archive_details MODIFY COLUMN type ENUM('purchase', 'arrival', 'repair_send', 'repair_arrival', 'payment');

COMMENT ON TABLE supplier_archives IS '供应商档案汇总表 - 包含供应商的完整业务统计数据';
COMMENT ON TABLE supplier_archive_details IS '供应商档案明细表 - 记录采购、到货、返修、支付等具体业务操作';
