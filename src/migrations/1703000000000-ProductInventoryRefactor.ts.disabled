import { MigrationInterface, QueryRunner } from 'typeorm';

export class ProductInventoryRefactor1703000000000
  implements MigrationInterface
{
  name = 'ProductInventoryRefactor1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 确保uuid-ossp扩展已启用
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

    // 1. 为产品表添加categoryCode字段
    await queryRunner.query(`
      ALTER TABLE "products" 
      ADD COLUMN IF NOT EXISTS "categoryCode" varchar(50)
    `);

    // 2. 创建默认商品分类（如果不存在）
    const defaultCategory = await queryRunner.query(`
      SELECT * FROM "product_categories" 
      WHERE "code" = 'DEFAULT' AND "isDeleted" = false
      LIMIT 1
    `);

    if (defaultCategory.length === 0) {
      await queryRunner.query(`
        INSERT INTO "product_categories" ("id", "code", "name", "sizes", "isDeleted", "createdAt", "updatedAt")
        VALUES (
          uuid_generate_v4(),
          'DEFAULT',
          '默认分类',
          ARRAY['S', 'M', 'L', 'XL'],
          false,
          NOW(),
          NOW()
        )
      `);
    }

    // 3. 为现有产品设置默认分类
    await queryRunner.query(`
      UPDATE "products" 
      SET "categoryCode" = 'DEFAULT'
      WHERE "categoryCode" IS NULL
    `);

    // 4. 检查inventory_details表是否存在，如果不存在则跳过相关操作
    const inventoryTableExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'inventory_details'
      );
    `);

    if (inventoryTableExists[0].exists) {
      console.log('inventory_details表存在，开始添加新字段...');

      // 4.1 添加SKU编码字段
      await queryRunner.query(`
        ALTER TABLE "inventory_details"
        ADD COLUMN IF NOT EXISTS "skuCode" varchar(150)
      `);

      // 4.2 添加尺寸编码字段
      await queryRunner.query(`
        ALTER TABLE "inventory_details"
        ADD COLUMN IF NOT EXISTS "sizeCode" varchar(100)
      `);

      // 4.3 从现有size字段复制数据到sizeCode
      await queryRunner.query(`
        UPDATE "inventory_details"
        SET "sizeCode" = "size"
        WHERE "sizeCode" IS NULL
      `);

      // 4.4 生成SKU编码
      await queryRunner.query(`
        UPDATE "inventory_details"
        SET "skuCode" = CONCAT("productCode", '-', "colorCode", '-', "sizeCode")
        WHERE "skuCode" IS NULL
      `);

      // 4.5 添加完整的库存管理字段
      await queryRunner.query(`
        ALTER TABLE "inventory_details"
        ADD COLUMN IF NOT EXISTS "totalStock" integer DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "actualStock" integer DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "reservedStock" integer DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "damagedStock" integer DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "purchasingStock" integer DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "needPurchaseStock" integer DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "safetyStock" integer DEFAULT 10,
        ADD COLUMN IF NOT EXISTS "minStock" integer DEFAULT 5,
        ADD COLUMN IF NOT EXISTS "maxStock" integer DEFAULT 1000,
        ADD COLUMN IF NOT EXISTS "avgCost" decimal(10,2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "latestCost" decimal(10,2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "isActive" boolean DEFAULT true,
        ADD COLUMN IF NOT EXISTS "warehouseLocation" varchar(100) DEFAULT 'A区',
        ADD COLUMN IF NOT EXISTS "totalInbound" integer DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "totalOutbound" integer DEFAULT 0,
        ADD COLUMN IF NOT EXISTS "lastInboundDate" timestamp,
        ADD COLUMN IF NOT EXISTS "lastOutboundDate" timestamp
      `);

      // 4.6 将现有quantity数据迁移到新字段
      await queryRunner.query(`
        UPDATE "inventory_details"
        SET
          "totalStock" = COALESCE("quantity", 0),
          "actualStock" = COALESCE("quantity", 0)
        WHERE "totalStock" = 0 AND "actualStock" = 0
      `);

      // 5. 创建inventory_details相关索引
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS "IDX_inventory_details_skuCode"
        ON "inventory_details" ("skuCode")
      `);

      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS "IDX_inventory_details_product_color_size"
        ON "inventory_details" ("productCode", "colorCode", "sizeCode")
      `);

      console.log('inventory_details表字段和索引添加完成');
    } else {
      console.log('inventory_details表不存在，跳过相关操作');
    }

    // 6. 创建products表索引
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_products_categoryCode"
      ON "products" ("categoryCode")
    `);

    // 7. 添加外键约束（如果不存在）
    const categoryConstraintExists = await queryRunner.query(`
      SELECT constraint_name FROM information_schema.table_constraints 
      WHERE table_name = 'products' AND constraint_name = 'FK_products_categoryCode'
    `);

    if (categoryConstraintExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "products" 
        ADD CONSTRAINT "FK_products_categoryCode" 
        FOREIGN KEY ("categoryCode") REFERENCES "product_categories"("code")
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚操作 - 按相反顺序执行

    // 1. 删除外键约束
    await queryRunner.query(`
      ALTER TABLE "products" 
      DROP CONSTRAINT IF EXISTS "FK_products_categoryCode"
    `);

    // 2. 删除索引
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_inventory_details_skuCode"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_inventory_details_product_color_size"`,
    );
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_products_categoryCode"`);

    // 3. 删除库存表的新字段
    await queryRunner.query(`
      ALTER TABLE "inventory_details" 
      DROP COLUMN IF EXISTS "totalStock",
      DROP COLUMN IF EXISTS "actualStock",
      DROP COLUMN IF EXISTS "reservedStock",
      DROP COLUMN IF EXISTS "damagedStock",
      DROP COLUMN IF EXISTS "purchasingStock",
      DROP COLUMN IF EXISTS "needPurchaseStock",
      DROP COLUMN IF EXISTS "safetyStock",
      DROP COLUMN IF EXISTS "minStock",
      DROP COLUMN IF EXISTS "maxStock",
      DROP COLUMN IF EXISTS "avgCost",
      DROP COLUMN IF EXISTS "latestCost",
      DROP COLUMN IF EXISTS "isActive",
      DROP COLUMN IF EXISTS "warehouseLocation",
      DROP COLUMN IF EXISTS "totalInbound",
      DROP COLUMN IF EXISTS "totalOutbound",
      DROP COLUMN IF EXISTS "lastInboundDate",
      DROP COLUMN IF EXISTS "lastOutboundDate",
      DROP COLUMN IF EXISTS "sizeCode",
      DROP COLUMN IF EXISTS "skuCode"
    `);

    // 4. 删除产品表的categoryCode字段
    await queryRunner.query(`
      ALTER TABLE "products" 
      DROP COLUMN IF EXISTS "categoryCode"
    `);

    // 5. 删除默认分类（可选，谨慎操作）
    // await queryRunner.query(`
    //   DELETE FROM "product_categories" WHERE "code" = 'DEFAULT'
    // `);
  }
}
