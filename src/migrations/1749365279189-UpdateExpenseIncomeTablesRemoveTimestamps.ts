import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateExpenseIncomeTablesRemoveTimestamps1749365279189
  implements MigrationInterface
{
  name = 'UpdateExpenseIncomeTablesRemoveTimestamps1749365279189';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 更新 expenses 表
    await queryRunner.query(
      `ALTER TABLE "expenses" DROP COLUMN IF EXISTS "created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "expenses" DROP COLUMN IF EXISTS "updated_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "expenses" DROP COLUMN IF EXISTS "deleted_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "expenses" ADD COLUMN IF NOT EXISTS "createDate" character varying(20) NULL`,
    );
    await queryRunner.query(
      `UPDATE "expenses" SET "createDate" = '2024-01-01' WHERE "createDate" IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "expenses" ALTER COLUMN "createDate" SET NOT NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "expenses"."createDate" IS '创建日期（字符串格式，必填）'`,
    );

    // 更新 expense_details 表
    await queryRunner.query(
      `ALTER TABLE "expense_details" DROP COLUMN IF EXISTS "created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "expense_details" DROP COLUMN IF EXISTS "updated_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "expense_details" DROP COLUMN IF EXISTS "deleted_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "expense_details" ADD COLUMN IF NOT EXISTS "create_date" character varying(20) NULL`,
    );
    await queryRunner.query(
      `UPDATE "expense_details" SET "create_date" = '2024-01-01' WHERE "create_date" IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "expense_details" ALTER COLUMN "create_date" SET NOT NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "expense_details"."create_date" IS '创建日期（字符串格式，必填）'`,
    );

    // 更新 incomes 表
    await queryRunner.query(
      `ALTER TABLE "incomes" DROP COLUMN IF EXISTS "created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "incomes" DROP COLUMN IF EXISTS "updated_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "incomes" DROP COLUMN IF EXISTS "deleted_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "incomes" ADD COLUMN IF NOT EXISTS "create_date" character varying(20) NULL`,
    );
    await queryRunner.query(
      `UPDATE "incomes" SET "create_date" = '2024-01-01' WHERE "create_date" IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "incomes" ALTER COLUMN "create_date" SET NOT NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "incomes"."create_date" IS '创建日期（字符串格式，必填）'`,
    );

    // 更新 income_details 表
    await queryRunner.query(
      `ALTER TABLE "income_details" DROP COLUMN IF EXISTS "created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "income_details" DROP COLUMN IF EXISTS "updated_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "income_details" DROP COLUMN IF EXISTS "deleted_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "income_details" ADD COLUMN IF NOT EXISTS "create_date" character varying(20) NULL`,
    );
    await queryRunner.query(
      `UPDATE "income_details" SET "create_date" = '2024-01-01' WHERE "create_date" IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "income_details" ALTER COLUMN "create_date" SET NOT NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "income_details"."create_date" IS '创建日期（字符串格式，必填）'`,
    );

    // 删除相关索引
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_expense_details_created_at"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_income_details_created_at"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 恢复 expenses 表
    await queryRunner.query(`ALTER TABLE "expenses" DROP COLUMN "create_date"`);
    await queryRunner.query(
      `ALTER TABLE "expenses" ADD "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "expenses" ADD "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "expenses" ADD "deleted_at" TIMESTAMP NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "expenses"."created_at" IS '创建时间'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "expenses"."updated_at" IS '更新时间'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "expenses"."deleted_at" IS '删除时间'`,
    );

    // 恢复 expense_details 表
    await queryRunner.query(
      `ALTER TABLE "expense_details" DROP COLUMN "create_date"`,
    );
    await queryRunner.query(
      `ALTER TABLE "expense_details" ADD "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "expense_details" ADD "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "expense_details" ADD "deleted_at" TIMESTAMP NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "expense_details"."created_at" IS '创建时间'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "expense_details"."updated_at" IS '更新时间'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "expense_details"."deleted_at" IS '删除时间'`,
    );

    // 恢复 incomes 表
    await queryRunner.query(`ALTER TABLE "incomes" DROP COLUMN "create_date"`);
    await queryRunner.query(
      `ALTER TABLE "incomes" ADD "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "incomes" ADD "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "incomes" ADD "deleted_at" TIMESTAMP NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "incomes"."created_at" IS '创建时间'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "incomes"."updated_at" IS '更新时间'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "incomes"."deleted_at" IS '删除时间'`,
    );

    // 恢复 income_details 表
    await queryRunner.query(
      `ALTER TABLE "income_details" DROP COLUMN "create_date"`,
    );
    await queryRunner.query(
      `ALTER TABLE "income_details" ADD "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "income_details" ADD "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "income_details" ADD "deleted_at" TIMESTAMP NULL`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "income_details"."created_at" IS '创建时间'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "income_details"."updated_at" IS '更新时间'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "income_details"."deleted_at" IS '删除时间'`,
    );

    // 恢复索引
    await queryRunner.query(
      `CREATE INDEX "idx_expense_details_created_at" ON "expense_details" ("created_at")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_income_details_created_at" ON "income_details" ("created_at")`,
    );
  }
}
