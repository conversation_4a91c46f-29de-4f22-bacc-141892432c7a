import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissingCommissionContractFields1749872208311
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 检查并添加缺失的字段到 commission_contracts 表

    // 添加 paymentStatus 字段（如果不存在）
    const paymentStatusExists = await queryRunner.hasColumn(
      'commission_contracts',
      'paymentStatus',
    );
    if (!paymentStatusExists) {
      // 先创建枚举类型（如果不存在）
      await queryRunner.query(`
        DO $$ BEGIN
          CREATE TYPE "commission_contracts_paymentstatus_enum" AS ENUM('unpaid', 'partial_paid', 'fully_paid', 'overdue');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
      `);

      // 直接添加枚举类型的列，避免类型转换问题
      await queryRunner.query(`
        ALTER TABLE "commission_contracts"
        ADD COLUMN "paymentStatus" "commission_contracts_paymentstatus_enum" NOT NULL DEFAULT 'unpaid'
      `);
    }

    // 添加 paidAmount 字段（如果不存在）
    const paidAmountExists = await queryRunner.hasColumn(
      'commission_contracts',
      'paidAmount',
    );
    if (!paidAmountExists) {
      await queryRunner.query(`
        ALTER TABLE "commission_contracts" 
        ADD COLUMN "paidAmount" numeric(15,2) NOT NULL DEFAULT 0
      `);
    }

    // 添加 remainingDebtAmount 字段（如果不存在）
    const remainingDebtAmountExists = await queryRunner.hasColumn(
      'commission_contracts',
      'remainingDebtAmount',
    );
    if (!remainingDebtAmountExists) {
      await queryRunner.query(`
        ALTER TABLE "commission_contracts" 
        ADD COLUMN "remainingDebtAmount" numeric(15,2) NOT NULL DEFAULT 0
      `);
    }

    // 添加 lastPaymentDate 字段（如果不存在）
    const lastPaymentDateExists = await queryRunner.hasColumn(
      'commission_contracts',
      'lastPaymentDate',
    );
    if (!lastPaymentDateExists) {
      await queryRunner.query(`
        ALTER TABLE "commission_contracts" 
        ADD COLUMN "lastPaymentDate" date
      `);
    }

    // 添加字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN "commission_contracts"."paymentStatus" IS '还款状态'
    `);
    await queryRunner.query(`
      COMMENT ON COLUMN "commission_contracts"."paidAmount" IS '已还款金额'
    `);
    await queryRunner.query(`
      COMMENT ON COLUMN "commission_contracts"."remainingDebtAmount" IS '剩余欠款金额'
    `);
    await queryRunner.query(`
      COMMENT ON COLUMN "commission_contracts"."lastPaymentDate" IS '最后还款日期'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除添加的字段
    await queryRunner.query(`
      ALTER TABLE "commission_contracts" DROP COLUMN IF EXISTS "lastPaymentDate"
    `);
    await queryRunner.query(`
      ALTER TABLE "commission_contracts" DROP COLUMN IF EXISTS "remainingDebtAmount"
    `);
    await queryRunner.query(`
      ALTER TABLE "commission_contracts" DROP COLUMN IF EXISTS "paidAmount"
    `);
    await queryRunner.query(`
      ALTER TABLE "commission_contracts" DROP COLUMN IF EXISTS "paymentStatus"
    `);

    // 删除枚举类型
    await queryRunner.query(`
      DROP TYPE IF EXISTS "commission_contracts_paymentstatus_enum"
    `);
  }
}
