import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeleteAllIncomeData1749872208309 implements MigrationInterface {
  name = 'DeleteAllIncomeData1749872208309';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 删除所有收入明细数据
    await queryRunner.query(`DELETE FROM "income_details"`);

    // 删除所有收入主表数据
    await queryRunner.query(`DELETE FROM "incomes"`);

    // 重置序列（如果有的话）
    await queryRunner.query(
      `ALTER SEQUENCE IF EXISTS income_details_id_seq RESTART WITH 1`,
    );
    await queryRunner.query(
      `ALTER SEQUENCE IF EXISTS incomes_id_seq RESTART WITH 1`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 此迁移不可逆，因为数据已被删除
    // 如果需要恢复数据，请从备份中恢复
    console.log(
      'Warning: This migration deleted all income data and cannot be reversed automatically.',
    );
    console.log('Please restore from backup if needed.');
  }
}
