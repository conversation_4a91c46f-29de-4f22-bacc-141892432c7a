-- 回滚脚本：将四个资产实体恢复到原始的自动时间戳模式
-- 执行前请备份数据库
-- 注意：此脚本将丢失 create_date 字段中的数据

-- 开始事务
BEGIN;

-- =====================================================
-- 1. 回滚运营资产主表 (operating_assets)
-- =====================================================

-- 添加自动时间戳字段
ALTER TABLE operating_assets 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL COMMENT '删除时间';

-- 删除 createDate 字段和索引
DROP INDEX IF EXISTS idx_operating_assets_create_date;
ALTER TABLE operating_assets DROP COLUMN IF EXISTS create_date;

-- =====================================================
-- 2. 回滚运营资产明细表 (operating_asset_details)
-- =====================================================

-- 添加自动时间戳字段
ALTER TABLE operating_asset_details 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL COMMENT '删除时间';

-- 删除 createDate 字段和索引
DROP INDEX IF EXISTS idx_operating_asset_details_create_date;
ALTER TABLE operating_asset_details DROP COLUMN IF EXISTS create_date;

-- =====================================================
-- 3. 回滚研发成本主表 (rd_costs)
-- =====================================================

-- 添加自动时间戳字段
ALTER TABLE rd_costs 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL COMMENT '删除时间';

-- 删除 createDate 字段和索引
DROP INDEX IF EXISTS idx_rd_costs_create_date;
ALTER TABLE rd_costs DROP COLUMN IF EXISTS create_date;

-- =====================================================
-- 4. 回滚研发成本明细表 (rd_cost_details)
-- =====================================================

-- 添加自动时间戳字段
ALTER TABLE rd_cost_details 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL COMMENT '删除时间';

-- 删除 createDate 字段和索引
DROP INDEX IF EXISTS idx_rd_cost_details_create_date;
ALTER TABLE rd_cost_details DROP COLUMN IF EXISTS create_date;

-- =====================================================
-- 5. 回滚租赁资产主表 (rental_assets)
-- =====================================================

-- 添加自动时间戳字段
ALTER TABLE rental_assets 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL COMMENT '删除时间';

-- 删除 createDate 字段和索引
DROP INDEX IF EXISTS idx_rental_assets_create_date;
ALTER TABLE rental_assets DROP COLUMN IF EXISTS create_date;

-- =====================================================
-- 6. 回滚租赁资产明细表 (rental_asset_details)
-- =====================================================

-- 添加自动时间戳字段
ALTER TABLE rental_asset_details 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL COMMENT '删除时间';

-- 删除 createDate 字段和索引
DROP INDEX IF EXISTS idx_rental_asset_details_create_date;
ALTER TABLE rental_asset_details DROP COLUMN IF EXISTS create_date;

-- =====================================================
-- 7. 回滚固定资产主表 (fixed_assets)
-- =====================================================

-- 添加自动时间戳字段
ALTER TABLE fixed_assets 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL COMMENT '删除时间';

-- 删除 createDate 字段和索引
DROP INDEX IF EXISTS idx_fixed_assets_create_date;
ALTER TABLE fixed_assets DROP COLUMN IF EXISTS create_date;

-- =====================================================
-- 8. 回滚固定资产明细表 (fixed_asset_details)
-- =====================================================

-- 添加自动时间戳字段
ALTER TABLE fixed_asset_details 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL COMMENT '删除时间';

-- 删除 createDate 字段和索引
DROP INDEX IF EXISTS idx_fixed_asset_details_create_date;
ALTER TABLE fixed_asset_details DROP COLUMN IF EXISTS create_date;

-- 提交事务
COMMIT;

-- =====================================================
-- 回滚完成提示
-- =====================================================
-- 回滚脚本执行完成！
-- 
-- 已恢复的更改：
-- 1. 所有资产主表和明细表都恢复了自动时间戳字段
-- 2. 删除了所有 create_date 字段和相关索引
-- 3. 表结构已恢复到原始状态
-- 
-- 注意事项：
-- - create_date 字段中的数据已丢失
-- - 需要恢复代码中的 TypeORM 装饰器
-- - 需要更新相关的 DTO 和服务逻辑
