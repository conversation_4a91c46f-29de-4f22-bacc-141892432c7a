-- 销售订单系统数据库迁移脚本
-- 创建时间: 2025-06-23

-- 1. 创建销售订单主表
CREATE TABLE IF NOT EXISTS sales_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(50) UNIQUE NOT NULL COMMENT '销售订单编号',
    sales_person_code VARCHAR(50) NOT NULL COMMENT '销售人员编码',
    order_date DATE NOT NULL COMMENT '订单日期',
    expected_delivery_date DATE COMMENT '期望交货日期',
    priority VARCHAR(20) DEFAULT 'normal' COMMENT '订单优先级',
    status VARCHAR(20) DEFAULT 'draft' COMMENT '订单状态',
    
    -- 业务选项字段
    is_drop_shipping BOOLEAN DEFAULT FALSE COMMENT '是否代发',
    is_released BOOLEAN DEFAULT FALSE COMMENT '是否放单',
    
    -- 物流相关字段
    shipping_method VARCHAR(20) DEFAULT 'collect' COMMENT '物流方式',
    shipping_fee DECIMAL(10,2) DEFAULT 0 COMMENT '运费金额',
    shipping_company VARCHAR(200) COMMENT '物流公司',
    tracking_number VARCHAR(100) COMMENT '物流单号',
    
    -- 金额统计字段
    total_quantity INTEGER DEFAULT 0 COMMENT '总商品数量',
    total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '商品总金额',
    grand_total DECIMAL(15,2) DEFAULT 0 COMMENT '订单总金额',
    
    -- 客户统计字段
    customer_count INTEGER DEFAULT 1 COMMENT '客户数量',
    customer_codes TEXT COMMENT '主要客户编码列表',
    
    -- 审核相关字段
    created_by_user_code VARCHAR(50) COMMENT '创建人编码',
    confirmed_by_user_code VARCHAR(50) COMMENT '确认人编码',
    confirmed_at TIMESTAMP COMMENT '确认时间',
    
    remark TEXT COMMENT '备注',
    
    -- 系统字段
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP COMMENT '删除时间'
);

-- 2. 创建销售订单明细表
CREATE TABLE IF NOT EXISTS sales_order_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sales_order_id UUID NOT NULL COMMENT '销售订单ID',
    
    -- 商品信息字段
    product_code VARCHAR(100) NOT NULL COMMENT '商品编码',
    color_code VARCHAR(50) NOT NULL COMMENT '颜色编码',
    size_code VARCHAR(10) NOT NULL COMMENT '尺寸编码',
    sku_code VARCHAR(150) NOT NULL COMMENT 'SKU编码',
    product_name VARCHAR(200) COMMENT '商品名称',
    color_name VARCHAR(100) COMMENT '颜色名称',
    
    -- 数量相关字段
    quantity INTEGER NOT NULL COMMENT '销售数量',
    shipped_quantity INTEGER DEFAULT 0 COMMENT '已发货数量',
    pending_ship_quantity INTEGER DEFAULT 0 COMMENT '待发货数量',
    reserved_quantity INTEGER DEFAULT 0 COMMENT '库存预留数量',
    stock_at_order INTEGER DEFAULT 0 COMMENT '下单时库存数量',
    
    -- 价格相关字段
    price_type VARCHAR(20) DEFAULT 'retail' COMMENT '价格类型',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_amount DECIMAL(15,2) NOT NULL COMMENT '小计金额',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '折扣金额',
    actual_amount DECIMAL(15,2) NOT NULL COMMENT '实际金额',
    
    -- 成本相关字段
    cost_price DECIMAL(10,2) DEFAULT 0 COMMENT '成本价',
    total_cost DECIMAL(15,2) DEFAULT 0 COMMENT '成本总额',
    gross_profit DECIMAL(15,2) DEFAULT 0 COMMENT '毛利润',
    
    -- 统计字段
    category_code VARCHAR(50) COMMENT '商品分类编码',
    brand_code VARCHAR(50) COMMENT '品牌编码',
    supplier_code VARCHAR(50) COMMENT '供应商编码',
    
    -- 业务字段
    expected_ship_date DATE COMMENT '期望发货日期',
    remark TEXT COMMENT '明细备注',
    
    -- 系统字段
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id)
);

-- 3. 创建销售订单客户关联表
CREATE TABLE IF NOT EXISTS sales_order_customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sales_order_id UUID NOT NULL COMMENT '销售订单ID',
    customer_code VARCHAR(50) NOT NULL COMMENT '客户编码',
    customer_name VARCHAR(100) COMMENT '客户名称',
    
    -- 分配数量和金额
    allocated_quantity INTEGER DEFAULT 0 COMMENT '分配总数量',
    allocated_amount DECIMAL(15,2) DEFAULT 0 COMMENT '分配总金额',
    allocated_shipping_fee DECIMAL(10,2) DEFAULT 0 COMMENT '分配运费',
    total_payable DECIMAL(15,2) DEFAULT 0 COMMENT '客户应付总额',
    
    -- 客户状态
    status VARCHAR(20) DEFAULT 'pending' COMMENT '客户订单状态',
    
    -- 收货信息
    receiver_name VARCHAR(100) COMMENT '收货人姓名',
    receiver_phone VARCHAR(20) COMMENT '收货人电话',
    shipping_address VARCHAR(500) COMMENT '收货地址',
    province_code INTEGER COMMENT '省份代码',
    city VARCHAR(100) COMMENT '城市',
    district VARCHAR(100) COMMENT '区县',
    
    -- 物流信息
    shipping_company VARCHAR(200) COMMENT '物流公司',
    tracking_number VARCHAR(100) COMMENT '物流单号',
    shipped_at TIMESTAMP COMMENT '发货时间',
    delivered_at TIMESTAMP COMMENT '送达时间',
    
    -- 优先级和特殊要求
    priority INTEGER DEFAULT 0 COMMENT '客户优先级',
    is_urgent BOOLEAN DEFAULT FALSE COMMENT '是否加急',
    special_requirements TEXT COMMENT '特殊要求',
    
    -- 商品明细分配
    item_allocations JSONB COMMENT '商品明细分配',
    
    remark TEXT COMMENT '客户备注',
    
    -- 系统字段
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id)
);

-- 4. 创建索引
-- 销售订单主表索引
CREATE INDEX IF NOT EXISTS idx_sales_orders_order_number ON sales_orders(order_number);
CREATE INDEX IF NOT EXISTS idx_sales_orders_sales_person_date ON sales_orders(sales_person_code, order_date);
CREATE INDEX IF NOT EXISTS idx_sales_orders_status_date ON sales_orders(status, order_date);
CREATE INDEX IF NOT EXISTS idx_sales_orders_created_at ON sales_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_sales_orders_is_deleted ON sales_orders(is_deleted);

-- 销售订单明细表索引
CREATE INDEX IF NOT EXISTS idx_sales_order_details_order_sku ON sales_order_details(sales_order_id, sku_code);
CREATE INDEX IF NOT EXISTS idx_sales_order_details_product_color_size ON sales_order_details(product_code, color_code, size_code);
CREATE INDEX IF NOT EXISTS idx_sales_order_details_is_deleted ON sales_order_details(is_deleted);

-- 销售订单客户关联表索引
CREATE INDEX IF NOT EXISTS idx_sales_order_customers_order_customer ON sales_order_customers(sales_order_id, customer_code);
CREATE INDEX IF NOT EXISTS idx_sales_order_customers_customer_status ON sales_order_customers(customer_code, status);
CREATE INDEX IF NOT EXISTS idx_sales_order_customers_is_deleted ON sales_order_customers(is_deleted);

-- 5. 添加约束检查
ALTER TABLE sales_orders 
ADD CONSTRAINT chk_sales_orders_priority 
CHECK (priority IN ('urgent', 'high', 'normal', 'low'));

ALTER TABLE sales_orders 
ADD CONSTRAINT chk_sales_orders_status 
CHECK (status IN ('draft', 'confirmed', 'shipped', 'delivered', 'completed', 'cancelled'));

ALTER TABLE sales_orders 
ADD CONSTRAINT chk_sales_orders_shipping_method 
CHECK (shipping_method IN ('collect', 'prepaid'));

ALTER TABLE sales_order_details 
ADD CONSTRAINT chk_sales_order_details_price_type 
CHECK (price_type IN ('retail', 'pre_order', 'restock', 'spot'));

ALTER TABLE sales_order_customers 
ADD CONSTRAINT chk_sales_order_customers_status 
CHECK (status IN ('pending', 'confirmed', 'shipped', 'delivered', 'completed', 'cancelled'));

-- 6. 添加注释
COMMENT ON TABLE sales_orders IS '销售订单主表';
COMMENT ON TABLE sales_order_details IS '销售订单明细表';
COMMENT ON TABLE sales_order_customers IS '销售订单客户关联表';
