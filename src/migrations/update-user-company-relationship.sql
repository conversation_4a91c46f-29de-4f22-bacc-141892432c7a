-- 用户和公司关系重构的SQL迁移脚本
-- 执行前请备份数据库

-- 1. 为用户表添加新字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_company_admin BOOLEAN DEFAULT FALSE COMMENT '是否为公司管理员';
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_code VARCHAR(50) NULL COMMENT '所属公司编码';

-- 2. 添加外键约束（如果公司表存在）
-- ALTER TABLE users ADD CONSTRAINT fk_users_company_code 
-- FOREIGN KEY (company_code) REFERENCES companies(code) ON DELETE SET NULL;

-- 3. 删除公司员工关联表（如果存在）
DROP TABLE IF EXISTS company_employees;

-- 4. 更新现有数据（可选）
-- 如果需要将现有的公司负责人设置为公司管理员，可以执行以下语句：
-- UPDATE users u 
-- SET is_company_admin = TRUE, 
--     company_code = (SELECT c.code FROM companies c WHERE c.manager_code = u.code)
-- WHERE u.code IN (SELECT manager_code FROM companies WHERE manager_code IS NOT NULL);

-- 5. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_company_code ON users(company_code);
CREATE INDEX IF NOT EXISTS idx_users_is_company_admin ON users(is_company_admin);
CREATE INDEX IF NOT EXISTS idx_users_is_super_admin ON users(is_super_admin);

-- 6. 添加约束检查
-- 确保超级管理员全局唯一（通过应用层控制，这里只是注释说明）
-- 超级管理员不能同时是公司管理员（可选约束）
-- ALTER TABLE users ADD CONSTRAINT chk_admin_exclusive 
-- CHECK (NOT (is_super_admin = TRUE AND is_company_admin = TRUE));

-- 7. 更新用户表注释
COMMENT ON COLUMN users.is_super_admin IS '是否为超级管理员（全局唯一，通过应用层控制）';
COMMENT ON COLUMN users.is_company_admin IS '是否为公司管理员（每个公司可以有多个）';
COMMENT ON COLUMN users.company_code IS '所属公司编码，关联companies表的code字段';

-- 8. 数据验证查询（执行后检查结果）
-- 检查超级管理员数量（应该只有0个或1个）
-- SELECT COUNT(*) as super_admin_count FROM users WHERE is_super_admin = TRUE AND is_deleted = FALSE;

-- 检查公司管理员分布
-- SELECT company_code, COUNT(*) as admin_count 
-- FROM users 
-- WHERE is_company_admin = TRUE AND is_deleted = FALSE 
-- GROUP BY company_code;

-- 检查用户公司关联情况
-- SELECT 
--   COUNT(CASE WHEN company_code IS NOT NULL THEN 1 END) as users_with_company,
--   COUNT(CASE WHEN company_code IS NULL THEN 1 END) as users_without_company,
--   COUNT(*) as total_users
-- FROM users 
-- WHERE is_deleted = FALSE;
