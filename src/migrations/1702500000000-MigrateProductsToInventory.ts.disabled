import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateProductsToInventory1702500000000
  implements MigrationInterface
{
  name = 'MigrateProductsToInventory1702500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('开始迁移现有商品数据到库存表...');

    // 检查 inventory_details 表是否存在
    const tableExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'inventory_details'
      );
    `);

    if (!tableExists[0].exists) {
      console.log('inventory_details 表不存在，先创建表...');

      // 确保uuid-ossp扩展已启用
      await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

      // 创建 inventory_details 表
      await queryRunner.query(`
        CREATE TABLE "inventory_details" (
          "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
          "productCode" varchar(100) NOT NULL,
          "colorCode" varchar(50) NOT NULL,
          "size" varchar(100),
          "sizecode" varchar(100) NOT NULL DEFAULT '',
          "quantity" integer NOT NULL DEFAULT 0,
          "totalstock" integer NOT NULL DEFAULT 0,
          "actualstock" integer NOT NULL DEFAULT 0,
          "reservedstock" integer NOT NULL DEFAULT 0,
          "damagedstock" integer NOT NULL DEFAULT 0,
          "purchasingstock" integer NOT NULL DEFAULT 0,
          "needpurchasestock" integer NOT NULL DEFAULT 0,
          "safetyStock" integer NOT NULL DEFAULT 10,
          "minStock" integer NOT NULL DEFAULT 5,
          "maxStock" integer NOT NULL DEFAULT 1000,
          "avgCost" decimal(10,2) NOT NULL DEFAULT 0,
          "latestCost" decimal(10,2) NOT NULL DEFAULT 0,
          "isActive" boolean NOT NULL DEFAULT true,
          "warehouseLocation" varchar(100) NOT NULL DEFAULT 'A区',
          "totalInbound" integer NOT NULL DEFAULT 0,
          "totalOutbound" integer NOT NULL DEFAULT 0,
          "lastInboundDate" timestamp,
          "lastOutboundDate" timestamp,
          "remark" text,
          "isDeleted" boolean NOT NULL DEFAULT false,
          "createdAt" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "deletedAt" timestamp,
          "colorImages" jsonb,
          "colorPriceInfo" jsonb,
          "colorAccessories" jsonb,
          "skucode" varchar(150)
        )
      `);

      // 创建索引
      await queryRunner.query(`
        CREATE UNIQUE INDEX "IDX_inventory_unique" ON "inventory_details" ("productCode", "colorCode", "size")
      `);

      await queryRunner.query(`
        CREATE INDEX "IDX_inventory_details_productCode" ON "inventory_details" ("productCode")
      `);

      await queryRunner.query(`
        CREATE INDEX "IDX_inventory_details_colorCode" ON "inventory_details" ("colorCode")
      `);

      await queryRunner.query(`
        CREATE INDEX "IDX_inventory_details_isDeleted" ON "inventory_details" ("isDeleted")
      `);

      console.log('inventory_details 表创建完成');
    }

    // 查询所有现有的商品
    const products = await queryRunner.query(`
      SELECT 
        p.code as "productCode",
        p."colorSizeCombinations"
      FROM products p 
      WHERE p."isDeleted" = false 
        AND p."colorSizeCombinations" IS NOT NULL
        AND p."colorSizeCombinations" != '[]'::jsonb
    `);

    console.log(`找到 ${products.length} 个商品需要迁移`);

    let totalInventoryRecords = 0;

    for (const product of products) {
      const { productCode, colorSizeCombinations } = product;

      if (!colorSizeCombinations || colorSizeCombinations.length === 0) {
        console.log(`商品 ${productCode} 没有颜色尺寸组合，跳过`);
        continue;
      }

      // 解析颜色尺寸组合
      for (const colorCombo of colorSizeCombinations) {
        const { colorCode, sizes } = colorCombo;

        if (!colorCode || !sizes || sizes.length === 0) {
          console.log(`商品 ${productCode} 的颜色组合数据不完整，跳过`);
          continue;
        }

        // 为每个尺寸创建库存记录
        for (const size of sizes) {
          // 检查是否已存在该库存记录
          const existingRecord = await queryRunner.query(
            `
            SELECT id FROM inventory_details 
            WHERE "productCode" = $1 AND "colorCode" = $2 AND size = $3 AND "isDeleted" = false
          `,
            [productCode, colorCode, size],
          );

          if (existingRecord.length > 0) {
            console.log(
              `库存记录已存在: ${productCode}-${colorCode}-${size}，跳过`,
            );
            continue;
          }

          // 创建库存记录 - 使用实际的字段名
          await queryRunner.query(
            `
            INSERT INTO inventory_details (
              id,
              "productCode",
              "colorCode",
              size,
              sizecode,
              quantity,
              remark,
              "isDeleted",
              "createdAt",
              "updatedAt"
            ) VALUES (
              uuid_generate_v4(),
              $1,
              $2,
              $3,
              $3,
              0,
              NULL,
              false,
              NOW(),
              NOW()
            )
          `,
            [productCode, colorCode, size],
          );

          totalInventoryRecords++;
        }
      }
    }

    console.log(`迁移完成！总共创建了 ${totalInventoryRecords} 条库存记录`);

    // 验证迁移结果
    const inventoryCount = await queryRunner.query(`
      SELECT COUNT(*) as count FROM inventory_details WHERE "isDeleted" = false
    `);

    console.log(`验证：库存表中现有 ${inventoryCount[0].count} 条记录`);

    // 创建一些基础索引来提高查询性能
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_inventory_details_productCode" 
      ON "inventory_details" ("productCode")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_inventory_details_colorCode" 
      ON "inventory_details" ("colorCode")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_inventory_details_isDeleted" 
      ON "inventory_details" ("isDeleted")
    `);

    console.log('基础索引创建完成');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('回滚：删除所有迁移生成的库存记录...');

    // 删除索引
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_inventory_details_productCode"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_inventory_details_colorCode"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_inventory_details_isDeleted"`,
    );

    // 删除所有库存记录（这是一个危险操作，在生产环境中要谨慎）
    await queryRunner.query(`
      DELETE FROM inventory_details
      WHERE quantity = 0
        AND remark IS NULL
    `);

    console.log('回滚完成');
  }
}
