import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateSkusTable1749365279200 implements MigrationInterface {
  name = 'CreateSkusTable1749365279200';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 检查表是否已存在
    const tableExists = await queryRunner.hasTable('skus');
    if (tableExists) {
      console.log('SKUs table already exists, skipping creation');
      return;
    }

    // 创建 SKUs 表
    await queryRunner.createTable(
      new Table({
        name: 'skus',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'code',
            type: 'varchar',
            length: '100',
            isUnique: true,
            comment: 'SKU编码（自动生成，唯一）',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '200',
            comment: '商品名称',
          },
          {
            name: 'manufacturerCode',
            type: 'varchar',
            length: '100',
            isNullable: true,
            comment: '厂商编码',
          },
          {
            name: 'brandCode',
            type: 'varchar',
            length: '50',
            comment: '品牌编码',
          },
          {
            name: 'supplierCode',
            type: 'varchar',
            length: '50',
            comment: '供应商编码',
          },
          {
            name: 'categoryCode',
            type: 'varchar',
            length: '50',
            comment: '商品分类编码',
          },
          {
            name: 'colorCode',
            type: 'varchar',
            length: '50',
            comment: '颜色编码',
          },
          {
            name: 'craftDescription',
            type: 'varchar',
            length: '500',
            isNullable: true,
            comment: '工艺描述',
          },
          {
            name: 'clothingCost',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
            comment: '服装成本',
          },
          {
            name: 'retailPrice',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
            comment: '零售价',
          },
          {
            name: 'preOrderPrice',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
            comment: '预订价',
          },
          {
            name: 'restockPrice',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
            comment: '补货价',
          },
          {
            name: 'spotPrice',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
            comment: '现货价',
          },
          {
            name: 'accessories',
            type: 'jsonb',
            isNullable: true,
            comment: '辅料数组（JSON格式存储）',
          },
          {
            name: 'images',
            type: 'jsonb',
            isNullable: true,
            comment: 'SKU图片数组（JSON格式存储）',
          },
          {
            name: 'isDeleted',
            type: 'boolean',
            default: false,
            comment: '是否已删除',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            comment: '创建时间',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            comment: '更新时间',
          },
        ],
      }),
      true,
    );

    // 创建索引
    await queryRunner.createIndex(
      'skus',
      new Index('IDX_skus_code', ['code']),
    );
    await queryRunner.createIndex(
      'skus',
      new Index('IDX_skus_brand_code', ['brandCode']),
    );
    await queryRunner.createIndex(
      'skus',
      new Index('IDX_skus_supplier_code', ['supplierCode']),
    );
    await queryRunner.createIndex(
      'skus',
      new Index('IDX_skus_category_code', ['categoryCode']),
    );
    await queryRunner.createIndex(
      'skus',
      new Index('IDX_skus_color_code', ['colorCode']),
    );
    await queryRunner.createIndex(
      'skus',
      new Index('IDX_skus_is_deleted', ['isDeleted']),
    );

    // 为 JSONB 字段创建 GIN 索引
    await queryRunner.query(
      `CREATE INDEX "IDX_skus_accessories_gin" ON "skus" USING gin("accessories")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_skus_images_gin" ON "skus" USING gin("images")`,
    );

    console.log('SKUs table created successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除 GIN 索引
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_skus_images_gin"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_skus_accessories_gin"`);

    // 删除其他索引
    await queryRunner.dropIndex('skus', 'IDX_skus_is_deleted');
    await queryRunner.dropIndex('skus', 'IDX_skus_color_code');
    await queryRunner.dropIndex('skus', 'IDX_skus_category_code');
    await queryRunner.dropIndex('skus', 'IDX_skus_supplier_code');
    await queryRunner.dropIndex('skus', 'IDX_skus_brand_code');
    await queryRunner.dropIndex('skus', 'IDX_skus_code');

    // 删除表
    await queryRunner.dropTable('skus');

    console.log('SKUs table dropped successfully');
  }
}
