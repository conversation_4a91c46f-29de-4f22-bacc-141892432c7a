import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveMainImagesFromProduct1749365279188
  implements MigrationInterface
{
  name = 'RemoveMainImagesFromProduct1749365279188';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 检查字段是否存在再删除
    const columnExists = await queryRunner.hasColumn('products', 'mainImages');
    if (columnExists) {
      await queryRunner.query(
        `ALTER TABLE "products" DROP COLUMN "mainImages"`,
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "products" ADD "mainImages" text array`,
    );
  }
}
