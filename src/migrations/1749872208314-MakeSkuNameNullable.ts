import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeSkuNameNullable1749872208314 implements MigrationInterface {
  name = 'MakeSkuNameNullable1749872208314';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 修改SKU表的name字段为可空
    await queryRunner.query(
      `ALTER TABLE "skus" ALTER COLUMN "name" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚：将name字段改回非空（注意：如果有空值数据，回滚会失败）
    await queryRunner.query(
      `UPDATE "skus" SET "name" = '未命名商品' WHERE "name" IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "skus" ALTER COLUMN "name" SET NOT NULL`,
    );
  }
}
