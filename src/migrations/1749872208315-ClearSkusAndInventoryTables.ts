import { MigrationInterface, QueryRunner } from 'typeorm';

export class ClearSkusAndInventoryTables1749872208315 implements MigrationInterface {
  name = 'ClearSkusAndInventoryTables1749872208315';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('开始清空SKU和库存表数据...');

    // 先清空库存表（因为有外键约束）
    const inventoryCount = await queryRunner.query(`
      SELECT COUNT(*) as count FROM skus_inventory
    `);
    console.log(`库存表当前有 ${inventoryCount[0].count} 条记录`);

    await queryRunner.query(`DELETE FROM skus_inventory`);
    console.log('库存表数据已清空');

    // 再清空SKU表
    const skuCount = await queryRunner.query(`
      SELECT COUNT(*) as count FROM skus
    `);
    console.log(`SKU表当前有 ${skuCount[0].count} 条记录`);

    await queryRunner.query(`DELETE FROM skus`);
    console.log('SKU表数据已清空');

    console.log('清空操作完成，新的SKU编码格式已生效');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('注意：此迁移的回滚操作无法恢复已删除的数据');
    console.log('如需恢复数据，请从备份中还原');
  }
}
