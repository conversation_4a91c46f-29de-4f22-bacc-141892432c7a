import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateCommissionContractsTables1749872208310
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建佣金发货申请书主表
    await queryRunner.createTable(
      new Table({
        name: 'commission_contracts',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'contractNumber',
            type: 'varchar',
            length: '50',
            isUnique: true,
            comment: '申请书编号',
          },
          {
            name: 'companyName',
            type: 'varchar',
            length: '200',
            comment: '贸易公司名称',
          },
          {
            name: 'partyBName',
            type: 'varchar',
            length: '100',
            comment: '乙方姓名',
          },
          {
            name: 'partyBIdCard',
            type: 'varchar',
            length: '18',
            comment: '乙方身份证号',
          },
          {
            name: 'applicationYear',
            type: 'int',
            comment: '申请年份',
          },
          {
            name: 'applicationMonth',
            type: 'int',
            comment: '申请月份',
          },
          {
            name: 'applicationDay',
            type: 'int',
            comment: '申请日期',
          },
          {
            name: 'actualShipmentAmount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            comment: '实际发货金额',
          },
          {
            name: 'actualShipmentAmountInWords',
            type: 'varchar',
            length: '100',
            comment: '实际发货金额大写（自动生成）',
          },
          {
            name: 'requestedDebtAmount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            comment: '本次申请欠款发货金额',
          },
          {
            name: 'requestedDebtAmountInWords',
            type: 'varchar',
            length: '100',
            comment: '本次申请欠款发货金额大写（自动生成）',
          },
          {
            name: 'repaymentYear',
            type: 'int',
            comment: '归还年份',
          },
          {
            name: 'repaymentMonth',
            type: 'int',
            comment: '归还月份',
          },
          {
            name: 'repaymentDay',
            type: 'int',
            comment: '归还日期',
          },
          {
            name: 'totalAccumulatedDebt',
            type: 'decimal',
            precision: 15,
            scale: 2,
            comment: '累计欠款金额',
          },
          {
            name: 'totalAccumulatedDebtInWords',
            type: 'varchar',
            length: '100',
            comment: '累计欠款金额大写（自动生成）',
          },
          {
            name: 'status',
            type: 'enum',
            enum: [
              'draft',
              'submitted',
              'approved',
              'rejected',
              'completed',
              'cancelled',
            ],
            default: "'draft'",
            comment: '申请书状态',
          },
          {
            name: 'paymentStatus',
            type: 'enum',
            enum: ['unpaid', 'partial_paid', 'fully_paid', 'overdue'],
            default: "'unpaid'",
            comment: '还款状态',
          },
          {
            name: 'paidAmount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            default: 0,
            comment: '已还款金额',
          },
          {
            name: 'remainingDebtAmount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            default: 0,
            comment: '剩余欠款金额',
          },
          {
            name: 'lastPaymentDate',
            type: 'date',
            isNullable: true,
            comment: '最后还款日期',
          },
          {
            name: 'remarks',
            type: 'text',
            isNullable: true,
            comment: '备注',
          },
          {
            name: 'createdByUserCode',
            type: 'varchar',
            length: '50',
            isNullable: true,
            comment: '创建人员编码',
          },
          {
            name: 'approvedByUserCode',
            type: 'varchar',
            length: '50',
            isNullable: true,
            comment: '审批人员编码',
          },
          {
            name: 'approvedAt',
            type: 'timestamp',
            isNullable: true,
            comment: '审批时间',
          },
          {
            name: 'isDeleted',
            type: 'boolean',
            default: false,
            comment: '是否已删除',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            comment: '创建时间',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            comment: '更新时间',
          },
          {
            name: 'deletedAt',
            type: 'timestamp',
            isNullable: true,
            comment: '删除时间',
          },
        ],
      }),
      true,
    );

    // 创建佣金发货申请书明细表
    await queryRunner.createTable(
      new Table({
        name: 'commission_contract_details',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'commissionContractId',
            type: 'uuid',
            comment: '佣金发货申请书ID',
          },
          {
            name: 'brandName',
            type: 'varchar',
            length: '100',
            comment: '品牌名称',
          },
          {
            name: 'quantity',
            type: 'int',
            comment: '预定数量',
          },
          {
            name: 'amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            comment: '品牌货款金额',
          },
          {
            name: 'sortOrder',
            type: 'int',
            default: 1,
            comment: '排序序号',
          },
          {
            name: 'isDeleted',
            type: 'boolean',
            default: false,
            comment: '是否已删除',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            comment: '创建时间',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            comment: '更新时间',
          },
          {
            name: 'deletedAt',
            type: 'timestamp',
            isNullable: true,
            comment: '删除时间',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['commissionContractId'],
            referencedTableName: 'commission_contracts',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
      true,
    );

    // 创建索引
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_commission_contracts_contract_number"
      ON "commission_contracts" ("contractNumber")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_commission_contracts_party_application"
      ON "commission_contracts" ("partyBName", "applicationYear", "applicationMonth")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_commission_contracts_status_created"
      ON "commission_contracts" ("status", "createdAt")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_commission_contracts_status_created"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_commission_contracts_party_application"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_commission_contracts_contract_number"`,
    );

    // 删除表
    await queryRunner.dropTable('commission_contract_details');
    await queryRunner.dropTable('commission_contracts');
  }
}
