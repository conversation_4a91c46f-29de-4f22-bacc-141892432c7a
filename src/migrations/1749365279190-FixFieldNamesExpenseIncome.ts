import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixFieldNamesExpenseIncome1749365279190
  implements MigrationInterface
{
  name = 'FixFieldNamesExpenseIncome1749365279190';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 修复字段名：create_date -> createDate

    // 检查并修复 expenses 表
    const expensesHasCreateDate = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'expenses' AND column_name = 'create_date'
        `);

    if (expensesHasCreateDate.length > 0) {
      await queryRunner.query(
        `ALTER TABLE "expenses" RENAME COLUMN "create_date" TO "createDate"`,
      );
    }

    // 检查并修复 expense_details 表
    const expenseDetailsHasCreateDate = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'expense_details' AND column_name = 'create_date'
        `);

    if (expenseDetailsHasCreateDate.length > 0) {
      // 检查目标字段是否已存在
      const expenseDetailsHasCreateDateCamel = await queryRunner.query(`
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'expense_details' AND column_name = 'createDate'
            `);

      if (expenseDetailsHasCreateDateCamel.length === 0) {
        await queryRunner.query(
          `ALTER TABLE "expense_details" RENAME COLUMN "create_date" TO "createDate"`,
        );
      } else {
        // 如果目标字段已存在，删除旧字段
        await queryRunner.query(
          `ALTER TABLE "expense_details" DROP COLUMN IF EXISTS "create_date"`,
        );
      }
    }

    // 检查并修复 incomes 表
    const incomesHasCreateDate = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'incomes' AND column_name = 'create_date'
        `);

    if (incomesHasCreateDate.length > 0) {
      // 检查目标字段是否已存在
      const incomesHasCreateDateCamel = await queryRunner.query(`
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'incomes' AND column_name = 'createDate'
            `);

      if (incomesHasCreateDateCamel.length === 0) {
        await queryRunner.query(
          `ALTER TABLE "incomes" RENAME COLUMN "create_date" TO "createDate"`,
        );
      } else {
        // 如果目标字段已存在，删除旧字段
        await queryRunner.query(
          `ALTER TABLE "incomes" DROP COLUMN IF EXISTS "create_date"`,
        );
      }
    }

    // 检查并修复 income_details 表
    const incomeDetailsHasCreateDate = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'income_details' AND column_name = 'create_date'
        `);

    if (incomeDetailsHasCreateDate.length > 0) {
      // 检查目标字段是否已存在
      const incomeDetailsHasCreateDateCamel = await queryRunner.query(`
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'income_details' AND column_name = 'createDate'
            `);

      if (incomeDetailsHasCreateDateCamel.length === 0) {
        await queryRunner.query(
          `ALTER TABLE "income_details" RENAME COLUMN "create_date" TO "createDate"`,
        );
      } else {
        // 如果目标字段已存在，删除旧字段
        await queryRunner.query(
          `ALTER TABLE "income_details" DROP COLUMN IF EXISTS "create_date"`,
        );
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚：createDate -> create_date

    // 检查并回滚 expenses 表
    const expensesHasCreateDate = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'expenses' AND column_name = 'createDate'
        `);

    if (expensesHasCreateDate.length > 0) {
      await queryRunner.query(
        `ALTER TABLE "expenses" RENAME COLUMN "createDate" TO "create_date"`,
      );
    }

    // 检查并回滚 expense_details 表
    const expenseDetailsHasCreateDate = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'expense_details' AND column_name = 'createDate'
        `);

    if (expenseDetailsHasCreateDate.length > 0) {
      await queryRunner.query(
        `ALTER TABLE "expense_details" RENAME COLUMN "createDate" TO "create_date"`,
      );
    }

    // 检查并回滚 incomes 表
    const incomesHasCreateDate = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'incomes' AND column_name = 'createDate'
        `);

    if (incomesHasCreateDate.length > 0) {
      await queryRunner.query(
        `ALTER TABLE "incomes" RENAME COLUMN "createDate" TO "create_date"`,
      );
    }

    // 检查并回滚 income_details 表
    const incomeDetailsHasCreateDate = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'income_details' AND column_name = 'createDate'
        `);

    if (incomeDetailsHasCreateDate.length > 0) {
      await queryRunner.query(
        `ALTER TABLE "income_details" RENAME COLUMN "createDate" TO "create_date"`,
      );
    }
  }
}
