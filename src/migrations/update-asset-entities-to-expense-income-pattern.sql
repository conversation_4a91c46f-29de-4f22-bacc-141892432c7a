-- 数据库迁移脚本：将四个资产实体更新为支出收入模块模式
-- 执行前请备份数据库
-- 执行时间：预计需要几分钟，取决于数据量

-- 开始事务
BEGIN;

-- =====================================================
-- 1. 更新运营资产主表 (operating_assets)
-- =====================================================

-- 添加 createDate 字段
ALTER TABLE operating_assets 
ADD COLUMN IF NOT EXISTS create_date VARCHAR(20) 
COMMENT '创建日期（字符串格式，必填）';

-- 将现有的 createdAt 数据迁移到 createDate（转换为日期字符串格式）
UPDATE operating_assets 
SET create_date = TO_CHAR(created_at, 'YYYY-MM-DD')
WHERE create_date IS NULL;

-- 设置 createDate 为非空
ALTER TABLE operating_assets 
ALTER COLUMN create_date SET NOT NULL;

-- 删除自动时间戳字段
ALTER TABLE operating_assets 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at,
DROP COLUMN IF EXISTS deleted_at;

-- =====================================================
-- 2. 更新运营资产明细表 (operating_asset_details)
-- =====================================================

-- 添加 createDate 字段
ALTER TABLE operating_asset_details 
ADD COLUMN IF NOT EXISTS create_date VARCHAR(20) 
COMMENT '创建日期（字符串格式，必填）';

-- 将现有的 createdAt 数据迁移到 createDate
UPDATE operating_asset_details 
SET create_date = TO_CHAR(created_at, 'YYYY-MM-DD')
WHERE create_date IS NULL;

-- 设置 createDate 为非空
ALTER TABLE operating_asset_details 
ALTER COLUMN create_date SET NOT NULL;

-- 删除自动时间戳字段
ALTER TABLE operating_asset_details 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS deleted_at;

-- =====================================================
-- 3. 更新研发成本主表 (rd_costs)
-- =====================================================

-- 添加 createDate 字段
ALTER TABLE rd_costs 
ADD COLUMN IF NOT EXISTS create_date VARCHAR(20) 
COMMENT '创建日期（字符串格式，必填）';

-- 将现有的 createdAt 数据迁移到 createDate
UPDATE rd_costs 
SET create_date = TO_CHAR(created_at, 'YYYY-MM-DD')
WHERE create_date IS NULL;

-- 设置 createDate 为非空
ALTER TABLE rd_costs 
ALTER COLUMN create_date SET NOT NULL;

-- 删除自动时间戳字段
ALTER TABLE rd_costs 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at,
DROP COLUMN IF EXISTS deleted_at;

-- =====================================================
-- 4. 更新研发成本明细表 (rd_cost_details)
-- =====================================================

-- 添加 createDate 字段
ALTER TABLE rd_cost_details 
ADD COLUMN IF NOT EXISTS create_date VARCHAR(20) 
COMMENT '创建日期（字符串格式，必填）';

-- 将现有的 createdAt 数据迁移到 createDate
UPDATE rd_cost_details 
SET create_date = TO_CHAR(created_at, 'YYYY-MM-DD')
WHERE create_date IS NULL;

-- 设置 createDate 为非空
ALTER TABLE rd_cost_details 
ALTER COLUMN create_date SET NOT NULL;

-- 删除自动时间戳字段
ALTER TABLE rd_cost_details 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS deleted_at;

-- =====================================================
-- 5. 更新租赁资产主表 (rental_assets)
-- =====================================================

-- 添加 createDate 字段
ALTER TABLE rental_assets 
ADD COLUMN IF NOT EXISTS create_date VARCHAR(20) 
COMMENT '创建日期（字符串格式，必填）';

-- 将现有的 createdAt 数据迁移到 createDate
UPDATE rental_assets 
SET create_date = TO_CHAR(created_at, 'YYYY-MM-DD')
WHERE create_date IS NULL;

-- 设置 createDate 为非空
ALTER TABLE rental_assets 
ALTER COLUMN create_date SET NOT NULL;

-- 删除自动时间戳字段
ALTER TABLE rental_assets 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at,
DROP COLUMN IF EXISTS deleted_at;

-- =====================================================
-- 6. 更新租赁资产明细表 (rental_asset_details)
-- =====================================================

-- 添加 createDate 字段
ALTER TABLE rental_asset_details 
ADD COLUMN IF NOT EXISTS create_date VARCHAR(20) 
COMMENT '创建日期（字符串格式，必填）';

-- 将现有的 createdAt 数据迁移到 createDate
UPDATE rental_asset_details 
SET create_date = TO_CHAR(created_at, 'YYYY-MM-DD')
WHERE create_date IS NULL;

-- 设置 createDate 为非空
ALTER TABLE rental_asset_details 
ALTER COLUMN create_date SET NOT NULL;

-- 删除自动时间戳字段
ALTER TABLE rental_asset_details 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS deleted_at;

-- =====================================================
-- 7. 更新固定资产主表 (fixed_assets)
-- =====================================================

-- 添加 createDate 字段
ALTER TABLE fixed_assets 
ADD COLUMN IF NOT EXISTS create_date VARCHAR(20) 
COMMENT '创建日期（字符串格式，必填）';

-- 将现有的 createdAt 数据迁移到 createDate
UPDATE fixed_assets 
SET create_date = TO_CHAR(created_at, 'YYYY-MM-DD')
WHERE create_date IS NULL;

-- 设置 createDate 为非空
ALTER TABLE fixed_assets 
ALTER COLUMN create_date SET NOT NULL;

-- 删除自动时间戳字段
ALTER TABLE fixed_assets 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at,
DROP COLUMN IF EXISTS deleted_at;

-- =====================================================
-- 8. 更新固定资产明细表 (fixed_asset_details)
-- =====================================================

-- 添加 createDate 字段
ALTER TABLE fixed_asset_details 
ADD COLUMN IF NOT EXISTS create_date VARCHAR(20) 
COMMENT '创建日期（字符串格式，必填）';

-- 将现有的 createdAt 数据迁移到 createDate
UPDATE fixed_asset_details 
SET create_date = TO_CHAR(created_at, 'YYYY-MM-DD')
WHERE create_date IS NULL;

-- 设置 createDate 为非空
ALTER TABLE fixed_asset_details 
ALTER COLUMN create_date SET NOT NULL;

-- 删除自动时间戳字段
ALTER TABLE fixed_asset_details 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS deleted_at;

-- =====================================================
-- 9. 创建索引以优化查询性能
-- =====================================================

-- 为 createDate 字段创建索引
CREATE INDEX IF NOT EXISTS idx_operating_assets_create_date ON operating_assets(create_date);
CREATE INDEX IF NOT EXISTS idx_operating_asset_details_create_date ON operating_asset_details(create_date);
CREATE INDEX IF NOT EXISTS idx_rd_costs_create_date ON rd_costs(create_date);
CREATE INDEX IF NOT EXISTS idx_rd_cost_details_create_date ON rd_cost_details(create_date);
CREATE INDEX IF NOT EXISTS idx_rental_assets_create_date ON rental_assets(create_date);
CREATE INDEX IF NOT EXISTS idx_rental_asset_details_create_date ON rental_asset_details(create_date);
CREATE INDEX IF NOT EXISTS idx_fixed_assets_create_date ON fixed_assets(create_date);
CREATE INDEX IF NOT EXISTS idx_fixed_asset_details_create_date ON fixed_asset_details(create_date);

-- 提交事务
COMMIT;

-- =====================================================
-- 迁移完成提示
-- =====================================================
-- 迁移脚本执行完成！
-- 
-- 已完成的更改：
-- 1. 所有资产主表和明细表都添加了 create_date 字段
-- 2. 现有的时间戳数据已迁移到新的 create_date 字段
-- 3. 删除了所有自动时间戳字段 (created_at, updated_at, deleted_at)
-- 4. 为 create_date 字段创建了索引以优化查询性能
-- 
-- 注意事项：
-- - 现有数据的 create_date 将设置为原 created_at 的日期部分
-- - 新记录需要前端提供 create_date 字段
-- - 请测试应用程序以确保所有功能正常工作
