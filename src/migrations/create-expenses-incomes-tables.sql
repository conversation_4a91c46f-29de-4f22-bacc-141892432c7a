-- 创建支出和收入模块的数据库表
-- 执行前请备份数据库

-- 1. 创建支出主表
CREATE TABLE IF NOT EXISTS expenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '支出总金额',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间'
);

-- 2. 创建支出明细表
CREATE TABLE IF NOT EXISTS expense_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    expense_id UUID NOT NULL COMMENT '支出ID',
    supplier_code VARCHAR(50) NOT NULL COMMENT '供应商编码（必填）',
    supplier_name VARCHAR(255) NULL COMMENT '供应商名称（冗余字段，便于查询）',
    amount DECIMAL(15,2) NOT NULL COMMENT '支出金额（保留两位小数）',
    screenshot VARCHAR(500) NULL COMMENT '截图URL（选填）',
    remark TEXT NULL COMMENT '备注（选填）',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    -- 外键约束
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE CASCADE
);

-- 3. 创建收入主表
CREATE TABLE IF NOT EXISTS incomes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '收入总金额',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间'
);

-- 4. 创建收入明细表
CREATE TABLE IF NOT EXISTS income_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    income_id UUID NOT NULL COMMENT '收入ID',
    customer_code VARCHAR(50) NOT NULL COMMENT '客户编码（必填）',
    customer_name VARCHAR(255) NULL COMMENT '客户名称（冗余字段，便于查询）',
    responsible_user_code VARCHAR(50) NOT NULL COMMENT '负责人编码（必填）',
    responsible_user_name VARCHAR(255) NULL COMMENT '负责人姓名（冗余字段，便于查询）',
    amount DECIMAL(15,2) NOT NULL COMMENT '收入金额（保留两位小数）',
    screenshot VARCHAR(500) NULL COMMENT '截图URL（选填）',
    remark TEXT NULL COMMENT '备注（选填）',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    -- 外键约束
    FOREIGN KEY (income_id) REFERENCES incomes(id) ON DELETE CASCADE
);

-- 5. 创建索引以提高查询性能
-- 支出明细表索引
CREATE INDEX IF NOT EXISTS idx_expense_details_expense_id ON expense_details(expense_id);
CREATE INDEX IF NOT EXISTS idx_expense_details_supplier_code ON expense_details(supplier_code);
CREATE INDEX IF NOT EXISTS idx_expense_details_created_at ON expense_details(created_at);
CREATE INDEX IF NOT EXISTS idx_expense_details_is_deleted ON expense_details(is_deleted);
CREATE INDEX IF NOT EXISTS idx_expense_details_supplier_name ON expense_details(supplier_name);

-- 收入明细表索引
CREATE INDEX IF NOT EXISTS idx_income_details_income_id ON income_details(income_id);
CREATE INDEX IF NOT EXISTS idx_income_details_customer_code ON income_details(customer_code);
CREATE INDEX IF NOT EXISTS idx_income_details_responsible_user_code ON income_details(responsible_user_code);
CREATE INDEX IF NOT EXISTS idx_income_details_created_at ON income_details(created_at);
CREATE INDEX IF NOT EXISTS idx_income_details_is_deleted ON income_details(is_deleted);
CREATE INDEX IF NOT EXISTS idx_income_details_customer_name ON income_details(customer_name);
CREATE INDEX IF NOT EXISTS idx_income_details_responsible_user_name ON income_details(responsible_user_name);

-- 6. 添加约束检查
-- 支出金额必须大于0
ALTER TABLE expense_details ADD CONSTRAINT chk_expense_amount_positive CHECK (amount > 0);

-- 收入金额必须大于0
ALTER TABLE income_details ADD CONSTRAINT chk_income_amount_positive CHECK (amount > 0);

-- 7. 添加表注释
COMMENT ON TABLE expenses IS '支出主表 - 记录支出总金额';
COMMENT ON TABLE expense_details IS '支出明细表 - 记录具体的支出明细，关联供应商';
COMMENT ON TABLE incomes IS '收入主表 - 记录收入总金额';
COMMENT ON TABLE income_details IS '收入明细表 - 记录具体的收入明细，关联客户和负责人';

-- 8. 初始化数据（可选）
-- 插入初始的支出和收入记录
INSERT INTO expenses (total_amount) VALUES (0) ON CONFLICT DO NOTHING;
INSERT INTO incomes (total_amount) VALUES (0) ON CONFLICT DO NOTHING;

-- 9. 数据验证查询（执行后检查结果）
-- 检查表是否创建成功
-- SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('expenses', 'expense_details', 'incomes', 'income_details');

-- 检查索引是否创建成功
-- SELECT indexname FROM pg_indexes WHERE tablename IN ('expense_details', 'income_details');

-- 检查约束是否创建成功
-- SELECT conname, contype FROM pg_constraint WHERE conrelid IN (
--     SELECT oid FROM pg_class WHERE relname IN ('expense_details', 'income_details')
-- );
