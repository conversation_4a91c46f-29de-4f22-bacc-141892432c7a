import { MigrationInterface, QueryRunner } from 'typeorm';

export class ClearAndRecreateSalesOrdersTables1749872208320
  implements MigrationInterface
{
  name = 'ClearAndRecreateSalesOrdersTables1749872208320';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. 检查表是否存在，如果存在则清空数据但保留结构
    const salesOrdersExists = await queryRunner.hasTable('sales_orders');
    if (salesOrdersExists) {
      // 清空现有数据
      await queryRunner.query(`TRUNCATE TABLE "sales_order_customers" CASCADE`);
      await queryRunner.query(`TRUNCATE TABLE "sales_order_details" CASCADE`);
      await queryRunner.query(`TRUNCATE TABLE "sales_orders" CASCADE`);

      // 删除表以重建结构
      await queryRunner.query(
        `DROP TABLE IF EXISTS "sales_order_customers" CASCADE`,
      );
      await queryRunner.query(
        `DROP TABLE IF EXISTS "sales_order_details" CASCADE`,
      );
      await queryRunner.query(`DROP TABLE IF EXISTS "sales_orders" CASCADE`);
    }

    // 2. 创建销售订单主表
    await queryRunner.query(`
      CREATE TABLE "sales_orders" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        "orderNumber" varchar(50) UNIQUE NOT NULL,
        "salesPersonCode" varchar(50) NOT NULL,
        "orderDate" date NOT NULL,
        "expectedDeliveryDate" date,
        "priority" varchar(20) DEFAULT 'normal' CHECK ("priority" IN ('urgent', 'high', 'normal', 'low')),
        "status" varchar(20) DEFAULT 'draft' CHECK ("status" IN ('draft', 'pending_shipment', 'completed', 'cancelled')),
        
        -- 业务选项字段
        "isDropShipping" boolean DEFAULT false,
        "isReleased" boolean DEFAULT false,
        
        -- 物流相关字段
        "shippingMethod" varchar(20) DEFAULT 'collect' CHECK ("shippingMethod" IN ('collect', 'prepaid')),
        "shippingFee" decimal(10,2) DEFAULT 0,
        "shippingCompany" varchar(200),
        "trackingNumber" varchar(100),
        "shipperUserCode" varchar(50),
        
        -- 客户信息
        "customerCode" varchar(50) NOT NULL,
        "dropShipCustomerCode" varchar(50),
        
        -- 金额统计字段
        "totalQuantity" integer DEFAULT 0,
        "totalAmount" decimal(15,2) DEFAULT 0,
        "grandTotal" decimal(15,2) DEFAULT 0,
        
        -- 审核相关字段
        "createdByUserCode" varchar(50),
        "confirmedByUserCode" varchar(50),
        "confirmedAt" timestamp,
        
        "remark" text,
        
        -- 系统字段
        "isDeleted" boolean DEFAULT false,
        "createdAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        "deletedAt" timestamp
      )
    `);

    // 3. 创建销售订单明细表
    await queryRunner.query(`
      CREATE TABLE "sales_order_details" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        "salesOrderId" uuid NOT NULL,
        
        -- 库存记录关联
        "inventoryId" uuid NOT NULL,
        
        -- 商品信息字段（冗余存储）
        "productCode" varchar(100) NOT NULL,
        "colorCode" varchar(50) NOT NULL,
        "sizeCode" varchar(10) NOT NULL,
        "skuCode" varchar(150) NOT NULL,
        "productName" varchar(200),
        "colorName" varchar(100),
        
        -- 数量相关字段
        "quantity" integer NOT NULL,
        "shippedQuantity" integer DEFAULT 0,
        "pendingShipQuantity" integer DEFAULT 0,
        "reservedQuantity" integer DEFAULT 0,
        "stockAtOrder" integer DEFAULT 0,
        
        -- 价格相关字段
        "priceType" varchar(20) DEFAULT 'retail' CHECK ("priceType" IN ('retail', 'pre_order', 'restock', 'spot')),
        "unitPrice" decimal(10,2) NOT NULL,
        "totalAmount" decimal(15,2) NOT NULL,
        "discountAmount" decimal(10,2) DEFAULT 0,
        "actualAmount" decimal(15,2) NOT NULL,
        
        -- 成本相关字段
        "costPrice" decimal(10,2) DEFAULT 0,
        "totalCost" decimal(15,2) DEFAULT 0,
        "grossProfit" decimal(15,2) DEFAULT 0,
        
        -- 统计字段
        "categoryCode" varchar(50),
        "brandCode" varchar(50),
        "supplierCode" varchar(50),
        
        -- 业务字段
        "expectedShipDate" date,
        "remark" text,
        
        -- 系统字段
        "isDeleted" boolean DEFAULT false,
        "createdAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY ("salesOrderId") REFERENCES "sales_orders"("id") ON DELETE CASCADE
      )
    `);

    // 4. 创建销售订单客户关联表
    await queryRunner.query(`
      CREATE TABLE "sales_order_customers" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        "salesOrderId" uuid NOT NULL,
        "customerCode" varchar(50) NOT NULL,
        "customerName" varchar(100),
        
        -- 分配数量和金额
        "allocatedQuantity" integer DEFAULT 0,
        "allocatedAmount" decimal(15,2) DEFAULT 0,
        "allocatedShippingFee" decimal(10,2) DEFAULT 0,
        "totalPayable" decimal(15,2) DEFAULT 0,
        
        -- 客户状态
        "status" varchar(20) DEFAULT 'pending' CHECK ("status" IN ('pending', 'confirmed', 'shipped', 'delivered', 'completed', 'cancelled')),
        
        -- 收货信息
        "receiverName" varchar(100),
        "receiverPhone" varchar(20),
        "shippingAddress" varchar(500),
        "provinceCode" integer,
        "city" varchar(100),
        "district" varchar(100),
        
        -- 物流信息
        "shippingCompany" varchar(200),
        "trackingNumber" varchar(100),
        "shippedAt" timestamp,
        "deliveredAt" timestamp,
        
        -- 优先级和特殊要求
        "priority" integer DEFAULT 0,
        "isUrgent" boolean DEFAULT false,
        "specialRequirements" text,
        
        -- 商品明细分配
        "itemAllocations" jsonb,
        
        "remark" text,
        
        -- 系统字段
        "isDeleted" boolean DEFAULT false,
        "createdAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY ("salesOrderId") REFERENCES "sales_orders"("id") ON DELETE CASCADE
      )
    `);

    // 5. 创建索引
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_orders_orderNumber" ON "sales_orders" ("orderNumber")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_orders_salesPerson_date" ON "sales_orders" ("salesPersonCode", "orderDate")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_orders_status_date" ON "sales_orders" ("status", "orderDate")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_orders_createdAt" ON "sales_orders" ("createdAt")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_orders_isDeleted" ON "sales_orders" ("isDeleted")`,
    );

    await queryRunner.query(
      `CREATE INDEX "IDX_sales_order_details_order_sku" ON "sales_order_details" ("salesOrderId", "skuCode")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_order_details_product_color_size" ON "sales_order_details" ("productCode", "colorCode", "sizeCode")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_order_details_isDeleted" ON "sales_order_details" ("isDeleted")`,
    );

    await queryRunner.query(
      `CREATE INDEX "IDX_sales_order_customers_order_customer" ON "sales_order_customers" ("salesOrderId", "customerCode")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_order_customers_customer_status" ON "sales_order_customers" ("customerCode", "status")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_sales_order_customers_isDeleted" ON "sales_order_customers" ("isDeleted")`,
    );

    // 6. 添加注释
    await queryRunner.query(
      `COMMENT ON TABLE "sales_orders" IS '销售订单主表'`,
    );
    await queryRunner.query(
      `COMMENT ON TABLE "sales_order_details" IS '销售订单明细表'`,
    );
    await queryRunner.query(
      `COMMENT ON TABLE "sales_order_customers" IS '销售订单客户关联表'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除表
    await queryRunner.query(
      `DROP TABLE IF EXISTS "sales_order_customers" CASCADE`,
    );
    await queryRunner.query(
      `DROP TABLE IF EXISTS "sales_order_details" CASCADE`,
    );
    await queryRunner.query(`DROP TABLE IF EXISTS "sales_orders" CASCADE`);
  }
}
