import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPinyinCodeRemarksToCustomers1749871588444
  implements MigrationInterface
{
  name = 'AddPinyinCodeRemarksToCustomers1749871588444';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First delete related data to avoid foreign key constraint violations
    await queryRunner.query(`DELETE FROM "customer_profile_details"`);

    // Then delete all existing customer data as requested
    await queryRunner.query(`DELETE FROM "customers"`);

    // Add the new required pinyinCode column
    await queryRunner.query(
      `ALTER TABLE "customers" ADD COLUMN IF NOT EXISTS "pinyinCode" character varying(50)`,
    );

    // Add the new optional remarks column
    await queryRunner.query(
      `ALTER TABLE "customers" ADD COLUMN IF NOT EXISTS "remarks" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the added columns
    await queryRunner.query(`ALTER TABLE "customers" DROP COLUMN "remarks"`);
    await queryRunner.query(`ALTER TABLE "customers" DROP COLUMN "pinyinCode"`);
  }
}
