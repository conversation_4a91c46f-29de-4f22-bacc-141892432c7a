import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSkusInventoryTable1749872208313
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 确保 uuid-ossp 扩展已启用
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

    // 创建库存表
    await queryRunner.query(`
      CREATE TABLE "skus_inventory" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "skuId" uuid NOT NULL,
        "size" varchar(20) NOT NULL,
        "currentStock" int DEFAULT 0,
        "purchasingStock" int DEFAULT 0,
        "needRestockStock" int DEFAULT 0,
        "isOutOfStock" boolean DEFAULT false,
        "remarks" text,
        "lastInventoryDate" timestamp,
        "lastInventoryCount" int,
        "isDeleted" boolean DEFAULT false,
        "createdAt" timestamp DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" timestamp DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建唯一索引：同一SKU的同一尺码只能有一条库存记录
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_skus_inventory_sku_size_unique" 
      ON "skus_inventory" ("skuId", "size") 
      WHERE "isDeleted" = false
    `);

    // 创建外键约束
    await queryRunner.query(`
      ALTER TABLE "skus_inventory" 
      ADD CONSTRAINT "FK_skus_inventory_skuId" 
      FOREIGN KEY ("skuId") REFERENCES "skus"("id") ON DELETE CASCADE
    `);

    // 创建其他索引
    await queryRunner.query(`
      CREATE INDEX "IDX_skus_inventory_size" ON "skus_inventory" ("size")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_skus_inventory_currentStock" ON "skus_inventory" ("currentStock")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_skus_inventory_isOutOfStock" ON "skus_inventory" ("isOutOfStock")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_skus_inventory_isDeleted" ON "skus_inventory" ("isDeleted")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_skus_inventory_updatedAt" ON "skus_inventory" ("updatedAt")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除外键约束
    await queryRunner.query(`
      ALTER TABLE "skus_inventory" DROP CONSTRAINT "FK_skus_inventory_skuId"
    `);

    // 删除索引
    await queryRunner.query(`DROP INDEX "IDX_skus_inventory_sku_size_unique"`);
    await queryRunner.query(`DROP INDEX "IDX_skus_inventory_size"`);
    await queryRunner.query(`DROP INDEX "IDX_skus_inventory_currentStock"`);
    await queryRunner.query(`DROP INDEX "IDX_skus_inventory_isOutOfStock"`);
    await queryRunner.query(`DROP INDEX "IDX_skus_inventory_isDeleted"`);
    await queryRunner.query(`DROP INDEX "IDX_skus_inventory_updatedAt"`);

    // 删除表
    await queryRunner.query(`DROP TABLE "skus_inventory"`);
  }
}
