-- 为SKU表添加images字段的迁移脚本
-- 执行时间：2024-01-01

-- 添加images字段到skus表
ALTER TABLE skus ADD COLUMN images jsonb;

-- 添加字段注释
COMMENT ON COLUMN skus.images IS 'SKU图片数组（JSON格式存储）';

-- 创建索引以提高查询性能（可选）
CREATE INDEX idx_skus_images ON skus USING gin(images);

-- 验证字段添加成功
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'skus' AND column_name = 'images';
