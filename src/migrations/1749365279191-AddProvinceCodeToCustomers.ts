import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProvinceCodeToCustomers1749365279191
  implements MigrationInterface
{
  name = 'AddProvinceCodeToCustomers1749365279191';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "customers" ADD COLUMN IF NOT EXISTS "provinceCode" integer`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "customers" DROP COLUMN "provinceCode"`,
    );
  }
}
