import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBankAccountFieldsToUsers1749365279192
  implements MigrationInterface
{
  name = 'AddBankAccountFieldsToUsers1749365279192';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "bankAccountName" character varying(100)`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "bankAccountNumber" character varying(50)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "bankAccountNumber"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "bankAccountName"`,
    );
  }
}
