import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Brand } from '@/brands/entities/brand.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';

// 辅料数量接口
export interface AccessoryQuantity {
  accessoryId: string;
  quantity: number;
  colorCode?: string; // 新增：关联特定颜色，如果为空则适用于所有颜色
}

// 价格调整接口
export interface PriceAdjustment {
  clothingCostAdjustment?: number;
  accessoryCostAdjustment?: number;
  retailPriceAdjustment?: number;
  preOrderPriceAdjustment?: number;
  restockPriceAdjustment?: number;
  spotPriceAdjustment?: number;
}

// 颜色尺寸组合接口（扩展版）
export interface ColorSizeCombination {
  colorCode: string;
  sizes: string[];

  // 颜色级别的辅料配置（可选，每种颜色不一定需要指定辅料）
  accessories?: AccessoryQuantity[];

  // 颜色级别的价格调整（可选，相对于基础价格的增减）
  priceAdjustments?: PriceAdjustment;

  // 颜色专属图片（可选）
  images?: string[];

  // 颜色级别的备注（可选）
  remark?: string;
}

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '商品ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  @ApiProperty({ description: '商品编码（必填，唯一）', example: 'PROD001' })
  code: string;

  @Column({ type: 'varchar', length: 200 })
  @ApiProperty({ description: '商品名称（必填）', example: '时尚T恤' })
  name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @ApiProperty({
    description: '厂商编码（选填）',
    example: 'MFG001',
    required: false,
  })
  manufacturerCode: string | null;

  // 品牌关联（多对一）
  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '品牌信息',
    type: () => Brand,
  })
  brand: Brand;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({
    description: '品牌编码（必填，关联brand表）',
    example: 'BRAND001',
  })
  brandCode: string;

  // 供应商关联（多对一）
  @ManyToOne(() => Supplier)
  @JoinColumn({ name: 'supplierCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '供应商信息',
    type: () => Supplier,
  })
  supplier: Supplier;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({
    description: '供应商编码（必填，关联supplier表）',
    example: 'SUP001',
  })
  supplierCode: string;

  // 6种价格字段
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '服装成本',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '服装成本（必填）', example: 50.0 })
  clothingCost: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '辅料成本',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '辅料成本（必填）', example: 10.0 })
  accessoryCost: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '零售价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '零售价（必填）', example: 120.0 })
  retailPrice: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '预订价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '预订价（必填）', example: 100.0 })
  preOrderPrice: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '补货价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '补货价（必填）', example: 110.0 })
  restockPrice: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '现货价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '现货价（必填）', example: 115.0 })
  spotPrice: number;

  // 辅料数组（JSON格式存储）
  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({
    description: '辅料数组（包含辅料ID和数量）',
    example: [
      { accessoryId: 'acc-uuid-1', quantity: 2 },
      { accessoryId: 'acc-uuid-2', quantity: 1 },
    ],
    required: false,
  })
  accessories: AccessoryQuantity[] | null;

  // 颜色尺寸组合（JSON格式存储）
  @Column({ type: 'jsonb' })
  @ApiProperty({
    description: '颜色尺寸组合（颜色编码对应多个尺寸）',
    example: [
      { colorCode: 'COLOR001', sizes: ['S', 'M', 'L'] },
      { colorCode: 'COLOR002', sizes: ['M', 'L', 'XL'] },
    ],
  })
  colorSizeCombinations: ColorSizeCombination[];

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;
}
