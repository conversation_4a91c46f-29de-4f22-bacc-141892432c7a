import {
  Controller,
  Post,
  Get,
  Body,
  Logger,
  UseGuards,
  Request,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { LogoutDto } from './dto/logout.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ChangePasswordDto } from './dto/change-password.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({
    status: 200,
    description: 'User has been successfully logged in.',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid credentials.',
  })
  async login(@Body() loginDto: LoginDto) {
    this.logger.log(`Login attempt for user: ${loginDto.userCode}`);
    return this.authService.login(loginDto.userCode, loginDto.password);
  }

  // 移除管理员注册方法，使用专门的用户管理接口

  // 移除首次注册方法，使用初始化服务创建默认管理员

  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change user password' })
  @ApiResponse({
    status: 200,
    description: 'Password has been successfully changed.',
  })
  @ApiResponse({
    status: 401,
    description: 'Current password is incorrect.',
  })
  async changePassword(
    @Request() req: any,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    this.logger.log(`Change password attempt for user code: ${req.user.code}`);
    return this.authService.changePassword(
      req.user.code,
      changePasswordDto.currentPassword,
      changePasswordDto.newPassword,
    );
  }

  // 移除管理员相关方法，使用专门的用户管理接口

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '用户退出登录' })
  @ApiResponse({
    status: 200,
    description: '退出登录成功',
  })
  @ApiResponse({
    status: 401,
    description: 'Token无效或已过期',
  })
  async logout(@Request() req: any, @Body() logoutDto: LogoutDto) {
    this.logger.log(`Logout attempt for user ID: ${req.user.id}`);

    // 从请求头中提取token
    const authHeader = req.headers.authorization;
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      throw new UnauthorizedException('Token不存在');
    }

    if (logoutDto.logoutAllDevices) {
      return this.authService.logoutAllDevices(req.user.id);
    } else {
      return this.authService.logout(req.user.id, token, logoutDto.deviceId);
    }
  }

  @Post('logout-all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '退出所有设备' })
  @ApiResponse({
    status: 200,
    description: '已退出所有设备',
  })
  @ApiResponse({
    status: 401,
    description: 'Token无效或已过期',
  })
  async logoutAllDevices(@Request() req: any) {
    this.logger.log(`Logout all devices attempt for user ID: ${req.user.id}`);
    return this.authService.logoutAllDevices(req.user.id);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({
    status: 200,
    description: '获取用户信息成功',
  })
  @ApiResponse({
    status: 401,
    description: 'Token无效或已过期',
  })
  async getProfile(@Request() req: any) {
    this.logger.log(`Get profile attempt for user code: ${req.user.code}`);
    return this.authService.getProfile(req.user.code);
  }

  // 移除管理员获取用户方法，使用专门的用户管理接口
}
