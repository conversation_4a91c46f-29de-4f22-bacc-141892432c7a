import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
  Logger,
  Inject,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '@/users/users.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

import { JwtPayload } from './interfaces/jwt-payload.interface';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async validateUser(userCode: string, password: string): Promise<any> {
    this.logger.debug(`Validating user with user code: ${userCode}`);

    const user = await this.usersService.findByCode(userCode);
    if (!user) {
      return null;
    }

    const isPasswordValid = await this.usersService.verifyPassword(
      password,
      user.password,
    );
    if (!isPasswordValid) {
      return null;
    }

    const { password: _, ...result } = user;
    return result;
  }

  async login(userCode: string, password: string) {
    this.logger.log(`User login attempt: ${userCode}`);

    const user = await this.validateUser(userCode, password);
    if (!user) {
      throw new UnauthorizedException('用户编号或密码错误');
    }

    const payload: JwtPayload = { sub: user.code, userCode: user.code };

    return {
      code: 200,
      data: {
        accessToken: this.jwtService.sign(payload, { expiresIn: '7d' }),
        user,
      },
      message: '登录成功',
    };
  }

  // 移除adminRegister方法，使用专门的用户管理接口

  // 移除firstRegister方法，使用初始化服务创建默认管理员

  async changePassword(
    userCode: string,
    currentPassword: string,
    newPassword: string,
  ) {
    this.logger.log(`Change password attempt for user code: ${userCode}`);

    // Find the user
    const user = await this.usersService.findByCode(userCode);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // Verify current password
    const isPasswordValid = await this.usersService.verifyPassword(
      currentPassword,
      user.password,
    );
    if (!isPasswordValid) {
      throw new UnauthorizedException('当前密码错误');
    }

    // Update password
    await this.usersService.update(
      userCode,
      { password: newPassword },
      userCode,
    );

    return {
      code: 200,
      data: null,
      message: '密码修改成功',
    };
  }

  // 移除管理员相关方法，使用专门的用户管理接口

  /**
   * 用户退出登录
   */
  async logout(userCode: string, token: string, deviceId?: string) {
    this.logger.log(
      `User ${userCode} logging out${deviceId ? ` from device ${deviceId}` : ''}`,
    );

    try {
      // 解析token获取过期时间
      const decoded = this.jwtService.decode(token) as any;
      if (decoded && decoded.exp) {
        // 计算token剩余有效时间（秒）
        const now = Math.floor(Date.now() / 1000);
        const ttl = decoded.exp - now;

        if (ttl > 0) {
          // 将token加入黑名单，设置过期时间为token的剩余有效时间
          const blacklistKey = `blacklist:${token}`;
          await this.cacheManager.set(blacklistKey, true, ttl * 1000); // cache-manager使用毫秒
          this.logger.debug(`Token added to blacklist with TTL: ${ttl}s`);
        }
      }

      return {
        code: 200,
        data: null,
        message: '退出登录成功',
      };
    } catch (error) {
      this.logger.error(`Error during logout: ${error.message}`);
      // 即使加入黑名单失败，也返回成功（客户端可以删除token）
      return {
        code: 200,
        data: null,
        message: '退出登录成功',
      };
    }
  }

  /**
   * 退出所有设备
   */
  async logoutAllDevices(userCode: string) {
    this.logger.log(`User ${userCode} logging out from all devices`);

    try {
      // 为用户生成一个新的token版本号，使所有旧token失效
      const versionKey = `user_token_version:${userCode}`;
      const currentVersion =
        (await this.cacheManager.get<number>(versionKey)) || 0;
      const newVersion = currentVersion + 1;

      // 设置新版本号，有效期7天（与token过期时间一致）
      await this.cacheManager.set(
        versionKey,
        newVersion,
        7 * 24 * 60 * 60 * 1000,
      );

      this.logger.debug(
        `Updated token version for user ${userCode} to ${newVersion}`,
      );

      return {
        code: 200,
        data: null,
        message: '已退出所有设备',
      };
    } catch (error) {
      this.logger.error(`Error during logout all devices: ${error.message}`);
      throw new BadRequestException('退出登录失败，请重试');
    }
  }

  /**
   * 检查token是否在黑名单中
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistKey = `blacklist:${token}`;
      const isBlacklisted = await this.cacheManager.get(blacklistKey);
      return !!isBlacklisted;
    } catch (error) {
      this.logger.error(`Error checking token blacklist: ${error.message}`);
      return false; // 如果检查失败，默认不在黑名单中
    }
  }

  /**
   * 检查用户token版本是否有效
   */
  async isTokenVersionValid(
    userCode: string,
    tokenIssuedAt: number,
  ): Promise<boolean> {
    try {
      const versionKey = `user_token_version:${userCode}`;
      const currentVersion =
        (await this.cacheManager.get<number>(versionKey)) || 0;

      // 如果没有版本记录，说明用户从未执行过"退出所有设备"，token有效
      if (currentVersion === 0) {
        return true;
      }

      // 检查token签发时间是否在版本更新之后
      // 这里需要在JWT payload中包含版本信息，暂时简化处理
      return true;
    } catch (error) {
      this.logger.error(`Error checking token version: ${error.message}`);
      return true; // 如果检查失败，默认有效
    }
  }

  /**
   * 获取用户信息
   */
  async getProfile(userCode: string) {
    this.logger.log(`Getting profile for user ${userCode}`);

    try {
      const user = await this.usersService.findByCode(userCode);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 移除敏感信息
      const { password, ...userProfile } = user;

      return {
        code: 200,
        data: userProfile,
        message: '获取用户信息成功',
      };
    } catch (error) {
      this.logger.error(`Error getting profile: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('获取用户信息失败');
    }
  }
}
