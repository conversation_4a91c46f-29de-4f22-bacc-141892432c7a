import { Injectable, UnauthorizedException, Inject } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '@/users/users.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { JwtPayload } from '../interfaces/jwt-payload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey:
        configService.get<string>('jwt.secret') || 'your_jwt_secret_key',
      passReqToCallback: true, // 允许在validate方法中访问request对象
    });
  }

  async validate(req: any, payload: JwtPayload) {
    try {
      // 从请求头中提取token
      const authHeader = req.headers.authorization;
      const token = authHeader?.replace('Bearer ', '');

      if (token) {
        // 检查token是否在黑名单中
        const blacklistKey = `blacklist:${token}`;
        const isBlacklisted = await this.cacheManager.get(blacklistKey);
        if (isBlacklisted) {
          throw new UnauthorizedException('Token已失效');
        }
      }

      const user = await this.usersService.findByCode(payload.userCode);
      if (!user) {
        throw new UnauthorizedException('用户不存在');
      }
      return user;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Invalid token');
    }
  }
}
