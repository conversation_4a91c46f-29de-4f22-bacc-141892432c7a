import { IsString, IsNotEmpty, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    description: '用户编号',
    example: 'husky',
  })
  @IsString()
  @IsNotEmpty()
  userCode: string;

  @ApiProperty({
    description: 'The password of the user',
    example: '541888',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 20)
  password: string;
}
