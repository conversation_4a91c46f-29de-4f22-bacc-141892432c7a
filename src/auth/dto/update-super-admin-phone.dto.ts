import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsChinaMobilePhone } from '@/common/validators/is-china-mobile-phone.validator';

export class UpdateSuperAdminPhoneDto {
  @ApiProperty({
    description: '新手机号',
    example: '13800138000',
  })
  @IsString()
  @IsNotEmpty()
  @IsChinaMobilePhone({ message: '请输入有效的中国大陆手机号' })
  newPhoneNumber: string;
}
