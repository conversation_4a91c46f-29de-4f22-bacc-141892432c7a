import { IsString, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsChinaMobilePhone } from '@/common/validators/is-china-mobile-phone.validator';

export class AdminUpdateUserDto {
  @ApiProperty({
    description: '目标用户ID',
    example: 'uuid-string',
  })
  @IsString()
  @IsNotEmpty()
  targetUserId: string;

  @ApiProperty({
    description: '手机号',
    example: '13800138000',
    required: false,
  })
  @IsString()
  @IsOptional()
  @IsChinaMobilePhone({ message: '请输入有效的中国大陆手机号' })
  phoneNumber?: string;

  @ApiProperty({
    description: '昵称',
    example: 'user001',
    required: false,
  })
  @IsString()
  @IsOptional()
  nickname?: string;

  @ApiProperty({
    description: '真实姓名',
    example: '张三',
    required: false,
  })
  @IsString()
  @IsOptional()
  realName?: string;

  @ApiProperty({
    description: '用户编号',
    example: '00001',
    required: false,
  })
  @IsString()
  @IsOptional()
  userCode?: string;

  @ApiProperty({
    description: '是否超级管理员',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isSuperAdmin?: boolean;

  @ApiProperty({
    description: '职位ID',
    example: 'uuid-string',
    required: false,
  })
  @IsString()
  @IsOptional()
  positionId?: string;

  @ApiProperty({
    description: '是否启用',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: '关联的公司ID',
    example: 'uuid-string',
    required: false,
  })
  @IsString()
  @IsOptional()
  shopId?: string;
}
