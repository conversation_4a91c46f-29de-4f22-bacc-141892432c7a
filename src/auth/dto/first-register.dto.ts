import { IsString, IsNotEmpty, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsChinaMobilePhone } from '@/common/validators/is-china-mobile-phone.validator';

export class FirstRegisterDto {
  @ApiProperty({
    description: 'The phone number of the user',
    example: '13800138000',
  })
  @IsString()
  @IsNotEmpty()
  @IsChinaMobilePhone({ message: '请输入有效的中国大陆手机号' })
  phoneNumber: string;

  @ApiProperty({
    description: 'The password of the user',
    example: 'password123',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 20)
  password: string;

  @ApiProperty({
    description: 'The nickname of the user',
    example: 'husky',
  })
  @IsString()
  @IsNotEmpty()
  nickname: string;

  @ApiProperty({
    description: 'The real name of the user',
    example: '胡斯阔',
  })
  @IsString()
  @IsNotEmpty()
  realName: string;
}
