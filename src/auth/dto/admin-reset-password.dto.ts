import { IsString, IsNotEmpty, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AdminResetPasswordDto {
  @ApiProperty({
    description: '目标用户ID',
    example: 'uuid-string',
  })
  @IsString()
  @IsNotEmpty()
  targetUserId: string;

  @ApiProperty({
    description: '新密码',
    example: 'newpassword123',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 20)
  newPassword: string;
}
