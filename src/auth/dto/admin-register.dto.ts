import {
  IsString,
  IsNotEmpty,
  Length,
  IsOptional,
  IsBoolean,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AdminRegisterDto {
  @ApiProperty({
    description: '密码',
    example: 'password123',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 20)
  password: string;

  @ApiProperty({
    description: '用户编号',
    example: '00001',
  })
  @IsString()
  @IsNotEmpty()
  userCode: string;

  @ApiProperty({
    description: '手机号',
    example: '13800138000',
    required: false,
  })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({
    description: '昵称',
    example: 'user001',
    required: false,
  })
  @IsString()
  @IsOptional()
  nickname?: string;

  @ApiProperty({
    description: '真实姓名',
    example: '张三',
    required: false,
  })
  @IsString()
  @IsOptional()
  realName?: string;

  @ApiProperty({
    description: '是否启用',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: '关联的公司ID',
    example: 'uuid-string',
    required: false,
  })
  @IsString()
  @IsOptional()
  shopId?: string;
}
