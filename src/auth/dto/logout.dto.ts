import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class LogoutDto {
  @ApiProperty({
    description: '设备标识（可选，用于多设备登录管理）',
    required: false,
    example: 'web-browser-chrome',
  })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty({
    description: '是否退出所有设备',
    required: false,
    example: false,
  })
  @IsOptional()
  logoutAllDevices?: boolean;
}
