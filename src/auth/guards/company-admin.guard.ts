import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UsersService } from '@/users/users.service';

@Injectable()
export class CompanyAdminGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private usersService: UsersService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('用户未认证');
    }

    // 检查用户是否为超级管理员（超级管理员拥有所有权限）
    const isSuperAdmin = await this.usersService.isSuperAdmin(user.code);
    if (isSuperAdmin) {
      return true;
    }

    // 检查用户是否为公司管理员
    const isCompanyAdmin = await this.usersService.isCompanyAdmin(user.code);
    if (!isCompanyAdmin) {
      throw new ForbiddenException('需要公司管理员权限');
    }

    return true;
  }
}

/**
 * 装饰器：要求超级管理员或公司管理员权限
 */
export const RequireCompanyAdmin = () => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    // 这个装饰器可以与 @UseGuards(CompanyAdminGuard) 一起使用
  };
};
