import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UsersService } from '@/users/users.service';

export enum AdminLevel {
  SUPER_ADMIN = 'super_admin',
  COMPANY_ADMIN = 'company_admin',
  SAME_COMPANY = 'same_company', // 同公司用户
}

export const ADMIN_LEVELS_KEY = 'admin_levels';

/**
 * 设置所需的管理员级别
 * @param levels 允许的管理员级别数组
 */
export const RequireAdminLevels = (...levels: AdminLevel[]) =>
  SetMetadata(ADMIN_LEVELS_KEY, levels);

@Injectable()
export class FlexibleAdminGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private usersService: UsersService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredLevels = this.reflector.getAllAndOverride<AdminLevel[]>(
      ADMIN_LEVELS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredLevels || requiredLevels.length === 0) {
      return true; // 如果没有设置权限要求，则允许访问
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('用户未认证');
    }

    const userInfo = await this.usersService.findByCode(user.code);
    if (!userInfo) {
      throw new ForbiddenException('用户不存在');
    }

    // 检查超级管理员权限
    if (requiredLevels.includes(AdminLevel.SUPER_ADMIN) && userInfo.isSuperAdmin) {
      return true;
    }

    // 检查公司管理员权限
    if (requiredLevels.includes(AdminLevel.COMPANY_ADMIN) && userInfo.isCompanyAdmin) {
      return true;
    }

    // 检查同公司权限（需要从请求参数中获取目标公司信息）
    if (requiredLevels.includes(AdminLevel.SAME_COMPANY)) {
      const targetCompanyCode = this.extractCompanyCodeFromRequest(request);
      if (targetCompanyCode && userInfo.companyCode === targetCompanyCode) {
        return true;
      }
    }

    throw new ForbiddenException('权限不足');
  }

  /**
   * 从请求中提取公司编码
   * 可以从路径参数、查询参数或请求体中获取
   */
  private extractCompanyCodeFromRequest(request: any): string | null {
    // 从路径参数中获取
    if (request.params?.companyCode) {
      return request.params.companyCode;
    }

    // 从查询参数中获取
    if (request.query?.companyCode) {
      return request.query.companyCode;
    }

    // 从请求体中获取
    if (request.body?.companyCode) {
      return request.body.companyCode;
    }

    return null;
  }
}

/**
 * 常用权限组合的便捷装饰器
 */

// 只允许超级管理员
export const SuperAdminOnly = () =>
  RequireAdminLevels(AdminLevel.SUPER_ADMIN);

// 允许超级管理员或公司管理员
export const AdminOnly = () =>
  RequireAdminLevels(AdminLevel.SUPER_ADMIN, AdminLevel.COMPANY_ADMIN);

// 允许超级管理员、公司管理员或同公司用户
export const CompanyMembersOnly = () =>
  RequireAdminLevels(
    AdminLevel.SUPER_ADMIN,
    AdminLevel.COMPANY_ADMIN,
    AdminLevel.SAME_COMPANY,
  );
