import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    
    if (!requiredRoles) {
      return true;
    }
    
    const { user } = context.switchToHttp().getRequest();
    
    // 如果用户是超级管理员，允许访问所有资源
    if (user.isAdmin) {
      return true;
    }
    
    // 检查用户是否有所需角色
    return requiredRoles.some(role => {
      if (role === 'admin') {
        return user.isAdmin;
      }
      
      // 如果有其他角色逻辑，可以在这里添加
      return false;
    });
  }
}
