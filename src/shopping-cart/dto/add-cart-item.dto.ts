import { ApiProperty } from '@nestjs/swagger';
import {
  IsUUID,
  IsInt,
  IsDecimal,
  IsOptional,
  IsString,
  Min,
  Max,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class AddCartItemDto {
  @ApiProperty({
    description: '库存记录ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @IsUUID(4, { message: '库存记录ID必须是有效的UUID' })
  inventoryId: string;

  @ApiProperty({
    description: '购买数量',
    example: 2,
    minimum: 1,
    maximum: 9999,
  })
  @IsInt({ message: '购买数量必须是整数' })
  @Min(1, { message: '购买数量不能小于1' })
  @Max(9999, { message: '购买数量不能超过9999' })
  quantity: number;

  @ApiProperty({
    description: '单价',
    example: 299.99,
  })
  @Transform(({ value }) => parseFloat(value))
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: '单价必须是有效的数字，最多2位小数' },
  )
  unitPrice: number;

  @ApiProperty({
    description: '商品备注',
    example: '客户指定颜色',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '商品备注必须是字符串' })
  remark?: string;
}

export class UpdateCartItemQuantityDto {
  @ApiProperty({
    description: '新的购买数量',
    example: 3,
    minimum: 1,
    maximum: 9999,
  })
  @IsInt({ message: '购买数量必须是整数' })
  @Min(1, { message: '购买数量不能小于1' })
  @Max(9999, { message: '购买数量不能超过9999' })
  quantity: number;
}

export class UpdateCartItemDto {
  @ApiProperty({
    description: '购买数量',
    example: 3,
    minimum: 1,
    maximum: 9999,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: '购买数量必须是整数' })
  @Min(1, { message: '购买数量不能小于1' })
  @Max(9999, { message: '购买数量不能超过9999' })
  quantity?: number;

  @ApiProperty({
    description: '单价',
    example: 299.99,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: '单价必须是有效的数字，最多2位小数' },
  )
  unitPrice?: number;

  @ApiProperty({
    description: '商品备注',
    example: '客户指定颜色',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '商品备注必须是字符串' })
  remark?: string;
}
