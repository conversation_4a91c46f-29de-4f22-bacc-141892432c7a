import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsDateString,
  IsEnum,
  IsBoolean,
  IsDecimal,
  MaxLength,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { SalesOrderPriority } from '@/sales-orders/entities/sales-order.entity';

export class CartToOrderDto {
  @ApiProperty({
    description: '客户编码',
    example: 'CUST001',
  })
  @IsString({ message: '客户编码必须是字符串' })
  @MaxLength(50, { message: '客户编码长度不能超过50个字符' })
  customerCode: string;

  @ApiProperty({
    description: '订单日期',
    example: '2025-01-19',
  })
  @IsDateString({}, { message: '订单日期格式不正确' })
  orderDate: string;

  @ApiProperty({
    description: '期望交货日期',
    example: '2025-01-25',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '期望交货日期格式不正确' })
  expectedDeliveryDate?: string;

  @ApiProperty({
    description: '订单优先级',
    enum: SalesOrderPriority,
    example: SalesOrderPriority.NORMAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(SalesOrderPriority, { message: '订单优先级值无效' })
  priority?: SalesOrderPriority;

  @ApiProperty({
    description: '是否代发',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '是否代发必须是布尔值' })
  isDropShipping?: boolean;

  @ApiProperty({
    description: '是否已发布',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '是否已发布必须是布尔值' })
  isReleased?: boolean;

  @ApiProperty({
    description: '配送方式',
    example: '快递配送',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '配送方式必须是字符串' })
  @MaxLength(100, { message: '配送方式长度不能超过100个字符' })
  shippingMethod?: string;

  @ApiProperty({
    description: '运费',
    example: 15.00,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: '运费必须是有效的数字，最多2位小数' },
  )
  shippingFee?: number;

  @ApiProperty({
    description: '物流公司',
    example: '顺丰快递',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '物流公司必须是字符串' })
  @MaxLength(100, { message: '物流公司长度不能超过100个字符' })
  shippingCompany?: string;

  @ApiProperty({
    description: '发货人用户编码',
    example: 'user002',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '发货人用户编码必须是字符串' })
  @MaxLength(50, { message: '发货人用户编码长度不能超过50个字符' })
  shipperUserCode?: string;

  @ApiProperty({
    description: '代发客户编码',
    example: 'CUST002',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '代发客户编码必须是字符串' })
  @MaxLength(50, { message: '代发客户编码长度不能超过50个字符' })
  dropShipCustomerCode?: string;

  @ApiProperty({
    description: '订单备注',
    example: '客户要求加急处理',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '订单备注必须是字符串' })
  remark?: string;

  @ApiProperty({
    description: '选中的购物车商品ID列表（如果不提供则转换所有商品）',
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: '选中商品ID列表必须是数组' })
  @IsString({ each: true, message: '商品ID必须是字符串' })
  selectedItemIds?: string[];
}

export class CartItemPriceUpdateDto {
  @ApiProperty({
    description: '商品ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @IsString({ message: '商品ID必须是字符串' })
  itemId: string;

  @ApiProperty({
    description: '价格类型',
    example: 'retail',
    enum: ['retail', 'wholesale', 'spot', 'preorder'],
  })
  @IsString({ message: '价格类型必须是字符串' })
  @IsEnum(['retail', 'wholesale', 'spot', 'preorder'], {
    message: '价格类型必须是 retail、wholesale、spot 或 preorder 之一',
  })
  priceType: string;

  @ApiProperty({
    description: '单价',
    example: 299.99,
  })
  @Transform(({ value }) => parseFloat(value))
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: '单价必须是有效的数字，最多2位小数' },
  )
  unitPrice: number;

  @ApiProperty({
    description: '折扣金额',
    example: 20.00,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  @IsDecimal(
    { decimal_digits: '0,2' },
    { message: '折扣金额必须是有效的数字，最多2位小数' },
  )
  discountAmount?: number;
}

export class CartToOrderWithPricesDto extends CartToOrderDto {
  @ApiProperty({
    description: '商品价格更新列表',
    type: [CartItemPriceUpdateDto],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: '商品价格更新列表必须是数组' })
  @ValidateNested({ each: true })
  @Type(() => CartItemPriceUpdateDto)
  itemPriceUpdates?: CartItemPriceUpdateDto[];
}
