import { ApiProperty } from '@nestjs/swagger';

export class ShoppingCartItemDto {
  @ApiProperty({
    description: '购物车商品ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: '库存记录ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  inventoryId: string;

  @ApiProperty({
    description: 'SKU编码',
    example: 'SKU001',
  })
  skuCode: string;

  @ApiProperty({
    description: '商品名称',
    example: '春季新款T恤',
  })
  productName: string;

  @ApiProperty({
    description: '颜色编码',
    example: 'RED001',
  })
  colorCode: string;

  @ApiProperty({
    description: '颜色名称',
    example: '红色',
  })
  colorName: string;

  @ApiProperty({
    description: '尺码',
    example: 'M',
  })
  size: string;

  @ApiProperty({
    description: '品牌编码',
    example: 'BRAND001',
  })
  brandCode: string;

  @ApiProperty({
    description: '品牌名称',
    example: '知名品牌',
  })
  brandName: string;

  @ApiProperty({
    description: '购买数量',
    example: 2,
  })
  quantity: number;

  @ApiProperty({
    description: '单价',
    example: 299.99,
  })
  unitPrice: number;

  @ApiProperty({
    description: '小计金额',
    example: 599.98,
  })
  totalAmount: number;

  @ApiProperty({
    description: '当前库存数量',
    example: 50,
  })
  currentStock: number;

  @ApiProperty({
    description: '是否库存充足',
    example: true,
  })
  isStockSufficient: boolean;

  @ApiProperty({
    description: '添加时的库存数量',
    example: 52,
    required: false,
  })
  stockAtAdd?: number;

  @ApiProperty({
    description: '商品备注',
    example: '客户指定颜色',
    required: false,
  })
  remark?: string;

  @ApiProperty({
    description: '添加时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;
}

export class ShoppingCartDto {
  @ApiProperty({
    description: '购物车ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: '用户编码',
    example: 'user001',
  })
  userCode: string;

  @ApiProperty({
    description: '用户名称',
    example: '张三',
    required: false,
  })
  userName?: string;

  @ApiProperty({
    description: '购物车商品总数量',
    example: 5,
  })
  totalQuantity: number;

  @ApiProperty({
    description: '购物车商品总金额',
    example: 1500.00,
  })
  totalAmount: number;

  @ApiProperty({
    description: '商品种类数',
    example: 3,
  })
  itemCount: number;

  @ApiProperty({
    description: '是否为空购物车',
    example: false,
  })
  isEmpty: boolean;

  @ApiProperty({
    description: '最后更新时间',
    example: '2025-01-19T08:00:00.000Z',
    required: false,
  })
  lastUpdatedAt?: Date;

  @ApiProperty({
    description: '购物车备注',
    example: '临时保存的商品选择',
    required: false,
  })
  remark?: string;

  @ApiProperty({
    description: '购物车商品列表',
    type: [ShoppingCartItemDto],
  })
  items: ShoppingCartItemDto[];

  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;
}

export class ShoppingCartSummaryDto {
  @ApiProperty({
    description: '购物车ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: '用户编码',
    example: 'user001',
  })
  userCode: string;

  @ApiProperty({
    description: '购物车商品总数量',
    example: 5,
  })
  totalQuantity: number;

  @ApiProperty({
    description: '购物车商品总金额',
    example: 1500.00,
  })
  totalAmount: number;

  @ApiProperty({
    description: '商品种类数',
    example: 3,
  })
  itemCount: number;

  @ApiProperty({
    description: '是否为空购物车',
    example: false,
  })
  isEmpty: boolean;

  @ApiProperty({
    description: '最后更新时间',
    example: '2025-01-19T08:00:00.000Z',
    required: false,
  })
  lastUpdatedAt?: Date;
}
