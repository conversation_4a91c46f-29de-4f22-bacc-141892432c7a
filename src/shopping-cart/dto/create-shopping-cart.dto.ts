import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, MaxLength } from 'class-validator';

export class CreateShoppingCartDto {
  @ApiProperty({
    description: '用户编码',
    example: 'user001',
  })
  @IsString({ message: '用户编码必须是字符串' })
  @MaxLength(50, { message: '用户编码长度不能超过50个字符' })
  userCode: string;

  @ApiProperty({
    description: '购物车备注',
    example: '临时保存的商品选择',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '购物车备注必须是字符串' })
  remark?: string;
}
