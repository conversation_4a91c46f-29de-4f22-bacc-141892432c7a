import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { ShoppingCart } from './entities/shopping-cart.entity';
import { ShoppingCartItem } from './entities/shopping-cart-item.entity';
import { SkuInventory } from '@/skus-inventory/entities/sku-inventory.entity';
import { User } from '@/users/entities/user.entity';
import {
  AddCartItemDto,
  UpdateCartItemDto,
  UpdateCartItemQuantityDto,
} from './dto/add-cart-item.dto';
import {
  ShoppingCartDto,
  ShoppingCartItemDto,
  ShoppingCartSummaryDto,
} from './dto/shopping-cart-response.dto';
import { CartToOrderWithPricesDto } from './dto/cart-to-order.dto';
import {
  CreateSalesOrderDto,
  CreateSalesOrderDetailDto,
} from '@/sales-orders/dto/create-sales-order.dto';
import { PriceType } from '@/sales-orders/entities/sales-order-detail.entity';
import { ShippingMethod } from '@/sales-orders/entities/sales-order.entity';

@Injectable()
export class ShoppingCartService {
  private readonly logger = new Logger(ShoppingCartService.name);

  constructor(
    @InjectRepository(ShoppingCart)
    private readonly cartRepository: Repository<ShoppingCart>,
    @InjectRepository(ShoppingCartItem)
    private readonly cartItemRepository: Repository<ShoppingCartItem>,
    @InjectRepository(SkuInventory)
    private readonly skuInventoryRepository: Repository<SkuInventory>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 获取或创建用户购物车
   */
  async getOrCreateCart(userCode: string): Promise<ShoppingCartDto> {
    this.logger.log(`获取或创建用户购物车: userCode=${userCode}`);

    // 验证用户是否存在
    const user = await this.userRepository.findOne({
      where: { code: userCode, isDeleted: false },
    });
    if (!user) {
      throw new NotFoundException(`用户 ${userCode} 不存在`);
    }

    // 查找现有购物车
    let cart = await this.cartRepository.findOne({
      where: { userCode, isDeleted: false },
      relations: [
        'items',
        'items.inventory',
        'items.inventory.sku',
        'items.inventory.sku.brand',
        'items.inventory.sku.color',
      ],
    });

    // 如果不存在则创建新购物车
    if (!cart) {
      cart = this.cartRepository.create({
        userCode,
        totalQuantity: 0,
        totalAmount: 0,
        lastUpdatedAt: new Date(),
      });
      cart = await this.cartRepository.save(cart);
      cart.items = [];
    } else {
      // 检查购物车中是否有商品档案信息过时的商品
      await this.checkAndUpdateOutdatedItems(cart);
    }

    return this.transformToDto(cart);
  }

  /**
   * 检查并更新过时的商品档案信息
   */
  private async checkAndUpdateOutdatedItems(cart: ShoppingCart): Promise<void> {
    const itemsToUpdate: ShoppingCartItem[] = [];

    for (const item of cart.items.filter((item) => !item.isDeleted)) {
      if (item.inventory && item.inventory.sku) {
        const sku = item.inventory.sku;
        const brand = sku.brand;
        const color = sku.color;

        // 检查商品档案信息是否有变化
        const hasChanges =
          item.skuCode !== sku.code ||
          item.productName !== sku.name ||
          item.colorCode !== sku.colorCode ||
          item.colorName !== color?.name ||
          item.size !== item.inventory.size ||
          item.brandCode !== sku.brandCode ||
          item.brandName !== brand?.name;

        if (hasChanges) {
          // 更新商品档案快照信息
          item.skuCode = sku.code;
          item.productName = sku.name;
          item.colorCode = sku.colorCode;
          item.colorName = color?.name || null;
          item.size = item.inventory.size;
          item.brandCode = sku.brandCode;
          item.brandName = brand?.name || null;

          itemsToUpdate.push(item);
        }
      }
    }

    // 批量更新有变化的商品
    if (itemsToUpdate.length > 0) {
      await this.cartItemRepository.save(itemsToUpdate);
      this.logger.log(
        `自动更新了 ${itemsToUpdate.length} 个购物车商品的档案信息`,
      );
    }
  }

  /**
   * 添加商品到购物车
   */
  async addItem(
    userCode: string,
    addItemDto: AddCartItemDto,
  ): Promise<ShoppingCartDto> {
    this.logger.log(
      `添加商品到购物车: userCode=${userCode}, inventoryId=${addItemDto.inventoryId}, quantity=${addItemDto.quantity}`,
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取购物车
      const cart = await this.getOrCreateCart(userCode);

      // 验证库存记录是否存在
      const inventory = await this.skuInventoryRepository.findOne({
        where: { id: addItemDto.inventoryId, isDeleted: false },
        relations: ['sku', 'sku.brand', 'sku.color'],
      });

      if (!inventory) {
        throw new NotFoundException(
          `库存记录 ${addItemDto.inventoryId} 不存在`,
        );
      }

      // 检查库存是否充足
      if (inventory.currentStock < addItemDto.quantity) {
        throw new BadRequestException(
          `库存不足，当前库存：${inventory.currentStock}，需要：${addItemDto.quantity}`,
        );
      }

      if (inventory.currentStock === 0) {
        throw new BadRequestException('商品已售罄，无法添加到购物车');
      }

      // 检查购物车中是否已存在相同商品
      const existingItem = await this.cartItemRepository.findOne({
        where: {
          cartId: cart.id,
          inventoryId: addItemDto.inventoryId,
          isDeleted: false,
        },
      });

      if (existingItem) {
        // 更新现有商品数量
        const newQuantity = existingItem.quantity + addItemDto.quantity;

        // 再次检查库存
        if (inventory.currentStock < newQuantity) {
          throw new BadRequestException(
            `库存不足，当前库存：${inventory.currentStock}，购物车已有：${existingItem.quantity}，总需要：${newQuantity}`,
          );
        }

        existingItem.quantity = newQuantity;
        existingItem.totalAmount = newQuantity * addItemDto.unitPrice;
        existingItem.remark = addItemDto.remark || existingItem.remark;

        await queryRunner.manager.save(existingItem);
      } else {
        // 创建新的购物车商品
        const cartItem = queryRunner.manager.create(ShoppingCartItem, {
          cartId: cart.id,
          inventoryId: addItemDto.inventoryId,
          skuCode: inventory.sku?.code,
          productName: inventory.sku?.name,
          colorCode: inventory.sku?.colorCode,
          colorName: inventory.sku?.color?.name,
          size: inventory.size,
          brandCode: inventory.sku?.brandCode,
          brandName: inventory.sku?.brand?.name,
          quantity: addItemDto.quantity,
          unitPrice: addItemDto.unitPrice,
          totalAmount: addItemDto.quantity * addItemDto.unitPrice,
          stockAtAdd: inventory.currentStock,
          remark: addItemDto.remark,
        });

        await queryRunner.manager.save(cartItem);
      }

      // 更新购物车汇总信息
      await this.updateCartSummary(cart.id, queryRunner.manager);

      await queryRunner.commitTransaction();

      // 返回更新后的购物车
      return this.getCartById(cart.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 更新购物车商品数量
   */
  async updateItemQuantity(
    userCode: string,
    itemId: string,
    updateDto: UpdateCartItemQuantityDto,
  ): Promise<ShoppingCartDto> {
    this.logger.log(
      `更新购物车商品数量: userCode=${userCode}, itemId=${itemId}, quantity=${updateDto.quantity}`,
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找购物车商品
      const cartItem = await this.cartItemRepository.findOne({
        where: { id: itemId, isDeleted: false },
        relations: ['cart', 'inventory'],
      });

      if (!cartItem) {
        throw new NotFoundException('购物车商品不存在');
      }

      // 验证购物车所有者
      if (cartItem.cart.userCode !== userCode) {
        throw new BadRequestException('无权限操作此购物车商品');
      }

      // 检查库存是否充足
      if (
        cartItem.inventory &&
        cartItem.inventory.currentStock < updateDto.quantity
      ) {
        throw new BadRequestException(
          `库存不足，当前库存：${cartItem.inventory.currentStock}，需要：${updateDto.quantity}`,
        );
      }

      // 更新商品数量和金额
      cartItem.quantity = updateDto.quantity;
      cartItem.totalAmount = updateDto.quantity * cartItem.unitPrice;

      await queryRunner.manager.save(cartItem);

      // 更新购物车汇总信息
      await this.updateCartSummary(cartItem.cartId, queryRunner.manager);

      await queryRunner.commitTransaction();

      // 返回更新后的购物车
      return this.getCartById(cartItem.cartId);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 更新购物车商品信息
   */
  async updateItem(
    userCode: string,
    itemId: string,
    updateDto: UpdateCartItemDto,
  ): Promise<ShoppingCartDto> {
    this.logger.log(
      `更新购物车商品信息: userCode=${userCode}, itemId=${itemId}`,
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找购物车商品
      const cartItem = await this.cartItemRepository.findOne({
        where: { id: itemId, isDeleted: false },
        relations: ['cart', 'inventory'],
      });

      if (!cartItem) {
        throw new NotFoundException('购物车商品不存在');
      }

      // 验证购物车所有者
      if (cartItem.cart.userCode !== userCode) {
        throw new BadRequestException('无权限操作此购物车商品');
      }

      // 更新数量（如果提供）
      if (updateDto.quantity !== undefined) {
        if (
          cartItem.inventory &&
          cartItem.inventory.currentStock < updateDto.quantity
        ) {
          throw new BadRequestException(
            `库存不足，当前库存：${cartItem.inventory.currentStock}，需要：${updateDto.quantity}`,
          );
        }
        cartItem.quantity = updateDto.quantity;
      }

      // 更新单价（如果提供）
      if (updateDto.unitPrice !== undefined) {
        cartItem.unitPrice = updateDto.unitPrice;
      }

      // 更新备注（如果提供）
      if (updateDto.remark !== undefined) {
        cartItem.remark = updateDto.remark;
      }

      // 重新计算总金额
      cartItem.totalAmount = cartItem.quantity * cartItem.unitPrice;

      await queryRunner.manager.save(cartItem);

      // 更新购物车汇总信息
      await this.updateCartSummary(cartItem.cartId, queryRunner.manager);

      await queryRunner.commitTransaction();

      // 返回更新后的购物车
      return this.getCartById(cartItem.cartId);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 删除购物车商品
   */
  async removeItem(userCode: string, itemId: string): Promise<ShoppingCartDto> {
    this.logger.log(`删除购物车商品: userCode=${userCode}, itemId=${itemId}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找购物车商品
      const cartItem = await this.cartItemRepository.findOne({
        where: { id: itemId, isDeleted: false },
        relations: ['cart'],
      });

      if (!cartItem) {
        throw new NotFoundException('购物车商品不存在');
      }

      // 验证购物车所有者
      if (cartItem.cart.userCode !== userCode) {
        throw new BadRequestException('无权限操作此购物车商品');
      }

      // 软删除商品
      cartItem.isDeleted = true;
      await queryRunner.manager.save(cartItem);

      // 更新购物车汇总信息
      await this.updateCartSummary(cartItem.cartId, queryRunner.manager);

      await queryRunner.commitTransaction();

      // 返回更新后的购物车
      return this.getCartById(cartItem.cartId);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 清空购物车
   */
  async clearCart(userCode: string): Promise<ShoppingCartDto> {
    this.logger.log(`清空购物车: userCode=${userCode}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取购物车
      const cart = await this.cartRepository.findOne({
        where: { userCode, isDeleted: false },
      });

      if (!cart) {
        throw new NotFoundException('购物车不存在');
      }

      // 软删除所有商品
      await queryRunner.manager.update(
        ShoppingCartItem,
        { cartId: cart.id, isDeleted: false },
        { isDeleted: true },
      );

      // 更新购物车汇总信息
      await this.updateCartSummary(cart.id, queryRunner.manager);

      await queryRunner.commitTransaction();

      // 返回更新后的购物车
      return this.getCartById(cart.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 获取购物车详情
   */
  async getCart(userCode: string): Promise<ShoppingCartDto> {
    this.logger.log(`获取购物车详情: userCode=${userCode}`);

    const cart = await this.cartRepository.findOne({
      where: { userCode, isDeleted: false },
      relations: [
        'items',
        'items.inventory',
        'items.inventory.sku',
        'items.inventory.sku.brand',
        'items.inventory.sku.color',
        'user',
      ],
    });

    if (!cart) {
      throw new NotFoundException('购物车不存在');
    }

    return this.transformToDto(cart);
  }

  /**
   * 获取购物车摘要信息
   */
  async getCartSummary(userCode: string): Promise<ShoppingCartSummaryDto> {
    this.logger.log(`获取购物车摘要: userCode=${userCode}`);

    const cart = await this.cartRepository.findOne({
      where: { userCode, isDeleted: false },
    });

    if (!cart) {
      throw new NotFoundException('购物车不存在');
    }

    return {
      id: cart.id,
      userCode: cart.userCode,
      totalQuantity: cart.totalQuantity,
      totalAmount: cart.totalAmount,
      itemCount: cart.itemCount,
      isEmpty: cart.isEmpty,
      lastUpdatedAt: cart.lastUpdatedAt || undefined,
    };
  }

  /**
   * 检查购物车商品库存状态
   */
  async checkCartStock(userCode: string): Promise<{
    isAllStockSufficient: boolean;
    insufficientItems: Array<{
      itemId: string;
      skuCode: string;
      productName: string;
      size: string;
      requiredQuantity: number;
      currentStock: number;
    }>;
  }> {
    this.logger.log(`检查购物车库存状态: userCode=${userCode}`);

    const cart = await this.cartRepository.findOne({
      where: { userCode, isDeleted: false },
      relations: ['items', 'items.inventory'],
    });

    if (!cart) {
      throw new NotFoundException('购物车不存在');
    }

    const insufficientItems: Array<{
      itemId: string;
      skuCode: string;
      productName: string;
      size: string;
      requiredQuantity: number;
      currentStock: number;
    }> = [];
    let isAllStockSufficient = true;

    for (const item of cart.items.filter((item) => !item.isDeleted)) {
      if (!item.inventory || item.inventory.currentStock < item.quantity) {
        isAllStockSufficient = false;
        insufficientItems.push({
          itemId: item.id,
          skuCode: item.skuCode || '',
          productName: item.productName || '',
          size: item.size,
          requiredQuantity: item.quantity,
          currentStock: item.inventory ? item.inventory.currentStock : 0,
        });
      }
    }

    return {
      isAllStockSufficient,
      insufficientItems,
    };
  }

  /**
   * 根据ID获取购物车
   */
  private async getCartById(cartId: string): Promise<ShoppingCartDto> {
    const cart = await this.cartRepository.findOne({
      where: { id: cartId, isDeleted: false },
      relations: [
        'items',
        'items.inventory',
        'items.inventory.sku',
        'items.inventory.sku.brand',
        'items.inventory.sku.color',
        'user',
      ],
    });

    if (!cart) {
      throw new NotFoundException('购物车不存在');
    }

    return this.transformToDto(cart);
  }

  /**
   * 更新购物车汇总信息
   */
  private async updateCartSummary(cartId: string, manager: any): Promise<void> {
    const items = await manager.find(ShoppingCartItem, {
      where: { cartId, isDeleted: false },
    });

    const totalQuantity = items.reduce(
      (sum: number, item: any) => sum + item.quantity,
      0,
    );
    const totalAmount = items.reduce(
      (sum: number, item: any) => sum + item.totalAmount,
      0,
    );

    await manager.update(ShoppingCart, cartId, {
      totalQuantity,
      totalAmount,
      lastUpdatedAt: new Date(),
    });
  }

  /**
   * 转换为DTO
   */
  private transformToDto(cart: ShoppingCart): ShoppingCartDto {
    const items = cart.items
      ? cart.items
          .filter((item) => !item.isDeleted)
          .map((item) => this.transformItemToDto(item))
      : [];

    return {
      id: cart.id,
      userCode: cart.userCode,
      userName: cart.user?.nickname || undefined,
      totalQuantity: cart.totalQuantity,
      totalAmount: cart.totalAmount,
      itemCount: items.length,
      isEmpty: cart.totalQuantity === 0 || items.length === 0,
      lastUpdatedAt: cart.lastUpdatedAt || undefined,
      remark: cart.remark || undefined,
      items,
      createdAt: cart.createdAt,
      updatedAt: cart.updatedAt,
    };
  }

  /**
   * 转换商品为DTO
   * 优先使用关联的实时商品档案信息，如果不存在则使用快照数据
   */
  private transformItemToDto(item: ShoppingCartItem): ShoppingCartItemDto {
    // 优先使用实时的商品档案信息
    const inventory = item.inventory;
    const sku = inventory?.sku;
    const brand = sku?.brand;
    const color = sku?.color;

    return {
      id: item.id,
      inventoryId: item.inventoryId,
      // 优先使用实时数据，如果不存在则使用快照数据
      skuCode: sku?.code || item.skuCode || '',
      productName: sku?.name || item.productName || '',
      colorCode: sku?.colorCode || item.colorCode || '',
      colorName: color?.name || item.colorName || '',
      size: inventory?.size || item.size,
      brandCode: sku?.brandCode || item.brandCode || '',
      brandName: brand?.name || item.brandName || '',
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalAmount: item.totalAmount,
      currentStock: inventory ? inventory.currentStock : 0,
      isStockSufficient: inventory
        ? inventory.currentStock >= item.quantity
        : false,
      stockAtAdd: item.stockAtAdd || undefined,
      remark: item.remark || undefined,
      createdAt: item.createdAt,
    };
  }

  /**
   * 刷新购物车商品档案信息
   * 将购物车中的快照数据更新为最新的商品档案信息
   */
  async refreshCartItemsInfo(userCode: string): Promise<ShoppingCartDto> {
    this.logger.log(`刷新购物车商品档案信息: userCode=${userCode}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取购物车及其商品
      const cart = await this.cartRepository.findOne({
        where: { userCode, isDeleted: false },
        relations: [
          'items',
          'items.inventory',
          'items.inventory.sku',
          'items.inventory.sku.brand',
          'items.inventory.sku.color',
        ],
      });

      if (!cart) {
        throw new NotFoundException('购物车不存在');
      }

      // 更新每个商品的档案信息
      for (const item of cart.items.filter((item) => !item.isDeleted)) {
        if (item.inventory && item.inventory.sku) {
          const sku = item.inventory.sku;
          const brand = sku.brand;
          const color = sku.color;

          // 更新商品档案快照信息
          item.skuCode = sku.code;
          item.productName = sku.name;
          item.colorCode = sku.colorCode;
          item.colorName = color?.name || null;
          item.size = item.inventory.size;
          item.brandCode = sku.brandCode;
          item.brandName = brand?.name || null;

          await queryRunner.manager.save(item);
        }
      }

      await queryRunner.commitTransaction();

      // 重新获取更新后的购物车
      const updatedCart = await this.cartRepository.findOne({
        where: { userCode, isDeleted: false },
        relations: [
          'items',
          'items.inventory',
          'items.inventory.sku',
          'items.inventory.sku.brand',
          'items.inventory.sku.color',
        ],
      });

      return this.transformToDto(updatedCart!);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `刷新购物车商品档案信息失败: ${error.message}`,
        error.stack,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 批量清理无效的购物车商品
   * 清理库存记录已删除或SKU已删除的商品
   */
  async cleanupInvalidCartItems(): Promise<void> {
    this.logger.log('开始批量清理无效的购物车商品');

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找所有购物车商品，包含库存和SKU关联
      const allCartItems = await this.cartItemRepository.find({
        where: { isDeleted: false },
        relations: ['inventory', 'inventory.sku'],
      });

      const itemsToDelete: string[] = [];

      for (const item of allCartItems) {
        // 检查库存记录是否存在且未删除
        if (!item.inventory || item.inventory.isDeleted) {
          itemsToDelete.push(item.id);
          continue;
        }

        // 检查SKU是否存在且未删除
        if (!item.inventory.sku || item.inventory.sku.isDeleted) {
          itemsToDelete.push(item.id);
          continue;
        }
      }

      // 批量软删除无效商品
      if (itemsToDelete.length > 0) {
        await queryRunner.manager.update(
          ShoppingCartItem,
          { id: In(itemsToDelete) },
          { isDeleted: true },
        );

        this.logger.log(`清理了 ${itemsToDelete.length} 个无效的购物车商品`);
      }

      // 更新购物车汇总信息
      if (itemsToDelete.length > 0) {
        const affectedCarts = await queryRunner.manager
          .createQueryBuilder(ShoppingCart, 'cart')
          .innerJoin('cart.items', 'item')
          .where('item.id IN (:...itemIds)', { itemIds: itemsToDelete })
          .getMany();

        for (const cart of affectedCarts) {
          await this.updateCartSummary(cart.id, queryRunner);
        }
      }

      await queryRunner.commitTransaction();
      this.logger.log('批量清理无效购物车商品完成');
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `批量清理无效购物车商品失败: ${error.message}`,
        error.stack,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 将购物车转换为销售订单DTO
   */
  async convertCartToSalesOrder(
    userCode: string,
    cartToOrderDto: CartToOrderWithPricesDto,
  ): Promise<CreateSalesOrderDto> {
    this.logger.log(`将购物车转换为销售订单: userCode=${userCode}`);

    // 获取购物车
    const cart = await this.cartRepository.findOne({
      where: { userCode, isDeleted: false },
      relations: ['items', 'items.inventory'],
    });

    if (!cart) {
      throw new NotFoundException('购物车不存在');
    }

    // 获取要转换的商品
    let itemsToConvert = cart.items.filter((item) => !item.isDeleted);

    if (
      cartToOrderDto.selectedItemIds &&
      cartToOrderDto.selectedItemIds.length > 0
    ) {
      itemsToConvert = itemsToConvert.filter((item) =>
        cartToOrderDto.selectedItemIds!.includes(item.id),
      );
    }

    if (itemsToConvert.length === 0) {
      throw new BadRequestException('购物车中没有可转换的商品');
    }

    // 检查库存
    for (const item of itemsToConvert) {
      if (!item.inventory || item.inventory.currentStock < item.quantity) {
        throw new BadRequestException(
          `商品 ${item.productName} (${item.size}) 库存不足，当前库存：${item.inventory?.currentStock || 0}，需要：${item.quantity}`,
        );
      }
    }

    // 构建价格更新映射
    const priceUpdateMap = new Map<
      string,
      { priceType: string; unitPrice: number; discountAmount?: number }
    >();
    if (cartToOrderDto.itemPriceUpdates) {
      cartToOrderDto.itemPriceUpdates.forEach((update) => {
        priceUpdateMap.set(update.itemId, {
          priceType: update.priceType,
          unitPrice: update.unitPrice,
          discountAmount: update.discountAmount,
        });
      });
    }

    // 转换为销售订单明细
    const details: CreateSalesOrderDetailDto[] = itemsToConvert.map((item) => {
      const priceUpdate = priceUpdateMap.get(item.id);
      const unitPrice = priceUpdate?.unitPrice || item.unitPrice;
      const priceType = priceUpdate?.priceType || 'retail';
      const discountAmount = priceUpdate?.discountAmount || 0;

      return {
        inventoryId: item.inventoryId,
        quantity: item.quantity,
        priceType: priceType as PriceType,
        unitPrice,
        discountAmount,
        remark: item.remark || undefined,
      };
    });

    // 构建销售订单DTO
    const salesOrderDto: CreateSalesOrderDto = {
      salesPersonCode: userCode,
      customerCode: cartToOrderDto.customerCode,
      orderDate: cartToOrderDto.orderDate,
      expectedDeliveryDate: cartToOrderDto.expectedDeliveryDate,
      priority: cartToOrderDto.priority,
      isDropShipping: cartToOrderDto.isDropShipping,
      isReleased: cartToOrderDto.isReleased,
      shippingMethod: cartToOrderDto.shippingMethod as ShippingMethod,
      shippingFee: cartToOrderDto.shippingFee,
      shippingCompany: cartToOrderDto.shippingCompany,
      shipperUserCode: cartToOrderDto.shipperUserCode,
      dropShipCustomerCode: cartToOrderDto.dropShipCustomerCode,
      remark: cartToOrderDto.remark,
      details,
      // 添加客户关联信息
      customers: [
        {
          customerCode: cartToOrderDto.customerCode,
          allocatedQuantity: itemsToConvert.reduce(
            (sum, item) => sum + item.quantity,
            0,
          ),
          allocatedAmount: itemsToConvert.reduce(
            (sum, item) => sum + item.totalAmount,
            0,
          ),
          allocatedShippingFee: cartToOrderDto.shippingFee || 0,
        },
      ],
    };

    return salesOrderDto;
  }

  /**
   * 购物车转销售订单后清理已转换的商品
   */
  async clearConvertedItems(
    userCode: string,
    convertedItemIds: string[],
  ): Promise<void> {
    this.logger.log(
      `清理已转换的购物车商品: userCode=${userCode}, itemCount=${convertedItemIds.length}`,
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取购物车
      const cart = await this.cartRepository.findOne({
        where: { userCode, isDeleted: false },
      });

      if (!cart) {
        throw new NotFoundException('购物车不存在');
      }

      // 软删除已转换的商品
      if (convertedItemIds.length > 0) {
        await queryRunner.manager.update(
          ShoppingCartItem,
          {
            cartId: cart.id,
            id: In(convertedItemIds),
            isDeleted: false,
          },
          { isDeleted: true },
        );
      }

      // 更新购物车汇总信息
      await this.updateCartSummary(cart.id, queryRunner.manager);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
