import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ShoppingCartService } from './shopping-cart.service';
import {
  AddCartItemDto,
  UpdateCartItemDto,
  UpdateCartItemQuantityDto,
} from './dto/add-cart-item.dto';
import {
  ShoppingCartDto,
  ShoppingCartSummaryDto,
} from './dto/shopping-cart-response.dto';
import { CartToOrderWithPricesDto } from './dto/cart-to-order.dto';

@ApiTags('购物车管理')
@Controller('shopping-cart')
export class ShoppingCartController {
  private readonly logger = new Logger(ShoppingCartController.name);

  constructor(private readonly shoppingCartService: ShoppingCartService) {}

  @Get()
  @ApiOperation({ summary: '获取用户购物车详情' })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '获取购物车成功',
    type: ShoppingCartDto,
  })
  async getCart(@Query('userCode') userCode: string) {
    this.logger.log(`获取购物车: userCode=${userCode}`);

    const cart = await this.shoppingCartService.getOrCreateCart(userCode);

    return {
      code: 200,
      data: cart,
      message: '获取购物车成功',
    };
  }

  @Get('summary')
  @ApiOperation({ summary: '获取用户购物车摘要信息' })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '获取购物车摘要成功',
    type: ShoppingCartSummaryDto,
  })
  async getCartSummary(@Query('userCode') userCode: string) {
    this.logger.log(`获取购物车摘要: userCode=${userCode}`);

    const summary = await this.shoppingCartService.getCartSummary(userCode);

    return {
      code: 200,
      data: summary,
      message: '获取购物车摘要成功',
    };
  }

  @Post('items')
  @ApiOperation({ summary: '添加商品到购物车' })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '添加商品成功',
    type: ShoppingCartDto,
  })
  async addItem(
    @Query('userCode') userCode: string,
    @Body() addItemDto: AddCartItemDto,
  ) {
    this.logger.log(
      `添加商品到购物车: userCode=${userCode}, inventoryId=${addItemDto.inventoryId}`,
    );

    const cart = await this.shoppingCartService.addItem(userCode, addItemDto);

    return {
      code: 200,
      data: cart,
      message: '添加商品到购物车成功',
    };
  }

  @Patch('items/:itemId/quantity')
  @ApiOperation({ summary: '更新购物车商品数量' })
  @ApiParam({
    name: 'itemId',
    description: '购物车商品ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '更新商品数量成功',
    type: ShoppingCartDto,
  })
  async updateItemQuantity(
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @Query('userCode') userCode: string,
    @Body() updateDto: UpdateCartItemQuantityDto,
  ) {
    this.logger.log(
      `更新购物车商品数量: userCode=${userCode}, itemId=${itemId}, quantity=${updateDto.quantity}`,
    );

    const cart = await this.shoppingCartService.updateItemQuantity(
      userCode,
      itemId,
      updateDto,
    );

    return {
      code: 200,
      data: cart,
      message: '更新商品数量成功',
    };
  }

  @Patch('items/:itemId')
  @ApiOperation({ summary: '更新购物车商品信息' })
  @ApiParam({
    name: 'itemId',
    description: '购物车商品ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '更新商品信息成功',
    type: ShoppingCartDto,
  })
  async updateItem(
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @Query('userCode') userCode: string,
    @Body() updateDto: UpdateCartItemDto,
  ) {
    this.logger.log(
      `更新购物车商品信息: userCode=${userCode}, itemId=${itemId}`,
    );

    const cart = await this.shoppingCartService.updateItem(
      userCode,
      itemId,
      updateDto,
    );

    return {
      code: 200,
      data: cart,
      message: '更新商品信息成功',
    };
  }

  @Delete('items/:itemId')
  @ApiOperation({ summary: '删除购物车商品' })
  @ApiParam({
    name: 'itemId',
    description: '购物车商品ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '删除商品成功',
    type: ShoppingCartDto,
  })
  async removeItem(
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @Query('userCode') userCode: string,
  ) {
    this.logger.log(`删除购物车商品: userCode=${userCode}, itemId=${itemId}`);

    const cart = await this.shoppingCartService.removeItem(userCode, itemId);

    return {
      code: 200,
      data: cart,
      message: '删除商品成功',
    };
  }

  @Delete('clear')
  @ApiOperation({ summary: '清空购物车' })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '清空购物车成功',
    type: ShoppingCartDto,
  })
  async clearCart(@Query('userCode') userCode: string) {
    this.logger.log(`清空购物车: userCode=${userCode}`);

    const cart = await this.shoppingCartService.clearCart(userCode);

    return {
      code: 200,
      data: cart,
      message: '清空购物车成功',
    };
  }

  @Get('check-stock')
  @ApiOperation({ summary: '检查购物车商品库存状态' })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '检查库存状态成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '检查库存状态成功' },
        data: {
          type: 'object',
          properties: {
            isAllStockSufficient: { type: 'boolean', example: true },
            insufficientItems: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  itemId: { type: 'string' },
                  skuCode: { type: 'string' },
                  productName: { type: 'string' },
                  size: { type: 'string' },
                  requiredQuantity: { type: 'number' },
                  currentStock: { type: 'number' },
                },
              },
            },
          },
        },
      },
    },
  })
  async checkCartStock(@Query('userCode') userCode: string) {
    this.logger.log(`检查购物车库存状态: userCode=${userCode}`);

    const result = await this.shoppingCartService.checkCartStock(userCode);

    return {
      code: 200,
      data: result,
      message: '检查库存状态成功',
    };
  }

  @Post('refresh-items-info')
  @ApiOperation({ summary: '刷新购物车商品档案信息' })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '刷新成功',
    type: ShoppingCartDto,
  })
  async refreshCartItemsInfo(@Query('userCode') userCode: string) {
    this.logger.log(`刷新购物车商品档案信息: userCode=${userCode}`);

    const cart = await this.shoppingCartService.refreshCartItemsInfo(userCode);

    return {
      code: 200,
      data: cart,
      message: '刷新购物车商品档案信息成功',
    };
  }

  @Post('cleanup-invalid-items')
  @ApiOperation({ summary: '清理无效的购物车商品' })
  @ApiResponse({
    status: 200,
    description: '清理成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '清理无效购物车商品成功' },
        data: { type: 'null' },
      },
    },
  })
  async cleanupInvalidCartItems() {
    this.logger.log('开始清理无效的购物车商品');

    await this.shoppingCartService.cleanupInvalidCartItems();

    return {
      code: 200,
      data: null,
      message: '清理无效购物车商品成功',
    };
  }

  @Post('convert-to-order')
  @ApiOperation({ summary: '将购物车转换为销售订单DTO' })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '转换成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '购物车转换为销售订单DTO成功' },
        data: {
          type: 'object',
          description: '销售订单DTO',
        },
      },
    },
  })
  async convertCartToSalesOrder(
    @Query('userCode') userCode: string,
    @Body() cartToOrderDto: CartToOrderWithPricesDto,
  ) {
    this.logger.log(
      `将购物车转换为销售订单: userCode=${userCode}, customerCode=${cartToOrderDto.customerCode}`,
    );

    const salesOrderDto =
      await this.shoppingCartService.convertCartToSalesOrder(
        userCode,
        cartToOrderDto,
      );

    return {
      code: 200,
      data: salesOrderDto,
      message: '购物车转换为销售订单DTO成功',
    };
  }

  @Post('clear-converted-items')
  @ApiOperation({ summary: '清理已转换为销售订单的购物车商品' })
  @ApiQuery({
    name: 'userCode',
    description: '用户编码',
    example: 'user001',
  })
  @ApiResponse({
    status: 200,
    description: '清理成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '清理已转换商品成功' },
        data: { type: 'null' },
      },
    },
  })
  async clearConvertedItems(
    @Query('userCode') userCode: string,
    @Body() body: { convertedItemIds: string[] },
  ) {
    this.logger.log(
      `清理已转换的购物车商品: userCode=${userCode}, itemCount=${body.convertedItemIds.length}`,
    );

    await this.shoppingCartService.clearConvertedItems(
      userCode,
      body.convertedItemIds,
    );

    return {
      code: 200,
      data: null,
      message: '清理已转换商品成功',
    };
  }
}
