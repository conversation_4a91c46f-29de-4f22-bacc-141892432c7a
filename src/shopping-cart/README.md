# 购物车功能模块

## 功能概述

购物车功能模块为销售开单系统提供了便捷的商品选择和暂存功能，用户可以先将商品添加到购物车，然后统一进行结算和开单操作。

## 核心功能

### 1. 购物车管理

- ✅ 自动创建/获取用户购物车
- ✅ 购物车商品总数量和总金额自动计算
- ✅ 购物车摘要信息查询
- ✅ 购物车详情查询（包含商品列表）

### 2. 商品管理

- ✅ 添加商品到购物车（支持库存检查）
- ✅ 更新购物车商品数量
- ✅ 更新购物车商品信息（数量、单价、备注）
- ✅ 删除购物车商品
- ✅ 清空购物车
- ✅ 实时库存状态检查

### 3. 库存集成

- ✅ 添加商品时自动检查库存充足性
- ✅ 实时显示当前库存数量
- ✅ 库存不足时阻止添加操作
- ✅ 购物车商品库存状态批量检查

### 4. 销售订单转换

- ✅ 购物车转换为销售订单DTO
- ✅ 支持选择性转换（指定商品ID）
- ✅ 支持价格类型和单价更新
- ✅ 转换前库存再次验证
- ✅ 转换后清理已转换商品

## 数据库设计

### 购物车表 (shopping_carts)

```sql
- id: UUID主键
- user_code: 用户编码（外键关联users表）
- total_quantity: 购物车商品总数量
- total_amount: 购物车商品总金额
- last_updated_at: 最后更新时间
- remark: 购物车备注
- is_deleted: 软删除标记
- created_at: 创建时间
- updated_at: 更新时间
```

### 购物车商品表 (shopping_cart_items)

```sql
- id: UUID主键
- cart_id: 购物车ID（外键）
- inventory_id: 库存记录ID（外键关联skus_inventory表）
- sku_code: SKU编码（冗余字段）
- product_name: 商品名称（冗余字段）
- color_code: 颜色编码（冗余字段）
- color_name: 颜色名称（冗余字段）
- size: 尺码
- brand_code: 品牌编码（冗余字段）
- brand_name: 品牌名称（冗余字段）
- quantity: 购买数量
- unit_price: 单价
- total_amount: 小计金额
- stock_at_add: 添加时的库存数量
- remark: 商品备注
- is_deleted: 软删除标记
- created_at: 创建时间
- updated_at: 更新时间
```

### 索引设计

- **唯一索引**：(user_code) - 确保每个用户只有一个购物车
- **唯一索引**：(cart_id, inventory_id) - 确保同一购物车中同一库存商品只有一条记录
- **普通索引**：cart_id, inventory_id, is_deleted, created_at, updated_at

## API接口

### 1. 获取购物车详情

```
GET /shopping-cart?userCode=user001
```

### 2. 获取购物车摘要

```
GET /shopping-cart/summary?userCode=user001
```

### 3. 添加商品到购物车

```
POST /shopping-cart/items?userCode=user001
{
  "inventoryId": "uuid",
  "quantity": 2,
  "unitPrice": 299.99,
  "remark": "客户指定颜色"
}
```

### 4. 更新商品数量

```
PATCH /shopping-cart/items/{itemId}/quantity?userCode=user001
{
  "quantity": 3
}
```

### 5. 更新商品信息

```
PATCH /shopping-cart/items/{itemId}?userCode=user001
{
  "quantity": 3,
  "unitPrice": 289.99,
  "remark": "更新备注"
}
```

### 6. 删除商品

```
DELETE /shopping-cart/items/{itemId}?userCode=user001
```

### 7. 清空购物车

```
DELETE /shopping-cart/clear?userCode=user001
```

### 8. 检查库存状态

```
GET /shopping-cart/check-stock?userCode=user001
```

### 9. 转换为销售订单

```
POST /shopping-cart/convert-to-order?userCode=user001
{
  "customerCode": "CUST001",
  "orderDate": "2025-01-19",
  "expectedDeliveryDate": "2025-01-25",
  "priority": "normal",
  "shippingMethod": "快递配送",
  "shippingFee": 15.00,
  "selectedItemIds": ["item-uuid-1", "item-uuid-2"],
  "itemPriceUpdates": [
    {
      "itemId": "item-uuid-1",
      "priceType": "retail",
      "unitPrice": 299.99,
      "discountAmount": 20.00
    }
  ]
}
```

## 业务逻辑

### 1. 购物车创建

- 每个用户只能有一个购物车
- 首次访问时自动创建购物车
- 购物车为空时不会被删除，保持持久化

### 2. 商品添加逻辑

- 添加前检查库存是否充足
- 如果购物车中已存在相同商品，则累加数量
- 自动保存商品信息快照（SKU、颜色、品牌等）
- 记录添加时的库存数量

### 3. 库存检查

- 添加商品时实时检查库存
- 更新数量时重新验证库存
- 提供批量库存状态检查接口
- 库存不足时阻止操作并提示具体信息

### 4. 汇总计算

- 自动计算购物车总数量和总金额
- 每次商品变更后更新汇总信息
- 支持商品种类数统计

### 5. 销售订单转换

- 支持全部转换或选择性转换
- 转换前再次验证库存状态
- 支持价格类型和单价调整
- 转换成功后可选择清理已转换商品

## 使用场景

### 1. 日常开单流程

1. 销售人员浏览商品库存
2. 将需要的商品添加到购物车
3. 在购物车中调整数量和价格
4. 检查库存状态
5. 转换为销售订单并提交

### 2. 批量选择商品

1. 根据客户需求添加多种商品
2. 暂存在购物车中
3. 与客户确认后统一开单

### 3. 分批开单

1. 添加大量商品到购物车
2. 根据库存情况分批转换为订单
3. 剩余商品继续保留在购物车

## 优势特点

1. **提升效率**：避免重复选择商品，支持批量操作
2. **库存安全**：实时库存检查，防止超卖
3. **数据一致性**：事务保证数据完整性
4. **用户体验**：直观的购物车界面，操作简便
5. **灵活转换**：支持选择性转换和价格调整
6. **持久化存储**：购物车内容持久保存，不会丢失

## 注意事项

1. 每个用户只能有一个购物车
2. 购物车商品与库存记录强关联，删除库存会影响购物车
3. 库存检查是实时的，建议在转换订单前再次检查
4. 商品信息采用快照模式，不会随SKU信息变更而自动更新
5. 软删除机制确保数据可追溯性
