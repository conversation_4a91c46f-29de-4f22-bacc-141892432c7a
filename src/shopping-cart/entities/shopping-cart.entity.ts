import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@/users/entities/user.entity';
import { ShoppingCartItem } from './shopping-cart-item.entity';

@Entity('shopping_carts')
@Index(['userCode'], { unique: true }) // 每个用户只能有一个购物车
export class ShoppingCart {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '购物车ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: '用户编码',
  })
  @ApiProperty({
    description: '用户编码',
    example: 'user001',
  })
  userCode: string;

  // 用户关联
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'userCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '用户信息',
    type: () => User,
    required: false,
  })
  user: User | null;

  @Column({
    type: 'int',
    default: 0,
    comment: '购物车商品总数量',
  })
  @ApiProperty({
    description: '购物车商品总数量',
    example: 5,
    default: 0,
  })
  totalQuantity: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '购物车商品总金额',
  })
  @ApiProperty({
    description: '购物车商品总金额',
    example: 1500.0,
    default: 0,
  })
  totalAmount: number;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '最后更新时间',
  })
  @ApiProperty({
    description: '最后更新时间',
    example: '2025-01-19T08:00:00.000Z',
    required: false,
  })
  lastUpdatedAt: Date | null;

  @Column({
    type: 'text',
    nullable: true,
    comment: '购物车备注',
  })
  @ApiProperty({
    description: '购物车备注',
    example: '临时保存的商品选择',
    required: false,
  })
  remark: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => ShoppingCartItem, (item) => item.cart)
  @ApiProperty({
    description: '购物车商品列表',
    type: () => [ShoppingCartItem],
  })
  items: ShoppingCartItem[];

  // 计算属性：购物车商品种类数
  get itemCount(): number {
    return this.items ? this.items.length : 0;
  }

  // 计算属性：是否为空购物车
  get isEmpty(): boolean {
    return this.totalQuantity === 0 || this.itemCount === 0;
  }
}
