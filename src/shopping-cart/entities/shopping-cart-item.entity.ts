import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { ShoppingCart } from './shopping-cart.entity';
import { SkuInventory } from '@/skus-inventory/entities/sku-inventory.entity';

@Entity('shopping_cart_items')
@Index(['cartId', 'inventoryId'], { unique: true }) // 同一购物车中同一库存商品只能有一条记录
export class ShoppingCartItem {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '购物车商品ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Column({
    type: 'uuid',
    comment: '购物车ID',
  })
  @ApiProperty({
    description: '购物车ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  cartId: string;

  // 购物车关联
  @ManyToOne(() => ShoppingCart, (cart) => cart.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'cartId' })
  @ApiProperty({
    description: '购物车信息',
    type: () => ShoppingCart,
  })
  cart: ShoppingCart;

  @Column({
    type: 'uuid',
    comment: '库存记录ID',
  })
  @ApiProperty({
    description: '库存记录ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  inventoryId: string;

  // 库存记录关联
  @ManyToOne(() => SkuInventory, { nullable: true })
  @JoinColumn({ name: 'inventoryId' })
  @ApiProperty({
    description: '库存记录信息',
    type: () => SkuInventory,
    required: false,
  })
  inventory: SkuInventory | null;

  // 商品信息快照（冗余字段，提高查询性能）
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'SKU编码',
  })
  @ApiProperty({
    description: 'SKU编码',
    example: 'SKU001',
    required: false,
  })
  skuCode: string | null;

  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: '商品名称',
  })
  @ApiProperty({
    description: '商品名称',
    example: '春季新款T恤',
    required: false,
  })
  productName: string | null;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '颜色编码',
  })
  @ApiProperty({
    description: '颜色编码',
    example: 'RED001',
    required: false,
  })
  colorCode: string | null;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '颜色名称',
  })
  @ApiProperty({
    description: '颜色名称',
    example: '红色',
    required: false,
  })
  colorName: string | null;

  @Column({
    type: 'varchar',
    length: 20,
    comment: '尺码',
  })
  @ApiProperty({
    description: '尺码',
    example: 'M',
  })
  size: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '品牌编码',
  })
  @ApiProperty({
    description: '品牌编码',
    example: 'BRAND001',
    required: false,
  })
  brandCode: string | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '品牌名称',
  })
  @ApiProperty({
    description: '品牌名称',
    example: '知名品牌',
    required: false,
  })
  brandName: string | null;

  @Column({
    type: 'int',
    comment: '购买数量',
  })
  @ApiProperty({
    description: '购买数量',
    example: 2,
  })
  quantity: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '单价',
  })
  @ApiProperty({
    description: '单价',
    example: 299.99,
  })
  unitPrice: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '小计金额',
  })
  @ApiProperty({
    description: '小计金额（数量 × 单价）',
    example: 599.98,
  })
  totalAmount: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '添加时的库存数量',
  })
  @ApiProperty({
    description: '添加时的库存数量',
    example: 50,
    required: false,
  })
  stockAtAdd: number | null;

  @Column({
    type: 'text',
    nullable: true,
    comment: '商品备注',
  })
  @ApiProperty({
    description: '商品备注',
    example: '客户指定颜色',
    required: false,
  })
  remark: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({
    description: '创建时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({
    description: '更新时间',
    example: '2025-01-19T08:00:00.000Z',
  })
  updatedAt: Date;

  // 计算属性：是否库存充足
  get isStockSufficient(): boolean {
    return this.inventory
      ? this.inventory.currentStock >= this.quantity
      : false;
  }

  // 计算属性：当前库存状态
  get currentStock(): number {
    return this.inventory ? this.inventory.currentStock : 0;
  }
}
