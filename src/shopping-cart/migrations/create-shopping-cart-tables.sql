-- 创建购物车表
CREATE TABLE shopping_carts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_code VARCHAR(50) NOT NULL,
    total_quantity INTEGER DEFAULT 0 NOT NULL,
    total_amount DECIMAL(10,2) DEFAULT 0 NOT NULL,
    last_updated_at TIMESTAMP,
    remark TEXT,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 创建购物车商品表
CREATE TABLE shopping_cart_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cart_id UUID NOT NULL,
    inventory_id UUID NOT NULL,
    sku_code VARCHAR(100),
    product_name VARCHAR(200),
    color_code VARCHAR(50),
    color_name VARCHAR(50),
    size VARCHAR(20) NOT NULL,
    brand_code VARCHAR(50),
    brand_name VARCHAR(100),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    stock_at_add INTEGER,
    remark TEXT,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 创建索引
CREATE UNIQUE INDEX idx_shopping_carts_user_code ON shopping_carts(user_code) WHERE is_deleted = FALSE;
CREATE INDEX idx_shopping_carts_user_code_deleted ON shopping_carts(user_code, is_deleted);
CREATE INDEX idx_shopping_carts_updated_at ON shopping_carts(updated_at);

CREATE UNIQUE INDEX idx_shopping_cart_items_cart_inventory ON shopping_cart_items(cart_id, inventory_id) WHERE is_deleted = FALSE;
CREATE INDEX idx_shopping_cart_items_cart_id ON shopping_cart_items(cart_id);
CREATE INDEX idx_shopping_cart_items_inventory_id ON shopping_cart_items(inventory_id);
CREATE INDEX idx_shopping_cart_items_deleted ON shopping_cart_items(is_deleted);
CREATE INDEX idx_shopping_cart_items_created_at ON shopping_cart_items(created_at);

-- 添加外键约束
ALTER TABLE shopping_carts 
ADD CONSTRAINT fk_shopping_carts_user_code 
FOREIGN KEY (user_code) REFERENCES users(code);

ALTER TABLE shopping_cart_items 
ADD CONSTRAINT fk_shopping_cart_items_cart_id 
FOREIGN KEY (cart_id) REFERENCES shopping_carts(id) ON DELETE CASCADE;

ALTER TABLE shopping_cart_items 
ADD CONSTRAINT fk_shopping_cart_items_inventory_id 
FOREIGN KEY (inventory_id) REFERENCES skus_inventory(id);

-- 添加注释
COMMENT ON TABLE shopping_carts IS '购物车表';
COMMENT ON COLUMN shopping_carts.id IS '购物车ID（UUID）';
COMMENT ON COLUMN shopping_carts.user_code IS '用户编码';
COMMENT ON COLUMN shopping_carts.total_quantity IS '购物车商品总数量';
COMMENT ON COLUMN shopping_carts.total_amount IS '购物车商品总金额';
COMMENT ON COLUMN shopping_carts.last_updated_at IS '最后更新时间';
COMMENT ON COLUMN shopping_carts.remark IS '购物车备注';
COMMENT ON COLUMN shopping_carts.is_deleted IS '是否已删除';
COMMENT ON COLUMN shopping_carts.created_at IS '创建时间';
COMMENT ON COLUMN shopping_carts.updated_at IS '更新时间';

COMMENT ON TABLE shopping_cart_items IS '购物车商品表';
COMMENT ON COLUMN shopping_cart_items.id IS '购物车商品ID（UUID）';
COMMENT ON COLUMN shopping_cart_items.cart_id IS '购物车ID';
COMMENT ON COLUMN shopping_cart_items.inventory_id IS '库存记录ID';
COMMENT ON COLUMN shopping_cart_items.sku_code IS 'SKU编码';
COMMENT ON COLUMN shopping_cart_items.product_name IS '商品名称';
COMMENT ON COLUMN shopping_cart_items.color_code IS '颜色编码';
COMMENT ON COLUMN shopping_cart_items.color_name IS '颜色名称';
COMMENT ON COLUMN shopping_cart_items.size IS '尺码';
COMMENT ON COLUMN shopping_cart_items.brand_code IS '品牌编码';
COMMENT ON COLUMN shopping_cart_items.brand_name IS '品牌名称';
COMMENT ON COLUMN shopping_cart_items.quantity IS '购买数量';
COMMENT ON COLUMN shopping_cart_items.unit_price IS '单价';
COMMENT ON COLUMN shopping_cart_items.total_amount IS '小计金额';
COMMENT ON COLUMN shopping_cart_items.stock_at_add IS '添加时的库存数量';
COMMENT ON COLUMN shopping_cart_items.remark IS '商品备注';
COMMENT ON COLUMN shopping_cart_items.is_deleted IS '是否已删除';
COMMENT ON COLUMN shopping_cart_items.created_at IS '创建时间';
COMMENT ON COLUMN shopping_cart_items.updated_at IS '更新时间';
