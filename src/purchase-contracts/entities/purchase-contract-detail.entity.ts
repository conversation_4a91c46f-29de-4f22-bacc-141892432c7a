import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PurchaseContract } from './purchase-contract.entity';
import { Accessory } from '@/accessories/entities/accessory.entity';

@Entity('purchase_contract_details')
export class PurchaseContractDetail {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '明细ID' })
  id: string;

  @Column({ type: 'uuid', comment: '采购合同ID' })
  purchaseContractId: string;

  @Column({ type: 'uuid', nullable: true, comment: '辅料ID（关联accessories表）' })
  @ApiProperty({ description: '辅料ID', example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', required: false })
  accessoryId?: string;

  @Column({
    type: 'int',
    comment: '下单数量',
  })
  @ApiProperty({ description: '下单数量', example: 100 })
  quantity: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '单价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '单价', example: 5.50 })
  unitPrice: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '合计金额（单价 × 数量）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '合计金额', example: 550.00 })
  totalAmount: number;

  @Column({
    type: 'date',
    nullable: true,
    comment: '货期（可选）',
  })
  @ApiProperty({ 
    description: '货期', 
    example: '2025-01-15', 
    required: false 
  })
  deliveryDate?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  @ApiProperty({ 
    description: '备注', 
    example: '需要特殊包装', 
    required: false 
  })
  remark?: string;



  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联采购合同主表
  @ManyToOne(() => PurchaseContract, (contract) => contract.details)
  @JoinColumn({ name: 'purchaseContractId' })
  purchaseContract: PurchaseContract;

  // 关联辅料表
  @ManyToOne(() => Accessory, { nullable: true })
  @JoinColumn({ name: 'accessoryId' })
  @ApiProperty({ description: '辅料信息', type: () => Accessory, required: false })
  accessory?: Accessory;
}
