import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { PurchaseContractDetail } from './purchase-contract-detail.entity';

// 采购合同状态枚举
export enum PurchaseContractStatus {
  DRAFT = 'draft', // 草稿
  CONFIRMED = 'confirmed', // 已确认
  RECEIVED = 'received', // 已收货
  COMPLETED = 'completed', // 已完成
}

@Entity('purchase_contracts')
export class PurchaseContract {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '采购合同ID' })
  id: string;

  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: '采购订单编号',
  })
  @ApiProperty({ description: '采购订单编号', example: 'PO202501001' })
  orderNumber: string;

  @Column({ type: 'varchar', length: 50, comment: '供应商编码' })
  @ApiProperty({ description: '供应商编码', example: 'SUP001' })
  supplierCode: string;

  @Column({
    type: 'date',
    comment: '采购订单日期',
  })
  @ApiProperty({ description: '采购订单日期', example: '2025-01-01' })
  orderDate: Date;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '合计货款（自动计算）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '合计货款', example: 50000.0 })
  totalAmount: number;

  @Column({
    type: 'int',
    default: 0,
    comment: '合计数量（自动计算）',
  })
  @ApiProperty({ description: '合计数量', example: 1000 })
  totalQuantity: number;

  @Column({
    type: 'enum',
    enum: PurchaseContractStatus,
    default: PurchaseContractStatus.DRAFT,
    comment: '合同状态',
  })
  @ApiProperty({
    description: '合同状态',
    enum: PurchaseContractStatus,
    example: PurchaseContractStatus.DRAFT,
  })
  status: PurchaseContractStatus;

  @Column({
    type: 'date',
    nullable: true,
    comment: '预计交货日期',
  })
  @ApiProperty({
    description: '预计交货日期',
    example: '2025-02-01',
    required: false,
  })
  expectedDeliveryDate?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: '合同备注',
  })
  @ApiProperty({
    description: '合同备注',
    example: '春季新品采购',
    required: false,
  })
  remark?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '创建人员编码',
  })
  @ApiProperty({
    description: '创建人员编码',
    example: 'user001',
    required: false,
  })
  createdByUserCode?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联供应商表
  @ManyToOne(() => Supplier, { nullable: false })
  @JoinColumn({ name: 'supplierCode', referencedColumnName: 'code' })
  @ApiProperty({ description: '供应商信息', type: () => Supplier })
  supplier: Supplier;

  // 关联采购合同明细
  @OneToMany(
    () => PurchaseContractDetail,
    (detail) => detail.purchaseContract,
    {
      cascade: true,
    },
  )
  @ApiProperty({
    description: '采购合同明细列表',
    type: () => [PurchaseContractDetail],
  })
  details: PurchaseContractDetail[];
}
