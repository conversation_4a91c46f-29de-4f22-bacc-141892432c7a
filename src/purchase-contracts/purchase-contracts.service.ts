import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';

import { PurchaseContract } from './entities/purchase-contract.entity';
import { PurchaseContractDetail } from './entities/purchase-contract-detail.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { Accessory } from '@/accessories/entities/accessory.entity';
import { CreatePurchaseContractDto } from './dto/create-purchase-contract.dto';
import { UpdatePurchaseContractDto } from './dto/update-purchase-contract.dto';
import { QueryPurchaseContractsDto } from './dto/query-purchase-contracts.dto';

// 内部处理接口
interface ProcessedExportDto {
  contractIds?: string[];
}

@Injectable()
export class PurchaseContractsService {
  private readonly logger = new Logger(PurchaseContractsService.name);

  constructor(
    @InjectRepository(PurchaseContract)
    private purchaseContractRepository: Repository<PurchaseContract>,
    @InjectRepository(PurchaseContractDetail)
    private purchaseContractDetailRepository: Repository<PurchaseContractDetail>,
    @InjectRepository(Supplier)
    private supplierRepository: Repository<Supplier>,
    @InjectRepository(Accessory)
    private accessoryRepository: Repository<Accessory>,
    private dataSource: DataSource,
  ) {}

  // 生成采购订单编号
  private async generateOrderNumber(supplierCode: string): Promise<string> {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');

    // 基础格式：PO + 供应商编码 + 年月日时分秒
    const baseOrderNumber = `PO${supplierCode}${year}${month}${day}${hour}${minute}${second}`;

    // 检查是否已存在相同的订单编号
    let orderNumber = baseOrderNumber;
    let counter = 1;

    while (true) {
      const existingContract = await this.purchaseContractRepository.findOne({
        where: { orderNumber, isDeleted: false },
      });

      if (!existingContract) {
        break;
      }

      // 如果存在重复，在后面加上序号
      orderNumber = `${baseOrderNumber}${String(counter).padStart(2, '0')}`;
      counter++;

      // 防止无限循环，最多尝试99次
      if (counter > 99) {
        // 如果还是重复，加上毫秒数
        const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
        orderNumber = `${baseOrderNumber}${milliseconds}`;
        break;
      }
    }

    this.logger.log(
      `Generated order number: ${orderNumber} for supplier: ${supplierCode}`,
    );
    return orderNumber;
  }

  // 验证辅料是否存在并获取辅料信息（兼容新旧模式）
  private async validateAndEnrichAccessories(
    details: any[],
    supplierCode: string,
  ): Promise<any[]> {
    const enrichedDetails: any[] = [];

    for (const detail of details) {
      const { accessoryId, quantity, unitPrice, deliveryDate, remark } = detail;

      // 验证必需的数值字段
      if (quantity == null || isNaN(Number(quantity))) {
        throw new BadRequestException(
          `数量必须是有效的数字，当前值: ${quantity}`,
        );
      }
      if (unitPrice != null && isNaN(Number(unitPrice))) {
        throw new BadRequestException(
          `单价必须是有效的数字，当前值: ${unitPrice}`,
        );
      }

      const safeQuantity = Number(quantity);
      const safeUnitPrice = unitPrice != null ? Number(unitPrice) : null;

      // 如果提供了accessoryId，使用新的简化模式
      if (accessoryId) {
        // 查找辅料
        const accessory = await this.accessoryRepository.findOne({
          where: { id: accessoryId, isDeleted: false },
        });

        if (!accessory) {
          throw new BadRequestException(`辅料 ${accessoryId} 不存在`);
        }

        // 验证辅料是否属于指定供应商
        if (accessory.supplierCode !== supplierCode) {
          throw new BadRequestException(
            `辅料 ${accessory.name} 不属于供应商 ${supplierCode}，实际供应商为 ${accessory.supplierCode}`,
          );
        }

        // 使用辅料成本价作为默认单价
        const finalUnitPrice = safeUnitPrice || accessory.costPrice;
        const detailTotalAmount = safeQuantity * finalUnitPrice;

        // 确保 totalAmount 不为 null、undefined 或 NaN
        const safeTotalAmount =
          isNaN(detailTotalAmount) || detailTotalAmount == null
            ? 0
            : detailTotalAmount;

        this.logger.debug(
          `Calculating totalAmount: quantity=${quantity}, unitPrice=${finalUnitPrice}, totalAmount=${safeTotalAmount}`,
        );

        enrichedDetails.push({
          accessoryId,
          quantity: safeQuantity,
          unitPrice: finalUnitPrice,
          totalAmount: safeTotalAmount,
          deliveryDate: deliveryDate ? new Date(deliveryDate) : null,
          remark,
        });
      } else {
        // 兼容旧模式，直接使用提供的数据
        const finalUnitPrice = safeUnitPrice || 0;
        const detailTotalAmount = safeQuantity * finalUnitPrice;

        // 确保 totalAmount 不为 null、undefined 或 NaN
        const safeTotalAmount =
          isNaN(detailTotalAmount) || detailTotalAmount == null
            ? 0
            : detailTotalAmount;

        this.logger.debug(
          `Calculating totalAmount (legacy mode): quantity=${quantity}, unitPrice=${finalUnitPrice}, totalAmount=${safeTotalAmount}`,
        );

        enrichedDetails.push({
          ...detail,
          quantity: safeQuantity,
          unitPrice: finalUnitPrice,
          totalAmount: safeTotalAmount,
          deliveryDate: deliveryDate ? new Date(deliveryDate) : null,
        });
      }
    }

    return enrichedDetails;
  }

  // 创建采购合同
  async create(
    createDto: CreatePurchaseContractDto,
  ): Promise<PurchaseContract> {
    this.logger.log(
      `Creating purchase contract for supplier: ${createDto.supplierCode}`,
    );

    // 验证供应商是否存在
    const supplier = await this.supplierRepository.findOne({
      where: { code: createDto.supplierCode, isDeleted: false },
    });
    if (!supplier) {
      throw new BadRequestException(`供应商 ${createDto.supplierCode} 不存在`);
    }

    // 验证辅料并获取丰富的明细信息
    const enrichedDetails = await this.validateAndEnrichAccessories(
      createDto.details,
      createDto.supplierCode,
    );

    // 生成或验证订单编号
    let orderNumber = createDto.orderNumber;
    if (!orderNumber) {
      // 如果没有提供订单编号，自动生成
      orderNumber = await this.generateOrderNumber(createDto.supplierCode);
    } else {
      // 如果提供了订单编号，验证是否已存在
      const existingContract = await this.purchaseContractRepository.findOne({
        where: { orderNumber, isDeleted: false },
      });
      if (existingContract) {
        throw new ConflictException(`采购订单编号 ${orderNumber} 已存在`);
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 计算合计金额和数量
      let totalAmount = 0;
      let totalQuantity = 0;

      enrichedDetails.forEach((detail) => {
        totalAmount += detail.totalAmount;
        totalQuantity += detail.quantity;
      });

      // 创建采购合同主记录
      const contract = this.purchaseContractRepository.create({
        orderNumber,
        supplierCode: createDto.supplierCode,
        orderDate: new Date(createDto.orderDate),
        totalAmount,
        totalQuantity,
        status: createDto.status,
        expectedDeliveryDate: createDto.expectedDeliveryDate
          ? new Date(createDto.expectedDeliveryDate)
          : undefined,
        remark: createDto.remark,
        createdByUserCode: createDto.createdByUserCode,
        isDeleted: false,
      });

      const savedContract = await queryRunner.manager.save(contract);

      // 创建采购合同明细记录
      const details = enrichedDetails.map((detail) =>
        this.purchaseContractDetailRepository.create({
          ...detail,
          purchaseContractId: savedContract.id,
          isDeleted: false,
        }),
      );

      await queryRunner.manager.save(details);
      await queryRunner.commitTransaction();

      this.logger.log(
        `Purchase contract created successfully: ${savedContract.id}`,
      );

      // 返回完整的合同信息
      return this.findOne(savedContract.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Failed to create purchase contract', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询采购合同
  async findAll(queryDto: QueryPurchaseContractsDto) {
    const {
      page,
      pageSize,
      orderNumber,
      supplierCode,
      supplierName,
      status,
      startDate,
      endDate,
      search,
      createdByUserCode,
    } = queryDto;

    this.logger.log(
      `Querying purchase contracts: page=${page}, pageSize=${pageSize}`,
    );

    // 构建查询 - 列表查询不加载明细数据，提高性能
    const queryBuilder = this.purchaseContractRepository
      .createQueryBuilder('contract')
      .leftJoinAndSelect('contract.supplier', 'supplier')
      .where('contract.isDeleted = false')
      .orderBy('contract.createdAt', 'DESC');

    // 添加订单编号筛选
    if (orderNumber) {
      queryBuilder.andWhere('contract.orderNumber ILIKE :orderNumber', {
        orderNumber: `%${orderNumber}%`,
      });
    }

    // 添加供应商编码筛选
    if (supplierCode) {
      queryBuilder.andWhere('contract.supplierCode = :supplierCode', {
        supplierCode,
      });
    }

    // 添加供应商名称筛选
    if (supplierName) {
      queryBuilder.andWhere('supplier.name ILIKE :supplierName', {
        supplierName: `%${supplierName}%`,
      });
    }

    // 添加状态筛选
    if (status) {
      queryBuilder.andWhere('contract.status = :status', { status });
    }

    // 添加日期范围筛选
    if (startDate) {
      queryBuilder.andWhere('contract.orderDate >= :startDate', {
        startDate: new Date(startDate),
      });
    }

    if (endDate) {
      queryBuilder.andWhere('contract.orderDate <= :endDate', {
        endDate: new Date(endDate),
      });
    }

    // 添加创建人员筛选
    if (createdByUserCode) {
      queryBuilder.andWhere('contract.createdByUserCode = :createdByUserCode', {
        createdByUserCode,
      });
    }

    // 添加搜索筛选
    if (search) {
      queryBuilder.andWhere(
        '(contract.orderNumber ILIKE :search OR supplier.name ILIKE :search OR contract.remark ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    const [contracts, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    // 格式化返回数据 - 列表查询使用简化格式，不包含明细
    const formattedContracts = contracts.map((contract) =>
      this.formatContractListResponse(contract),
    );

    return {
      contracts: formattedContracts,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  // 格式化合同列表响应数据（不包含明细，提高性能）
  private formatContractListResponse(contract: PurchaseContract) {
    const baseResponse = {
      id: contract.id,
      orderNumber: contract.orderNumber,
      supplierCode: contract.supplierCode,
      orderDate: contract.orderDate,
      totalAmount: contract.totalAmount,
      totalQuantity: contract.totalQuantity,
      status: contract.status,
      expectedDeliveryDate: contract.expectedDeliveryDate,
      remark: contract.remark,
      createdByUserCode: contract.createdByUserCode,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
    };

    // 添加供应商信息
    if (contract.supplier) {
      Object.assign(baseResponse, {
        supplierName: contract.supplier.name,
        supplierAddress: contract.supplier.address,
        supplierContactName: contract.supplier.contactName,
        supplierContactPhone: contract.supplier.contactPhone,
      });
    }

    return baseResponse;
  }

  // 格式化合同详情响应数据（包含完整明细）
  private formatContractResponse(contract: PurchaseContract) {
    const baseResponse = {
      id: contract.id,
      orderNumber: contract.orderNumber,
      supplierCode: contract.supplierCode,
      orderDate: contract.orderDate,
      totalAmount: contract.totalAmount,
      totalQuantity: contract.totalQuantity,
      status: contract.status,
      expectedDeliveryDate: contract.expectedDeliveryDate,
      remark: contract.remark,
      createdByUserCode: contract.createdByUserCode,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
    };

    // 添加供应商信息
    if (contract.supplier) {
      Object.assign(baseResponse, {
        supplierName: contract.supplier.name,
        supplierAddress: contract.supplier.address,
        supplierContactName: contract.supplier.contactName,
        supplierContactPhone: contract.supplier.contactPhone,
      });
    }

    // 添加明细信息（包含辅料信息）
    if (contract.details) {
      Object.assign(baseResponse, {
        details: contract.details
          .filter((detail) => !detail.isDeleted)
          .map((detail) => ({
            id: detail.id,
            accessoryId: detail.accessoryId,
            quantity: detail.quantity,
            unitPrice: detail.unitPrice,
            totalAmount: detail.totalAmount,
            deliveryDate: detail.deliveryDate,
            remark: detail.remark,
            createdAt: detail.createdAt,
            updatedAt: detail.updatedAt,
            // 辅料信息（如果已加载）
            accessory: detail.accessory
              ? {
                  id: detail.accessory.id,
                  articleNumber: detail.accessory.articleNumber,
                  name: detail.accessory.name,
                  brandName: detail.accessory.brandName,
                  supplierStyleNumber: detail.accessory.supplierStyleNumber,
                  color: detail.accessory.color,
                  size: detail.accessory.size,
                  costPrice: detail.accessory.costPrice,
                  imageUrl: detail.accessory.imageUrl,
                  supplierCode: detail.accessory.supplierCode,
                }
              : null,
          })),
      });
    }

    return baseResponse;
  }

  // 根据ID查询采购合同
  async findOne(id: string): Promise<any> {
    this.logger.log(`Finding purchase contract by id: ${id}`);

    const contract = await this.purchaseContractRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['supplier', 'details'],
    });

    if (!contract) {
      throw new NotFoundException(`采购合同 ${id} 不存在`);
    }

    return this.formatContractResponse(contract);
  }

  // 验证状态转换是否合法
  private validateStatusTransition(
    currentStatus: string,
    newStatus: string,
  ): void {
    // 定义允许的状态转换
    const allowedTransitions = {
      draft: ['confirmed'], // 草稿 -> 已确认
      confirmed: ['draft', 'received'], // 已确认 -> 草稿、已收货
      received: ['completed'], // 已收货 -> 已完成
      completed: [], // 已完成不能转换到其他状态
    };

    if (currentStatus === newStatus) {
      return; // 状态没有变化，允许
    }

    const allowed = allowedTransitions[currentStatus] || [];
    if (!allowed.includes(newStatus)) {
      throw new BadRequestException(
        `不允许从状态 "${this.getStatusDisplayName(currentStatus)}" 转换到 "${this.getStatusDisplayName(newStatus)}"`,
      );
    }
  }

  // 获取状态显示名称
  private getStatusDisplayName(status: string): string {
    const statusMap = {
      draft: '草稿',
      confirmed: '已确认',
      received: '已收货',
      completed: '已完成',
    };
    return statusMap[status] || status;
  }

  // 更新采购合同
  async update(
    id: string,
    updateDto: UpdatePurchaseContractDto,
  ): Promise<void> {
    this.logger.log(`Updating purchase contract ${id}`);

    const contract = await this.purchaseContractRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['details'],
    });

    if (!contract) {
      throw new NotFoundException(`采购合同 ${id} 不存在`);
    }

    // 验证状态转换（如果更新了状态）
    if (updateDto.status && updateDto.status !== contract.status) {
      this.validateStatusTransition(contract.status, updateDto.status);
    }

    // 验证明细编辑权限：只有草稿状态可以编辑明细
    if (updateDto.details) {
      // 如果要更新明细，检查当前状态或目标状态是否为草稿
      const finalStatus = updateDto.status || contract.status;
      if (finalStatus !== 'draft') {
        throw new BadRequestException('只有草稿状态的合同才能编辑明细');
      }
    }

    // 验证供应商是否存在（如果更新了供应商编码）
    if (
      updateDto.supplierCode &&
      updateDto.supplierCode !== contract.supplierCode
    ) {
      const supplier = await this.supplierRepository.findOne({
        where: { code: updateDto.supplierCode, isDeleted: false },
      });
      if (!supplier) {
        throw new BadRequestException(
          `供应商 ${updateDto.supplierCode} 不存在`,
        );
      }
    }

    // 验证辅料是否存在（如果更新了明细）
    let enrichedDetails: any[] = [];
    if (updateDto.details) {
      const supplierCodeToUse = updateDto.supplierCode || contract.supplierCode;
      enrichedDetails = await this.validateAndEnrichAccessories(
        updateDto.details,
        supplierCodeToUse,
      );
    }

    // 验证订单编号是否已存在（如果更新了订单编号）
    if (
      updateDto.orderNumber &&
      updateDto.orderNumber !== contract.orderNumber
    ) {
      const existingContract = await this.purchaseContractRepository.findOne({
        where: { orderNumber: updateDto.orderNumber, isDeleted: false },
      });
      if (existingContract) {
        throw new ConflictException(
          `采购订单编号 ${updateDto.orderNumber} 已存在`,
        );
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 如果更新了明细，需要重新计算合计金额和数量
      if (updateDto.details) {
        // 软删除原有明细
        await queryRunner.manager.update(
          PurchaseContractDetail,
          { purchaseContractId: id },
          { isDeleted: true, deletedAt: new Date() },
        );

        // 计算新的合计金额和数量
        let totalAmount = 0;
        let totalQuantity = 0;

        enrichedDetails.forEach((detail) => {
          totalAmount += detail.totalAmount;
          totalQuantity += detail.quantity;
        });

        // 创建新的明细记录
        const details = enrichedDetails.map((detail) =>
          this.purchaseContractDetailRepository.create({
            ...detail,
            purchaseContractId: id,
            isDeleted: false,
          }),
        );

        await queryRunner.manager.save(details);

        // 更新合计金额和数量
        contract.totalAmount = totalAmount;
        contract.totalQuantity = totalQuantity;
      }

      // 更新主记录
      const updateData = {
        ...updateDto,
        orderDate: updateDto.orderDate
          ? new Date(updateDto.orderDate)
          : undefined,
        expectedDeliveryDate: updateDto.expectedDeliveryDate
          ? new Date(updateDto.expectedDeliveryDate)
          : undefined,
      };

      Object.assign(contract, updateData);
      await queryRunner.manager.save(contract);

      await queryRunner.commitTransaction();

      this.logger.log(`Purchase contract ${id} updated successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update purchase contract ${id}`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除采购合同
  async remove(id: string): Promise<void> {
    this.logger.log(`Removing purchase contract ${id}`);

    const contract = await this.purchaseContractRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!contract) {
      throw new NotFoundException(`采购合同 ${id} 不存在`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 软删除主记录
      contract.isDeleted = true;
      contract.deletedAt = new Date();
      await queryRunner.manager.save(contract);

      // 软删除明细记录
      await queryRunner.manager.update(
        PurchaseContractDetail,
        { purchaseContractId: id },
        { isDeleted: true, deletedAt: new Date() },
      );

      await queryRunner.commitTransaction();

      this.logger.log(`Purchase contract ${id} removed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to remove purchase contract ${id}`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 根据辅料ID更新草稿状态合同的价格（简化版）
  async updateDraftContractPricesByAccessoryId(
    accessoryId: string,
    newUnitPrice?: number,
  ): Promise<void> {
    this.logger.log(
      `Updating draft contract prices for accessory ID: ${accessoryId}`,
    );

    // 查找辅料信息
    const accessory = await this.accessoryRepository.findOne({
      where: { id: accessoryId, isDeleted: false },
    });

    if (!accessory) {
      throw new BadRequestException(`辅料 ${accessoryId} 不存在`);
    }

    // 如果没有提供新价格，使用辅料表中的成本价
    const priceToUse =
      newUnitPrice !== undefined ? newUnitPrice : accessory.costPrice;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找所有草稿状态合同中使用该辅料的明细
      const draftContractDetails = await queryRunner.manager
        .createQueryBuilder(PurchaseContractDetail, 'detail')
        .leftJoin('detail.purchaseContract', 'contract')
        .where('detail.accessoryId = :accessoryId', { accessoryId })
        .andWhere('detail.isDeleted = false')
        .andWhere('contract.isDeleted = false')
        .andWhere('contract.status = :status', { status: 'draft' })
        .getMany();

      if (draftContractDetails.length === 0) {
        this.logger.log(
          `No draft contracts found using accessory: ${accessoryId}`,
        );
        await queryRunner.commitTransaction();
        return;
      }

      // 更新每个明细的单价和合计金额
      const updatedContractIds = new Set<string>();

      for (const detail of draftContractDetails) {
        const oldUnitPrice = detail.unitPrice;
        const oldTotalAmount = detail.totalAmount;

        detail.unitPrice = priceToUse;
        detail.totalAmount = detail.quantity * priceToUse;

        await queryRunner.manager.save(detail);
        updatedContractIds.add(detail.purchaseContractId);

        this.logger.log(
          `Updated detail ${detail.id}: price ${oldUnitPrice} -> ${priceToUse}, total ${oldTotalAmount} -> ${detail.totalAmount}`,
        );
      }

      // 重新计算所有受影响合同的总金额和总数量
      for (const contractId of updatedContractIds) {
        await this.recalculateContractTotals(contractId, queryRunner);
      }

      await queryRunner.commitTransaction();

      this.logger.log(
        `Successfully updated ${draftContractDetails.length} draft contract details across ${updatedContractIds.size} contracts`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to update draft contract prices for accessory: ${accessoryId}`,
        error,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 重新计算合同的总金额和总数量
  private async recalculateContractTotals(
    contractId: string,
    queryRunner: any,
  ): Promise<void> {
    // 查询合同的所有有效明细
    const details = await queryRunner.manager.find(PurchaseContractDetail, {
      where: { purchaseContractId: contractId, isDeleted: false },
    });

    // 计算新的总金额和总数量
    let totalAmount = 0;
    let totalQuantity = 0;

    for (const detail of details) {
      totalAmount += detail.totalAmount;
      totalQuantity += detail.quantity;
    }

    // 更新合同主记录
    await queryRunner.manager.update(
      PurchaseContract,
      { id: contractId },
      { totalAmount, totalQuantity },
    );

    this.logger.log(
      `Recalculated contract ${contractId}: totalAmount=${totalAmount}, totalQuantity=${totalQuantity}`,
    );
  }

  // 导出Excel
  async exportToExcel(exportDto?: ProcessedExportDto): Promise<Buffer> {
    this.logger.log('Exporting purchase contracts to Excel');

    // 构建查询条件
    const queryBuilder = this.purchaseContractRepository
      .createQueryBuilder('contract')
      .leftJoinAndSelect('contract.supplier', 'supplier')
      .leftJoinAndSelect('contract.details', 'details')
      .leftJoinAndSelect('details.accessory', 'accessory')
      .where('contract.isDeleted = false')
      .andWhere('details.isDeleted = false')
      .orderBy('contract.createdAt', 'DESC')
      .addOrderBy('details.createdAt', 'ASC');

    // 如果指定了合同ID，则只导出选中的合同
    if (exportDto?.contractIds && exportDto.contractIds.length > 0) {
      queryBuilder.andWhere('contract.id IN (:...contractIds)', {
        contractIds: exportDto.contractIds,
      });
    }

    const contracts = await queryBuilder.getMany();

    if (contracts.length === 0) {
      throw new BadRequestException('没有找到符合条件的采购合同数据');
    }

    // 计算统计信息
    let totalContracts = contracts.length;
    let totalAmount = 0;
    let totalQuantity = 0;
    let totalDetails = 0;

    contracts.forEach((contract) => {
      totalAmount += contract.totalAmount;
      totalQuantity += contract.totalQuantity;
      totalDetails += contract.details.length;
    });

    // 生成Excel
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('采购合同明细');

      // 设置列宽
      worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '采购订单编号', key: 'orderNumber', width: 20 },
        { header: '供应商编码', key: 'supplierCode', width: 15 },
        { header: '供应商名称', key: 'supplierName', width: 20 },
        { header: '订单日期', key: 'orderDate', width: 15 },
        { header: '合同状态', key: 'status', width: 12 },
        { header: '辅料编号', key: 'accessoryCode', width: 15 },
        { header: '辅料名称', key: 'accessoryName', width: 25 },
        { header: '品牌', key: 'brandName', width: 15 },
        { header: '颜色', key: 'color', width: 12 },
        { header: '尺寸', key: 'size', width: 12 },
        { header: '数量', key: 'quantity', width: 10 },
        { header: '单价', key: 'unitPrice', width: 12 },
        { header: '合计金额', key: 'totalAmount', width: 15 },
        { header: '货期', key: 'deliveryDate', width: 15 },
        { header: '辅料图片', key: 'accessoryImage', width: 30 },
        { header: '备注', key: 'remark', width: 30 },
      ];

      // 添加标题
      worksheet.mergeCells('A1:Q1');
      const titleCell = worksheet.getCell('A1');
      titleCell.value = '采购辅料合同明细报表';
      titleCell.font = { bold: true, size: 16 };
      titleCell.alignment = { horizontal: 'center' };

      // 添加概况信息
      let currentRow = 3;
      worksheet.getCell(`A${currentRow}`).value = '导出概况';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '合同总数';
      worksheet.getCell(`B${currentRow}`).value = `${totalContracts}个`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '明细总数';
      worksheet.getCell(`B${currentRow}`).value = `${totalDetails}条`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '合计金额';
      worksheet.getCell(`B${currentRow}`).value = `¥${totalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '合计数量';
      worksheet.getCell(`B${currentRow}`).value = `${totalQuantity}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出时间';
      worksheet.getCell(`B${currentRow}`).value = new Date().toLocaleString(
        'zh-CN',
      );
      currentRow += 2;

      // 添加明细记录标题
      worksheet.getCell(`A${currentRow}`).value = '明细记录';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      // 添加表头
      const headerRow = worksheet.getRow(currentRow);
      headerRow.values = [
        '序号',
        '采购订单编号',
        '供应商编码',
        '供应商名称',
        '订单日期',
        '合同状态',
        '辅料编号',
        '辅料名称',
        '品牌',
        '颜色',
        '尺寸',
        '数量',
        '单价',
        '合计金额',
        '货期',
        '辅料图片',
        '备注',
      ];
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
      currentRow++;

      // 添加明细数据和图片
      let detailIndex = 1;
      for (const contract of contracts) {
        for (const detail of contract.details) {
          const row = worksheet.getRow(currentRow);

          // 设置行高以容纳图片
          row.height = 120;

          // 状态映射
          const statusMap = {
            draft: '草稿',
            confirmed: '已确认',
            received: '已收货',
            completed: '已完成',
          };

          // 添加基本数据
          row.values = [
            detailIndex,
            contract.orderNumber,
            contract.supplierCode,
            contract.supplier?.name || '',
            contract.orderDate
              ? new Date(contract.orderDate).toLocaleDateString('zh-CN')
              : '',
            statusMap[contract.status] || contract.status,
            detail.accessory?.articleNumber || '',
            detail.accessory?.name || '',
            detail.accessory?.brandName || '',
            detail.accessory?.color || '',
            detail.accessory?.size || '',
            detail.quantity,
            `¥${detail.unitPrice.toFixed(2)}`,
            `¥${detail.totalAmount.toFixed(2)}`,
            detail.deliveryDate
              ? new Date(detail.deliveryDate).toLocaleDateString('zh-CN')
              : '',
            '', // 辅料图片列留空，用于插入图片
            detail.remark || '',
          ];

          // 下载并插入图片
          try {
            if (detail.accessory?.imageUrl) {
              this.logger.log(
                `Downloading accessory image: ${detail.accessory.imageUrl}`,
              );

              const imageResponse = await axios.get(detail.accessory.imageUrl, {
                responseType: 'arraybuffer',
                timeout: 10000,
              });

              const imageBuffer = Buffer.from(imageResponse.data);

              // 添加图片到工作簿
              const imageId = workbook.addImage({
                buffer: imageBuffer,
                extension: 'jpeg',
              });

              // 在辅料图片列插入图片
              worksheet.addImage(imageId, {
                tl: { col: 15, row: currentRow - 1 }, // 从辅料图片列开始
                ext: { width: 200, height: 100 }, // 图片大小
              });

              this.logger.log(
                `Image inserted successfully for detail ${detailIndex}`,
              );
            }
          } catch (imageError) {
            this.logger.warn(
              `Failed to load image for detail ${detailIndex}: ${detail.accessory?.imageUrl}`,
              imageError.message,
            );
            // 如果图片加载失败，在辅料图片列显示链接
            row.getCell(16).value = detail.accessory?.imageUrl || '';
          }

          currentRow++;
          detailIndex++;
        }
      }

      // 设置边框
      const borderStyle = {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const },
      };

      // 为表格添加边框
      const tableStartRow = currentRow - (totalDetails + 1);
      const tableEndRow = currentRow - 1;
      for (let row = tableStartRow; row <= tableEndRow; row++) {
        for (let col = 1; col <= 17; col++) {
          worksheet.getCell(row, col).border = borderStyle;
        }
      }

      // 生成Excel缓冲区
      const excelBuffer = await workbook.xlsx.writeBuffer();
      return Buffer.from(excelBuffer);
    } catch (error) {
      this.logger.error('Excel generation failed', error);
      throw new BadRequestException('Excel生成失败');
    }
  }
}
