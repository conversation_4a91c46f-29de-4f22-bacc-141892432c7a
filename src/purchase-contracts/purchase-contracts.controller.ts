import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  Res,
  Req,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { PurchaseContractsService } from './purchase-contracts.service';
import { CreatePurchaseContractDto } from './dto/create-purchase-contract.dto';
import { UpdatePurchaseContractDto } from './dto/update-purchase-contract.dto';
import { QueryPurchaseContractsDto } from './dto/query-purchase-contracts.dto';
import { PurchaseContractStatus } from './entities/purchase-contract.entity';

@ApiTags('purchase-contracts')
@Controller('purchase-contracts')
export class PurchaseContractsController {
  private readonly logger = new Logger(PurchaseContractsController.name);

  constructor(
    private readonly purchaseContractsService: PurchaseContractsService,
  ) {}

  @Post()
  @ApiOperation({ summary: '新增采购辅料合同' })
  @ApiResponse({
    status: 200,
    description: '采购合同创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 409,
    description: '采购订单编号已存在',
  })
  async create(@Body() createDto: CreatePurchaseContractDto) {
    this.logger.log(`Creating purchase contract: ${createDto.orderNumber}`);

    await this.purchaseContractsService.create(createDto);

    return {
      code: 200,
      data: null,
      message: '采购合同创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '分页查询采购合同列表' })
  @ApiQuery({
    name: 'page',
    description: '页码（必填）',
    example: 1,
    required: true,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量（必填）',
    example: 10,
    required: true,
  })
  @ApiQuery({
    name: 'orderNumber',
    description: '采购订单编号筛选（可选）',
    example: 'PO202501001',
    required: false,
  })
  @ApiQuery({
    name: 'supplierCode',
    description: '供应商编码筛选（可选）',
    example: 'SUP001',
    required: false,
  })
  @ApiQuery({
    name: 'supplierName',
    description: '供应商名称筛选（可选，模糊搜索）',
    example: '广州服装',
    required: false,
  })
  @ApiQuery({
    name: 'status',
    description: '合同状态筛选（可选）',
    enum: PurchaseContractStatus,
    example: PurchaseContractStatus.CONFIRMED,
    required: false,
  })
  @ApiQuery({
    name: 'startDate',
    description: '起始日期（可选）',
    example: '2025-01-01',
    required: false,
  })
  @ApiQuery({
    name: 'endDate',
    description: '终止日期（可选）',
    example: '2025-12-31',
    required: false,
  })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词（可选，模糊搜索订单编号、供应商名称、备注）',
    example: '春季',
    required: false,
  })
  @ApiQuery({
    name: 'createdByUserCode',
    description: '创建人员编码筛选（可选）',
    example: 'user001',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAll(@Query() queryDto: QueryPurchaseContractsDto) {
    this.logger.log(
      `Querying purchase contracts with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.purchaseContractsService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '获取采购合同详情' })
  @ApiParam({
    name: 'id',
    description: '采购合同ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '采购合同不存在',
  })
  async findOne(@Param('id') id: string) {
    this.logger.log(`Finding purchase contract by id: ${id}`);

    const contract = await this.purchaseContractsService.findOne(id);

    return {
      code: 200,
      data: contract,
      message: '查询成功',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新采购合同' })
  @ApiParam({
    name: 'id',
    description: '采购合同ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '采购合同不存在',
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdatePurchaseContractDto,
  ) {
    this.logger.log(`Updating purchase contract ${id}`);

    await this.purchaseContractsService.update(id, updateDto);

    return {
      code: 200,
      data: null,
      message: '采购合同更新成功',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除采购合同' })
  @ApiParam({
    name: 'id',
    description: '采购合同ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '采购合同不存在',
  })
  async remove(@Param('id') id: string) {
    this.logger.log(`Removing purchase contract ${id}`);

    await this.purchaseContractsService.remove(id);

    return {
      code: 200,
      data: null,
      message: '采购合同删除成功',
    };
  }

  @Get('export/excel')
  @ApiOperation({ summary: '导出采购合同明细Excel' })
  @ApiQuery({
    name: 'contractIds',
    description: '合同ID列表（逗号分隔，可选）- 如果不提供则导出所有合同明细',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  async exportToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Exporting purchase contracts to Excel`);

    try {
      // 从原始请求中获取查询参数
      const { contractIds } = req.query;

      // 构建导出参数
      const exportParams: any = {};

      if (contractIds) {
        // 处理contractIds参数（逗号分隔的字符串转换为数组）
        const decodedContractIds = decodeURIComponent(contractIds);
        exportParams.contractIds = decodedContractIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);
      }

      const excelBuffer =
        await this.purchaseContractsService.exportToExcel(exportParams);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="purchase-contracts-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }

  @Patch('update-draft-prices')
  @ApiOperation({ summary: '根据辅料ID更新草稿状态合同的价格' })
  @ApiQuery({
    name: 'accessoryId',
    description: '辅料ID（必填）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    required: true,
  })
  @ApiQuery({
    name: 'newUnitPrice',
    description: '新单价（可选，不提供则使用辅料成本价）',
    example: 12.5,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '价格更新成功',
  })
  async updateDraftContractPrices(
    @Query('accessoryId') accessoryId: string,
    @Query('newUnitPrice') newUnitPrice?: number,
  ) {
    this.logger.log(
      `Updating draft contract prices for accessory ID: ${accessoryId}`,
    );

    await this.purchaseContractsService.updateDraftContractPricesByAccessoryId(
      accessoryId,
      newUnitPrice ? Number(newUnitPrice) : undefined,
    );

    return {
      code: 200,
      data: null,
      message: '草稿状态合同价格更新成功',
    };
  }
}
