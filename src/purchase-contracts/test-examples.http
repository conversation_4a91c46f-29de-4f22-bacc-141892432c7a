### 采购辅料合同接口测试示例

### 1. 新增采购合同（自动生成订单编号）
POST http://localhost:3000/purchase-contracts
Content-Type: application/json

{
  "supplierCode": "SUP001",
  "orderDate": "2025-01-01",
  "status": "draft",
  "expectedDeliveryDate": "2025-02-01",
  "remark": "春季新品采购合同（自动生成编号）",
  "createdByUserCode": "husky",
  "details": [
    {
      "supplierItemCode": "SP-2025-001",
      "itemName": "纯棉拉链",
      "imageUrl": "https://example.com/zipper.jpg",
      "quantity": 100,
      "unitPrice": 5.50,
      "deliveryDate": "2025-01-15",
      "remark": "需要特殊包装",
      "specification": "20cm 黑色",
      "unit": "条"
    },
    {
      "supplierItemCode": "SP-2025-002",
      "itemName": "金属纽扣",
      "imageUrl": "https://example.com/button.jpg",
      "quantity": 200,
      "unitPrice": 2.30,
      "deliveryDate": "2025-01-20",
      "remark": "金色电镀",
      "specification": "15mm 圆形",
      "unit": "个"
    }
  ]
}

### 2. 新增采购合同（手动指定订单编号）
POST http://localhost:3000/purchase-contracts
Content-Type: application/json

{
  "orderNumber": "PO202501002",
  "supplierCode": "SUP001",
  "orderDate": "2025-01-02",
  "remark": "手动指定订单编号",
  "details": [
    {
      "supplierItemCode": "SP-2025-003",
      "itemName": "棉线",
      "quantity": 50,
      "unitPrice": 8.00,
      "unit": "卷"
    }
  ]
}

### 3. 新增采购合同（简化示例，自动生成编号）
POST http://localhost:3000/purchase-contracts
Content-Type: application/json

{
  "supplierCode": "SUP001",
  "orderDate": "2025-01-03",
  "details": [
    {
      "supplierItemCode": "SP-2025-004",
      "itemName": "松紧带",
      "quantity": 30,
      "unitPrice": 3.50,
      "unit": "米"
    }
  ]
}

### 3. 分页查询采购合同（基础查询）
GET http://localhost:3000/purchase-contracts?page=1&pageSize=10

### 4. 分页查询采购合同（带筛选条件）
GET http://localhost:3000/purchase-contracts?page=1&pageSize=10&supplierCode=SUP001&status=draft&startDate=2025-01-01&endDate=2025-12-31

### 5. 分页查询采购合同（搜索功能）
GET http://localhost:3000/purchase-contracts?page=1&pageSize=10&search=春季&supplierName=广州

### 6. 获取采购合同详情
GET http://localhost:3000/purchase-contracts/{{contractId}}

### 7. 更新采购合同状态
PATCH http://localhost:3000/purchase-contracts/{{contractId}}
Content-Type: application/json

{
  "status": "confirmed",
  "remark": "合同已确认，开始生产"
}

### 8. 更新采购合同（包含明细）
PATCH http://localhost:3000/purchase-contracts/{{contractId}}
Content-Type: application/json

{
  "expectedDeliveryDate": "2025-02-15",
  "remark": "延期交货",
  "details": [
    {
      "supplierItemCode": "SP-2025-001",
      "itemName": "纯棉拉链",
      "quantity": 150,
      "unitPrice": 5.20,
      "deliveryDate": "2025-01-25",
      "specification": "20cm 黑色",
      "unit": "条"
    }
  ]
}

### 9. 删除采购合同
DELETE http://localhost:3000/purchase-contracts/{{contractId}}

### 10. 导出Excel（全部合同明细）
GET http://localhost:3000/purchase-contracts/export/excel

### 11. 导出Excel（指定合同ID）
GET http://localhost:3000/purchase-contracts/export/excel?contractIds=uuid1,uuid2,uuid3

### 测试数据说明
# 在测试前，请确保：
# 1. 数据库中存在供应商 SUP001
# 2. 数据库中存在用户 husky
# 3. 替换 {{contractId}} 为实际的合同ID

### 批量创建测试数据
POST http://localhost:3000/purchase-contracts
Content-Type: application/json

{
  "orderNumber": "PO202501003",
  "supplierCode": "SUP001",
  "orderDate": "2025-01-03",
  "status": "confirmed",
  "details": [
    {
      "supplierItemCode": "SP-2025-004",
      "itemName": "尼龙拉链",
      "quantity": 80,
      "unitPrice": 4.20,
      "unit": "条"
    },
    {
      "supplierItemCode": "SP-2025-005",
      "itemName": "塑料纽扣",
      "quantity": 300,
      "unitPrice": 1.50,
      "unit": "个"
    },
    {
      "supplierItemCode": "SP-2025-006",
      "itemName": "松紧带",
      "quantity": 120,
      "unitPrice": 3.80,
      "unit": "米"
    }
  ]
}
