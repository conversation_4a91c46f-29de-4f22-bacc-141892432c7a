import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsDateString,
  IsEnum,
  IsInt,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PurchaseContractStatus } from '../entities/purchase-contract.entity';

export class QueryPurchaseContractsDto {
  @ApiProperty({
    description: '页码（必填）',
    example: 1,
  })
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码必须大于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填）',
    example: 10,
  })
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量必须大于0' })
  pageSize: number;

  @ApiProperty({
    description: '采购订单编号筛选（可选）',
    example: 'PO202501001',
    required: false,
  })
  @IsOptional()
  @IsString()
  orderNumber?: string;

  @ApiProperty({
    description: '供应商编码筛选（可选）',
    example: 'SUP001',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierCode?: string;

  @ApiProperty({
    description: '供应商名称筛选（可选，模糊搜索）',
    example: '广州服装',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierName?: string;

  @ApiProperty({
    description: '合同状态筛选（可选）',
    enum: PurchaseContractStatus,
    example: PurchaseContractStatus.CONFIRMED,
    required: false,
  })
  @IsOptional()
  @IsEnum(PurchaseContractStatus, {
    message: '合同状态必须是有效的枚举值',
  })
  status?: PurchaseContractStatus;

  @ApiProperty({
    description: '起始日期（可选）',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '起始日期格式不正确' })
  startDate?: string;

  @ApiProperty({
    description: '终止日期（可选）',
    example: '2025-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '终止日期格式不正确' })
  endDate?: string;

  @ApiProperty({
    description: '搜索关键词（可选，模糊搜索订单编号、供应商名称、备注）',
    example: '春季',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '创建人员编码筛选（可选）',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  createdByUserCode?: string;
}
