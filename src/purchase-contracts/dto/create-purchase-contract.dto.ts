import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDateString,
  IsArray,
  ValidateNested,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PurchaseContractStatus } from '../entities/purchase-contract.entity';

export class CreatePurchaseContractDetailDto {
  @ApiProperty({
    description: '辅料ID（可选，用于新的简化模式）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    required: false,
  })
  @IsOptional()
  @IsString()
  accessoryId?: string;

  @ApiProperty({
    description: '下单数量（必填）',
    example: 100,
  })
  @IsNotEmpty({ message: '下单数量不能为空' })
  @Type(() => Number)
  quantity: number;

  @ApiProperty({
    description: '单价（可选，不填则使用辅料成本价）',
    example: 5.50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  unitPrice?: number;

  @ApiProperty({
    description: '货期（可选）',
    example: '2025-01-15',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '货期格式不正确' })
  deliveryDate?: string;

  @ApiProperty({
    description: '备注（可选）',
    example: '需要特殊包装',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;
}

export class CreatePurchaseContractDto {
  @ApiProperty({
    description: '采购订单编号（可选，不传则自动生成）',
    example: 'PO202501001',
    required: false,
  })
  @IsOptional()
  @IsString()
  orderNumber?: string;

  @ApiProperty({
    description: '供应商编码（必填）',
    example: 'SUP001',
  })
  @IsString()
  @IsNotEmpty({ message: '供应商编码不能为空' })
  supplierCode: string;

  @ApiProperty({
    description: '采购订单日期（必填）',
    example: '2025-01-01',
  })
  @IsDateString({}, { message: '采购订单日期格式不正确' })
  orderDate: string;

  @ApiProperty({
    description: '合同状态（可选）',
    enum: PurchaseContractStatus,
    example: PurchaseContractStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(PurchaseContractStatus, {
    message: '合同状态必须是有效的枚举值',
  })
  status?: PurchaseContractStatus;

  @ApiProperty({
    description: '预计交货日期（可选）',
    example: '2025-02-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '预计交货日期格式不正确' })
  expectedDeliveryDate?: string;

  @ApiProperty({
    description: '合同备注（可选）',
    example: '春季新品采购',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;

  @ApiProperty({
    description: '创建人员编码（可选）',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  createdByUserCode?: string;

  @ApiProperty({
    description: '采购合同明细列表（必填）',
    type: [CreatePurchaseContractDetailDto],
  })
  @IsArray({ message: '采购合同明细必须是数组' })
  @ValidateNested({ each: true })
  @Type(() => CreatePurchaseContractDetailDto)
  details: CreatePurchaseContractDetailDto[];
}
