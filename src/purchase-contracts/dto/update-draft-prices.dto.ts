import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateDraftPricesBySupplierItemDto {
  @ApiProperty({
    description: '供应商款号（必填）',
    example: 'SP-2025-001',
  })
  @IsString()
  @IsNotEmpty({ message: '供应商款号不能为空' })
  supplierItemCode: string;

  @ApiProperty({
    description: '新单价（必填，保留两位小数）',
    example: 12.50,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '新单价必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '新单价不能小于0' })
  newUnitPrice: number;
}

export class UpdateDraftPricesByAccessoryDto {
  @ApiProperty({
    description: '供应商编码（必填）',
    example: 'SUP001',
  })
  @IsString()
  @IsNotEmpty({ message: '供应商编码不能为空' })
  supplierCode: string;

  @ApiProperty({
    description: '辅料名称（必填）',
    example: '纯棉拉链',
  })
  @IsString()
  @IsNotEmpty({ message: '辅料名称不能为空' })
  itemName: string;

  @ApiProperty({
    description: '规格型号（可选）',
    example: '20cm 黑色',
    required: false,
  })
  @IsOptional()
  @IsString()
  specification?: string;

  @ApiProperty({
    description: '新单价（可选，不提供则从辅料表查询最新价格）',
    example: 12.50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '新单价必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '新单价不能小于0' })
  newUnitPrice?: number;
}
