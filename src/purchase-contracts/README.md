# 采购辅料合同管理接口（简化版）

## 概述

采购辅料合同管理接口提供了完整的采购辅料合同管理功能，通过关联辅料表实现数据规范化和简化操作。

## 功能特性

### 采购合同管理

- **合同信息**: 采购订单编号、供应商信息、订单日期、合计货款等
- **订单编号**: 支持自动生成或手动指定，自动生成规则：PO + 供应商编码 + 年月日时分秒
- **状态管理**: 草稿、已确认、生产中、已发货、已收货、已完成、已取消
- **自动计算**: 合计金额和数量根据明细自动计算
- **价格更新**: 支持草稿状态合同的辅料价格自动更新

### 辅料明细管理（简化版）

- **辅料关联**: 通过辅料ID直接关联辅料表，自动获取辅料信息
- **智能定价**: 单价可选填，不填则自动使用辅料成本价
- **数量管理**: 下单数量、合计金额（自动计算）
- **交期管理**: 货期设置（可选）
- **备注信息**: 详细备注说明
- **数据一致性**: 辅料信息统一从辅料表获取，避免数据冗余

### Excel导出功能（简化版）

- **一键导出**: 直接导出采购合同明细Excel，无需复杂筛选
- **完整明细**: 包含合同信息和所有明细数据
- **图片支持**: 自动下载并嵌入辅料图片到Excel中
- **汇总统计**: 导出文件包含合同总数、明细总数、合计金额等统计信息
- **选择性导出**: 支持通过contractIds参数导出指定合同（可选）
- **格式美观**: 包含标题、概况信息、明细表格，带边框和样式

### 价格管理功能

- **草稿价格更新**: 当辅料价格变化时，自动更新草稿状态合同的价格
- **批量更新**: 支持按供应商款号或辅料信息批量更新价格
- **自动重算**: 价格更新后自动重新计算合同总金额和总数量
- **智能匹配**: 可从辅料表自动获取最新价格进行更新

## 数据库结构

### 采购合同主表 (purchase_contracts)

- `id` - UUID主键
- `order_number` - 采购订单编号（唯一）
- `supplier_code` - 供应商编码
- `order_date` - 采购订单日期
- `total_amount` - 合计货款（自动计算）
- `total_quantity` - 合计数量（自动计算）
- `status` - 合同状态
- `expected_delivery_date` - 预计交货日期
- `remark` - 合同备注
- `created_by_user_code` - 创建人员编码

### 采购合同明细表 (purchase_contract_details)

- `id` - UUID主键
- `purchase_contract_id` - 采购合同ID
- `supplier_code` - 供应商编码
- `supplier_item_code` - 供应商款号
- `item_name` - 辅料名称
- `image_url` - 辅料图片URL
- `quantity` - 下单数量
- `unit_price` - 单价
- `total_amount` - 合计金额（单价 × 数量）
- `delivery_date` - 货期
- `remark` - 备注
- `specification` - 规格型号
- `unit` - 单位

## API 接口

### 1. 新增采购合同

```
POST /purchase-contracts
```

**请求体示例（自动生成订单编号）：**

```json
{
  "supplierCode": "SUP001",
  "orderDate": "2025-01-01",
  "status": "draft",
  "expectedDeliveryDate": "2025-02-01",
  "remark": "春季新品采购",
  "createdByUserCode": "user001",
  "details": [
    {
      "supplierItemCode": "SP-2025-001",
      "itemName": "纯棉拉链",
      "imageUrl": "https://example.com/zipper.jpg",
      "quantity": 100,
      "unitPrice": 5.5,
      "deliveryDate": "2025-01-15",
      "remark": "需要特殊包装",
      "specification": "20cm 黑色",
      "unit": "条"
    }
  ]
}
```

**请求体示例（手动指定订单编号）：**

```json
{
  "orderNumber": "PO202501001",
  "supplierCode": "SUP001",
  "orderDate": "2025-01-01",
  "status": "draft",
  "expectedDeliveryDate": "2025-02-01",
  "remark": "春季新品采购",
  "createdByUserCode": "user001",
  "details": [
    {
      "supplierItemCode": "SP-2025-001",
      "itemName": "纯棉拉链",
      "imageUrl": "https://example.com/zipper.jpg",
      "quantity": 100,
      "unitPrice": 5.5,
      "deliveryDate": "2025-01-15",
      "remark": "需要特殊包装",
      "specification": "20cm 黑色",
      "unit": "条"
    }
  ]
}
```

### 2. 分页查询采购合同

```
GET /purchase-contracts?page=1&pageSize=10
```

**可选参数：**

- `orderNumber`: 采购订单编号筛选
- `supplierCode`: 供应商编码筛选
- `supplierName`: 供应商名称筛选（模糊搜索）
- `status`: 合同状态筛选
- `startDate`: 起始日期
- `endDate`: 终止日期
- `search`: 搜索关键词
- `createdByUserCode`: 创建人员编码筛选

**响应示例：**

```json
{
  "code": 200,
  "data": {
    "contracts": [...],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  },
  "message": "查询成功"
}
```

### 3. 获取合同详情

```
GET /purchase-contracts/:id
```

### 4. 更新合同

```
PATCH /purchase-contracts/:id
```

### 5. 删除合同

```
DELETE /purchase-contracts/:id
```

### 6. 导出Excel（简化版）

```
GET /purchase-contracts/export/excel
```

**可选参数：**

- `contractIds`: 合同ID列表（逗号分隔）- 如果不提供则导出所有合同明细

**特性：**

- **一键导出**: 无需复杂筛选，直接导出采购合同明细
- **完整信息**: 包含合同基本信息和所有明细数据
- **辅料图片**: 自动下载并嵌入辅料图片到Excel中
- **统计汇总**: 包含合同总数、明细总数、合计金额、合计数量等统计信息
- **美观格式**: 包含标题、概况信息、明细表格，带边框和样式
- **容错处理**: 如果图片下载失败，显示图片链接
- **选择性导出**: 可通过contractIds参数导出指定合同（可选）

**Excel内容结构：**

1. **标题**: 采购辅料合同明细报表
2. **导出概况**: 合同总数、明细总数、合计金额、合计数量、导出时间
3. **明细记录**: 包含以下列
   - 序号、采购订单编号、供应商编码、供应商名称
   - 订单日期、合同状态、辅料编号、辅料名称
   - 品牌、颜色、尺寸、数量、单价、合计金额
   - 货期、辅料图片、备注

### 7. 更新草稿状态合同价格（简化版）

```
PATCH /purchase-contracts/update-draft-prices
```

**必填参数：**

- `accessoryId`: 辅料ID

**可选参数：**

- `newUnitPrice`: 新单价（不提供则使用辅料成本价）

**功能：**

- 根据辅料ID精确匹配草稿合同中的辅料
- 支持从辅料表自动获取最新成本价
- 批量更新所有使用该辅料的草稿合同
- 自动重新计算所有受影响合同的总金额和总数量
- 使用事务确保数据一致性

## 合同状态说明

| 状态   | 英文值    | 说明                                 |
| ------ | --------- | ------------------------------------ |
| 草稿   | draft     | 初始状态，可以修改明细和基本信息     |
| 已确认 | confirmed | 合同已确认，不能修改明细，可转回草稿 |
| 已收货 | received  | 已收到货物，不能修改明细             |
| 已完成 | completed | 合同执行完成，不能修改任何信息       |

## 状态转换规则

- **草稿 → 已确认**：确认合同内容无误
- **已确认 → 草稿**：需要修改合同内容时可转回草稿
- **已确认 → 已收货**：收到供应商货物后更新状态
- **已收货 → 已完成**：合同执行完毕
- **已完成**：终态，不能转换到其他状态

## 明细编辑权限

- **只有草稿状态**的合同才能编辑明细（增加、删除、修改明细项）
- 其他状态的合同只能修改基本信息（如备注、预计交货日期等）
- 状态转换和明细编辑可以在同一次更新中进行

## 业务逻辑

### 订单编号自动生成规则

1. **生成格式**: PO + 供应商编码 + 年月日时分秒
2. **示例**: 供应商编码SUP001，生成时间2025-01-15 14:30:25，生成编号：POSUP00120250115143025
3. **防重复机制**:
   - 如果生成的编号已存在，会在后面加上两位序号（01、02...）
   - 如果序号用完（99次），会加上毫秒数确保唯一性
4. **手动指定**: 也可以手动指定订单编号，系统会验证唯一性

### 自动计算功能

1. **明细合计金额** = 单价 × 数量
2. **合同总金额** = 所有明细合计金额之和
3. **合同总数量** = 所有明细数量之和

### 数据验证

- 采购订单编号必须唯一
- 供应商编码必须存在于供应商表中
- **辅料验证**: 辅料ID必须对应辅料表中的有效辅料
- **供应商匹配**: 辅料必须属于指定的供应商
- 下单数量必须大于0
- 单价可选，不填则自动使用辅料成本价
- 金额字段最多保留两位小数

### 事务处理

- 创建合同时，主表和明细表在同一事务中处理
- 更新合同时，如果更新明细，会先软删除原明细再创建新明细
- 删除合同时，主表和明细表都进行软删除
- 价格更新时，使用事务确保数据一致性

### 价格更新机制

1. **草稿状态限制**: 只有草稿状态的合同才能自动更新价格
2. **自动重算**: 更新明细价格后，自动重新计算合同总金额和总数量
3. **批量处理**: 支持一次更新多个合同中的相同辅料价格
4. **智能匹配**: 可根据供应商编码、辅料名称、规格型号精确匹配
5. **价格来源**: 可手动指定新价格，或从辅料表自动获取最新价格

## 使用示例

### 创建采购合同

```bash
curl -X POST "http://localhost:3000/purchase-contracts" \
  -H "Content-Type: application/json" \
  -d '{
    "orderNumber": "PO202501001",
    "supplierCode": "SUP001",
    "orderDate": "2025-01-01",
    "details": [
      {
        "supplierItemCode": "SP-001",
        "itemName": "纯棉拉链",
        "quantity": 100,
        "unitPrice": 5.50
      }
    ]
  }'
```

### 查询合同列表

```bash
curl -X GET "http://localhost:3000/purchase-contracts?page=1&pageSize=10&supplierCode=SUP001"
```

### 导出Excel（全部合同明细）

```bash
curl -X GET "http://localhost:3000/purchase-contracts/export/excel"
```

### 导出Excel（指定合同）

```bash
curl -X GET "http://localhost:3000/purchase-contracts/export/excel?contractIds=uuid1,uuid2,uuid3"
```

### 更新草稿状态合同价格（指定新价格）

```bash
curl -X PATCH "http://localhost:3000/purchase-contracts/update-draft-prices?accessoryId=a1b2c3d4-e5f6-7890-abcd-ef1234567890&newUnitPrice=6.50"
```

### 自动更新草稿状态合同价格（使用辅料成本价）

```bash
curl -X PATCH "http://localhost:3000/purchase-contracts/update-draft-prices?accessoryId=a1b2c3d4-e5f6-7890-abcd-ef1234567890"
```

## 注意事项

1. **订单编号唯一性**: 采购订单编号在系统中必须唯一
2. **辅料关联**: 创建采购合同时，必须提供有效的辅料ID
3. **供应商匹配**: 辅料必须属于指定的供应商，否则创建失败
4. **智能定价**: 单价可选填，不填则自动使用辅料成本价
5. **事务安全**: 所有涉及多表操作的功能都使用事务处理
6. **软删除**: 删除操作为软删除，数据不会物理删除
7. **分页必填**: 查询接口的page和pageSize参数为必填
8. **状态转换控制**: 合同状态变更必须遵循预定义的转换规则
9. **明细编辑权限**: 只有草稿状态的合同才能编辑明细
10. **状态转换验证**: 系统会自动验证状态转换的合法性
11. **列表性能优化**: 列表查询不返回明细数据，提高查询性能
12. **价格更新限制**: 只有草稿状态的合同才能进行价格更新
13. **价格更新范围**: 价格更新会影响所有使用该辅料的草稿合同
14. **数据一致性**: 辅料信息统一从辅料表获取，确保数据准确性
15. **业务流程控制**: 已确认状态可以转回草稿，便于修改后重新确认
