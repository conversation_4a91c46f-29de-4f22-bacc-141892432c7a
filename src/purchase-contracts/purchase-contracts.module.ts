import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PurchaseContractsService } from './purchase-contracts.service';
import { PurchaseContractsController } from './purchase-contracts.controller';
import { PurchaseContract } from './entities/purchase-contract.entity';
import { PurchaseContractDetail } from './entities/purchase-contract-detail.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { Accessory } from '@/accessories/entities/accessory.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PurchaseContract,
      PurchaseContractDetail,
      Supplier,
      Accessory,
    ]),
  ],
  controllers: [PurchaseContractsController],
  providers: [PurchaseContractsService],
  exports: [PurchaseContractsService],
})
export class PurchaseContractsModule {}
