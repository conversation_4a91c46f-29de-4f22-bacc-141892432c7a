# 佣金发货申请书管理接口

## 概述

佣金发货申请书管理接口提供了完整的佣金发货申请书管理功能，严格按照合同模板设计，所有字段均为必填项，确保数据的完整性和准确性。

## 功能特性

### 申请书信息管理

- **贸易公司名称**: 固定为"广州莱了贸易有限公司"或其他贸易公司名称
- **乙方信息**: 乙方姓名和身份证号
- **申请日期**: 年、月、日分别存储，便于查询和统计
- **品牌货款明细**: 支持多个品牌的货款明细，包含品牌名称、数量和金额
- **金额管理**: 实际发货金额、本次申请欠款发货金额、累计欠款金额
- **归还日期**: 年、月、日分别存储
- **自动大写转换**: 所有金额字段自动转换为中文大写

### 数据验证

- **日期验证**: 申请日期和归还日期的合理性验证
- **金额验证**: 所有金额必须为正数，最多保留两位小数
- **品牌明细验证**: 至少包含一个品牌明细
- **归还日期验证**: 归还日期必须晚于申请日期

### 状态管理

- **草稿**: 初始状态，可以修改和删除
- **已提交**: 提交审批，不可修改
- **已审批**: 审批通过，不可修改和删除
- **已拒绝**: 审批拒绝，可以重新修改
- **已完成**: 流程完成
- **已取消**: 申请取消

## API接口

### 1. 创建佣金发货申请书

**POST** `/commission-contracts`

#### 请求参数

```json
{
  "companyName": "广州莱了贸易有限公司",
  "partyBName": "张三",
  "partyBIdCard": "110101199001011234",
  "applicationYear": 2025,
  "applicationMonth": 6,
  "applicationDay": 25,
  "brandDetails": [
    {
      "brandName": "PRLUO VALENTIN",
      "quantity": 25,
      "amount": 5000.00
    },
    {
      "brandName": "MASERDINO",
      "quantity": 30,
      "amount": 6000.00
    },
    {
      "brandName": "FENDIGEER",
      "quantity": 25,
      "amount": 4000.00
    }
  ],
  "actualShipmentAmount": 12000.00,
  "requestedDebtAmount": 3000.00,
  "repaymentYear": 2026,
  "repaymentMonth": 12,
  "repaymentDay": 25,
  "totalAccumulatedDebt": 8000.00
}
```

#### 响应示例

```json
{
  "code": 200,
  "data": null,
  "message": "佣金发货申请书创建成功"
}
```

### 2. 分页查询申请书列表

**GET** `/commission-contracts?page=1&pageSize=10`

#### 响应示例

```json
{
  "code": 200,
  "data": {
    "contracts": [
      {
        "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "contractNumber": "CC20250625143022",
        "companyName": "广州莱了贸易有限公司",
        "partyBName": "张三",
        "partyBIdCard": "1101011990****",
        "applicationDate": "2025年6月25日",
        "actualShipmentAmount": 12000.00,
        "requestedDebtAmount": 3000.00,
        "totalAccumulatedDebt": 8000.00,
        "repaymentDate": "2026年12月25日",
        "status": "draft",
        "createdAt": "2025-06-25T14:30:22.000Z",
        "updatedAt": "2025-06-25T14:30:22.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "获取佣金发货申请书列表成功"
}
```

### 3. 获取申请书详情

**GET** `/commission-contracts/{id}`

#### 响应示例

```json
{
  "code": 200,
  "data": {
    "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "contractNumber": "CC20250625143022",
    "companyName": "广州莱了贸易有限公司",
    "partyBName": "张三",
    "partyBIdCard": "110101199001011234",
    "applicationYear": 2025,
    "applicationMonth": 6,
    "applicationDay": 25,
    "actualShipmentAmount": 12000.00,
    "actualShipmentAmountInWords": "壹万贰仟元整",
    "requestedDebtAmount": 3000.00,
    "requestedDebtAmountInWords": "叁仟元整",
    "repaymentYear": 2026,
    "repaymentMonth": 12,
    "repaymentDay": 25,
    "totalAccumulatedDebt": 8000.00,
    "totalAccumulatedDebtInWords": "捌仟元整",
    "status": "draft",
    "brandDetails": [
      {
        "id": "b1c2d3e4-f5g6-7890-abcd-ef1234567890",
        "brandName": "PRLUO VALENTIN",
        "quantity": 25,
        "amount": 5000.00,
        "sortOrder": 1
      },
      {
        "id": "c1d2e3f4-g5h6-7890-abcd-ef1234567890",
        "brandName": "MASERDINO",
        "quantity": 30,
        "amount": 6000.00,
        "sortOrder": 2
      },
      {
        "id": "d1e2f3g4-h5i6-7890-abcd-ef1234567890",
        "brandName": "FENDIGEER",
        "quantity": 25,
        "amount": 4000.00,
        "sortOrder": 3
      }
    ],
    "createdAt": "2025-06-25T14:30:22.000Z",
    "updatedAt": "2025-06-25T14:30:22.000Z"
  },
  "message": "获取佣金发货申请书详情成功"
}
```

### 4. 删除申请书

**DELETE** `/commission-contracts/{id}`

#### 响应示例

```json
{
  "code": 200,
  "data": null,
  "message": "佣金发货申请书删除成功"
}
```

## 数据库表结构

### commission_contracts（佣金发货申请书主表）

- id: UUID主键
- contractNumber: 申请书编号（自动生成）
- companyName: 贸易公司名称
- partyBName: 乙方姓名
- partyBIdCard: 乙方身份证号
- applicationYear/Month/Day: 申请年月日
- actualShipmentAmount: 实际发货金额
- actualShipmentAmountInWords: 实际发货金额大写
- requestedDebtAmount: 本次申请欠款发货金额
- requestedDebtAmountInWords: 本次申请欠款发货金额大写
- repaymentYear/Month/Day: 归还年月日
- totalAccumulatedDebt: 累计欠款金额
- totalAccumulatedDebtInWords: 累计欠款金额大写
- status: 申请书状态

### commission_contract_details（佣金发货申请书明细表）

- id: UUID主键
- commissionContractId: 申请书ID
- brandName: 品牌名称
- quantity: 预定数量
- amount: 品牌货款金额
- sortOrder: 排序序号

## 特殊功能

### 自动大写转换

系统会自动将所有金额字段转换为中文大写：

- 12000.00 → "壹万贰仟元整"
- 3000.00 → "叁仟元整"
- 8000.50 → "捌仟元伍角"

### 编号自动生成

申请书编号格式：CC + 年月日时分秒
例如：CC20250625143022

## 注意事项

1. **所有字段均为必填项**，确保数据完整性
2. **金额自动转换大写**，前端只需传入数字
3. **日期分别存储**，便于查询和统计
4. **状态控制严格**，已审批的申请书不能修改或删除
5. **软删除机制**，删除的数据不会物理删除
6. **支持事务处理**，确保数据一致性
