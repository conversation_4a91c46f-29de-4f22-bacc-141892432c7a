/**
 * 数字转中文大写工具函数
 */

const chineseNumbers = [
  '零',
  '壹',
  '贰',
  '叁',
  '肆',
  '伍',
  '陆',
  '柒',
  '捌',
  '玖',
];
const chineseUnits = ['', '拾', '佰', '仟'];
const chineseBigUnits = ['', '万', '亿', '兆'];

/**
 * 将数字转换为中文大写
 * @param num 数字
 * @returns 中文大写字符串
 */
export function numberToChinese(num: number): string {
  if (num === 0) {
    return '零元整';
  }

  if (num < 0) {
    return '负' + numberToChinese(-num);
  }

  // 分离整数部分和小数部分
  const parts = num.toFixed(2).split('.');
  const integerPart = parseInt(parts[0]);
  const decimalPart = parseInt(parts[1]);

  let result = '';

  // 处理整数部分
  if (integerPart > 0) {
    result += convertIntegerToChinese(integerPart) + '元';
  } else {
    result += '零元';
  }

  // 处理小数部分（角和分）
  if (decimalPart > 0) {
    const jiao = Math.floor(decimalPart / 10);
    const fen = decimalPart % 10;

    if (jiao > 0) {
      result += chineseNumbers[jiao] + '角';
    }
    if (fen > 0) {
      result += chineseNumbers[fen] + '分';
    }
  } else {
    result += '整';
  }

  return result;
}

/**
 * 将整数转换为中文
 * @param num 整数
 * @returns 中文字符串
 */
function convertIntegerToChinese(num: number): string {
  if (num === 0) {
    return '';
  }

  let result = '';
  let unitIndex = 0;

  while (num > 0) {
    const section = num % 10000;
    if (section > 0) {
      const sectionStr = convertSectionToChinese(section);
      result = sectionStr + chineseBigUnits[unitIndex] + result;
    }
    num = Math.floor(num / 10000);
    unitIndex++;
  }

  return result;
}

/**
 * 将四位数字转换为中文
 * @param num 四位数字
 * @returns 中文字符串
 */
function convertSectionToChinese(num: number): string {
  if (num === 0) {
    return '';
  }

  let result = '';
  let needZero = false;

  for (let i = 3; i >= 0; i--) {
    const digit = Math.floor(num / Math.pow(10, i)) % 10;

    if (digit === 0) {
      // 只有在已经有非零数字且后面还有非零数字时才添加零
      if (needZero && result !== '' && hasMoreNonZeroDigits(num, i)) {
        result += '零';
        needZero = false;
      }
    } else {
      result += chineseNumbers[digit];
      if (i > 0) {
        result += chineseUnits[i];
      }
      needZero = true;
    }
  }

  return result;
}

/**
 * 检查指定位置后面是否还有非零数字
 * @param num 数字
 * @param currentPos 当前位置
 * @returns 是否有非零数字
 */
function hasMoreNonZeroDigits(num: number, currentPos: number): boolean {
  for (let i = currentPos - 1; i >= 0; i--) {
    const digit = Math.floor(num / Math.pow(10, i)) % 10;
    if (digit !== 0) {
      return true;
    }
  }
  return false;
}

/**
 * 简化版本：只处理常见的金额范围（0-999999.99）
 * @param num 数字
 * @returns 中文大写字符串
 */
export function simpleNumberToChinese(num: number): string {
  if (num === 0) {
    return '零元整';
  }

  if (num < 0 || num >= 1000000) {
    return numberToChinese(num); // 使用完整版本
  }

  const yuan = Math.floor(num);
  const jiao = Math.floor((num * 10) % 10);
  const fen = Math.floor((num * 100) % 10);

  let result = '';

  // 处理元
  if (yuan === 0) {
    result = '零元';
  } else {
    result = convertIntegerToChinese(yuan) + '元';
  }

  // 处理角分
  if (jiao === 0 && fen === 0) {
    result += '整';
  } else {
    if (jiao > 0) {
      result += chineseNumbers[jiao] + '角';
    } else if (fen > 0) {
      result += '零';
    }
    if (fen > 0) {
      result += chineseNumbers[fen] + '分';
    }
  }

  return result;
}
