import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommissionContractsService } from './commission-contracts.service';
import { CommissionContractsController } from './commission-contracts.controller';
import { CommissionContract } from './entities/commission-contract.entity';
import { CommissionContractDetail } from './entities/commission-contract-detail.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CommissionContract,
      CommissionContractDetail,
    ]),
  ],
  controllers: [CommissionContractsController],
  providers: [CommissionContractsService],
  exports: [CommissionContractsService],
})
export class CommissionContractsModule {}
