import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';

import {
  CommissionContract,
  CommissionContractStatus,
  PaymentStatus,
} from './entities/commission-contract.entity';
import { CommissionContractDetail } from './entities/commission-contract-detail.entity';
import { CreateCommissionContractDto } from './dto/create-commission-contract.dto';
import {
  QueryCommissionContractsDto,
  SortField,
  SortOrder,
} from './dto/query-commission-contracts.dto';
import {
  UpdateContractStatusDto,
  UpdatePaymentStatusDto,
  UpdateCommissionContractDto,
} from './dto/update-commission-contract.dto';
import { simpleNumberToChinese } from './utils/number-to-chinese.util';

@Injectable()
export class CommissionContractsService {
  private readonly logger = new Logger(CommissionContractsService.name);

  constructor(
    @InjectRepository(CommissionContract)
    private commissionContractRepository: Repository<CommissionContract>,
    @InjectRepository(CommissionContractDetail)
    private commissionContractDetailRepository: Repository<CommissionContractDetail>,
    private dataSource: DataSource,
  ) {}

  /**
   * 生成申请书编号
   */
  private generateContractNumber(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');

    return `CC${year}${month}${day}${hour}${minute}${second}`;
  }

  /**
   * 验证日期的合理性
   */
  private validateDate(year: number, month: number, day: number): boolean {
    const date = new Date(year, month - 1, day);
    return (
      date.getFullYear() === year &&
      date.getMonth() === month - 1 &&
      date.getDate() === day
    );
  }

  /**
   * 解析日期字符串为年月日
   */
  private parseDateString(dateString: string): {
    year: number;
    month: number;
    day: number;
  } {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new BadRequestException(`日期格式不正确: ${dateString}`);
    }
    return {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      day: date.getDate(),
    };
  }

  /**
   * 创建佣金发货申请书
   */
  async create(
    createDto: CreateCommissionContractDto,
  ): Promise<CommissionContract> {
    this.logger.log(
      `Creating commission contract for party: ${createDto.partyBName}`,
    );

    // 解析申请日期
    const applicationDateParts = this.parseDateString(
      createDto.applicationDate,
    );

    // 解析归还日期
    const repaymentDateParts = this.parseDateString(createDto.repaymentDate);

    // 验证申请日期
    if (
      !this.validateDate(
        applicationDateParts.year,
        applicationDateParts.month,
        applicationDateParts.day,
      )
    ) {
      throw new BadRequestException('申请日期不合法');
    }

    // 验证归还日期
    if (
      !this.validateDate(
        repaymentDateParts.year,
        repaymentDateParts.month,
        repaymentDateParts.day,
      )
    ) {
      throw new BadRequestException('归还日期不合法');
    }

    // 验证归还日期必须晚于申请日期
    const applicationDate = new Date(createDto.applicationDate);
    const repaymentDate = new Date(createDto.repaymentDate);

    if (repaymentDate <= applicationDate) {
      throw new BadRequestException('归还日期必须晚于申请日期');
    }

    // 验证品牌明细不能为空
    if (!createDto.brandDetails || createDto.brandDetails.length === 0) {
      throw new BadRequestException('品牌货款明细不能为空');
    }

    // 生成申请书编号
    const contractNumber = this.generateContractNumber();

    // 检查申请书编号是否已存在
    const existingContract = await this.commissionContractRepository.findOne({
      where: { contractNumber, isDeleted: false },
    });
    if (existingContract) {
      throw new ConflictException(`申请书编号 ${contractNumber} 已存在`);
    }

    // 使用事务创建申请书和明细
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 创建申请书主记录
      const contract = queryRunner.manager.create(CommissionContract, {
        contractNumber,
        companyName: createDto.companyName,
        partyBName: createDto.partyBName,
        partyBIdCard: createDto.partyBIdCard,
        applicationYear: applicationDateParts.year,
        applicationMonth: applicationDateParts.month,
        applicationDay: applicationDateParts.day,
        actualShipmentAmount: createDto.actualShipmentAmount,
        actualShipmentAmountInWords: simpleNumberToChinese(
          createDto.actualShipmentAmount,
        ),
        requestedDebtAmount: createDto.requestedDebtAmount,
        requestedDebtAmountInWords: simpleNumberToChinese(
          createDto.requestedDebtAmount,
        ),
        repaymentYear: repaymentDateParts.year,
        repaymentMonth: repaymentDateParts.month,
        repaymentDay: repaymentDateParts.day,
        totalAccumulatedDebt: createDto.totalAccumulatedDebt,
        totalAccumulatedDebtInWords: simpleNumberToChinese(
          createDto.totalAccumulatedDebt,
        ),
        status: CommissionContractStatus.DRAFT,
        paymentStatus: PaymentStatus.UNPAID,
        paidAmount: 0,
        remainingDebtAmount: createDto.requestedDebtAmount,
        isDeleted: false,
      });

      const savedContract = await queryRunner.manager.save(contract);

      // 创建明细记录
      for (let i = 0; i < createDto.brandDetails.length; i++) {
        const detailData = createDto.brandDetails[i];
        const detail = queryRunner.manager.create(CommissionContractDetail, {
          commissionContractId: savedContract.id,
          brandName: detailData.brandName,
          quantity: detailData.quantity,
          amount: detailData.amount,
          sortOrder: i + 1,
          isDeleted: false,
        });

        await queryRunner.manager.save(detail);
      }

      await queryRunner.commitTransaction();

      this.logger.log(
        `Commission contract created successfully: ${contractNumber}`,
      );
      return savedContract;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to create commission contract: ${error.message}`,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 计算逾期天数
   */
  private calculateOverdueDays(
    repaymentYear: number,
    repaymentMonth: number,
    repaymentDay: number,
  ): number {
    const repaymentDate = new Date(
      repaymentYear,
      repaymentMonth - 1,
      repaymentDay,
    );
    const today = new Date();
    const diffTime = today.getTime() - repaymentDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  /**
   * 自动计算还款状态
   */
  private calculatePaymentStatus(contract: CommissionContract): PaymentStatus {
    const overdueDays = this.calculateOverdueDays(
      contract.repaymentYear,
      contract.repaymentMonth,
      contract.repaymentDay,
    );

    // 安全地获取字段值，如果字段不存在则使用默认值
    const remainingDebtAmount =
      contract.remainingDebtAmount ?? contract.requestedDebtAmount ?? 0;
    const paidAmount = contract.paidAmount ?? 0;

    if (remainingDebtAmount <= 0) {
      return PaymentStatus.FULLY_PAID;
    } else if (overdueDays > 0) {
      return PaymentStatus.OVERDUE;
    } else if (paidAmount > 0) {
      return PaymentStatus.PARTIAL_PAID;
    } else {
      return PaymentStatus.UNPAID;
    }
  }

  /**
   * 获取还款状态文本
   */
  private getPaymentStatusText(status: PaymentStatus): string {
    switch (status) {
      case PaymentStatus.UNPAID:
        return '未还款';
      case PaymentStatus.PARTIAL_PAID:
        return '部分还款';
      case PaymentStatus.FULLY_PAID:
        return '已还款';
      case PaymentStatus.OVERDUE:
        return '已逾期';
      default:
        return '未知状态';
    }
  }

  /**
   * 分页查询佣金发货申请书
   */
  async findAll(queryDto: QueryCommissionContractsDto) {
    const {
      page,
      pageSize,
      contractNumber,
      companyName,
      partyBName,
      status,
      paymentStatus,
      applicationStartDate,
      applicationEndDate,
      repaymentStartDate,
      repaymentEndDate,
      search,
      sortField = SortField.CREATED_AT,
      sortOrder = SortOrder.DESC,
    } = queryDto;

    this.logger.log(
      `Querying commission contracts: page=${page}, pageSize=${pageSize}`,
    );

    // 构建查询
    const queryBuilder = this.commissionContractRepository
      .createQueryBuilder('contract')
      .where('contract.isDeleted = false');

    // 添加申请书编号筛选
    if (contractNumber) {
      queryBuilder.andWhere('contract.contractNumber ILIKE :contractNumber', {
        contractNumber: `%${contractNumber}%`,
      });
    }

    // 添加贸易公司名称筛选
    if (companyName) {
      queryBuilder.andWhere('contract.companyName ILIKE :companyName', {
        companyName: `%${companyName}%`,
      });
    }

    // 添加乙方姓名筛选
    if (partyBName) {
      queryBuilder.andWhere('contract.partyBName ILIKE :partyBName', {
        partyBName: `%${partyBName}%`,
      });
    }

    // 添加状态筛选
    if (status) {
      queryBuilder.andWhere('contract.status = :status', { status });
    }

    // 添加还款状态筛选
    if (paymentStatus) {
      queryBuilder.andWhere('contract.paymentStatus = :paymentStatus', {
        paymentStatus,
      });
    }

    // 添加申请日期范围筛选
    if (applicationStartDate) {
      const startDateParts = this.parseDateString(applicationStartDate);
      queryBuilder.andWhere(
        '(contract.applicationYear > :startYear OR ' +
          '(contract.applicationYear = :startYear AND contract.applicationMonth > :startMonth) OR ' +
          '(contract.applicationYear = :startYear AND contract.applicationMonth = :startMonth AND contract.applicationDay >= :startDay))',
        {
          startYear: startDateParts.year,
          startMonth: startDateParts.month,
          startDay: startDateParts.day,
        },
      );
    }

    if (applicationEndDate) {
      const endDateParts = this.parseDateString(applicationEndDate);
      queryBuilder.andWhere(
        '(contract.applicationYear < :endYear OR ' +
          '(contract.applicationYear = :endYear AND contract.applicationMonth < :endMonth) OR ' +
          '(contract.applicationYear = :endYear AND contract.applicationMonth = :endMonth AND contract.applicationDay <= :endDay))',
        {
          endYear: endDateParts.year,
          endMonth: endDateParts.month,
          endDay: endDateParts.day,
        },
      );
    }

    // 添加归还日期范围筛选
    if (repaymentStartDate) {
      const startDateParts = this.parseDateString(repaymentStartDate);
      queryBuilder.andWhere(
        '(contract.repaymentYear > :repaymentStartYear OR ' +
          '(contract.repaymentYear = :repaymentStartYear AND contract.repaymentMonth > :repaymentStartMonth) OR ' +
          '(contract.repaymentYear = :repaymentStartYear AND contract.repaymentMonth = :repaymentStartMonth AND contract.repaymentDay >= :repaymentStartDay))',
        {
          repaymentStartYear: startDateParts.year,
          repaymentStartMonth: startDateParts.month,
          repaymentStartDay: startDateParts.day,
        },
      );
    }

    if (repaymentEndDate) {
      const endDateParts = this.parseDateString(repaymentEndDate);
      queryBuilder.andWhere(
        '(contract.repaymentYear < :repaymentEndYear OR ' +
          '(contract.repaymentYear = :repaymentEndYear AND contract.repaymentMonth < :repaymentEndMonth) OR ' +
          '(contract.repaymentYear = :repaymentEndYear AND contract.repaymentMonth = :repaymentEndMonth AND contract.repaymentDay <= :repaymentEndDay))',
        {
          repaymentEndYear: endDateParts.year,
          repaymentEndMonth: endDateParts.month,
          repaymentEndDay: endDateParts.day,
        },
      );
    }

    // 添加关键词搜索
    if (search) {
      queryBuilder.andWhere(
        '(contract.contractNumber ILIKE :search OR contract.partyBName ILIKE :search OR contract.partyBIdCard ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 添加排序
    if (sortField === SortField.APPLICATION_DATE) {
      queryBuilder.orderBy('contract.applicationYear', sortOrder);
      queryBuilder.addOrderBy('contract.applicationMonth', sortOrder);
      queryBuilder.addOrderBy('contract.applicationDay', sortOrder);
    } else if (sortField === SortField.REPAYMENT_DATE) {
      queryBuilder.orderBy('contract.repaymentYear', sortOrder);
      queryBuilder.addOrderBy('contract.repaymentMonth', sortOrder);
      queryBuilder.addOrderBy('contract.repaymentDay', sortOrder);
    } else {
      queryBuilder.orderBy(`contract.${sortField}`, sortOrder);
    }

    // 分页查询
    const contracts = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getMany();

    return {
      contracts: contracts.map((contract) => {
        const overdueDays = this.calculateOverdueDays(
          contract.repaymentYear,
          contract.repaymentMonth,
          contract.repaymentDay,
        );

        // 自动计算还款状态
        const currentPaymentStatus = this.calculatePaymentStatus(contract);

        return {
          id: contract.id,
          contractNumber: contract.contractNumber,
          companyName: contract.companyName,
          partyBName: contract.partyBName,
          partyBIdCard: contract.partyBIdCard.substring(0, 10) + '****', // 脱敏处理
          applicationDate: `${contract.applicationYear}-${String(contract.applicationMonth).padStart(2, '0')}-${String(contract.applicationDay).padStart(2, '0')}`,
          applicationDateDisplay: `${contract.applicationYear}年${contract.applicationMonth}月${contract.applicationDay}日`,
          actualShipmentAmount: contract.actualShipmentAmount,
          requestedDebtAmount: contract.requestedDebtAmount,
          totalAccumulatedDebt: contract.totalAccumulatedDebt,
          paidAmount: contract.paidAmount ?? 0,
          remainingDebtAmount:
            contract.remainingDebtAmount ?? contract.requestedDebtAmount ?? 0,
          repaymentDate: `${contract.repaymentYear}-${String(contract.repaymentMonth).padStart(2, '0')}-${String(contract.repaymentDay).padStart(2, '0')}`,
          repaymentDateDisplay: `${contract.repaymentYear}年${contract.repaymentMonth}月${contract.repaymentDay}日`,
          overdueDays,
          overdueDaysText:
            overdueDays > 0
              ? `逾期${overdueDays}天`
              : overdueDays === 0
                ? '今日到期'
                : `还有${Math.abs(overdueDays)}天到期`,
          status: contract.status,
          paymentStatus: currentPaymentStatus,
          paymentStatusText: this.getPaymentStatusText(currentPaymentStatus),
          lastPaymentDate: contract.lastPaymentDate,
          createdAt: contract.createdAt,
          updatedAt: contract.updatedAt,
        };
      }),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 根据ID查询佣金发货申请书详情
   */
  async findOne(id: string): Promise<any> {
    this.logger.log(`Finding commission contract by id: ${id}`);

    const contract = await this.commissionContractRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['brandDetails'],
    });

    if (!contract) {
      throw new NotFoundException(`佣金发货申请书 ${id} 不存在`);
    }

    return {
      id: contract.id,
      contractNumber: contract.contractNumber,
      companyName: contract.companyName,
      partyBName: contract.partyBName,
      partyBIdCard: contract.partyBIdCard,
      applicationDate: `${contract.applicationYear}-${String(contract.applicationMonth).padStart(2, '0')}-${String(contract.applicationDay).padStart(2, '0')}`,
      applicationYear: contract.applicationYear,
      applicationMonth: contract.applicationMonth,
      applicationDay: contract.applicationDay,
      actualShipmentAmount: contract.actualShipmentAmount,
      actualShipmentAmountInWords: contract.actualShipmentAmountInWords,
      requestedDebtAmount: contract.requestedDebtAmount,
      requestedDebtAmountInWords: contract.requestedDebtAmountInWords,
      repaymentDate: `${contract.repaymentYear}-${String(contract.repaymentMonth).padStart(2, '0')}-${String(contract.repaymentDay).padStart(2, '0')}`,
      repaymentYear: contract.repaymentYear,
      repaymentMonth: contract.repaymentMonth,
      repaymentDay: contract.repaymentDay,
      totalAccumulatedDebt: contract.totalAccumulatedDebt,
      totalAccumulatedDebtInWords: contract.totalAccumulatedDebtInWords,
      status: contract.status,
      remarks: contract.remarks,
      createdByUserCode: contract.createdByUserCode,
      approvedByUserCode: contract.approvedByUserCode,
      approvedAt: contract.approvedAt,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
      brandDetails:
        contract.brandDetails
          ?.filter((detail) => !detail.isDeleted)
          .sort((a, b) => a.sortOrder - b.sortOrder)
          .map((detail) => ({
            id: detail.id,
            brandName: detail.brandName,
            quantity: detail.quantity,
            amount: detail.amount,
            sortOrder: detail.sortOrder,
          })) || [],
    };
  }

  /**
   * 更新佣金发货申请书（仅草稿状态）
   */
  async update(
    id: string,
    updateDto: UpdateCommissionContractDto,
  ): Promise<void> {
    this.logger.log(`Updating commission contract ${id}`);

    const contract = await this.commissionContractRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['brandDetails'],
    });

    if (!contract) {
      throw new NotFoundException(`佣金发货申请书 ${id} 不存在`);
    }

    // 只有草稿状态才能修改
    if (contract.status !== CommissionContractStatus.DRAFT) {
      throw new BadRequestException('只有草稿状态的申请书才能修改');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 更新主表数据
      const updateData: any = {};

      if (updateDto.companyName !== undefined) {
        updateData.companyName = updateDto.companyName;
      }
      if (updateDto.partyBName !== undefined) {
        updateData.partyBName = updateDto.partyBName;
      }
      if (updateDto.partyBIdCard !== undefined) {
        updateData.partyBIdCard = updateDto.partyBIdCard;
      }
      if (updateDto.applicationDate !== undefined) {
        const applicationDateParts = this.parseDateString(
          updateDto.applicationDate,
        );
        updateData.applicationYear = applicationDateParts.year;
        updateData.applicationMonth = applicationDateParts.month;
        updateData.applicationDay = applicationDateParts.day;
      }
      if (updateDto.actualShipmentAmount !== undefined) {
        updateData.actualShipmentAmount = updateDto.actualShipmentAmount;
        updateData.actualShipmentAmountInWords = simpleNumberToChinese(
          updateDto.actualShipmentAmount,
        );
      }
      if (updateDto.requestedDebtAmount !== undefined) {
        updateData.requestedDebtAmount = updateDto.requestedDebtAmount;
        updateData.requestedDebtAmountInWords = simpleNumberToChinese(
          updateDto.requestedDebtAmount,
        );
        updateData.remainingDebtAmount = updateDto.requestedDebtAmount;
      }
      if (updateDto.repaymentDate !== undefined) {
        const repaymentDateParts = this.parseDateString(
          updateDto.repaymentDate,
        );
        updateData.repaymentYear = repaymentDateParts.year;
        updateData.repaymentMonth = repaymentDateParts.month;
        updateData.repaymentDay = repaymentDateParts.day;
      }
      if (updateDto.totalAccumulatedDebt !== undefined) {
        updateData.totalAccumulatedDebt = updateDto.totalAccumulatedDebt;
        updateData.totalAccumulatedDebtInWords = simpleNumberToChinese(
          updateDto.totalAccumulatedDebt,
        );
      }
      if (updateDto.remarks !== undefined) {
        updateData.remarks = updateDto.remarks;
      }

      // 更新主表
      if (Object.keys(updateData).length > 0) {
        await queryRunner.manager.update(CommissionContract, id, updateData);
      }

      // 处理明细数据
      if (updateDto.brandDetails && updateDto.brandDetails.length > 0) {
        // 删除现有明细
        await queryRunner.manager.delete(CommissionContractDetail, {
          commissionContractId: id,
        });

        // 创建新明细
        const brandDetails = updateDto.brandDetails.map((detail, index) => {
          const brandDetail = new CommissionContractDetail();
          brandDetail.commissionContractId = id;
          brandDetail.brandName = detail.brandName;
          brandDetail.quantity = detail.quantity;
          brandDetail.amount = detail.amount;
          brandDetail.sortOrder = detail.sortOrder || index + 1;
          return brandDetail;
        });

        await queryRunner.manager.save(CommissionContractDetail, brandDetails);
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Commission contract updated successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to update commission contract: ${error.message}`,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 删除佣金发货申请书（软删除）
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`Deleting commission contract ${id}`);

    const contract = await this.commissionContractRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!contract) {
      throw new NotFoundException(`佣金发货申请书 ${id} 不存在`);
    }

    // 检查状态是否允许删除
    if (
      contract.status === CommissionContractStatus.APPROVED ||
      contract.status === CommissionContractStatus.COMPLETED
    ) {
      throw new BadRequestException('已审批或已完成的申请书不能删除');
    }

    // 使用事务删除申请书和明细
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 软删除明细记录
      await queryRunner.manager.update(
        CommissionContractDetail,
        { commissionContractId: id, isDeleted: false },
        { isDeleted: true, deletedAt: new Date() },
      );

      // 软删除主记录
      await queryRunner.manager.update(
        CommissionContract,
        { id },
        { isDeleted: true, deletedAt: new Date() },
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Commission contract deleted successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to delete commission contract: ${error.message}`,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 更新合同状态
   */
  async updateStatus(
    id: string,
    updateDto: UpdateContractStatusDto,
  ): Promise<void> {
    this.logger.log(
      `Updating commission contract status ${id} to ${updateDto.status}`,
    );

    const contract = await this.commissionContractRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!contract) {
      throw new NotFoundException(`佣金发货申请书 ${id} 不存在`);
    }

    // 状态转换验证
    if (
      contract.status === CommissionContractStatus.COMPLETED ||
      contract.status === CommissionContractStatus.CANCELLED
    ) {
      throw new BadRequestException('已完成或已取消的申请书不能修改状态');
    }

    // 更新状态
    const updateData: any = {
      status: updateDto.status,
      remarks: updateDto.remarks || contract.remarks,
    };

    // 如果是审批通过，记录审批信息
    if (updateDto.status === CommissionContractStatus.APPROVED) {
      updateData.approvedByUserCode = updateDto.approvedByUserCode;
      updateData.approvedAt = new Date();
    }

    await this.commissionContractRepository.update(id, updateData);
    this.logger.log(`Commission contract status updated successfully: ${id}`);
  }

  /**
   * 更新还款状态
   */
  async updatePaymentStatus(
    id: string,
    updateDto: UpdatePaymentStatusDto,
  ): Promise<void> {
    this.logger.log(`Updating commission contract payment status ${id}`);

    const contract = await this.commissionContractRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!contract) {
      throw new NotFoundException(`佣金发货申请书 ${id} 不存在`);
    }

    // 计算剩余欠款金额
    const remainingDebtAmount = Math.max(
      0,
      contract.requestedDebtAmount - updateDto.paidAmount,
    );

    // 更新还款信息
    const updateData: any = {
      paymentStatus: updateDto.paymentStatus,
      paidAmount: updateDto.paidAmount,
      remainingDebtAmount,
      lastPaymentDate: updateDto.lastPaymentDate
        ? new Date(updateDto.lastPaymentDate)
        : null,
      remarks: updateDto.remarks || contract.remarks,
    };

    await this.commissionContractRepository.update(id, updateData);
    this.logger.log(
      `Commission contract payment status updated successfully: ${id}`,
    );
  }

  /**
   * 生成PDF合同
   */
  async generatePdf(
    id: string,
  ): Promise<{ pdfBuffer: Buffer; filename: string }> {
    this.logger.log(`Generating PDF for commission contract ${id}`);

    const contract = await this.findOne(id);
    this.logger.log(
      `Contract data retrieved for PDF generation: ${contract.contractNumber}`,
    );

    const html = this.generateContractHtml(contract);
    this.logger.log(
      `HTML content generated, length: ${html.length} characters`,
    );

    let browser: any = null;
    try {
      // 使用 puppeteer 生成PDF
      const puppeteer = require('puppeteer');

      this.logger.log('Launching Puppeteer browser...');
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-extensions',
          '--disable-plugins',
          '--disable-default-apps',
          '--disable-hang-monitor',
          '--disable-prompt-on-repost',
          '--disable-sync',
          '--disable-translate',
          '--disable-ipc-flooding-protection',
          '--memory-pressure-off',
          '--max_old_space_size=4096',
        ],
        timeout: 60000,
      });

      this.logger.log('Creating new page...');
      const page = await browser.newPage();

      // 设置页面超时和视口
      await page.setDefaultTimeout(60000);
      await page.setViewport({ width: 1200, height: 800 });

      this.logger.log('Setting page content...');
      await page.setContent(html, {
        waitUntil: 'domcontentloaded', // 改为更宽松的等待条件
        timeout: 60000, // 增加超时时间
      });

      this.logger.log('Generating PDF...');
      const pdf = await page.pdf({
        format: 'A4',
        margin: {
          top: '20mm',
          right: '20mm',
          bottom: '20mm',
          left: '20mm',
        },
        printBackground: true,
        preferCSSPageSize: true,
      });

      this.logger.log('PDF generated successfully');

      // 生成文件名：甲方名字+时间
      const now = new Date();
      const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`;
      const timeStr = `${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`;
      const filename = encodeURIComponent(
        `${contract.partyBName}_佣金发货申请书_${dateStr}_${timeStr}.pdf`,
      );

      return { pdfBuffer: pdf, filename };
    } catch (error) {
      this.logger.error(`Failed to generate PDF: ${error.message}`);
      this.logger.error(`Error stack: ${error.stack}`);

      // 尝试使用简化的PDF生成方法
      this.logger.log('Attempting simplified PDF generation...');
      try {
        return await this.generateSimplifiedPdf(contract);
      } catch (fallbackError) {
        this.logger.error(
          `Simplified PDF generation also failed: ${fallbackError.message}`,
        );
        throw new Error(
          `PDF生成失败: ${error.message}. 备用方法也失败: ${fallbackError.message}`,
        );
      }
    } finally {
      // 确保浏览器被正确关闭
      if (browser) {
        try {
          await browser.close();
          this.logger.log('Browser closed successfully');
        } catch (closeError) {
          this.logger.error(`Failed to close browser: ${closeError.message}`);
        }
      }
    }
  }

  /**
   * 简化的PDF生成方法（备用方案）
   */
  private async generateSimplifiedPdf(
    contract: any,
  ): Promise<{ pdfBuffer: Buffer; filename: string }> {
    this.logger.log('Using simplified PDF generation method...');

    let browser: any = null;
    try {
      const puppeteer = require('puppeteer');

      // 使用最简单的配置启动浏览器
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        timeout: 30000,
      });

      const page = await browser.newPage();

      // 设置简单的HTML内容
      const simpleHtml = this.generateSimpleContractHtml(contract);

      await page.setContent(simpleHtml, {
        waitUntil: 'load',
        timeout: 30000,
      });

      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
      });

      const now = new Date();
      const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`;
      const timeStr = `${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`;
      const filename = encodeURIComponent(
        `${contract.partyBName}_佣金发货申请书_${dateStr}_${timeStr}.pdf`,
      );

      return { pdfBuffer: pdf, filename };
    } finally {
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          this.logger.error(
            `Failed to close simplified browser: ${closeError.message}`,
          );
        }
      }
    }
  }

  /**
   * 生成简化的合同HTML内容
   */
  private generateSimpleContractHtml(contract: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>佣金发货申请书</title>
    <style>
        body { font-family: Arial, sans-serif; font-size: 14px; margin: 20px; }
        .header { text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 20px; }
        .content { margin: 10px 0; }
        .section { margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #000; padding: 5px; text-align: left; }
    </style>
</head>
<body>
    <div class="header">佣金发货申请书</div>

    <div class="content">
        <div class="section">
            <strong>贸易公司：</strong>${contract.companyName}
        </div>

        <div class="section">
            <strong>乙方姓名：</strong>${contract.partyBName}
        </div>

        <div class="section">
            <strong>身份证号：</strong>${contract.partyBIdCard}
        </div>

        <div class="section">
            <strong>申请日期：</strong>${contract.applicationYear}年${contract.applicationMonth}月${contract.applicationDay}日
        </div>

        <div class="section">
            <strong>品牌明细：</strong>
            <table>
                <tr>
                    <th>品牌名称</th>
                    <th>数量</th>
                    <th>金额</th>
                </tr>
                ${contract.brandDetails
                  .map(
                    (detail: any) => `
                    <tr>
                        <td>${detail.brandName}</td>
                        <td>${detail.quantity}件</td>
                        <td>${detail.amount}元</td>
                    </tr>
                `,
                  )
                  .join('')}
            </table>
        </div>

        <div class="section">
            <strong>实际发货金额：</strong>${contract.actualShipmentAmount}元（${contract.actualShipmentAmountInWords}）
        </div>

        <div class="section">
            <strong>本次申请欠款发货金额：</strong>${contract.requestedDebtAmount}元（${contract.requestedDebtAmountInWords}）
        </div>

        <div class="section">
            <strong>归还日期：</strong>${contract.repaymentYear}年${contract.repaymentMonth}月${contract.repaymentDay}日
        </div>

        <div class="section">
            <strong>累计欠款金额：</strong>${contract.totalAccumulatedDebt}元（${contract.totalAccumulatedDebtInWords}）
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * 生成合同HTML内容
   */
  private generateContractHtml(contract: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>佣金发货申请书</title>
    <style>
        body { font-family: 'SimSun', serif; font-size: 14px; line-height: 1.6; margin: 40px; }
        .header { text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 30px; }
        .content { margin: 20px 0; }
        .company { margin-bottom: 20px; }
        .section { margin: 15px 0; }
        .signature { margin-top: 50px; text-align: right; }
        .highlight { font-weight: bold; display: inline-block; min-width: 100px; }
        .amount-underline { border-bottom: 1px solid #000; display: inline-block; min-width: 80px; }
        .brand-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .brand-table td { padding: 5px; border: 1px solid #000; }
        .date-compact { margin: 0 2px; }
    </style>
</head>
<body>
    <div class="header">佣金发货申请书</div>

    <div class="company">${contract.companyName}：</div>

    <div class="content">
        <div class="section">
            本人<span class="highlight">${contract.partyBName}</span>（身份证号：<span class="highlight">${contract.partyBIdCard}</span>）于
            <span class="highlight date-compact">${contract.applicationYear}</span>年<span class="highlight date-compact">${contract.applicationMonth}</span>月<span class="highlight date-compact">${contract.applicationDay}</span>日从贵司预定
        </div>

        <table class="brand-table">
            ${contract.brandDetails
              .map(
                (detail: any) => `
                <tr>
                    <td>${detail.brandName}</td>
                    <td>${detail.quantity}件</td>
                    <td>货款${detail.amount}元</td>
                </tr>
            `,
              )
              .join('')}
        </table>

        <div class="section">
            共计<span class="amount-underline">${contract.brandDetails.reduce((sum: number, detail: any) => sum + detail.amount, 0)}</span>元（大写：人民币
            <span class="highlight">${simpleNumberToChinese(contract.brandDetails.reduce((sum: number, detail: any) => sum + detail.amount, 0))}</span>），
            提货前本人应该支付本次提货款金额给贵司，但由于本人经营资金周转困难，特向贵司申请先行发货，本次实际发货金额
            <span class="amount-underline">${contract.actualShipmentAmount}</span>元（大写：人民币<span class="highlight">${simpleNumberToChinese(contract.actualShipmentAmount)}</span>），本次申请欠款发货金额
            <span class="amount-underline">${contract.requestedDebtAmount}</span>元（大写：人民币<span class="highlight">${simpleNumberToChinese(contract.requestedDebtAmount)}</span>）。
            并承诺此次欠款发货金额于<span class="highlight date-compact">${contract.repaymentYear}</span>年<span class="highlight date-compact">${contract.repaymentMonth}</span>月<span class="highlight date-compact">${contract.repaymentDay}</span>日前归还。
        </div>

        <div class="section">
            另，本人与贵司合作多年，截止申请之日前本人尚欠贵司服装累计货款共计人民币
            <span class="amount-underline">${contract.totalAccumulatedDebt}</span>元（大写：人民币<span class="highlight">${simpleNumberToChinese(contract.totalAccumulatedDebt)}</span>）。
        </div>
    </div>

    <div class="signature">
        <div>申请人（签字并手印）：</div>
        <div style="margin-top: 20px;">时间：<span class="highlight date-compact">${contract.applicationYear}</span>年<span class="highlight date-compact">${contract.applicationMonth}</span>月<span class="highlight date-compact">${contract.applicationDay}</span>日</div>
    </div>
</body>
</html>
    `;
  }
}
