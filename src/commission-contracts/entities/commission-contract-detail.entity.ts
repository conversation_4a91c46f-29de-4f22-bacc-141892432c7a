import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { CommissionContract } from './commission-contract.entity';

@Entity('commission_contract_details')
export class CommissionContractDetail {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '明细ID' })
  id: string;

  @Column({ type: 'uuid', comment: '佣金发货申请书ID' })
  commissionContractId: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '品牌名称',
  })
  @ApiProperty({ description: '品牌名称', example: 'PRLUO VALENTIN' })
  brandName: string;

  @Column({
    type: 'int',
    comment: '预定数量',
  })
  @ApiProperty({ description: '预定数量', example: 25 })
  quantity: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '品牌货款金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '品牌货款金额', example: 5000.00 })
  amount: number;

  @Column({
    type: 'int',
    default: 1,
    comment: '排序序号',
  })
  @ApiProperty({ description: '排序序号', example: 1 })
  sortOrder: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联佣金发货申请书主表
  @ManyToOne(() => CommissionContract, (contract) => contract.brandDetails)
  @JoinColumn({ name: 'commissionContractId' })
  commissionContract: CommissionContract;
}
