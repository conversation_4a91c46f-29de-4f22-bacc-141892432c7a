import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { CommissionContractDetail } from './commission-contract-detail.entity';

export enum CommissionContractStatus {
  DRAFT = 'draft', // 草稿
  SUBMITTED = 'submitted', // 已提交
  APPROVED = 'approved', // 已审批
  REJECTED = 'rejected', // 已拒绝
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
}

export enum PaymentStatus {
  UNPAID = 'unpaid', // 未还款
  PARTIAL_PAID = 'partial_paid', // 部分还款
  FULLY_PAID = 'fully_paid', // 已还款
  OVERDUE = 'overdue', // 已逾期
}

@Entity('commission_contracts')
@Index(['contractNumber'], { unique: true })
@Index(['partyBName', 'applicationYear', 'applicationMonth'])
@Index(['status', 'createdAt'])
export class CommissionContract {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '佣金发货申请书ID' })
  id: string;

  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: '申请书编号',
  })
  @ApiProperty({ description: '申请书编号', example: 'CC20250625001' })
  contractNumber: string;

  @Column({
    type: 'varchar',
    length: 200,
    comment: '贸易公司名称',
  })
  @ApiProperty({ description: '贸易公司名称', example: '广州莱了贸易有限公司' })
  companyName: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '乙方姓名',
  })
  @ApiProperty({ description: '乙方姓名', example: '张三' })
  partyBName: string;

  @Column({
    type: 'varchar',
    length: 18,
    comment: '乙方身份证号',
  })
  @ApiProperty({ description: '乙方身份证号', example: '110101199001011234' })
  partyBIdCard: string;

  @Column({
    type: 'int',
    comment: '申请年份',
  })
  @ApiProperty({ description: '申请年份', example: 2025 })
  applicationYear: number;

  @Column({
    type: 'int',
    comment: '申请月份',
  })
  @ApiProperty({ description: '申请月份', example: 6 })
  applicationMonth: number;

  @Column({
    type: 'int',
    comment: '申请日期',
  })
  @ApiProperty({ description: '申请日期', example: 25 })
  applicationDay: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '实际发货金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '实际发货金额', example: 12000.0 })
  actualShipmentAmount: number;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '实际发货金额大写（自动生成）',
  })
  @ApiProperty({ description: '实际发货金额大写', example: '壹万贰仟元整' })
  actualShipmentAmountInWords: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '本次申请欠款发货金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '本次申请欠款发货金额', example: 3000.0 })
  requestedDebtAmount: number;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '本次申请欠款发货金额大写（自动生成）',
  })
  @ApiProperty({ description: '本次申请欠款发货金额大写', example: '叁仟元整' })
  requestedDebtAmountInWords: string;

  @Column({
    type: 'int',
    comment: '归还年份',
  })
  @ApiProperty({ description: '归还年份', example: 2026 })
  repaymentYear: number;

  @Column({
    type: 'int',
    comment: '归还月份',
  })
  @ApiProperty({ description: '归还月份', example: 12 })
  repaymentMonth: number;

  @Column({
    type: 'int',
    comment: '归还日期',
  })
  @ApiProperty({ description: '归还日期', example: 25 })
  repaymentDay: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '累计欠款金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '累计欠款金额', example: 8000.0 })
  totalAccumulatedDebt: number;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '累计欠款金额大写（自动生成）',
  })
  @ApiProperty({ description: '累计欠款金额大写', example: '捌仟元整' })
  totalAccumulatedDebtInWords: string;

  @Column({
    type: 'enum',
    enum: CommissionContractStatus,
    default: CommissionContractStatus.DRAFT,
    comment: '申请书状态',
  })
  @ApiProperty({
    description: '申请书状态',
    enum: CommissionContractStatus,
    example: CommissionContractStatus.DRAFT,
  })
  status: CommissionContractStatus;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.UNPAID,
    comment: '还款状态',
  })
  @ApiProperty({
    description: '还款状态',
    enum: PaymentStatus,
    example: PaymentStatus.UNPAID,
  })
  paymentStatus: PaymentStatus;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '已还款金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '已还款金额', example: 1000.0 })
  paidAmount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '剩余欠款金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '剩余欠款金额', example: 2000.0 })
  remainingDebtAmount: number;

  @Column({
    type: 'date',
    nullable: true,
    comment: '最后还款日期',
  })
  @ApiProperty({ description: '最后还款日期', required: false })
  lastPaymentDate?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  @ApiProperty({ description: '备注', required: false })
  remarks?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '创建人员编码',
  })
  @ApiProperty({
    description: '创建人员编码',
    example: 'user001',
    required: false,
  })
  createdByUserCode?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '审批人员编码',
  })
  @ApiProperty({
    description: '审批人员编码',
    example: 'user002',
    required: false,
  })
  approvedByUserCode?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '审批时间',
  })
  @ApiProperty({ description: '审批时间', required: false })
  approvedAt?: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联佣金发货申请书明细
  @OneToMany(
    () => CommissionContractDetail,
    (detail) => detail.commissionContract,
    {
      cascade: true,
    },
  )
  @ApiProperty({
    description: '品牌货款明细列表',
    type: () => [CommissionContractDetail],
  })
  brandDetails: CommissionContractDetail[];
}
