import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Logger,
  UseGuards,
  <PERSON>,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CommissionContractsService } from './commission-contracts.service';
import { CreateCommissionContractDto } from './dto/create-commission-contract.dto';
import { QueryCommissionContractsDto } from './dto/query-commission-contracts.dto';
import {
  UpdateContractStatusDto,
  UpdatePaymentStatusDto,
  UpdateCommissionContractDto,
} from './dto/update-commission-contract.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('commission-contracts')
@Controller('commission-contracts')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CommissionContractsController {
  private readonly logger = new Logger(CommissionContractsController.name);

  constructor(
    private readonly commissionContractsService: CommissionContractsService,
  ) {}

  @Post()
  @ApiOperation({ summary: '新增佣金发货申请书' })
  @ApiResponse({
    status: 200,
    description: '佣金发货申请书创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 409,
    description: '申请书编号已存在',
  })
  async create(@Body() createDto: CreateCommissionContractDto) {
    this.logger.log(
      `Creating commission contract for: ${createDto.partyBName}`,
    );

    await this.commissionContractsService.create(createDto);

    return {
      code: 200,
      data: null,
      message: '佣金发货申请书创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '分页查询佣金发货申请书列表' })
  @ApiResponse({
    status: 200,
    description: '获取佣金发货申请书列表成功',
  })
  async findAll(@Query() queryDto: QueryCommissionContractsDto) {
    this.logger.log(
      `Querying commission contracts: page=${queryDto.page}, pageSize=${queryDto.pageSize}`,
    );

    const result = await this.commissionContractsService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取佣金发货申请书列表成功',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取佣金发货申请书详情' })
  @ApiParam({
    name: 'id',
    description: '佣金发货申请书ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '获取佣金发货申请书详情成功',
  })
  @ApiResponse({
    status: 404,
    description: '佣金发货申请书不存在',
  })
  async findOne(@Param('id') id: string) {
    this.logger.log(`Getting commission contract details for id: ${id}`);

    const contract = await this.commissionContractsService.findOne(id);

    return {
      code: 200,
      data: contract,
      message: '获取佣金发货申请书详情成功',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新佣金发货申请书（仅草稿状态）' })
  @ApiParam({
    name: 'id',
    description: '佣金发货申请书ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '佣金发货申请书更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '佣金发货申请书不存在',
  })
  @ApiResponse({
    status: 400,
    description: '只有草稿状态的申请书才能修改',
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateCommissionContractDto,
  ) {
    this.logger.log(`Updating commission contract ${id}`);

    await this.commissionContractsService.update(id, updateDto);

    return {
      code: 200,
      data: null,
      message: '佣金发货申请书更新成功',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除佣金发货申请书（软删除）' })
  @ApiParam({
    name: 'id',
    description: '佣金发货申请书ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '佣金发货申请书删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '佣金发货申请书不存在',
  })
  @ApiResponse({
    status: 400,
    description: '已审批或已完成的申请书不能删除',
  })
  async remove(@Param('id') id: string) {
    this.logger.log(`Deleting commission contract ${id}`);

    await this.commissionContractsService.remove(id);

    return {
      code: 200,
      data: null,
      message: '佣金发货申请书删除成功',
    };
  }

  @Patch(':id/status')
  @ApiOperation({ summary: '更新佣金发货申请书状态' })
  @ApiParam({
    name: 'id',
    description: '佣金发货申请书ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '佣金发货申请书状态更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '佣金发货申请书不存在',
  })
  @ApiResponse({
    status: 400,
    description: '状态更新失败',
  })
  async updateStatus(
    @Param('id') id: string,
    @Body() updateDto: UpdateContractStatusDto,
  ) {
    this.logger.log(
      `Updating commission contract status ${id} to ${updateDto.status}`,
    );

    await this.commissionContractsService.updateStatus(id, updateDto);

    return {
      code: 200,
      data: null,
      message: '佣金发货申请书状态更新成功',
    };
  }

  @Patch(':id/payment')
  @ApiOperation({ summary: '更新佣金发货申请书还款状态' })
  @ApiParam({
    name: 'id',
    description: '佣金发货申请书ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '佣金发货申请书还款状态更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '佣金发货申请书不存在',
  })
  async updatePaymentStatus(
    @Param('id') id: string,
    @Body() updateDto: UpdatePaymentStatusDto,
  ) {
    this.logger.log(`Updating commission contract payment status ${id}`);

    await this.commissionContractsService.updatePaymentStatus(id, updateDto);

    return {
      code: 200,
      data: null,
      message: '佣金发货申请书还款状态更新成功',
    };
  }

  @Get(':id/pdf')
  @ApiOperation({ summary: '生成佣金发货申请书PDF' })
  @ApiParam({
    name: 'id',
    description: '佣金发货申请书ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'PDF生成成功',
  })
  @ApiResponse({
    status: 404,
    description: '佣金发货申请书不存在',
  })
  async generatePdf(@Param('id') id: string, @Res() res: Response) {
    this.logger.log(`Generating PDF for commission contract ${id}`);

    try {
      const { pdfBuffer, filename } =
        await this.commissionContractsService.generatePdf(id);

      res.set({
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length,
      });

      res.send(pdfBuffer);
    } catch (error) {
      this.logger.error(`PDF generation failed: ${error.message}`);

      res.status(500).json({
        code: 500,
        message: `PDF生成失败: ${error.message}`,
        data: null,
      });
    }
  }
}
