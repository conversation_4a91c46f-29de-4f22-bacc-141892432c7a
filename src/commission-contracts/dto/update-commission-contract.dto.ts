import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsDateString,
  IsEnum,
  IsNumber,
  IsArray,
  ValidateNested,
  IsInt,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  CommissionContractStatus,
  PaymentStatus,
} from '../entities/commission-contract.entity';

export class UpdateContractStatusDto {
  @ApiProperty({
    description: '申请书状态',
    enum: CommissionContractStatus,
    example: CommissionContractStatus.APPROVED,
  })
  @IsEnum(CommissionContractStatus, {
    message: '申请书状态必须是有效的枚举值',
  })
  status: CommissionContractStatus;

  @ApiProperty({
    description: '审批人员编码（审批时必填）',
    example: 'user002',
    required: false,
  })
  @IsOptional()
  @IsString()
  approvedByUserCode?: string;

  @ApiProperty({
    description: '审批备注',
    required: false,
  })
  @IsOptional()
  @IsString()
  remarks?: string;
}

export class UpdatePaymentStatusDto {
  @ApiProperty({
    description: '还款状态',
    enum: PaymentStatus,
    example: PaymentStatus.PARTIAL_PAID,
  })
  @IsEnum(PaymentStatus, {
    message: '还款状态必须是有效的枚举值',
  })
  paymentStatus: PaymentStatus;

  @ApiProperty({
    description: '已还款金额',
    example: 1500.0,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '已还款金额必须是数字且最多保留两位小数' },
  )
  paidAmount: number;

  @ApiProperty({
    description: '最后还款日期',
    example: '2025-06-25',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '最后还款日期格式不正确' })
  lastPaymentDate?: string;

  @ApiProperty({
    description: '还款备注',
    required: false,
  })
  @IsOptional()
  @IsString()
  remarks?: string;
}

export class UpdateCommissionContractBrandDetailDto {
  @ApiProperty({
    description: '明细ID（更新时必填）',
    required: false,
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({
    description: '品牌名称',
    example: '耐克',
  })
  @IsString()
  brandName: string;

  @ApiProperty({
    description: '数量',
    example: 10,
  })
  @Type(() => Number)
  @IsInt({ message: '数量必须是整数' })
  @Min(1, { message: '数量必须大于0' })
  quantity: number;

  @ApiProperty({
    description: '金额',
    example: 1000.0,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '金额必须是数字且最多保留两位小数' },
  )
  amount: number;

  @ApiProperty({
    description: '排序',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  sortOrder?: number;
}

export class UpdateCommissionContractDto {
  @ApiProperty({
    description: '贸易公司名称',
    example: '广州莱了贸易有限公司',
    required: false,
  })
  @IsOptional()
  @IsString()
  companyName?: string;

  @ApiProperty({
    description: '乙方姓名',
    example: '张三',
    required: false,
  })
  @IsOptional()
  @IsString()
  partyBName?: string;

  @ApiProperty({
    description: '乙方身份证号',
    example: '110101199001011234',
    required: false,
  })
  @IsOptional()
  @IsString()
  partyBIdCard?: string;

  @ApiProperty({
    description: '申请日期（格式：YYYY-MM-DD）',
    example: '2025-06-25',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '申请日期必须是字符串' })
  applicationDate?: string;

  @ApiProperty({
    description: '实际发货金额',
    example: 8000.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '实际发货金额必须是数字且最多保留两位小数' },
  )
  actualShipmentAmount?: number;

  @ApiProperty({
    description: '申请欠款发货金额',
    example: 2000.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '申请欠款发货金额必须是数字且最多保留两位小数' },
  )
  requestedDebtAmount?: number;

  @ApiProperty({
    description: '归还日期（格式：YYYY-MM-DD）',
    example: '2026-12-25',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '归还日期必须是字符串' })
  repaymentDate?: string;

  @ApiProperty({
    description: '累计欠款金额',
    example: 8000.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '累计欠款金额必须是数字且最多保留两位小数' },
  )
  totalAccumulatedDebt?: number;

  @ApiProperty({
    description: '备注',
    required: false,
  })
  @IsOptional()
  @IsString()
  remarks?: string;

  @ApiProperty({
    description: '品牌明细列表',
    type: [UpdateCommissionContractBrandDetailDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateCommissionContractBrandDetailDto)
  brandDetails?: UpdateCommissionContractBrandDetailDto[];
}
