import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsDateString,
  IsEnum,
  IsInt,
  Min,
  IsNumber,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  CommissionContractStatus,
  PaymentStatus,
} from '../entities/commission-contract.entity';

export enum SortField {
  CREATED_AT = 'createdAt',
  APPLICATION_DATE = 'applicationDate',
  REPAYMENT_DATE = 'repaymentDate',
  ACTUAL_SHIPMENT_AMOUNT = 'actualShipmentAmount',
  REQUESTED_DEBT_AMOUNT = 'requestedDebtAmount',
  TOTAL_ACCUMULATED_DEBT = 'totalAccumulatedDebt',
  OVERDUE_DAYS = 'overdueDays',
  REMAINING_DEBT_AMOUNT = 'remainingDebtAmount',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class QueryCommissionContractsDto {
  @ApiProperty({
    description: '页码（必填）',
    example: 1,
    required: true,
  })
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码必须大于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填）',
    example: 10,
    required: true,
  })
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量必须大于0' })
  pageSize: number;

  @ApiProperty({
    description: '申请书编号（模糊搜索）',
    example: 'CC20250625',
    required: false,
  })
  @IsOptional()
  @IsString()
  contractNumber?: string;

  @ApiProperty({
    description: '贸易公司名称（模糊搜索）',
    example: '广州莱了贸易有限公司',
    required: false,
  })
  @IsOptional()
  @IsString()
  companyName?: string;

  @ApiProperty({
    description: '乙方姓名（模糊搜索）',
    example: '张三',
    required: false,
  })
  @IsOptional()
  @IsString()
  partyBName?: string;

  @ApiProperty({
    description: '申请书状态',
    enum: CommissionContractStatus,
    example: CommissionContractStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(CommissionContractStatus, {
    message: '申请书状态必须是有效的枚举值',
  })
  status?: CommissionContractStatus;

  @ApiProperty({
    description: '还款状态',
    enum: PaymentStatus,
    example: PaymentStatus.UNPAID,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentStatus, {
    message: '还款状态必须是有效的枚举值',
  })
  paymentStatus?: PaymentStatus;

  @ApiProperty({
    description: '申请开始日期（格式：YYYY-MM-DD）',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '申请开始日期格式不正确' })
  applicationStartDate?: string;

  @ApiProperty({
    description: '申请结束日期（格式：YYYY-MM-DD）',
    example: '2025-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '申请结束日期格式不正确' })
  applicationEndDate?: string;

  @ApiProperty({
    description: '归还开始日期（格式：YYYY-MM-DD）',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '归还开始日期格式不正确' })
  repaymentStartDate?: string;

  @ApiProperty({
    description: '归还结束日期（格式：YYYY-MM-DD）',
    example: '2026-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '归还结束日期格式不正确' })
  repaymentEndDate?: string;

  @ApiProperty({
    description: '搜索关键词（模糊搜索申请书编号、乙方姓名、身份证号）',
    example: '张三',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '最小实际发货金额',
    example: 1000.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '最小实际发货金额必须是数字' })
  minActualShipmentAmount?: number;

  @ApiProperty({
    description: '最大实际发货金额',
    example: 50000.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '最大实际发货金额必须是数字' })
  maxActualShipmentAmount?: number;

  @ApiProperty({
    description: '最小申请欠款发货金额',
    example: 500.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '最小申请欠款发货金额必须是数字' })
  minRequestedDebtAmount?: number;

  @ApiProperty({
    description: '最大申请欠款发货金额',
    example: 10000.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '最大申请欠款发货金额必须是数字' })
  maxRequestedDebtAmount?: number;

  @ApiProperty({
    description: '最小累计欠款金额',
    example: 1000.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '最小累计欠款金额必须是数字' })
  minTotalAccumulatedDebt?: number;

  @ApiProperty({
    description: '最大累计欠款金额',
    example: 20000.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '最大累计欠款金额必须是数字' })
  maxTotalAccumulatedDebt?: number;

  @ApiProperty({
    description: '是否只显示逾期记录',
    example: false,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  onlyOverdue?: boolean;

  @ApiProperty({
    description: '最小逾期天数（正数表示已逾期，负数表示未到期）',
    example: 0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '最小逾期天数必须是整数' })
  minOverdueDays?: number;

  @ApiProperty({
    description: '最大逾期天数',
    example: 30,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '最大逾期天数必须是整数' })
  maxOverdueDays?: number;

  @ApiProperty({
    description: '排序字段',
    enum: SortField,
    example: SortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortField, { message: '排序字段必须是有效的枚举值' })
  sortField?: SortField;

  @ApiProperty({
    description: '排序方向',
    enum: SortOrder,
    example: SortOrder.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, { message: '排序方向必须是有效的枚举值' })
  sortOrder?: SortOrder;
}
