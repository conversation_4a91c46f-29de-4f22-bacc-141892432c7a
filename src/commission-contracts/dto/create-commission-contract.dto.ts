import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsArray,
  ValidateNested,
  IsInt,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCommissionContractBrandDetailDto {
  @ApiProperty({
    description: '品牌名称（必填）',
    example: 'PRLUO VALENTIN',
  })
  @IsString()
  @IsNotEmpty({ message: '品牌名称不能为空' })
  brandName: string;

  @ApiProperty({
    description: '预定数量（必填）',
    example: 25,
  })
  @IsNotEmpty({ message: '预定数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '预定数量必须是整数' })
  @Min(1, { message: '预定数量必须大于0' })
  quantity: number;

  @ApiProperty({
    description: '品牌货款金额（必填）',
    example: 5000.0,
  })
  @IsNotEmpty({ message: '品牌货款金额不能为空' })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '品牌货款金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '品牌货款金额必须大于0' })
  amount: number;
}

export class CreateCommissionContractDto {
  @ApiProperty({
    description: '贸易公司名称（必填）',
    example: '广州莱了贸易有限公司',
  })
  @IsString()
  @IsNotEmpty({ message: '贸易公司名称不能为空' })
  companyName: string;

  @ApiProperty({
    description: '甲方姓名（必填）',
    example: '张三',
  })
  @IsString()
  @IsNotEmpty({ message: '甲方姓名不能为空' })
  partyBName: string;

  @ApiProperty({
    description: '甲方身份证号（必填）',
    example: '110101199001011234',
  })
  @IsString()
  @IsNotEmpty({ message: '甲方身份证号不能为空' })
  partyBIdCard: string;

  @ApiProperty({
    description: '申请日期（必填，格式：YYYY-MM-DD）',
    example: '2025-06-25',
  })
  @IsNotEmpty({ message: '申请日期不能为空' })
  @IsString({ message: '申请日期必须是字符串' })
  applicationDate: string;

  @ApiProperty({
    description: '品牌货款明细列表（必填）',
    type: [CreateCommissionContractBrandDetailDto],
  })
  @IsArray({ message: '品牌货款明细必须是数组' })
  @ValidateNested({ each: true })
  @Type(() => CreateCommissionContractBrandDetailDto)
  brandDetails: CreateCommissionContractBrandDetailDto[];

  @ApiProperty({
    description: '实际发货金额（必填）',
    example: 12000.0,
  })
  @IsNotEmpty({ message: '实际发货金额不能为空' })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '实际发货金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '实际发货金额必须大于0' })
  actualShipmentAmount: number;

  @ApiProperty({
    description: '本次申请欠款发货金额（必填）',
    example: 3000.0,
  })
  @IsNotEmpty({ message: '本次申请欠款发货金额不能为空' })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '本次申请欠款发货金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '本次申请欠款发货金额必须大于0' })
  requestedDebtAmount: number;

  @ApiProperty({
    description: '归还日期（必填，格式：YYYY-MM-DD）',
    example: '2026-12-25',
  })
  @IsNotEmpty({ message: '归还日期不能为空' })
  @IsString({ message: '归还日期必须是字符串' })
  repaymentDate: string;

  @ApiProperty({
    description: '累计欠款金额（必填）',
    example: 8000.0,
  })
  @IsNotEmpty({ message: '累计欠款金额不能为空' })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '累计欠款金额必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '累计欠款金额不能为负数' })
  totalAccumulatedDebt: number;
}
