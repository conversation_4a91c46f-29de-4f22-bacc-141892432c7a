import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import { CustomerPayment } from './entities/customer-payment.entity';
import {
  CustomerPaymentDetail,
  PaymentType,
} from './entities/customer-payment-detail.entity';
import { Customer } from '@/customers/entities/customer.entity';
import { User } from '@/users/entities/user.entity';
import { CreateCustomerPaymentDetailDto } from './dto/create-customer-payment-detail.dto';
import { UpdateCustomerPaymentDetailDto } from './dto/update-customer-payment-detail.dto';
import { QueryCustomerPaymentDetailsDto } from './dto/query-customer-payment-details.dto';

// 内部处理接口
interface ProcessedExportDto {
  startTime?: string;
  endTime?: string;
  detailIds?: string[];
  type?: PaymentType;
}

@Injectable()
export class CustomerPaymentsService {
  private readonly logger = new Logger(CustomerPaymentsService.name);

  constructor(
    @InjectRepository(CustomerPayment)
    private customerPaymentRepository: Repository<CustomerPayment>,
    @InjectRepository(CustomerPaymentDetail)
    private customerPaymentDetailRepository: Repository<CustomerPaymentDetail>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
  ) {}

  // 获取或创建客户支入记录
  private async getOrCreateCustomerPayment(): Promise<CustomerPayment> {
    let customerPayment = await this.customerPaymentRepository.findOne({
      where: { isDeleted: false },
    });

    if (!customerPayment) {
      customerPayment = this.customerPaymentRepository.create({
        totalAmount: 0,
        isDeleted: false,
      });
      customerPayment =
        await this.customerPaymentRepository.save(customerPayment);
      this.logger.log('Created new customer payment record');
    }

    return customerPayment;
  }

  // 创建客户支入明细
  async createDetail(createDetailDto: CreateCustomerPaymentDetailDto) {
    this.logger.log(
      `Creating customer payment detail with amount: ${createDetailDto.amount}`,
    );

    // 验证客户或用户是否存在
    if (createDetailDto.type === PaymentType.CUSTOMER_PAYMENT) {
      if (!createDetailDto.customerCode) {
        throw new BadRequestException('客户支付类型时客户编码不能为空');
      }
      const customer = await this.customerRepository.findOne({
        where: { code: createDetailDto.customerCode, isDeleted: false },
      });
      if (!customer) {
        throw new BadRequestException(
          `客户 ${createDetailDto.customerCode} 不存在`,
        );
      }
    } else if (createDetailDto.type === PaymentType.SERVICE_WITHDRAWAL) {
      if (!createDetailDto.userCode) {
        throw new BadRequestException('客服提现类型时用户编码不能为空');
      }
      const user = await this.userRepository.findOne({
        where: { code: createDetailDto.userCode, isDeleted: false },
      });
      if (!user) {
        throw new BadRequestException(
          `用户 ${createDetailDto.userCode} 不存在`,
        );
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建客户支入记录
      const customerPayment = await this.getOrCreateCustomerPayment();

      // 创建明细记录
      const detail = this.customerPaymentDetailRepository.create({
        customerPaymentId: customerPayment.id,
        type: createDetailDto.type,
        amount: createDetailDto.amount,
        screenshot: createDetailDto.screenshot,
        remark: createDetailDto.remark || null,
        customerCode: createDetailDto.customerCode || null,
        userCode: createDetailDto.userCode || null,
        isDeleted: false,
      });

      await queryRunner.manager.save(detail);

      // 更新总金额
      customerPayment.totalAmount += createDetailDto.amount;
      await queryRunner.manager.save(customerPayment);

      await queryRunner.commitTransaction();
      this.logger.log(
        `Customer payment detail created successfully with id: ${detail.id}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Failed to create customer payment detail', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询客户支入明细
  async findAllDetails(queryDto: QueryCustomerPaymentDetailsDto) {
    const {
      page = 1,
      pageSize = 10,
      startTime,
      endTime,
      search,
      type,
    } = queryDto;

    this.logger.log(
      `Querying customer payment details: page=${page}, pageSize=${pageSize}`,
    );

    // 获取客户支入总金额
    const customerPayment = await this.getOrCreateCustomerPayment();

    // 处理时间参数
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime) {
      // 如果是日期格式（如 2025-05-01），转换为当天开始时间
      if (startTime.length === 10) {
        processedStartTime = `${startTime}T00:00:00.000Z`;
      }
    }

    if (endTime) {
      // 如果是日期格式（如 2025-05-31），转换为当天结束时间
      if (endTime.length === 10) {
        processedEndTime = `${endTime}T23:59:59.999Z`;
      }
    }

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const queryBuilder = this.customerPaymentDetailRepository
        .createQueryBuilder('detail')
        .leftJoinAndSelect('detail.customer', 'customer')
        .leftJoinAndSelect('detail.user', 'user')
        .where('detail.isDeleted = false')
        .orderBy('detail.createdAt', 'DESC');

      // 添加时间范围过滤
      if (processedStartTime) {
        queryBuilder.andWhere('detail.createdAt >= :startTime', {
          startTime: processedStartTime,
        });
      }

      if (processedEndTime) {
        queryBuilder.andWhere('detail.createdAt <= :endTime', {
          endTime: processedEndTime,
        });
      }

      // 添加类型过滤
      if (type) {
        queryBuilder.andWhere('detail.type = :type', { type });
      }

      // 添加搜索过滤
      if (search) {
        queryBuilder.andWhere(
          '(customer.name ILIKE :search OR user.nickname ILIKE :search OR detail.remark ILIKE :search)',
          { search: `%${search}%` },
        );
      }

      const details = await queryBuilder.getMany();

      // 计算查询结果的总金额
      const queryResultTotalAmount = details.reduce(
        (sum, detail) => sum + detail.amount,
        0,
      );

      // 格式化返回数据
      const formattedDetails = details.map((detail) =>
        this.formatDetailResponse(detail),
      );

      return {
        details: formattedDetails,
        total: details.length,
        page: 0,
        pageSize: 0,
        totalAmount: queryResultTotalAmount,
      };
    }

    // 正常分页查询
    const queryBuilder = this.customerPaymentDetailRepository
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.customer', 'customer')
      .leftJoinAndSelect('detail.user', 'user')
      .where('detail.isDeleted = false')
      .orderBy('detail.createdAt', 'DESC');

    // 添加时间范围过滤
    if (processedStartTime) {
      queryBuilder.andWhere('detail.createdAt >= :startTime', {
        startTime: processedStartTime,
      });
    }

    if (processedEndTime) {
      queryBuilder.andWhere('detail.createdAt <= :endTime', {
        endTime: processedEndTime,
      });
    }

    // 添加类型过滤
    if (type) {
      queryBuilder.andWhere('detail.type = :type', { type });
    }

    // 添加搜索过滤
    if (search) {
      queryBuilder.andWhere(
        '(customer.name ILIKE :search OR user.nickname ILIKE :search OR detail.remark ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    const [details, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    // 计算查询结果的总金额
    const queryResultTotalAmount = details.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    // 格式化返回数据
    const formattedDetails = details.map((detail) =>
      this.formatDetailResponse(detail),
    );

    return {
      details: formattedDetails,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      totalAmount: queryResultTotalAmount,
    };
  }

  // 格式化明细响应数据
  private formatDetailResponse(detail: CustomerPaymentDetail) {
    const baseResponse = {
      id: detail.id,
      type: detail.type,
      amount: detail.amount,
      screenshot: detail.screenshot,
      remark: detail.remark,
      createdAt: detail.createdAt,
      updatedAt: detail.updatedAt,
    };

    if (detail.type === PaymentType.CUSTOMER_PAYMENT && detail.customer) {
      return {
        ...baseResponse,
        customerCode: detail.customerCode,
        customerName: detail.customer.name,
        customerPhone: detail.customer.phone,
        customerAddress: detail.customer.address,
      };
    } else if (detail.type === PaymentType.SERVICE_WITHDRAWAL && detail.user) {
      return {
        ...baseResponse,
        userCode: detail.userCode,
        userName: detail.user.nickname,
      };
    }

    return baseResponse;
  }

  // 根据ID查询明细
  async findDetailById(id: string): Promise<any> {
    this.logger.log(`Finding customer payment detail by id: ${id}`);

    const detail = await this.customerPaymentDetailRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['customer', 'user'],
    });

    if (!detail) {
      throw new NotFoundException(`客户支入明细 ${id} 不存在`);
    }

    return this.formatDetailResponse(detail);
  }

  // 更新明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateCustomerPaymentDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating customer payment detail ${id}`);

    const detail = await this.customerPaymentDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException(`客户支入明细 ${id} 不存在`);
    }

    // 验证客户或用户是否存在
    if (
      updateDetailDto.type === PaymentType.CUSTOMER_PAYMENT &&
      updateDetailDto.customerCode
    ) {
      const customer = await this.customerRepository.findOne({
        where: { code: updateDetailDto.customerCode, isDeleted: false },
      });
      if (!customer) {
        throw new BadRequestException(
          `客户 ${updateDetailDto.customerCode} 不存在`,
        );
      }
    } else if (
      updateDetailDto.type === PaymentType.SERVICE_WITHDRAWAL &&
      updateDetailDto.userCode
    ) {
      const user = await this.userRepository.findOne({
        where: { code: updateDetailDto.userCode, isDeleted: false },
      });
      if (!user) {
        throw new BadRequestException(
          `用户 ${updateDetailDto.userCode} 不存在`,
        );
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const customerPayment = await this.getOrCreateCustomerPayment();
      const oldAmount = detail.amount;

      // 更新明细
      Object.assign(detail, updateDetailDto);
      await queryRunner.manager.save(detail);

      // 更新总金额
      if (updateDetailDto.amount !== undefined) {
        customerPayment.totalAmount =
          customerPayment.totalAmount - oldAmount + updateDetailDto.amount;
        await queryRunner.manager.save(customerPayment);
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Customer payment detail ${id} updated successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to update customer payment detail ${id}`,
        error,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除明细
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing customer payment detail ${id}`);

    const detail = await this.customerPaymentDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException(`客户支入明细 ${id} 不存在`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const customerPayment = await this.getOrCreateCustomerPayment();

      // 软删除明细
      detail.isDeleted = true;
      detail.deletedAt = new Date();
      await queryRunner.manager.save(detail);

      // 更新总金额
      customerPayment.totalAmount -= detail.amount;
      await queryRunner.manager.save(customerPayment);

      await queryRunner.commitTransaction();
      this.logger.log(`Customer payment detail ${id} removed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to remove customer payment detail ${id}`,
        error,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出Excel
  async exportToExcel(exportDto?: ProcessedExportDto): Promise<Buffer> {
    this.logger.log('Exporting customer payment details to Excel');

    const customerPayment = await this.getOrCreateCustomerPayment();
    let queryBuilder = this.customerPaymentDetailRepository
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.customer', 'customer')
      .leftJoinAndSelect('detail.user', 'user')
      .where('detail.isDeleted = false')
      .orderBy('detail.createdAt', 'DESC');

    // 时间范围筛选
    if (exportDto?.startTime) {
      let startTime = exportDto.startTime;
      if (startTime.length === 10) {
        startTime = `${startTime}T00:00:00.000Z`;
      }
      queryBuilder.andWhere('detail.createdAt >= :startTime', { startTime });
    }

    if (exportDto?.endTime) {
      let endTime = exportDto.endTime;
      if (endTime.length === 10) {
        endTime = `${endTime}T23:59:59.999Z`;
      }
      queryBuilder.andWhere('detail.createdAt <= :endTime', { endTime });
    }

    // 类型筛选
    if (exportDto?.type) {
      queryBuilder.andWhere('detail.type = :type', { type: exportDto.type });
    }

    // 选择性导出
    if (exportDto?.detailIds && exportDto.detailIds.length > 0) {
      queryBuilder.andWhere('detail.id IN (:...detailIds)', {
        detailIds: exportDto.detailIds,
      });
    }

    const details = await queryBuilder.getMany();

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('客户支入明细');

    // 设置列标题
    const headers = [
      '序号',
      '支入类型',
      '金额',
      '客户编码',
      '客户名称',
      '客户手机号',
      '客户地址',
      '用户编码',
      '用户名称',
      '备注',
      '截图',
      '创建时间',
    ];

    worksheet.addRow(headers);

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 添加数据行
    for (let i = 0; i < details.length; i++) {
      const detail = details[i];
      const row = [
        i + 1,
        detail.type === PaymentType.CUSTOMER_PAYMENT ? '客户支付' : '客服提现',
        detail.amount,
        detail.type === PaymentType.CUSTOMER_PAYMENT ? detail.customerCode : '',
        detail.type === PaymentType.CUSTOMER_PAYMENT && detail.customer
          ? detail.customer.name
          : '',
        detail.type === PaymentType.CUSTOMER_PAYMENT && detail.customer
          ? detail.customer.phone || ''
          : '',
        detail.type === PaymentType.CUSTOMER_PAYMENT && detail.customer
          ? detail.customer.address || ''
          : '',
        detail.type === PaymentType.SERVICE_WITHDRAWAL ? detail.userCode : '',
        detail.type === PaymentType.SERVICE_WITHDRAWAL && detail.user
          ? detail.user.nickname
          : '',
        detail.remark || '',
        detail.screenshot,
        detail.createdAt.toLocaleString('zh-CN'),
      ];
      worksheet.addRow(row);
    }

    // 自动调整列宽
    worksheet.columns.forEach((column) => {
      column.width = 15;
    });

    // 添加总计行
    const totalAmount = details.reduce((sum, detail) => sum + detail.amount, 0);
    worksheet.addRow([
      '',
      '总计',
      totalAmount,
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
    ]);

    // 生成Excel缓冲区
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  // 导入Excel
  async importFromExcel(buffer: Buffer): Promise<any> {
    this.logger.log('Importing customer payment details from Excel');

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);
    const worksheet = workbook.getWorksheet(1);

    if (!worksheet) {
      throw new BadRequestException('Excel文件格式错误');
    }

    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[],
    };

    // 跳过标题行，从第二行开始处理
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // 跳过空行和总计行
      if (!row.getCell(2).value || row.getCell(2).value === '总计') {
        continue;
      }

      try {
        const type =
          row.getCell(2).value === '客户支付'
            ? PaymentType.CUSTOMER_PAYMENT
            : PaymentType.SERVICE_WITHDRAWAL;
        const amount = Number(row.getCell(3).value);
        const customerCode = row.getCell(4).value?.toString() || null;
        const userCode = row.getCell(8).value?.toString() || null;
        const remark = row.getCell(10).value?.toString() || null;
        const screenshot = row.getCell(11).value?.toString() || '';

        if (!amount || amount <= 0) {
          results.errors.push(`第${rowNumber}行：金额无效`);
          results.failed++;
          continue;
        }

        if (!screenshot) {
          results.errors.push(`第${rowNumber}行：截图URL不能为空`);
          results.failed++;
          continue;
        }

        const createDto: CreateCustomerPaymentDetailDto = {
          type,
          amount,
          screenshot,
          remark: remark || undefined,
          customerCode: customerCode || undefined,
          userCode: userCode || undefined,
        };

        await this.createDetail(createDto);
        results.success++;
      } catch (error) {
        results.errors.push(`第${rowNumber}行：${error.message}`);
        results.failed++;
      }
    }

    return results;
  }

  // 生成导入模板Excel
  async generateImportTemplate(): Promise<Buffer> {
    this.logger.log('Generating customer payment import template');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('客户支入明细导入模板');

    // 设置列标题
    const headers = [
      '序号',
      '支入类型（客户支付/客服提现）',
      '金额',
      '客户编码（客户支付时必填）',
      '客户名称（仅供参考）',
      '客户手机号（仅供参考）',
      '客户地址（仅供参考）',
      '用户编码（客服提现时必填）',
      '用户名称（仅供参考）',
      '备注（可选）',
      '截图URL（必填）',
      '创建时间（仅供参考）',
    ];

    worksheet.addRow(headers);

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 添加示例数据行
    const exampleRows = [
      [
        1,
        '客户支付',
        1000.5,
        'CUS001',
        '张三',
        '13800138000',
        '北京市朝阳区xxx街道xxx号',
        '',
        '',
        '客户支付货款',
        'https://example.com/screenshot1.jpg',
        '2024-01-01 10:00:00',
      ],
      [
        2,
        '客服提现',
        500.0,
        '',
        '',
        '',
        '',
        'user001',
        '李四',
        '客服提现',
        'https://example.com/screenshot2.jpg',
        '2024-01-01 11:00:00',
      ],
    ];

    exampleRows.forEach((row) => {
      worksheet.addRow(row);
    });

    // 自动调整列宽
    worksheet.columns.forEach((column) => {
      column.width = 15;
    });

    // 添加说明
    worksheet.addRow([]);
    worksheet.addRow(['说明：']);
    worksheet.addRow(['1. 支入类型只能填写"客户支付"或"客服提现"']);
    worksheet.addRow(['2. 客户支付类型时，客户编码必填']);
    worksheet.addRow(['3. 客服提现类型时，用户编码必填']);
    worksheet.addRow(['4. 金额必须大于0，最多保留两位小数']);
    worksheet.addRow(['5. 截图URL必填']);
    worksheet.addRow(['6. 其他标注"仅供参考"的列在导入时会被忽略']);

    // 生成Excel缓冲区
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }
}
