import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  Res,
  Req,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiConsumes,
} from '@nestjs/swagger';
import { CustomerPaymentsService } from './customer-payments.service';
import { CreateCustomerPaymentDetailDto } from './dto/create-customer-payment-detail.dto';
import { UpdateCustomerPaymentDetailDto } from './dto/update-customer-payment-detail.dto';
import { QueryCustomerPaymentDetailsDto } from './dto/query-customer-payment-details.dto';

@ApiTags('customer-payments')
@Controller('customer-payments')
export class CustomerPaymentsController {
  private readonly logger = new Logger(CustomerPaymentsController.name);

  constructor(
    private readonly customerPaymentsService: CustomerPaymentsService,
  ) {}

  @Post('details')
  @ApiOperation({ summary: '新增客户支入明细' })
  @ApiResponse({
    status: 200,
    description: '客户支入明细创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async createDetail(@Body() createDetailDto: CreateCustomerPaymentDetailDto) {
    this.logger.log(
      `Creating customer payment detail with amount: ${createDetailDto.amount}`,
    );

    await this.customerPaymentsService.createDetail(createDetailDto);

    return {
      code: 200,
      data: null,
      message: '客户支入明细创建成功',
    };
  }

  @Get('details')
  @ApiOperation({ summary: '分页查询客户支入明细列表' })
  @ApiQuery({
    name: 'page',
    description: '页码（必填）',
    example: 1,
    required: false,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量（必填）',
    example: 10,
    required: false,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词（模糊搜索客户名称、用户昵称、备注）',
    example: '张三',
    required: false,
  })
  @ApiQuery({
    name: 'type',
    description: '支入类型筛选',
    example: 'customer_payment',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllDetails(@Query() queryDto: QueryCustomerPaymentDetailsDto) {
    this.logger.log(
      `Querying customer payment details with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.customerPaymentsService.findAllDetails(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('details/:id')
  @ApiOperation({ summary: '获取客户支入明细详情' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async findDetailById(@Param('id') id: string) {
    this.logger.log(`Finding customer payment detail by id: ${id}`);

    const detail = await this.customerPaymentsService.findDetailById(id);

    return {
      code: 200,
      data: detail,
      message: '查询成功',
    };
  }

  @Patch('details/:id')
  @ApiOperation({ summary: '更新客户支入明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async updateDetail(
    @Param('id') id: string,
    @Body() updateDetailDto: UpdateCustomerPaymentDetailDto,
  ) {
    this.logger.log(`Updating customer payment detail ${id}`);

    await this.customerPaymentsService.updateDetail(id, updateDetailDto);

    return {
      code: 200,
      data: null,
      message: '客户支入明细更新成功',
    };
  }

  @Delete('details/:id')
  @ApiOperation({ summary: '删除客户支入明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async removeDetail(@Param('id') id: string) {
    this.logger.log(`Removing customer payment detail ${id}`);

    await this.customerPaymentsService.removeDetail(id);

    return {
      code: 200,
      data: null,
      message: '客户支入明细删除成功',
    };
  }

  @Get('export')
  @ApiOperation({ summary: '导出客户支入明细Excel' })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期',
    example: '2023-01-01',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期',
    example: '2023-12-31',
    required: false,
  })
  @ApiQuery({
    name: 'detailIds',
    description: '明细ID列表（逗号分隔）',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiQuery({
    name: 'type',
    description: '支入类型筛选',
    example: 'customer_payment',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  async exportToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Exporting customer payment details to Excel`);

    try {
      // 从原始请求中获取查询参数，绕过验证
      const { startTime, endTime, detailIds, type } = req.query;

      // 构建导出参数
      const exportParams: any = {};

      if (startTime) {
        exportParams.startTime = startTime;
      }

      if (endTime) {
        exportParams.endTime = endTime;
      }

      if (detailIds) {
        // 处理detailIds参数（逗号分隔的字符串转换为数组）
        // 先解码URL编码的字符串，然后分割
        const decodedDetailIds = decodeURIComponent(detailIds);
        exportParams.detailIds = decodedDetailIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);
      }

      if (type) {
        exportParams.type = type;
      }

      const excelBuffer =
        await this.customerPaymentsService.exportToExcel(exportParams);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="customer-payments-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }

  @Post('import')
  @ApiOperation({ summary: '导入客户支入明细Excel' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: '导入成功',
  })
  @ApiResponse({
    status: 400,
    description: '文件格式错误或数据验证失败',
  })
  @UseInterceptors(FileInterceptor('file'))
  async importFromExcel(@UploadedFile() file: Express.Multer.File) {
    this.logger.log('Importing customer payment details from Excel');

    if (!file) {
      return {
        code: 400,
        data: null,
        message: '请上传Excel文件',
      };
    }

    try {
      const result = await this.customerPaymentsService.importFromExcel(
        file.buffer,
      );

      return {
        code: 200,
        data: result,
        message: '导入完成',
      };
    } catch (error) {
      this.logger.error('Excel import failed', error);
      return {
        code: 400,
        data: null,
        message: `导入失败：${error.message}`,
      };
    }
  }

  @Get('import-template')
  @ApiOperation({ summary: '下载客户支入明细导入模板' })
  @ApiResponse({
    status: 200,
    description: '模板文件下载成功',
  })
  async downloadImportTemplate(@Res() res: Response) {
    this.logger.log('Downloading customer payment import template');

    try {
      const excelBuffer =
        await this.customerPaymentsService.generateImportTemplate();

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="customer-payment-import-template.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Template download failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: '模板下载失败',
      });
    }
  }
}
