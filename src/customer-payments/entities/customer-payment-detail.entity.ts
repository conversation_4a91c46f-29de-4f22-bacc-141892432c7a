import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CustomerPayment } from './customer-payment.entity';
import { Customer } from '@/customers/entities/customer.entity';
import { User } from '@/users/entities/user.entity';

// 支入类型枚举
export enum PaymentType {
  CUSTOMER_PAYMENT = 'customer_payment', // 客户支付
  SERVICE_WITHDRAWAL = 'service_withdrawal', // 客服提现
}

@Entity('customer_payment_details')
export class CustomerPaymentDetail {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', comment: '客户支入ID' })
  customerPaymentId: string;

  @Column({
    type: 'enum',
    enum: PaymentType,
    comment: '支入类型：客户支付或客服提现',
  })
  type: PaymentType;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: '明细金额（保留两位小数）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 500,
    comment: '截图URL（必填）',
  })
  screenshot: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注（可选）',
  })
  remark: string | null;

  // 客户编码（客户支付类型时必填）
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '客户编码（客户支付类型时必填）',
  })
  customerCode: string | null;

  // 用户编码（客服提现类型时必填）
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '用户编码（客服提现类型时必填）',
  })
  userCode: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联客户支入主表
  @ManyToOne(() => CustomerPayment, (payment) => payment.details)
  @JoinColumn({ name: 'customerPaymentId' })
  customerPayment: CustomerPayment;

  // 关联客户表（可选）
  @ManyToOne(() => Customer, { nullable: true })
  @JoinColumn({ name: 'customerCode', referencedColumnName: 'code' })
  customer: Customer | null;

  // 关联用户表（可选）
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'userCode', referencedColumnName: 'code' })
  user: User | null;
}
