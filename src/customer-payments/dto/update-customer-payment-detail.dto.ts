import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min, IsEnum, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentType } from '../entities/customer-payment-detail.entity';

export class UpdateCustomerPaymentDetailDto {
  @ApiProperty({
    description: '支入类型',
    enum: PaymentType,
    example: PaymentType.CUSTOMER_PAYMENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentType, { message: '支入类型必须是 customer_payment 或 service_withdrawal' })
  type?: PaymentType;

  @ApiProperty({
    description: '明细金额（保留两位小数）',
    example: 15000.50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '金额必须是数字且最多保留两位小数' })
  @Min(0.01, { message: '金额必须大于0' })
  amount?: number;

  @ApiProperty({
    description: '截图URL',
    example: 'https://example.com/screenshot.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  screenshot?: string;

  @ApiProperty({
    description: '备注',
    example: '客户支付货款',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;

  @ApiProperty({
    description: '客户编码（客户支付类型时必填）',
    example: 'CUS001',
    required: false,
  })
  @ValidateIf((o) => o.type === PaymentType.CUSTOMER_PAYMENT)
  @IsString()
  customerCode?: string;

  @ApiProperty({
    description: '用户编码（客服提现类型时必填）',
    example: 'user001',
    required: false,
  })
  @ValidateIf((o) => o.type === PaymentType.SERVICE_WITHDRAWAL)
  @IsString()
  userCode?: string;
}
