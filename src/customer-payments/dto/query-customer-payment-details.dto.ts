import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentType } from '../entities/customer-payment-detail.entity';

export class QueryCustomerPaymentDetailsDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  pageSize?: number = 10;

  @ApiProperty({
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiProperty({
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiProperty({
    description: '搜索关键词（模糊搜索客户名称、用户昵称、备注）',
    example: '张三',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '支入类型筛选',
    enum: PaymentType,
    example: PaymentType.CUSTOMER_PAYMENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentType, { message: '支入类型必须是 customer_payment 或 service_withdrawal' })
  type?: PaymentType;
}
