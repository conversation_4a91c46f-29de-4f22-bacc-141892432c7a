import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { InitModule } from './init/init.module';
import { CompaniesModule } from './companies/companies.module';
import { FileUploadModule } from './file-upload/file-upload.module';
import { SuppliersModule } from './suppliers/suppliers.module';
import { CustomersModule } from './customers/customers.module';
import { BrandsModule } from './brands/brands.module';
import { FixedAssetsModule } from './fixed-assets/fixed-assets.module';
import { OperatingAssetsModule } from './operating-assets/operating-assets.module';
import { RentalAssetsModule } from './rental-assets/rental-assets.module';
import { RdCostsModule } from './rd-costs/rd-costs.module';
import { ProductCategoriesModule } from './product-categories/product-categories.module';
import { ColorsModule } from './colors/colors.module';
import { AccessoriesModule } from './accessories/accessories.module';
import { SkusModule } from './skus/skus.module';
import { SkusInventoryModule } from './skus-inventory/skus-inventory.module';
import { IncomesModule } from './incomes/incomes.module';
import { ExpensesModule } from './expenses/expenses.module';
import { CustomerPaymentsModule } from './customer-payments/customer-payments.module';
import { CustomerProfilesModule } from './customer-profiles/customer-profiles.module';
import { PurchaseContractsModule } from './purchase-contracts/purchase-contracts.module';
import { SupplierArchivesModule } from './supplier-archives/supplier-archives.module';
import { MemosModule } from './memos/memos.module';
import { CommissionContractsModule } from './commission-contracts/commission-contracts.module';
import { SalesOrdersModule } from './sales-orders/sales-orders.module';
import { ShoppingCartModule } from './shopping-cart/shopping-cart.module';

import configuration from './config/configuration';
import tencentCosConfig from './config/tencent-cos.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration, tencentCosConfig],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('database.host'),
        port: configService.get<number>('database.port'),
        username: configService.get<string>('database.username'),
        password: configService.get<string>('database.password'),
        database: configService.get<string>('database.database'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize:
          process.env.DB_SYNCHRONIZE === 'true' ||
          process.env.NODE_ENV !== 'production',
        logging:
          process.env.DB_LOGGING === 'true' ||
          process.env.NODE_ENV !== 'production',
      }),
    }),
    CacheModule.register({
      isGlobal: true,
      ttl: 60 * 60 * 1000, // 1小时缓存
    }),
    AuthModule,
    UsersModule,
    InitModule,
    CompaniesModule,
    FileUploadModule,
    SuppliersModule,
    CustomersModule,
    BrandsModule,
    FixedAssetsModule,
    OperatingAssetsModule,
    RentalAssetsModule,
    RdCostsModule,
    ProductCategoriesModule,
    ColorsModule,
    AccessoriesModule,
    SkusModule,
    SkusInventoryModule,
    IncomesModule,
    ExpensesModule,
    CustomerPaymentsModule,
    CustomerProfilesModule,
    PurchaseContractsModule,
    SupplierArchivesModule,
    MemosModule,
    CommissionContractsModule,
    SalesOrdersModule,
    ShoppingCartModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
