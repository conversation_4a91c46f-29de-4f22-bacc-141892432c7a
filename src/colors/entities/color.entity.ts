import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('colors')
export class Color {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '颜色ID（UUID）', example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' })
  id: string;

  @Column({ type: 'varchar', length: 50, unique: true })
  @ApiProperty({ description: '颜色编码（唯一）', example: 'COLOR001' })
  code: string;

  @Column({ type: 'varchar', length: 6 })
  @ApiProperty({ description: '颜色名称（最多6个中文字符，必须以"色"结尾）', example: '红色' })
  name: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;
}
