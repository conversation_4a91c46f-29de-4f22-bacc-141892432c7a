import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ColorsService } from './colors.service';
import { CreateColorDto } from './dto/create-color.dto';
import { UpdateColorDto } from './dto/update-color.dto';
import { QueryColorsDto } from './dto/query-colors.dto';
import { ColorListResponseDto } from './dto/color-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('colors')
@Controller('colors')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ColorsController {
  private readonly logger = new Logger(ColorsController.name);

  constructor(private readonly colorsService: ColorsService) {}

  @Post()
  @ApiOperation({ summary: '创建颜色' })
  @ApiResponse({
    status: 200,
    description: '颜色创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '颜色编码已存在或颜色名称格式不正确',
  })
  async create(@Body() createColorDto: CreateColorDto) {
    this.logger.log(`Creating color ${createColorDto.code}`);

    await this.colorsService.create(createColorDto);

    return {
      code: 200,
      data: null,
      message: '颜色创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取颜色列表（支持模糊搜索）' })
  @ApiResponse({
    status: 200,
    description: '获取颜色列表成功',
    type: ColorListResponseDto,
  })
  async findAll(@Query() queryDto: QueryColorsDto) {
    this.logger.log(`Querying colors with params: ${JSON.stringify(queryDto)}`);

    const result = await this.colorsService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取颜色列表成功',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取颜色详情' })
  @ApiParam({
    name: 'id',
    description: '颜色ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '获取颜色详情成功',
  })
  @ApiResponse({
    status: 404,
    description: '颜色不存在',
  })
  async findOne(@Param('id') id: string) {
    this.logger.log(`Getting color details for id: ${id}`);

    const color = await this.colorsService.findOne(id);

    return {
      code: 200,
      data: {
        id: color.id,
        code: color.code,
        name: color.name,
        createdAt: color.createdAt,
        updatedAt: color.updatedAt,
      },
      message: '获取颜色详情成功',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新颜色信息' })
  @ApiParam({
    name: 'id',
    description: '颜色ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '颜色更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '颜色不存在',
  })
  async update(
    @Param('id') id: string,
    @Body() updateColorDto: UpdateColorDto,
  ) {
    this.logger.log(`Updating color ${id}`);

    await this.colorsService.update(id, updateColorDto);

    return {
      code: 200,
      data: null,
      message: '颜色更新成功',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除颜色（软删除）' })
  @ApiParam({
    name: 'id',
    description: '颜色ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: '颜色删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '颜色不存在',
  })
  async remove(@Param('id') id: string) {
    this.logger.log(`Deleting color ${id}`);

    await this.colorsService.remove(id);

    return {
      code: 200,
      data: null,
      message: '颜色删除成功',
    };
  }
}
