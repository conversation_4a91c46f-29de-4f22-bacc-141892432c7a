import { ApiProperty } from '@nestjs/swagger';

export class ColorDto {
  @ApiProperty({ description: '颜色ID', example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' })
  id: string;

  @ApiProperty({ description: '颜色编码', example: 'COLOR001' })
  code: string;

  @ApiProperty({ description: '颜色名称', example: '红色' })
  name: string;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' })
  updatedAt: Date;
}

export class ColorListResponseDto {
  @ApiProperty({ 
    description: '颜色列表', 
    type: [ColorDto] 
  })
  colors: ColorDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}
