import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';
import { IsChineseColorName } from '@/common/validators/is-chinese-color-name.validator';

export class CreateColorDto {
  @ApiProperty({
    description: '颜色编码（必填，唯一）',
    example: 'COLOR001',
  })
  @IsString()
  @IsNotEmpty({ message: '颜色编码不能为空' })
  code: string;

  @ApiProperty({
    description: '颜色名称（必填，最多6个中文字符，必须以"色"结尾）',
    example: '红色',
  })
  @IsString()
  @IsNotEmpty({ message: '颜色名称不能为空' })
  @IsChineseColorName({ message: '颜色名称必须是中文，最多6个字符，且必须以"色"结尾' })
  name: string;
}
