import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Color } from './entities/color.entity';
import { CreateColorDto } from './dto/create-color.dto';
import { UpdateColorDto } from './dto/update-color.dto';
import { QueryColorsDto } from './dto/query-colors.dto';

@Injectable()
export class ColorsService {
  private readonly logger = new Logger(ColorsService.name);

  constructor(
    @InjectRepository(Color)
    private colorsRepository: Repository<Color>,
  ) {}

  /**
   * 创建颜色
   */
  async create(createColorDto: CreateColorDto): Promise<Color> {
    this.logger.log(`Creating color with code: ${createColorDto.code}`);

    // 检查颜色编码是否已存在
    const existingColor = await this.colorsRepository.findOne({
      where: { code: createColorDto.code, isDeleted: false },
    });

    if (existingColor) {
      throw new BadRequestException('颜色编码已存在');
    }

    // 创建颜色
    const color = this.colorsRepository.create({
      ...createColorDto,
      isDeleted: false,
    });

    return await this.colorsRepository.save(color);
  }

  /**
   * 分页查询颜色列表（支持模糊搜索）
   */
  async findAll(queryDto: QueryColorsDto) {
    this.logger.log(`Querying colors with params: ${JSON.stringify(queryDto)}`);

    const { page, pageSize, search } = queryDto;

    // page和pageSize是必须参数
    if (page === undefined || pageSize === undefined) {
      return {
        colors: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // 处理分页参数：page=0&pageSize=0表示获取所有数据
    const isGetAll = page === 0 && pageSize === 0;
    const finalPage = isGetAll ? 1 : page;
    const finalPageSize = isGetAll ? undefined : pageSize;
    const skip = isGetAll ? 0 : (page - 1) * pageSize;

    const queryBuilder = this.colorsRepository
      .createQueryBuilder('color')
      .where('color.isDeleted = :isDeleted', { isDeleted: false });

    // 模糊搜索颜色编码和名称
    if (search) {
      queryBuilder.andWhere(
        '(color.code ILIKE :search OR color.name ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const colors = await queryBuilder
      .select([
        'color.id',
        'color.code',
        'color.name',
        'color.createdAt',
        'color.updatedAt',
      ])
      .orderBy('color.createdAt', 'DESC')
      .skip(skip)
      .take(finalPageSize)
      .getMany();

    return {
      colors,
      total,
      page: isGetAll ? 0 : finalPage,
      pageSize: isGetAll ? 0 : finalPageSize!,
      totalPages: isGetAll ? 1 : Math.ceil(total / finalPageSize!),
    };
  }

  /**
   * 根据ID查找颜色
   */
  async findById(id: string): Promise<Color | null> {
    return await this.colorsRepository.findOne({
      where: { id, isDeleted: false },
    });
  }

  /**
   * 根据ID获取颜色详情
   */
  async findOne(id: string): Promise<Color> {
    const color = await this.findById(id);
    if (!color) {
      throw new NotFoundException('颜色不存在');
    }
    return color;
  }

  /**
   * 更新颜色
   */
  async update(id: string, updateColorDto: UpdateColorDto): Promise<Color> {
    this.logger.log(`Updating color ${id}`);

    // 查找要更新的颜色
    const color = await this.findOne(id);

    // 更新字段
    if (updateColorDto.name !== undefined) {
      color.name = updateColorDto.name;
    }

    return await this.colorsRepository.save(color);
  }

  /**
   * 软删除颜色
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`Soft deleting color ${id}`);

    // 查找要删除的颜色
    const color = await this.findOne(id);

    // 软删除
    color.isDeleted = true;
    color.deletedAt = new Date();
    await this.colorsRepository.save(color);
  }
}
