# 供应商档案管理系统

## 功能概述

供应商档案管理系统为服装批发商提供全面的供应商业务数据管理和分析功能，包括采购、到货、返修等各个环节的详细记录和统计分析。

## 主要功能

### 1. 供应商档案明细管理
- **采购记录**: 记录采购数量、金额、订单ID等信息
- **到货记录**: 记录实际到货数量、瑕疵数量、物流信息等
- **返修记录**: 记录送修数量、修复结果等信息
- **支付记录**: 记录给供应商的打款金额、支付凭证等信息
- **图片支持**: 所有类型都支持上传相关图片（如订单截图、支付凭证等）
- **Excel导入导出**: 支持按类型导入导出明细数据

### 2. 供应商档案汇总统计
- **基础统计**: 总采购金额、数量、到货数量、总支付金额等
- **质量指标**: 瑕疵率、返修率、修复成功率等
- **业务指标**: 订单完成率、平均单价、信用等级等
- **财务指标**: 未结算金额、预付款余额、账期等
- **服务指标**: 准时交货率、响应速度、服务态度等

### 3. 智能计算功能
所有比率和评分都会根据明细数据自动计算：

#### 计算公式
- **瑕疵率** = 总瑕疵数量 / 总到货数量 × 100%
- **返修率** = 总送修数量 / 总到货数量 × 100%
- **修复成功率** = 总修复到货数量 / 总送修数量 × 100%
- **订单完成率** = 总到货数量 / 总采购数量 × 100%
- **平均单价** = 总采购金额 / 总采购数量
- **退货率** = 退货金额 / 总采购金额 × 100%

### 4. 选择性统计导出
- 支持选择特定明细记录进行统计
- 导出Excel时包含选中明细的累计统计数据
- 提供汇总和详细两种导出模式

### 5. 管理员功能
- 超级管理员可手动调整关键统计数据
- 支持修改总成交额、瑕疵率、返修率等字段
- 提供信用等级和评分管理功能

## API接口

### 明细管理接口
- `POST /supplier-archives/details` - 创建明细记录
- `GET /supplier-archives/details` - 分页查询明细列表
- `GET /supplier-archives/details/:id` - 获取明细详情
- `PATCH /supplier-archives/details/:id` - 更新明细记录
- `DELETE /supplier-archives/details/:id` - 删除明细记录

### 档案汇总接口
- `GET /supplier-archives/archives` - 分页查询档案汇总
- `GET /supplier-archives/archives/:supplierCode` - 获取档案详情
- `PATCH /supplier-archives/archives/:supplierCode/admin` - 管理员更新档案

### 导入导出接口
- `GET /supplier-archives/export` - 导出明细Excel（含选择性统计）
- `GET /supplier-archives/archives/export/summary` - 导出档案汇总Excel
- `POST /supplier-archives/import/:type` - 导入明细Excel
- `GET /supplier-archives/import-template/:type` - 下载导入模板

## 数据库结构

### 供应商档案主表 (supplier_archives)
包含供应商的完整统计数据，支持以下字段分类：

#### 基础统计字段
- `total_purchase_amount` - 总采购金额
- `total_purchase_quantity` - 总采购数量
- `total_arrival_quantity` - 总到货数量
- `total_defect_quantity` - 总瑕疵数量
- `total_repair_quantity` - 总送修数量
- `total_repaired_quantity` - 总修复到货数量
- `total_payment_amount` - 总支付金额

#### 计算型字段
- `defect_rate` - 瑕疵率（%）
- `repair_rate` - 返修率（%）
- `repair_success_rate` - 修复成功率（%）
- `order_completion_rate` - 订单完成率（%）
- `average_unit_price` - 平均单价
- `return_rate` - 退货率（%）

#### 业务管理字段
- `credit_level` - 信用等级（A/B/C/D）
- `cooperation_start_date` - 合作开始时间
- `last_transaction_date` - 最后交易时间
- `payment_cycle` - 平均付款周期（天）
- `delivery_cycle` - 平均交货周期（天）

#### 质量管控字段
- `quality_score` - 质量评分（1-10分）
- `return_count` - 退货次数
- `return_amount` - 退货金额

#### 财务字段
- `unsettled_amount` - 未结算金额
- `prepayment_balance` - 预付款余额
- `average_payment_period` - 平均账期（天）

#### 服务评价字段
- `on_time_delivery_rate` - 准时交货率（%）
- `response_speed_score` - 响应速度评分（1-10分）
- `service_attitude_score` - 服务态度评分（1-10分）
- `overall_score` - 综合评分（1-10分）

### 供应商档案明细表 (supplier_archive_details)
记录每笔具体的业务操作，支持五种类型：
- `purchase` - 采购记录
- `arrival` - 到货记录
- `repair_send` - 返厂修复记录
- `repair_arrival` - 修复到货记录
- `payment` - 支付记录

#### 新增字段
- `payment_amount` - 支付金额（支付类型必填）
- `image_url` - 相关图片URL（所有类型选填）

## 使用说明

### 1. 数据库迁移
执行 `src/migrations/add-supplier-archive-fields.sql` 文件来更新数据库结构。

### 2. 业务流程
1. 创建采购记录 → 系统自动更新总采购数据
2. 创建到货记录 → 系统自动计算瑕疵率、订单完成率等
3. 创建返修记录 → 系统自动计算返修率
4. 创建修复到货记录 → 系统自动计算修复成功率
5. 创建支付记录 → 系统自动更新总支付金额

### 3. 导出功能
- **明细导出**: 选择特定明细记录，导出时会包含这些记录的累计统计
- **汇总导出**: 导出所有供应商的档案汇总数据，支持简要和详细两种模式

### 4. 管理员功能
超级管理员可以通过 `PATCH /supplier-archives/archives/:supplierCode/admin` 接口手动调整：
- 总采购金额
- 瑕疵率、返修率等比率数据
- 信用等级和各项评分

## 注意事项

1. **数据一致性**: 所有比率数据会在明细变更时自动重新计算
2. **权限控制**: 管理员修改功能需要超级管理员权限
3. **数据验证**: 所有比率字段限制在0-100%范围内，评分限制在1-10分范围内
4. **性能优化**: 已为常用查询字段创建索引
5. **选择性统计**: 导出功能支持基于选中明细的精确统计计算
