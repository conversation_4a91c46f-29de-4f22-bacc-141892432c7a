import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsEnum,
  ValidateIf,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SupplierArchiveType } from '../entities/supplier-archive-detail.entity';

export class CreateSupplierArchiveDetailDto {
  @ApiProperty({
    description: '供应商编码',
    example: 'SUP001',
  })
  @IsString()
  @IsNotEmpty({ message: '供应商编码不能为空' })
  supplierCode: string;

  @ApiProperty({
    description: '档案类型',
    enum: SupplierArchiveType,
    example: SupplierArchiveType.PURCHASE,
  })
  @IsEnum(SupplierArchiveType, {
    message:
      '档案类型必须是 purchase、arrival、repair_send、repair_arrival 或 payment',
  })
  type: SupplierArchiveType;

  // 采购类型字段
  @ApiProperty({
    description: '商品总数量（采购类型必填）',
    example: 100,
    required: false,
  })
  @ValidateIf((o) => o.type === SupplierArchiveType.PURCHASE)
  @IsNotEmpty({ message: '采购类型时商品总数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '商品总数量必须是整数' })
  @Min(1, { message: '商品总数量必须大于0' })
  totalQuantity?: number;

  @ApiProperty({
    description: '商品总金额（采购类型必填）',
    example: 15000.5,
    required: false,
  })
  @ValidateIf((o) => o.type === SupplierArchiveType.PURCHASE)
  @IsNotEmpty({ message: '采购类型时商品总金额不能为空' })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '商品总金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '商品总金额必须大于0' })
  totalAmount?: number;

  @ApiProperty({
    description: '采购订单ID（采购类型选填）',
    example: 'PO20240101001',
    required: false,
  })
  @IsOptional()
  @IsString()
  purchaseOrderId?: string;

  // 到货类型字段
  @ApiProperty({
    description: '实际到货数量（到货类型必填）',
    example: 95,
    required: false,
  })
  @ValidateIf((o) => o.type === SupplierArchiveType.ARRIVAL)
  @IsNotEmpty({ message: '到货类型时实际到货数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '实际到货数量必须是整数' })
  @Min(0, { message: '实际到货数量不能小于0' })
  actualArrivalQuantity?: number;

  @ApiProperty({
    description: '瑕疵数量（到货类型必填）',
    example: 5,
    required: false,
  })
  @ValidateIf((o) => o.type === SupplierArchiveType.ARRIVAL)
  @IsNotEmpty({ message: '到货类型时瑕疵数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '瑕疵数量必须是整数' })
  @Min(0, { message: '瑕疵数量不能小于0' })
  defectQuantity?: number;

  @ApiProperty({
    description: '物流订单ID（到货、返厂修复、修复到货类型选填）',
    example: 'LO20240101001',
    required: false,
  })
  @IsOptional()
  @IsString()
  logisticsOrderId?: string;

  // 返厂修复类型字段
  @ApiProperty({
    description: '送修商品数量（返厂修复类型必填）',
    example: 10,
    required: false,
  })
  @ValidateIf((o) => o.type === SupplierArchiveType.REPAIR_SEND)
  @IsNotEmpty({ message: '返厂修复类型时送修商品数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '送修商品数量必须是整数' })
  @Min(1, { message: '送修商品数量必须大于0' })
  repairQuantity?: number;

  // 修复到货类型字段
  @ApiProperty({
    description: '实际修复商品数（修复到货类型必填）',
    example: 8,
    required: false,
  })
  @ValidateIf((o) => o.type === SupplierArchiveType.REPAIR_ARRIVAL)
  @IsNotEmpty({ message: '修复到货类型时实际修复商品数不能为空' })
  @Type(() => Number)
  @IsInt({ message: '实际修复商品数必须是整数' })
  @Min(0, { message: '实际修复商品数不能小于0' })
  actualRepairedQuantity?: number;

  @ApiProperty({
    description: '总到货数（修复到货类型必填）',
    example: 10,
    required: false,
  })
  @ValidateIf((o) => o.type === SupplierArchiveType.REPAIR_ARRIVAL)
  @IsNotEmpty({ message: '修复到货类型时总到货数不能为空' })
  @Type(() => Number)
  @IsInt({ message: '总到货数必须是整数' })
  @Min(0, { message: '总到货数不能小于0' })
  totalArrivalQuantity?: number;

  // 支付类型字段
  @ApiProperty({
    description: '支付金额（支付类型必填）',
    example: 50000.0,
    required: false,
  })
  @ValidateIf((o) => o.type === SupplierArchiveType.PAYMENT)
  @IsNotEmpty({ message: '支付类型时支付金额不能为空' })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '支付金额必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '支付金额必须大于0' })
  paymentAmount?: number;

  // 图片字段（所有类型选填）
  @ApiProperty({
    description: '相关图片URL（所有类型选填）',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiProperty({
    description: '备注',
    example: '质量良好，按时到货',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;
}
