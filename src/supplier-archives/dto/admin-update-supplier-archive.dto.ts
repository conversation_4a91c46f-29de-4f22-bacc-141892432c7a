import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsN<PERSON>ber, IsString, IsIn, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class AdminUpdateSupplierArchiveDto {
  @ApiProperty({
    description: '总采购金额（管理员可修改）',
    example: 150000.50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '总采购金额必须是数字且最多保留两位小数' })
  @Min(0, { message: '总采购金额不能小于0' })
  totalPurchaseAmount?: number;

  @ApiProperty({
    description: '瑕疵率（%）（管理员可修改）',
    example: 5.26,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '瑕疵率必须是数字且最多保留两位小数' })
  @Min(0, { message: '瑕疵率不能小于0' })
  @Max(100, { message: '瑕疵率不能大于100' })
  defectRate?: number;

  @ApiProperty({
    description: '返修率（%）（管理员可修改）',
    example: 3.16,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '返修率必须是数字且最多保留两位小数' })
  @Min(0, { message: '返修率不能小于0' })
  @Max(100, { message: '返修率不能大于100' })
  repairRate?: number;

  @ApiProperty({
    description: '修复成功率（%）（管理员可修改）',
    example: 83.33,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '修复成功率必须是数字且最多保留两位小数' })
  @Min(0, { message: '修复成功率不能小于0' })
  @Max(100, { message: '修复成功率不能大于100' })
  repairSuccessRate?: number;

  @ApiProperty({
    description: '信用等级（A/B/C/D）',
    example: 'B',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['A', 'B', 'C', 'D'], { message: '信用等级必须是 A、B、C 或 D' })
  creditLevel?: string;

  @ApiProperty({
    description: '质量评分（1-10分）',
    example: 7.5,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 1 }, { message: '质量评分必须是数字且最多保留一位小数' })
  @Min(1, { message: '质量评分不能小于1' })
  @Max(10, { message: '质量评分不能大于10' })
  qualityScore?: number;

  @ApiProperty({
    description: '退货金额（管理员可修改）',
    example: 5000.00,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '退货金额必须是数字且最多保留两位小数' })
  @Min(0, { message: '退货金额不能小于0' })
  returnAmount?: number;

  @ApiProperty({
    description: '退货率（%）（管理员可修改）',
    example: 3.33,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '退货率必须是数字且最多保留两位小数' })
  @Min(0, { message: '退货率不能小于0' })
  @Max(100, { message: '退货率不能大于100' })
  returnRate?: number;

  @ApiProperty({
    description: '未结算金额',
    example: 25000.00,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '未结算金额必须是数字且最多保留两位小数' })
  @Min(0, { message: '未结算金额不能小于0' })
  unsettledAmount?: number;

  @ApiProperty({
    description: '预付款余额',
    example: 10000.00,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '预付款余额必须是数字且最多保留两位小数' })
  @Min(0, { message: '预付款余额不能小于0' })
  prepaymentBalance?: number;

  @ApiProperty({
    description: '准时交货率（%）',
    example: 90.00,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '准时交货率必须是数字且最多保留两位小数' })
  @Min(0, { message: '准时交货率不能小于0' })
  @Max(100, { message: '准时交货率不能大于100' })
  onTimeDeliveryRate?: number;

  @ApiProperty({
    description: '响应速度评分（1-10分）',
    example: 8.0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 1 }, { message: '响应速度评分必须是数字且最多保留一位小数' })
  @Min(1, { message: '响应速度评分不能小于1' })
  @Max(10, { message: '响应速度评分不能大于10' })
  responseSpeedScore?: number;

  @ApiProperty({
    description: '服务态度评分（1-10分）',
    example: 8.5,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 1 }, { message: '服务态度评分必须是数字且最多保留一位小数' })
  @Min(1, { message: '服务态度评分不能小于1' })
  @Max(10, { message: '服务态度评分不能大于10' })
  serviceAttitudeScore?: number;

  @ApiProperty({
    description: '综合评分（1-10分）',
    example: 7.8,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 1 }, { message: '综合评分必须是数字且最多保留一位小数' })
  @Min(1, { message: '综合评分不能小于1' })
  @Max(10, { message: '综合评分不能大于10' })
  overallScore?: number;
}
