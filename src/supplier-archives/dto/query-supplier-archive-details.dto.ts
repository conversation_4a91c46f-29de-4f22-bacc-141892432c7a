import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsEnum, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { SupplierArchiveType } from '../entities/supplier-archive-detail.entity';

export class QuerySupplierArchiveDetailsDto {
  @ApiProperty({
    description: '页码（必填）',
    example: 1,
  })
  @IsNotEmpty({ message: '页码不能为空' })
  @Type(() => Number)
  page: number;

  @ApiProperty({
    description: '每页数量（必填）',
    example: 10,
  })
  @IsNotEmpty({ message: '每页数量不能为空' })
  @Type(() => Number)
  pageSize: number;

  @ApiProperty({
    description: '起始日期（必填）',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsNotEmpty({ message: '起始日期不能为空' })
  @IsDateString({}, { message: '起始日期格式不正确' })
  startTime: string;

  @ApiProperty({
    description: '终止日期（必填）',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsNotEmpty({ message: '终止日期不能为空' })
  @IsDateString({}, { message: '终止日期格式不正确' })
  endTime: string;

  @ApiProperty({
    description: '档案类型筛选（不传入时获取全部类型）',
    enum: SupplierArchiveType,
    example: SupplierArchiveType.PURCHASE,
    required: false,
  })
  @IsOptional()
  @IsEnum(SupplierArchiveType, { message: '档案类型必须是 purchase、arrival、repair_send 或 repair_arrival' })
  type?: SupplierArchiveType;

  @ApiProperty({
    description: '搜索关键词（模糊搜索供应商名称、备注）',
    example: '广州',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '供应商编码筛选',
    example: 'SUP001',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierCode?: string;
}
