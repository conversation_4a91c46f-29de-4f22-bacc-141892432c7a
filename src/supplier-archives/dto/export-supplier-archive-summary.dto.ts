import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray } from 'class-validator';

export class ExportSupplierArchiveSummaryDto {
  @ApiProperty({
    description: '供应商编码列表（逗号分隔，用于选择性导出）',
    example: 'SUP001,SUP002,SUP003',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierCodes?: string;

  @ApiProperty({
    description: '信用等级筛选',
    example: 'A',
    required: false,
  })
  @IsOptional()
  @IsString()
  creditLevel?: string;

  @ApiProperty({
    description: '导出类型',
    example: 'summary',
    required: false,
    enum: ['summary', 'detailed']
  })
  @IsOptional()
  @IsString()
  exportType?: 'summary' | 'detailed';

  @ApiProperty({
    description: '包含的字段（用于自定义导出内容）',
    example: ['basicInfo', 'qualityMetrics', 'financialData', 'performanceScores'],
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  includeFields?: string[];
}

// 内部处理接口
export interface ProcessedExportSummaryDto {
  supplierCodes?: string[];
  creditLevel?: string;
  exportType?: 'summary' | 'detailed';
  includeFields?: string[];
}
