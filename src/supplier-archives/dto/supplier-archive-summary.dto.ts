import { ApiProperty } from '@nestjs/swagger';

export class SupplierArchiveSummaryDto {
  @ApiProperty({ description: '供应商编码', example: 'SUP001' })
  supplierCode: string;

  @ApiProperty({ description: '供应商名称', example: '广州服装供应商' })
  supplierName: string;

  // 基础统计数据
  @ApiProperty({ description: '总采购金额', example: 150000.5 })
  totalPurchaseAmount: number;

  @ApiProperty({ description: '总采购数量', example: 1000 })
  totalPurchaseQuantity: number;

  @ApiProperty({ description: '总到货数量', example: 950 })
  totalArrivalQuantity: number;

  @ApiProperty({ description: '总瑕疵数量', example: 50 })
  totalDefectQuantity: number;

  @ApiProperty({ description: '总送修数量', example: 30 })
  totalRepairQuantity: number;

  @ApiProperty({ description: '总修复到货数量', example: 25 })
  totalRepairedQuantity: number;

  @ApiProperty({ description: '总支付金额', example: 120000.0 })
  totalPaymentAmount: number;

  // 计算型统计字段
  @ApiProperty({ description: '瑕疵率（%）', example: 5.26 })
  defectRate: number;

  @ApiProperty({ description: '返修率（%）', example: 3.16 })
  repairRate: number;

  @ApiProperty({ description: '修复成功率（%）', example: 83.33 })
  repairSuccessRate: number;

  @ApiProperty({ description: '订单完成率（%）', example: 95.0 })
  orderCompletionRate: number;

  // 业务关键字段
  @ApiProperty({ description: '平均单价', example: 150.0 })
  averageUnitPrice: number;

  @ApiProperty({
    description: '合作开始时间',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  cooperationStartDate: Date | null;

  @ApiProperty({
    description: '最后交易时间',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  lastTransactionDate: Date | null;

  @ApiProperty({ description: '信用等级（A/B/C/D）', example: 'B' })
  creditLevel: string;

  @ApiProperty({ description: '平均付款周期（天）', example: 30 })
  paymentCycle: number;

  @ApiProperty({ description: '平均交货周期（天）', example: 7 })
  deliveryCycle: number;

  // 质量管控字段
  @ApiProperty({ description: '质量评分（1-10分）', example: 7.5 })
  qualityScore: number;

  @ApiProperty({ description: '退货次数', example: 3 })
  returnCount: number;

  @ApiProperty({ description: '退货金额', example: 5000.0 })
  returnAmount: number;

  @ApiProperty({ description: '退货率（%）', example: 3.33 })
  returnRate: number;

  // 财务相关字段
  @ApiProperty({ description: '未结算金额', example: 25000.0 })
  unsettledAmount: number;

  @ApiProperty({ description: '预付款余额', example: 10000.0 })
  prepaymentBalance: number;

  @ApiProperty({ description: '平均账期（天）', example: 45 })
  averagePaymentPeriod: number;

  // 供应商表现字段
  @ApiProperty({ description: '准时交货率（%）', example: 90.0 })
  onTimeDeliveryRate: number;

  @ApiProperty({ description: '响应速度评分（1-10分）', example: 8.0 })
  responseSpeedScore: number;

  @ApiProperty({ description: '服务态度评分（1-10分）', example: 8.5 })
  serviceAttitudeScore: number;

  @ApiProperty({ description: '综合评分（1-10分）', example: 7.8 })
  overallScore: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class SupplierArchiveSummaryListDto {
  @ApiProperty({
    description: '供应商档案汇总列表',
    type: [SupplierArchiveSummaryDto],
  })
  archives: SupplierArchiveSummaryDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}
