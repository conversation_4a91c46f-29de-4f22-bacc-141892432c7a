import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import { SupplierArchive } from './entities/supplier-archive.entity';
import {
  SupplierArchiveDetail,
  SupplierArchiveType,
} from './entities/supplier-archive-detail.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { CreateSupplierArchiveDetailDto } from './dto/create-supplier-archive-detail.dto';
import { UpdateSupplierArchiveDetailDto } from './dto/update-supplier-archive-detail.dto';
import { QuerySupplierArchiveDetailsDto } from './dto/query-supplier-archive-details.dto';
import { QuerySupplierArchivesDto } from './dto/query-supplier-archives.dto';
import { AdminUpdateSupplierArchiveDto } from './dto/admin-update-supplier-archive.dto';
import {
  SupplierArchiveSummaryDto,
  SupplierArchiveSummaryListDto,
} from './dto/supplier-archive-summary.dto';
import { ProcessedExportSummaryDto } from './dto/export-supplier-archive-summary.dto';

// 内部处理接口
interface ProcessedExportDto {
  startTime: string;
  endTime: string;
  detailIds?: string[];
  type?: SupplierArchiveType;
  supplierCode?: string;
}

@Injectable()
export class SupplierArchivesService {
  private readonly logger = new Logger(SupplierArchivesService.name);

  constructor(
    @InjectRepository(SupplierArchive)
    private supplierArchiveRepository: Repository<SupplierArchive>,
    @InjectRepository(SupplierArchiveDetail)
    private supplierArchiveDetailRepository: Repository<SupplierArchiveDetail>,
    @InjectRepository(Supplier)
    private supplierRepository: Repository<Supplier>,
    private dataSource: DataSource,
  ) {}

  // 获取或创建供应商档案记录
  private async getOrCreateSupplierArchive(
    supplierCode: string,
  ): Promise<SupplierArchive> {
    let supplierArchive = await this.supplierArchiveRepository.findOne({
      where: { supplierCode, isDeleted: false },
    });

    if (!supplierArchive) {
      // 获取供应商信息
      const supplier = await this.supplierRepository.findOne({
        where: { code: supplierCode, isDeleted: false },
      });

      if (!supplier) {
        throw new BadRequestException(`供应商 ${supplierCode} 不存在`);
      }

      supplierArchive = this.supplierArchiveRepository.create({
        supplierCode,
        supplierName: supplier.name,
        totalPurchaseAmount: 0,
        totalPurchaseQuantity: 0,
        totalArrivalQuantity: 0,
        totalDefectQuantity: 0,
        totalRepairQuantity: 0,
        totalRepairedQuantity: 0,
        totalPaymentAmount: 0,
        // 新增字段初始化
        defectRate: 0,
        repairRate: 0,
        repairSuccessRate: 0,
        orderCompletionRate: 0,
        averageUnitPrice: 0,
        cooperationStartDate: new Date(),
        lastTransactionDate: null,
        creditLevel: 'C',
        paymentCycle: 0,
        deliveryCycle: 0,
        qualityScore: 5.0,
        returnCount: 0,
        returnAmount: 0,
        returnRate: 0,
        unsettledAmount: 0,
        prepaymentBalance: 0,
        averagePaymentPeriod: 0,
        onTimeDeliveryRate: 0,
        responseSpeedScore: 5.0,
        serviceAttitudeScore: 5.0,
        overallScore: 5.0,
        isDeleted: false,
      });
      supplierArchive =
        await this.supplierArchiveRepository.save(supplierArchive);
      this.logger.log(`Created new supplier archive for ${supplierCode}`);
    }

    return supplierArchive;
  }

  // 更新供应商档案统计数据
  private async updateSupplierArchiveStats(
    supplierCode: string,
    type: SupplierArchiveType,
    oldData: any = {},
    newData: any = {},
    isDelete: boolean = false,
  ): Promise<void> {
    const archive = await this.getOrCreateSupplierArchive(supplierCode);

    // 计算变化量
    const getDelta = (field: string) => {
      const oldValue = oldData[field] || 0;
      const newValue = isDelete ? 0 : newData[field] || 0;
      return newValue - oldValue;
    };

    switch (type) {
      case SupplierArchiveType.PURCHASE:
        archive.totalPurchaseQuantity += getDelta('totalQuantity');
        archive.totalPurchaseAmount += getDelta('totalAmount');
        break;
      case SupplierArchiveType.ARRIVAL:
        archive.totalArrivalQuantity += getDelta('actualArrivalQuantity');
        archive.totalDefectQuantity += getDelta('defectQuantity');
        break;
      case SupplierArchiveType.REPAIR_SEND:
        archive.totalRepairQuantity += getDelta('repairQuantity');
        break;
      case SupplierArchiveType.REPAIR_ARRIVAL:
        archive.totalRepairedQuantity += getDelta('actualRepairedQuantity');
        break;
      case SupplierArchiveType.PAYMENT:
        archive.totalPaymentAmount += getDelta('paymentAmount');
        break;
    }

    // 重新计算各种比率
    await this.recalculateArchiveRates(archive);
    await this.supplierArchiveRepository.save(archive);
  }

  // 重新计算供应商档案的各种比率
  private async recalculateArchiveRates(
    archive: SupplierArchive,
  ): Promise<void> {
    // 瑕疵率 = 总瑕疵数量 / 总到货数量 * 100%
    if (archive.totalArrivalQuantity > 0) {
      archive.defectRate = Number(
        (
          (archive.totalDefectQuantity / archive.totalArrivalQuantity) *
          100
        ).toFixed(2),
      );
    } else {
      archive.defectRate = 0;
    }

    // 返修率 = 总送修数量 / 总到货数量 * 100%
    if (archive.totalArrivalQuantity > 0) {
      archive.repairRate = Number(
        (
          (archive.totalRepairQuantity / archive.totalArrivalQuantity) *
          100
        ).toFixed(2),
      );
    } else {
      archive.repairRate = 0;
    }

    // 修复成功率 = 总修复到货数量 / 总送修数量 * 100%
    if (archive.totalRepairQuantity > 0) {
      archive.repairSuccessRate = Number(
        (
          (archive.totalRepairedQuantity / archive.totalRepairQuantity) *
          100
        ).toFixed(2),
      );
    } else {
      archive.repairSuccessRate = 0;
    }

    // 订单完成率 = 总到货数量 / 总采购数量 * 100%
    if (archive.totalPurchaseQuantity > 0) {
      archive.orderCompletionRate = Number(
        (
          (archive.totalArrivalQuantity / archive.totalPurchaseQuantity) *
          100
        ).toFixed(2),
      );
    } else {
      archive.orderCompletionRate = 0;
    }

    // 平均单价 = 总采购金额 / 总采购数量
    if (archive.totalPurchaseQuantity > 0) {
      archive.averageUnitPrice = Number(
        (archive.totalPurchaseAmount / archive.totalPurchaseQuantity).toFixed(
          2,
        ),
      );
    } else {
      archive.averageUnitPrice = 0;
    }

    // 退货率 = 退货金额 / 总采购金额 * 100%
    if (archive.totalPurchaseAmount > 0) {
      archive.returnRate = Number(
        ((archive.returnAmount / archive.totalPurchaseAmount) * 100).toFixed(2),
      );
    } else {
      archive.returnRate = 0;
    }

    // 更新最后交易时间
    archive.lastTransactionDate = new Date();
  }

  // 创建供应商档案明细
  async createDetail(createDetailDto: CreateSupplierArchiveDetailDto) {
    this.logger.log(
      `Creating supplier archive detail for ${createDetailDto.supplierCode}`,
    );

    // 验证供应商是否存在
    const supplier = await this.supplierRepository.findOne({
      where: { code: createDetailDto.supplierCode, isDeleted: false },
    });
    if (!supplier) {
      throw new BadRequestException(
        `供应商 ${createDetailDto.supplierCode} 不存在`,
      );
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建供应商档案
      await this.getOrCreateSupplierArchive(createDetailDto.supplierCode);

      // 创建明细记录
      const detail = this.supplierArchiveDetailRepository.create({
        supplierCode: createDetailDto.supplierCode,
        type: createDetailDto.type,
        totalQuantity: createDetailDto.totalQuantity || null,
        totalAmount: createDetailDto.totalAmount || null,
        purchaseOrderId: createDetailDto.purchaseOrderId || null,
        actualArrivalQuantity: createDetailDto.actualArrivalQuantity || null,
        defectQuantity: createDetailDto.defectQuantity || null,
        logisticsOrderId: createDetailDto.logisticsOrderId || null,
        repairQuantity: createDetailDto.repairQuantity || null,
        actualRepairedQuantity: createDetailDto.actualRepairedQuantity || null,
        totalArrivalQuantity: createDetailDto.totalArrivalQuantity || null,
        paymentAmount: createDetailDto.paymentAmount || null,
        imageUrl: createDetailDto.imageUrl || null,
        remark: createDetailDto.remark || null,
        isDeleted: false,
      });

      await queryRunner.manager.save(detail);

      // 更新统计数据
      await this.updateSupplierArchiveStats(
        createDetailDto.supplierCode,
        createDetailDto.type,
        {},
        createDetailDto,
      );

      await queryRunner.commitTransaction();
      this.logger.log(
        `Supplier archive detail created successfully with id: ${detail.id}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Failed to create supplier archive detail', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询供应商档案明细
  async findAllDetails(queryDto: QuerySupplierArchiveDetailsDto) {
    const { page, pageSize, startTime, endTime, type, search, supplierCode } =
      queryDto;

    this.logger.log(
      `Querying supplier archive details: page=${page}, pageSize=${pageSize}`,
    );

    // 处理时间参数
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime.length === 10) {
      processedStartTime = `${startTime}T00:00:00.000Z`;
    }

    if (endTime.length === 10) {
      processedEndTime = `${endTime}T23:59:59.999Z`;
    }

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const queryBuilder = this.supplierArchiveDetailRepository
        .createQueryBuilder('detail')
        .leftJoinAndSelect('detail.supplierArchive', 'archive')
        .where('detail.isDeleted = false')
        .andWhere('detail.createdAt >= :startTime', {
          startTime: processedStartTime,
        })
        .andWhere('detail.createdAt <= :endTime', { endTime: processedEndTime })
        .orderBy('detail.createdAt', 'DESC');

      // 添加类型过滤
      if (type) {
        queryBuilder.andWhere('detail.type = :type', { type });
      }

      // 添加供应商编码过滤
      if (supplierCode) {
        queryBuilder.andWhere('detail.supplierCode = :supplierCode', {
          supplierCode,
        });
      }

      // 添加搜索过滤
      if (search) {
        queryBuilder.andWhere(
          '(archive.supplierName ILIKE :search OR detail.remark ILIKE :search)',
          { search: `%${search}%` },
        );
      }

      const details = await queryBuilder.getMany();

      // 格式化返回数据
      const formattedDetails = details.map((detail) =>
        this.formatDetailResponse(detail),
      );

      return {
        details: formattedDetails,
        total: details.length,
        page: 0,
        pageSize: 0,
      };
    }

    // 正常分页查询
    const queryBuilder = this.supplierArchiveDetailRepository
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.supplierArchive', 'archive')
      .where('detail.isDeleted = false')
      .andWhere('detail.createdAt >= :startTime', {
        startTime: processedStartTime,
      })
      .andWhere('detail.createdAt <= :endTime', { endTime: processedEndTime })
      .orderBy('detail.createdAt', 'DESC');

    // 添加类型过滤
    if (type) {
      queryBuilder.andWhere('detail.type = :type', { type });
    }

    // 添加供应商编码过滤
    if (supplierCode) {
      queryBuilder.andWhere('detail.supplierCode = :supplierCode', {
        supplierCode,
      });
    }

    // 添加搜索过滤
    if (search) {
      queryBuilder.andWhere(
        '(archive.supplierName ILIKE :search OR detail.remark ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    const [details, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    // 格式化返回数据
    const formattedDetails = details.map((detail) =>
      this.formatDetailResponse(detail),
    );

    return {
      details: formattedDetails,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  // 格式化明细响应数据
  private formatDetailResponse(detail: SupplierArchiveDetail) {
    const baseResponse = {
      id: detail.id,
      supplierCode: detail.supplierCode,
      supplierName: detail.supplierArchive?.supplierName || '',
      type: detail.type,
      imageUrl: detail.imageUrl,
      remark: detail.remark,
      createdAt: detail.createdAt,
      updatedAt: detail.updatedAt,
    };

    // 根据类型返回相应字段
    switch (detail.type) {
      case SupplierArchiveType.PURCHASE:
        return {
          ...baseResponse,
          totalQuantity: detail.totalQuantity,
          totalAmount: detail.totalAmount,
          purchaseOrderId: detail.purchaseOrderId,
        };
      case SupplierArchiveType.ARRIVAL:
        return {
          ...baseResponse,
          actualArrivalQuantity: detail.actualArrivalQuantity,
          defectQuantity: detail.defectQuantity,
          logisticsOrderId: detail.logisticsOrderId,
        };
      case SupplierArchiveType.REPAIR_SEND:
        return {
          ...baseResponse,
          repairQuantity: detail.repairQuantity,
          logisticsOrderId: detail.logisticsOrderId,
        };
      case SupplierArchiveType.REPAIR_ARRIVAL:
        return {
          ...baseResponse,
          actualRepairedQuantity: detail.actualRepairedQuantity,
          totalArrivalQuantity: detail.totalArrivalQuantity,
          logisticsOrderId: detail.logisticsOrderId,
        };
      case SupplierArchiveType.PAYMENT:
        return {
          ...baseResponse,
          paymentAmount: detail.paymentAmount,
        };
      default:
        return baseResponse;
    }
  }

  // 根据ID查询明细
  async findDetailById(id: string): Promise<any> {
    this.logger.log(`Finding supplier archive detail by id: ${id}`);

    const detail = await this.supplierArchiveDetailRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['supplierArchive'],
    });

    if (!detail) {
      throw new NotFoundException(`供应商档案明细 ${id} 不存在`);
    }

    return this.formatDetailResponse(detail);
  }

  // 更新明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateSupplierArchiveDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating supplier archive detail ${id}`);

    const detail = await this.supplierArchiveDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException(`供应商档案明细 ${id} 不存在`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 保存旧数据用于统计更新
      const oldData = {
        totalQuantity: detail.totalQuantity,
        totalAmount: detail.totalAmount,
        actualArrivalQuantity: detail.actualArrivalQuantity,
        defectQuantity: detail.defectQuantity,
        repairQuantity: detail.repairQuantity,
        actualRepairedQuantity: detail.actualRepairedQuantity,
        paymentAmount: detail.paymentAmount,
      };

      // 更新明细
      Object.assign(detail, updateDetailDto);
      await queryRunner.manager.save(detail);

      // 更新统计数据
      await this.updateSupplierArchiveStats(
        detail.supplierCode,
        detail.type,
        oldData,
        updateDetailDto,
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Supplier archive detail ${id} updated successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to update supplier archive detail ${id}`,
        error,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除明细
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing supplier archive detail ${id}`);

    const detail = await this.supplierArchiveDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException(`供应商档案明细 ${id} 不存在`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 保存旧数据用于统计更新
      const oldData = {
        totalQuantity: detail.totalQuantity,
        totalAmount: detail.totalAmount,
        actualArrivalQuantity: detail.actualArrivalQuantity,
        defectQuantity: detail.defectQuantity,
        repairQuantity: detail.repairQuantity,
        actualRepairedQuantity: detail.actualRepairedQuantity,
        paymentAmount: detail.paymentAmount,
      };

      // 软删除明细
      detail.isDeleted = true;
      detail.deletedAt = new Date();
      await queryRunner.manager.save(detail);

      // 更新统计数据（减去删除的数据）
      await this.updateSupplierArchiveStats(
        detail.supplierCode,
        detail.type,
        oldData,
        {},
        true,
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Supplier archive detail ${id} removed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to remove supplier archive detail ${id}`,
        error,
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出Excel
  async exportToExcel(exportDto: ProcessedExportDto): Promise<Buffer> {
    this.logger.log('Exporting supplier archive details to Excel');

    let queryBuilder = this.supplierArchiveDetailRepository
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.supplierArchive', 'archive')
      .where('detail.isDeleted = false')
      .andWhere('detail.createdAt >= :startTime', {
        startTime: exportDto.startTime,
      })
      .andWhere('detail.createdAt <= :endTime', { endTime: exportDto.endTime })
      .orderBy('detail.createdAt', 'DESC');

    // 类型筛选
    if (exportDto.type) {
      queryBuilder.andWhere('detail.type = :type', { type: exportDto.type });
    }

    // 供应商编码筛选
    if (exportDto.supplierCode) {
      queryBuilder.andWhere('detail.supplierCode = :supplierCode', {
        supplierCode: exportDto.supplierCode,
      });
    }

    // 选择性导出
    if (exportDto.detailIds && exportDto.detailIds.length > 0) {
      queryBuilder.andWhere('detail.id IN (:...detailIds)', {
        detailIds: exportDto.detailIds,
      });
    }

    const details = await queryBuilder.getMany();

    // 计算选择性统计数据
    const statistics = this.calculateSelectedStatistics(details);

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('供应商档案明细');

    // 设置列标题
    const headers = [
      '序号',
      '供应商编码',
      '供应商名称',
      '档案类型',
      '商品总数量',
      '商品总金额',
      '采购订单ID',
      '实际到货数量',
      '瑕疵数量',
      '送修商品数量',
      '实际修复商品数',
      '总到货数',
      '物流订单ID',
      '支付金额',
      '图片URL',
      '备注',
      '创建时间',
    ];

    worksheet.addRow(headers);

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 添加数据行
    for (let i = 0; i < details.length; i++) {
      const detail = details[i];
      const typeMap = {
        purchase: '采购',
        arrival: '到货',
        repair_send: '返厂修复',
        repair_arrival: '修复到货',
        payment: '支付',
      };

      const row = [
        i + 1,
        detail.supplierCode,
        detail.supplierArchive?.supplierName || '',
        typeMap[detail.type] || detail.type,
        detail.totalQuantity || '',
        detail.totalAmount || '',
        detail.purchaseOrderId || '',
        detail.actualArrivalQuantity || '',
        detail.defectQuantity || '',
        detail.repairQuantity || '',
        detail.actualRepairedQuantity || '',
        detail.totalArrivalQuantity || '',
        detail.logisticsOrderId || '',
        detail.paymentAmount || '',
        detail.imageUrl || '',
        detail.remark || '',
        detail.createdAt.toLocaleString('zh-CN'),
      ];
      worksheet.addRow(row);
    }

    // 添加统计数据行
    worksheet.addRow([]); // 空行
    worksheet.addRow(['统计汇总（仅针对选中的明细）']);
    worksheet.addRow(['总采购金额', statistics.totalPurchaseAmount]);
    worksheet.addRow(['总采购数量', statistics.totalPurchaseQuantity]);
    worksheet.addRow(['总到货数量', statistics.totalArrivalQuantity]);
    worksheet.addRow(['总瑕疵数量', statistics.totalDefectQuantity]);
    worksheet.addRow(['总送修数量', statistics.totalRepairQuantity]);
    worksheet.addRow(['总修复到货数量', statistics.totalRepairedQuantity]);
    worksheet.addRow(['总支付金额', statistics.totalPaymentAmount]);
    worksheet.addRow(['瑕疵率（%）', statistics.defectRate]);
    worksheet.addRow(['返修率（%）', statistics.repairRate]);
    worksheet.addRow(['修复成功率（%）', statistics.repairSuccessRate]);
    worksheet.addRow(['订单完成率（%）', statistics.orderCompletionRate]);
    worksheet.addRow(['平均单价', statistics.averageUnitPrice]);

    // 设置统计数据行样式
    const statisticsStartRow = details.length + 3;
    for (let i = statisticsStartRow; i <= statisticsStartRow + 12; i++) {
      const row = worksheet.getRow(i);
      if (i === statisticsStartRow) {
        // 标题行
        row.font = { bold: true, size: 12 };
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFCCCCCC' },
        };
      } else {
        // 数据行
        row.font = { bold: true };
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFF0F0F0' },
        };
      }
    }

    // 自动调整列宽
    worksheet.columns.forEach((column) => {
      column.width = 15;
    });

    // 生成Excel缓冲区
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  // 导入Excel
  async importFromExcel(
    buffer: Buffer,
    type: SupplierArchiveType,
  ): Promise<any> {
    this.logger.log(
      `Importing supplier archive details from Excel with type: ${type}`,
    );

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);
    const worksheet = workbook.getWorksheet(1);

    if (!worksheet) {
      throw new BadRequestException('Excel文件格式错误');
    }

    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[],
    };

    // 跳过标题行，从第二行开始处理
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // 跳过空行
      if (!row.getCell(2).value) {
        continue;
      }

      try {
        const supplierCode = row.getCell(2).value?.toString() || '';
        const remark = row.getCell(14).value?.toString() || null;

        if (!supplierCode) {
          results.errors.push(`第${rowNumber}行：供应商编码不能为空`);
          results.failed++;
          continue;
        }

        const createDto: CreateSupplierArchiveDetailDto = {
          supplierCode,
          type,
          remark: remark || undefined,
        };

        // 根据类型设置相应字段
        switch (type) {
          case SupplierArchiveType.PURCHASE:
            createDto.totalQuantity = Number(row.getCell(5).value) || undefined;
            createDto.totalAmount = Number(row.getCell(6).value) || undefined;
            createDto.purchaseOrderId =
              row.getCell(7).value?.toString() || undefined;
            break;
          case SupplierArchiveType.ARRIVAL:
            createDto.actualArrivalQuantity =
              Number(row.getCell(8).value) || undefined;
            createDto.defectQuantity =
              Number(row.getCell(9).value) || undefined;
            createDto.logisticsOrderId =
              row.getCell(13).value?.toString() || undefined;
            break;
          case SupplierArchiveType.REPAIR_SEND:
            createDto.repairQuantity =
              Number(row.getCell(10).value) || undefined;
            createDto.logisticsOrderId =
              row.getCell(13).value?.toString() || undefined;
            break;
          case SupplierArchiveType.REPAIR_ARRIVAL:
            createDto.actualRepairedQuantity =
              Number(row.getCell(11).value) || undefined;
            createDto.totalArrivalQuantity =
              Number(row.getCell(12).value) || undefined;
            createDto.logisticsOrderId =
              row.getCell(13).value?.toString() || undefined;
            break;
          case SupplierArchiveType.PAYMENT:
            createDto.paymentAmount =
              Number(row.getCell(14).value) || undefined;
            break;
        }

        await this.createDetail(createDto);
        results.success++;
      } catch (error) {
        results.errors.push(`第${rowNumber}行：${error.message}`);
        results.failed++;
      }
    }

    return results;
  }

  // 分页查询供应商档案汇总
  async findAllArchives(
    queryDto: QuerySupplierArchivesDto,
  ): Promise<SupplierArchiveSummaryListDto> {
    const {
      page,
      pageSize,
      search,
      creditLevel,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = queryDto;

    this.logger.log(
      `Querying supplier archives: page=${page}, pageSize=${pageSize}`,
    );

    const queryBuilder = this.supplierArchiveRepository
      .createQueryBuilder('archive')
      .where('archive.isDeleted = false');

    // 添加搜索过滤
    if (search) {
      queryBuilder.andWhere(
        '(archive.supplierCode ILIKE :search OR archive.supplierName ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 添加信用等级过滤
    if (creditLevel) {
      queryBuilder.andWhere('archive.creditLevel = :creditLevel', {
        creditLevel,
      });
    }

    // 添加排序
    const validSortFields = [
      'totalPurchaseAmount',
      'defectRate',
      'repairRate',
      'qualityScore',
      'overallScore',
      'onTimeDeliveryRate',
      'createdAt',
    ];

    if (validSortFields.includes(sortBy)) {
      queryBuilder.orderBy(`archive.${sortBy}`, sortOrder);
    } else {
      queryBuilder.orderBy('archive.createdAt', 'DESC');
    }

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const archives = await queryBuilder.getMany();

      const formattedArchives = archives.map((archive) =>
        this.formatArchiveSummaryResponse(archive),
      );

      return {
        archives: formattedArchives,
        total: archives.length,
        page: 0,
        pageSize: 0,
        totalPages: 1,
      };
    }

    // 正常分页查询
    const [archives, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    const formattedArchives = archives.map((archive) =>
      this.formatArchiveSummaryResponse(archive),
    );

    return {
      archives: formattedArchives,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  // 根据供应商编码查询档案汇总
  async findArchiveBySupplierCode(
    supplierCode: string,
  ): Promise<SupplierArchiveSummaryDto> {
    this.logger.log(`Finding supplier archive by code: ${supplierCode}`);

    const archive = await this.supplierArchiveRepository.findOne({
      where: { supplierCode, isDeleted: false },
    });

    if (!archive) {
      throw new NotFoundException(`供应商档案 ${supplierCode} 不存在`);
    }

    return this.formatArchiveSummaryResponse(archive);
  }

  // 管理员更新供应商档案
  async adminUpdateArchive(
    supplierCode: string,
    updateDto: AdminUpdateSupplierArchiveDto,
  ): Promise<void> {
    this.logger.log(`Admin updating supplier archive ${supplierCode}`);

    const archive = await this.supplierArchiveRepository.findOne({
      where: { supplierCode, isDeleted: false },
    });

    if (!archive) {
      throw new NotFoundException(`供应商档案 ${supplierCode} 不存在`);
    }

    // 更新档案数据
    Object.assign(archive, updateDto);
    await this.supplierArchiveRepository.save(archive);

    this.logger.log(
      `Supplier archive ${supplierCode} updated successfully by admin`,
    );
  }

  // 计算选择性统计数据（用于导出）
  private calculateSelectedStatistics(details: SupplierArchiveDetail[]): any {
    const stats = {
      totalPurchaseAmount: 0,
      totalPurchaseQuantity: 0,
      totalArrivalQuantity: 0,
      totalDefectQuantity: 0,
      totalRepairQuantity: 0,
      totalRepairedQuantity: 0,
      totalPaymentAmount: 0,
      defectRate: 0,
      repairRate: 0,
      repairSuccessRate: 0,
      orderCompletionRate: 0,
      averageUnitPrice: 0,
    };

    // 累加各项数据
    details.forEach((detail) => {
      if (detail.type === SupplierArchiveType.PURCHASE) {
        stats.totalPurchaseAmount += detail.totalAmount || 0;
        stats.totalPurchaseQuantity += detail.totalQuantity || 0;
      } else if (detail.type === SupplierArchiveType.ARRIVAL) {
        stats.totalArrivalQuantity += detail.actualArrivalQuantity || 0;
        stats.totalDefectQuantity += detail.defectQuantity || 0;
      } else if (detail.type === SupplierArchiveType.REPAIR_SEND) {
        stats.totalRepairQuantity += detail.repairQuantity || 0;
      } else if (detail.type === SupplierArchiveType.REPAIR_ARRIVAL) {
        stats.totalRepairedQuantity += detail.actualRepairedQuantity || 0;
      } else if (detail.type === SupplierArchiveType.PAYMENT) {
        stats.totalPaymentAmount += detail.paymentAmount || 0;
      }
    });

    // 计算比率
    if (stats.totalArrivalQuantity > 0) {
      stats.defectRate = Number(
        (
          (stats.totalDefectQuantity / stats.totalArrivalQuantity) *
          100
        ).toFixed(2),
      );
      stats.repairRate = Number(
        (
          (stats.totalRepairQuantity / stats.totalArrivalQuantity) *
          100
        ).toFixed(2),
      );
    }

    if (stats.totalRepairQuantity > 0) {
      stats.repairSuccessRate = Number(
        (
          (stats.totalRepairedQuantity / stats.totalRepairQuantity) *
          100
        ).toFixed(2),
      );
    }

    if (stats.totalPurchaseQuantity > 0) {
      stats.orderCompletionRate = Number(
        (
          (stats.totalArrivalQuantity / stats.totalPurchaseQuantity) *
          100
        ).toFixed(2),
      );
      stats.averageUnitPrice = Number(
        (stats.totalPurchaseAmount / stats.totalPurchaseQuantity).toFixed(2),
      );
    }

    return stats;
  }

  // 格式化档案汇总响应数据
  private formatArchiveSummaryResponse(
    archive: SupplierArchive,
  ): SupplierArchiveSummaryDto {
    return {
      supplierCode: archive.supplierCode,
      supplierName: archive.supplierName,
      totalPurchaseAmount: archive.totalPurchaseAmount,
      totalPurchaseQuantity: archive.totalPurchaseQuantity,
      totalArrivalQuantity: archive.totalArrivalQuantity,
      totalDefectQuantity: archive.totalDefectQuantity,
      totalRepairQuantity: archive.totalRepairQuantity,
      totalRepairedQuantity: archive.totalRepairedQuantity,
      totalPaymentAmount: archive.totalPaymentAmount,
      defectRate: archive.defectRate,
      repairRate: archive.repairRate,
      repairSuccessRate: archive.repairSuccessRate,
      orderCompletionRate: archive.orderCompletionRate,
      averageUnitPrice: archive.averageUnitPrice,
      cooperationStartDate: archive.cooperationStartDate,
      lastTransactionDate: archive.lastTransactionDate,
      creditLevel: archive.creditLevel,
      paymentCycle: archive.paymentCycle,
      deliveryCycle: archive.deliveryCycle,
      qualityScore: archive.qualityScore,
      returnCount: archive.returnCount,
      returnAmount: archive.returnAmount,
      returnRate: archive.returnRate,
      unsettledAmount: archive.unsettledAmount,
      prepaymentBalance: archive.prepaymentBalance,
      averagePaymentPeriod: archive.averagePaymentPeriod,
      onTimeDeliveryRate: archive.onTimeDeliveryRate,
      responseSpeedScore: archive.responseSpeedScore,
      serviceAttitudeScore: archive.serviceAttitudeScore,
      overallScore: archive.overallScore,
      createdAt: archive.createdAt,
      updatedAt: archive.updatedAt,
    };
  }

  // 导出供应商档案汇总Excel
  async exportArchiveSummaryToExcel(
    exportDto: ProcessedExportSummaryDto,
  ): Promise<Buffer> {
    this.logger.log('Exporting supplier archive summary to Excel');

    let queryBuilder = this.supplierArchiveRepository
      .createQueryBuilder('archive')
      .where('archive.isDeleted = false');

    // 供应商编码筛选
    if (exportDto.supplierCodes && exportDto.supplierCodes.length > 0) {
      queryBuilder.andWhere('archive.supplierCode IN (:...supplierCodes)', {
        supplierCodes: exportDto.supplierCodes,
      });
    }

    // 信用等级筛选
    if (exportDto.creditLevel) {
      queryBuilder.andWhere('archive.creditLevel = :creditLevel', {
        creditLevel: exportDto.creditLevel,
      });
    }

    queryBuilder.orderBy('archive.overallScore', 'DESC');

    const archives = await queryBuilder.getMany();

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('供应商档案汇总');

    // 根据导出类型设置不同的列标题
    let headers: string[] = [];

    if (exportDto.exportType === 'detailed') {
      headers = [
        '序号',
        '供应商编码',
        '供应商名称',
        '总采购金额',
        '总采购数量',
        '总到货数量',
        '总瑕疵数量',
        '总送修数量',
        '总修复到货数量',
        '瑕疵率(%)',
        '返修率(%)',
        '修复成功率(%)',
        '订单完成率(%)',
        '平均单价',
        '合作开始时间',
        '最后交易时间',
        '信用等级',
        '付款周期(天)',
        '交货周期(天)',
        '质量评分',
        '退货次数',
        '退货金额',
        '退货率(%)',
        '未结算金额',
        '预付款余额',
        '平均账期(天)',
        '准时交货率(%)',
        '响应速度评分',
        '服务态度评分',
        '综合评分',
        '创建时间',
        '更新时间',
      ];
    } else {
      headers = [
        '序号',
        '供应商编码',
        '供应商名称',
        '总采购金额',
        '瑕疵率(%)',
        '返修率(%)',
        '质量评分',
        '信用等级',
        '准时交货率(%)',
        '综合评分',
        '最后交易时间',
      ];
    }

    worksheet.addRow(headers);

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 添加数据行
    for (let i = 0; i < archives.length; i++) {
      const archive = archives[i];
      let row: any[] = [];

      if (exportDto.exportType === 'detailed') {
        row = [
          i + 1,
          archive.supplierCode,
          archive.supplierName,
          archive.totalPurchaseAmount,
          archive.totalPurchaseQuantity,
          archive.totalArrivalQuantity,
          archive.totalDefectQuantity,
          archive.totalRepairQuantity,
          archive.totalRepairedQuantity,
          archive.defectRate,
          archive.repairRate,
          archive.repairSuccessRate,
          archive.orderCompletionRate,
          archive.averageUnitPrice,
          archive.cooperationStartDate
            ? archive.cooperationStartDate.toLocaleString('zh-CN')
            : '',
          archive.lastTransactionDate
            ? archive.lastTransactionDate.toLocaleString('zh-CN')
            : '',
          archive.creditLevel,
          archive.paymentCycle,
          archive.deliveryCycle,
          archive.qualityScore,
          archive.returnCount,
          archive.returnAmount,
          archive.returnRate,
          archive.unsettledAmount,
          archive.prepaymentBalance,
          archive.averagePaymentPeriod,
          archive.onTimeDeliveryRate,
          archive.responseSpeedScore,
          archive.serviceAttitudeScore,
          archive.overallScore,
          archive.createdAt.toLocaleString('zh-CN'),
          archive.updatedAt.toLocaleString('zh-CN'),
        ];
      } else {
        row = [
          i + 1,
          archive.supplierCode,
          archive.supplierName,
          archive.totalPurchaseAmount,
          archive.defectRate,
          archive.repairRate,
          archive.qualityScore,
          archive.creditLevel,
          archive.onTimeDeliveryRate,
          archive.overallScore,
          archive.lastTransactionDate
            ? archive.lastTransactionDate.toLocaleString('zh-CN')
            : '',
        ];
      }

      worksheet.addRow(row);
    }

    // 自动调整列宽
    worksheet.columns.forEach((column) => {
      column.width = 15;
    });

    // 生成Excel缓冲区
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  // 生成导入模板Excel
  async generateImportTemplate(type: SupplierArchiveType): Promise<Buffer> {
    this.logger.log(
      `Generating supplier archive import template for type: ${type}`,
    );

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('供应商档案明细导入模板');

    // 设置列标题
    const headers = [
      '序号',
      '供应商编码（必填）',
      '供应商名称（仅供参考）',
      '档案类型（仅供参考）',
      '商品总数量',
      '商品总金额',
      '采购订单ID',
      '实际到货数量',
      '瑕疵数量',
      '送修商品数量',
      '实际修复商品数',
      '总到货数',
      '物流订单ID',
      '支付金额',
      '图片URL',
      '备注',
      '创建时间（仅供参考）',
    ];

    worksheet.addRow(headers);

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 根据类型添加示例数据
    const typeMap = {
      purchase: '采购',
      arrival: '到货',
      repair_send: '返厂修复',
      repair_arrival: '修复到货',
      payment: '支付',
    };

    const exampleRow = [
      1,
      'SUP001',
      '广州服装供应商',
      typeMap[type],
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      'https://example.com/image.jpg',
      '示例备注',
      '2024-01-01 10:00:00',
    ];

    // 根据类型填充示例数据
    switch (type) {
      case SupplierArchiveType.PURCHASE:
        exampleRow[4] = 100; // 商品总数量
        exampleRow[5] = 15000.5; // 商品总金额
        exampleRow[6] = 'PO20240101001'; // 采购订单ID
        break;
      case SupplierArchiveType.ARRIVAL:
        exampleRow[7] = 95; // 实际到货数量
        exampleRow[8] = 5; // 瑕疵数量
        exampleRow[12] = 'LO20240101001'; // 物流订单ID
        break;
      case SupplierArchiveType.REPAIR_SEND:
        exampleRow[9] = 10; // 送修商品数量
        exampleRow[12] = 'LO20240101002'; // 物流订单ID
        break;
      case SupplierArchiveType.REPAIR_ARRIVAL:
        exampleRow[10] = 8; // 实际修复商品数
        exampleRow[11] = 10; // 总到货数
        exampleRow[12] = 'LO20240101003'; // 物流订单ID
        break;
      case SupplierArchiveType.PAYMENT:
        exampleRow[13] = 50000.0; // 支付金额
        break;
    }

    worksheet.addRow(exampleRow);

    // 自动调整列宽
    worksheet.columns.forEach((column) => {
      column.width = 15;
    });

    // 添加说明
    worksheet.addRow([]);
    worksheet.addRow(['说明：']);
    worksheet.addRow([`1. 当前模板用于导入"${typeMap[type]}"类型的档案`]);
    worksheet.addRow(['2. 供应商编码必填，且必须在系统中存在']);

    switch (type) {
      case SupplierArchiveType.PURCHASE:
        worksheet.addRow(['3. 商品总数量和商品总金额必填']);
        worksheet.addRow(['4. 采购订单ID选填']);
        break;
      case SupplierArchiveType.ARRIVAL:
        worksheet.addRow(['3. 实际到货数量和瑕疵数量必填']);
        worksheet.addRow(['4. 物流订单ID选填']);
        break;
      case SupplierArchiveType.REPAIR_SEND:
        worksheet.addRow(['3. 送修商品数量必填']);
        worksheet.addRow(['4. 物流订单ID选填']);
        break;
      case SupplierArchiveType.REPAIR_ARRIVAL:
        worksheet.addRow(['3. 实际修复商品数和总到货数必填']);
        worksheet.addRow(['4. 物流订单ID选填']);
        break;
      case SupplierArchiveType.PAYMENT:
        worksheet.addRow(['3. 支付金额必填']);
        worksheet.addRow(['4. 图片URL选填']);
        break;
    }

    worksheet.addRow(['5. 其他标注"仅供参考"的列在导入时会被忽略']);

    // 生成Excel缓冲区
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }
}
