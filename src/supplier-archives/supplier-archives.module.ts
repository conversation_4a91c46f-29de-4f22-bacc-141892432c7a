import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SupplierArchivesService } from './supplier-archives.service';
import { SupplierArchivesController } from './supplier-archives.controller';
import { SupplierArchive } from './entities/supplier-archive.entity';
import { SupplierArchiveDetail } from './entities/supplier-archive-detail.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SupplierArchive,
      SupplierArchiveDetail,
      Supplier,
    ]),
  ],
  controllers: [SupplierArchivesController],
  providers: [SupplierArchivesService],
  exports: [SupplierArchivesService],
})
export class SupplierArchivesModule {}
