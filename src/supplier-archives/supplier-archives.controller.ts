import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  Res,
  Req,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiConsumes,
} from '@nestjs/swagger';
import { SupplierArchivesService } from './supplier-archives.service';
import { CreateSupplierArchiveDetailDto } from './dto/create-supplier-archive-detail.dto';
import { UpdateSupplierArchiveDetailDto } from './dto/update-supplier-archive-detail.dto';
import { QuerySupplierArchiveDetailsDto } from './dto/query-supplier-archive-details.dto';
import { QuerySupplierArchivesDto } from './dto/query-supplier-archives.dto';
import { AdminUpdateSupplierArchiveDto } from './dto/admin-update-supplier-archive.dto';
import { ExportSupplierArchiveSummaryDto } from './dto/export-supplier-archive-summary.dto';
import { SupplierArchiveType } from './entities/supplier-archive-detail.entity';

@ApiTags('supplier-archives')
@Controller('supplier-archives')
export class SupplierArchivesController {
  private readonly logger = new Logger(SupplierArchivesController.name);

  constructor(
    private readonly supplierArchivesService: SupplierArchivesService,
  ) {}

  @Post('details')
  @ApiOperation({ summary: '新增供应商档案明细' })
  @ApiResponse({
    status: 200,
    description: '供应商档案明细创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async createDetail(@Body() createDetailDto: CreateSupplierArchiveDetailDto) {
    this.logger.log(
      `Creating supplier archive detail for ${createDetailDto.supplierCode}`,
    );

    await this.supplierArchivesService.createDetail(createDetailDto);

    return {
      code: 200,
      data: null,
      message: '供应商档案明细创建成功',
    };
  }

  @Get('details')
  @ApiOperation({ summary: '分页查询供应商档案明细列表' })
  @ApiQuery({
    name: 'page',
    description: '页码（必填）',
    example: 1,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量（必填）',
    example: 10,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期（必填）',
    example: '2023-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期（必填）',
    example: '2023-12-31T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'type',
    description: '档案类型筛选（不传入时获取全部类型）',
    example: 'purchase',
    required: false,
  })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词（模糊搜索供应商名称、备注）',
    example: '广州',
    required: false,
  })
  @ApiQuery({
    name: 'supplierCode',
    description: '供应商编码筛选',
    example: 'SUP001',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllDetails(@Query() queryDto: QuerySupplierArchiveDetailsDto) {
    this.logger.log(
      `Querying supplier archive details with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.supplierArchivesService.findAllDetails(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('details/:id')
  @ApiOperation({ summary: '获取供应商档案明细详情' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async findDetailById(@Param('id') id: string) {
    this.logger.log(`Finding supplier archive detail by id: ${id}`);

    const detail = await this.supplierArchivesService.findDetailById(id);

    return {
      code: 200,
      data: detail,
      message: '查询成功',
    };
  }

  @Patch('details/:id')
  @ApiOperation({ summary: '更新供应商档案明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async updateDetail(
    @Param('id') id: string,
    @Body() updateDetailDto: UpdateSupplierArchiveDetailDto,
  ) {
    this.logger.log(`Updating supplier archive detail ${id}`);

    await this.supplierArchivesService.updateDetail(id, updateDetailDto);

    return {
      code: 200,
      data: null,
      message: '供应商档案明细更新成功',
    };
  }

  @Delete('details/:id')
  @ApiOperation({ summary: '删除供应商档案明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async removeDetail(@Param('id') id: string) {
    this.logger.log(`Removing supplier archive detail ${id}`);

    await this.supplierArchivesService.removeDetail(id);

    return {
      code: 200,
      data: null,
      message: '供应商档案明细删除成功',
    };
  }

  @Get('export')
  @ApiOperation({ summary: '导出供应商档案明细Excel' })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期（必填）',
    example: '2023-01-01',
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期（必填）',
    example: '2023-12-31',
  })
  @ApiQuery({
    name: 'detailIds',
    description: '明细ID列表（逗号分隔）',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiQuery({
    name: 'type',
    description: '档案类型筛选',
    example: 'purchase',
    required: false,
  })
  @ApiQuery({
    name: 'supplierCode',
    description: '供应商编码筛选',
    example: 'SUP001',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  async exportToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Exporting supplier archive details to Excel`);

    try {
      // 从原始请求中获取查询参数
      const { startTime, endTime, detailIds, type, supplierCode } = req.query;

      if (!startTime || !endTime) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          code: 400,
          data: null,
          message: '起始日期和终止日期不能为空',
        });
      }

      // 构建导出参数
      const exportParams: any = {
        startTime:
          startTime.length === 10 ? `${startTime}T00:00:00.000Z` : startTime,
        endTime: endTime.length === 10 ? `${endTime}T23:59:59.999Z` : endTime,
      };

      if (detailIds) {
        const decodedDetailIds = decodeURIComponent(detailIds);
        exportParams.detailIds = decodedDetailIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);
      }

      if (type) {
        exportParams.type = type;
      }

      if (supplierCode) {
        exportParams.supplierCode = supplierCode;
      }

      const excelBuffer =
        await this.supplierArchivesService.exportToExcel(exportParams);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="supplier-archives-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }

  @Post('import/:type')
  @ApiOperation({ summary: '导入供应商档案明细Excel（需指定类型）' })
  @ApiParam({
    name: 'type',
    description: '档案类型',
    enum: SupplierArchiveType,
    example: SupplierArchiveType.PURCHASE,
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: '导入成功',
  })
  @ApiResponse({
    status: 400,
    description: '文件格式错误或数据验证失败',
  })
  @UseInterceptors(FileInterceptor('file'))
  async importFromExcel(
    @Param('type') type: SupplierArchiveType,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Importing supplier archive details from Excel with type: ${type}`,
    );

    if (!file) {
      return {
        code: 400,
        data: null,
        message: '请上传Excel文件',
      };
    }

    // 验证类型参数
    if (!Object.values(SupplierArchiveType).includes(type)) {
      return {
        code: 400,
        data: null,
        message: '档案类型参数无效',
      };
    }

    try {
      const result = await this.supplierArchivesService.importFromExcel(
        file.buffer,
        type,
      );

      return {
        code: 200,
        data: result,
        message: '导入完成',
      };
    } catch (error) {
      this.logger.error('Excel import failed', error);
      return {
        code: 400,
        data: null,
        message: `导入失败：${error.message}`,
      };
    }
  }

  @Get('import-template/:type')
  @ApiOperation({ summary: '下载供应商档案明细导入模板（需指定类型）' })
  @ApiParam({
    name: 'type',
    description: '档案类型',
    enum: SupplierArchiveType,
    example: SupplierArchiveType.PURCHASE,
  })
  @ApiResponse({
    status: 200,
    description: '模板文件下载成功',
  })
  async downloadImportTemplate(
    @Param('type') type: SupplierArchiveType,
    @Res() res: Response,
  ) {
    this.logger.log(
      `Downloading supplier archive import template for type: ${type}`,
    );

    // 验证类型参数
    if (!Object.values(SupplierArchiveType).includes(type)) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        code: 400,
        data: null,
        message: '档案类型参数无效',
      });
    }

    try {
      const excelBuffer =
        await this.supplierArchivesService.generateImportTemplate(type);

      const typeMap = {
        purchase: '采购',
        arrival: '到货',
        repair_send: '返厂修复',
        repair_arrival: '修复到货',
        payment: '支付',
      };

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="supplier-archive-${type}-template.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Template download failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: '模板下载失败',
      });
    }
  }

  // 新增供应商档案汇总相关接口

  @Get('archives')
  @ApiOperation({ summary: '分页查询供应商档案汇总列表' })
  @ApiQuery({
    name: 'page',
    description: '页码（必填）',
    example: 1,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量（必填）',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词（模糊搜索供应商编码和名称）',
    example: 'SUP001',
    required: false,
  })
  @ApiQuery({
    name: 'creditLevel',
    description: '信用等级筛选',
    example: 'A',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: '排序字段',
    example: 'overallScore',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: '排序方向',
    example: 'DESC',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllArchives(@Query() queryDto: QuerySupplierArchivesDto) {
    this.logger.log(
      `Querying supplier archives with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.supplierArchivesService.findAllArchives(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('archives/:supplierCode')
  @ApiOperation({ summary: '获取供应商档案汇总详情' })
  @ApiParam({
    name: 'supplierCode',
    description: '供应商编码',
    example: 'SUP001',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '供应商档案不存在',
  })
  async findArchiveBySupplierCode(@Param('supplierCode') supplierCode: string) {
    this.logger.log(`Finding supplier archive by code: ${supplierCode}`);

    const archive =
      await this.supplierArchivesService.findArchiveBySupplierCode(
        supplierCode,
      );

    return {
      code: 200,
      data: archive,
      message: '查询成功',
    };
  }

  @Patch('archives/:supplierCode/admin')
  @ApiOperation({ summary: '管理员更新供应商档案（超级管理员权限）' })
  @ApiParam({
    name: 'supplierCode',
    description: '供应商编码',
    example: 'SUP001',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '供应商档案不存在',
  })
  async adminUpdateArchive(
    @Param('supplierCode') supplierCode: string,
    @Body() updateDto: AdminUpdateSupplierArchiveDto,
  ) {
    this.logger.log(`Admin updating supplier archive ${supplierCode}`);

    await this.supplierArchivesService.adminUpdateArchive(
      supplierCode,
      updateDto,
    );

    return {
      code: 200,
      data: null,
      message: '供应商档案更新成功',
    };
  }

  @Get('archives/export/summary')
  @ApiOperation({ summary: '导出供应商档案汇总Excel' })
  @ApiQuery({
    name: 'supplierCodes',
    description: '供应商编码列表（逗号分隔）',
    example: 'SUP001,SUP002,SUP003',
    required: false,
  })
  @ApiQuery({
    name: 'creditLevel',
    description: '信用等级筛选',
    example: 'A',
    required: false,
  })
  @ApiQuery({
    name: 'exportType',
    description: '导出类型（summary/detailed）',
    example: 'summary',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  async exportArchiveSummaryToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Exporting supplier archive summary to Excel`);

    try {
      // 从原始请求中获取查询参数
      const { supplierCodes, creditLevel, exportType = 'summary' } = req.query;

      // 构建导出参数
      const exportParams: any = {
        exportType,
      };

      if (supplierCodes) {
        const decodedSupplierCodes = decodeURIComponent(supplierCodes);
        exportParams.supplierCodes = decodedSupplierCodes
          .split(',')
          .map((code: string) => code.trim())
          .filter((code: string) => code.length > 0);
      }

      if (creditLevel) {
        exportParams.creditLevel = creditLevel;
      }

      const excelBuffer =
        await this.supplierArchivesService.exportArchiveSummaryToExcel(
          exportParams,
        );

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="supplier-archive-summary-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }
}
