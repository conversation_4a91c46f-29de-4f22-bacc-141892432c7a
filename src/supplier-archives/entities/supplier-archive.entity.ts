import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { SupplierArchiveDetail } from './supplier-archive-detail.entity';

@Entity('supplier_archives')
export class SupplierArchive {
  @PrimaryColumn({
    type: 'varchar',
    length: 50,
    comment: '供应商编码（关联供应商表）',
  })
  supplierCode: string;

  @Column({ type: 'varchar', length: 100, comment: '供应商名称' })
  supplierName: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '总采购金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalPurchaseAmount: number;

  @Column({
    type: 'integer',
    default: 0,
    comment: '总采购数量',
  })
  totalPurchaseQuantity: number;

  @Column({
    type: 'integer',
    default: 0,
    comment: '总到货数量',
  })
  totalArrivalQuantity: number;

  @Column({
    type: 'integer',
    default: 0,
    comment: '总瑕疵数量',
  })
  totalDefectQuantity: number;

  @Column({
    type: 'integer',
    default: 0,
    comment: '总送修数量',
  })
  totalRepairQuantity: number;

  @Column({
    type: 'integer',
    default: 0,
    comment: '总修复到货数量',
  })
  totalRepairedQuantity: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '总支付金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalPaymentAmount: number;

  // 新增计算型统计字段
  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '瑕疵率（%）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  defectRate: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '返修率（%）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  repairRate: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '修复成功率（%）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  repairSuccessRate: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '订单完成率（%）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  orderCompletionRate: number;

  // 业务关键字段
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '平均单价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  averageUnitPrice: number;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '合作开始时间',
  })
  cooperationStartDate: Date | null;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '最后交易时间',
  })
  lastTransactionDate: Date | null;

  @Column({
    type: 'varchar',
    length: 10,
    default: 'C',
    comment: '信用等级（A/B/C/D）',
  })
  creditLevel: string;

  @Column({
    type: 'integer',
    default: 0,
    comment: '平均付款周期（天）',
  })
  paymentCycle: number;

  @Column({
    type: 'integer',
    default: 0,
    comment: '平均交货周期（天）',
  })
  deliveryCycle: number;

  // 质量管控字段
  @Column({
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 5.0,
    comment: '质量评分（1-10分）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  qualityScore: number;

  @Column({
    type: 'integer',
    default: 0,
    comment: '退货次数',
  })
  returnCount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '退货金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  returnAmount: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '退货率（%）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  returnRate: number;

  // 财务相关字段
  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '未结算金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  unsettledAmount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '预付款余额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  prepaymentBalance: number;

  @Column({
    type: 'integer',
    default: 0,
    comment: '平均账期（天）',
  })
  averagePaymentPeriod: number;

  // 供应商表现字段
  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '准时交货率（%）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  onTimeDeliveryRate: number;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 5.0,
    comment: '响应速度评分（1-10分）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  responseSpeedScore: number;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 5.0,
    comment: '服务态度评分（1-10分）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  serviceAttitudeScore: number;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 5.0,
    comment: '综合评分（1-10分）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  overallScore: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联供应商表
  @ManyToOne(() => Supplier)
  @JoinColumn({ name: 'supplierCode', referencedColumnName: 'code' })
  supplier: Supplier;

  // 关联供应商档案明细
  @OneToMany(() => SupplierArchiveDetail, (detail) => detail.supplierArchive)
  details: SupplierArchiveDetail[];
}
