import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { SupplierArchive } from './supplier-archive.entity';

// 供应商档案类型枚举
export enum SupplierArchiveType {
  PURCHASE = 'purchase', // 采购
  ARRIVAL = 'arrival', // 到货
  REPAIR_SEND = 'repair_send', // 返厂修复
  REPAIR_ARRIVAL = 'repair_arrival', // 修复到货
  PAYMENT = 'payment', // 支付
}

@Entity('supplier_archive_details')
export class SupplierArchiveDetail {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50, comment: '供应商编码' })
  supplierCode: string;

  @Column({
    type: 'enum',
    enum: SupplierArchiveType,
    comment: '档案类型：采购、到货、返厂修复、修复到货',
  })
  type: SupplierArchiveType;

  // 采购类型字段
  @Column({
    type: 'integer',
    nullable: true,
    comment: '商品总数量（采购类型必填）',
  })
  totalQuantity: number | null;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: '商品总金额（采购类型必填）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalAmount: number | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '采购订单ID（采购类型选填）',
  })
  purchaseOrderId: string | null;

  // 到货类型字段
  @Column({
    type: 'integer',
    nullable: true,
    comment: '实际到货数量（到货类型必填）',
  })
  actualArrivalQuantity: number | null;

  @Column({
    type: 'integer',
    nullable: true,
    comment: '瑕疵数量（到货类型必填）',
  })
  defectQuantity: number | null;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '物流订单ID（到货、返厂修复、修复到货类型选填）',
  })
  logisticsOrderId: string | null;

  // 返厂修复类型字段
  @Column({
    type: 'integer',
    nullable: true,
    comment: '送修商品数量（返厂修复类型必填）',
  })
  repairQuantity: number | null;

  // 修复到货类型字段
  @Column({
    type: 'integer',
    nullable: true,
    comment: '实际修复商品数（修复到货类型必填）',
  })
  actualRepairedQuantity: number | null;

  @Column({
    type: 'integer',
    nullable: true,
    comment: '总到货数（修复到货类型必填）',
  })
  totalArrivalQuantity: number | null;

  // 支付类型字段
  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: '支付金额（支付类型必填）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  paymentAmount: number | null;

  // 图片字段（所有类型选填）
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '相关图片URL（所有类型选填）',
  })
  imageUrl: string | null;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  remark: string | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联供应商档案主表
  @ManyToOne(() => SupplierArchive, (archive) => archive.details)
  @JoinColumn({ name: 'supplierCode', referencedColumnName: 'supplierCode' })
  supplierArchive: SupplierArchive;
}
