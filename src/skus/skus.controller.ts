import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Logger,
  UseGuards,
  Query,
  UseInterceptors,
  UploadedFile,
  Res,
  HttpStatus,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { SkusService } from './skus.service';
import { CreateSkuDto } from './dto/create-sku.dto';
import { UpdateSkuDto } from './dto/update-sku.dto';
import { QuerySkusDto } from './dto/query-skus.dto';
import { SkuResponseDto } from './dto/sku-response.dto';
import { SkuImportResponseDto } from './dto/import-sku.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('SKUs管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('skus')
export class SkusController {
  private readonly logger = new Logger(SkusController.name);

  constructor(private readonly skusService: SkusService) {}

  @Post()
  @ApiOperation({
    summary: '创建SKU',
    description: '创建新的SKU，系统会自动生成SKU编码',
  })
  @ApiResponse({
    status: 200,
    description: 'SKU创建成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'SKU创建成功' },
        data: { type: 'null' },
      },
    },
  })
  async create(@Body() createSkuDto: CreateSkuDto) {
    this.logger.log(`Creating SKU ${createSkuDto.name || '(无名称)'}`);
    return await this.skusService.create(createSkuDto);
  }

  @Get()
  @ApiOperation({
    summary: '获取SKU列表',
    description: '获取所有未删除的SKU列表',
  })
  @ApiResponse({
    status: 200,
    description: '获取SKU列表成功',
  })
  async findAll() {
    this.logger.log('Getting all SKUs');
    const skus = await this.skusService.findAll();
    return {
      code: 200,
      message: '获取SKU列表成功',
      data: skus,
    };
  }

  @Get('paginated')
  @ApiOperation({
    summary: '分页查询SKU列表',
    description: '支持多种搜索条件的分页查询SKU列表，返回辅料成本和总成本',
  })
  @ApiResponse({
    status: 200,
    description: '获取SKU分页列表成功',
    type: SkuResponseDto,
  })
  async findAllPaginated(@Query() queryDto: QuerySkusDto) {
    this.logger.log(
      `Getting paginated SKUs with params: ${JSON.stringify(queryDto)}`,
    );
    const result = await this.skusService.findAllPaginated(queryDto);
    return {
      code: 200,
      message: '获取SKU分页列表成功',
      data: result,
    };
  }

  @Get(':id')
  @ApiOperation({
    summary: '获取SKU详情',
    description: '根据ID获取SKU详细信息',
  })
  @ApiResponse({
    status: 200,
    description: '获取SKU详情成功',
  })
  async findOne(@Param('id') id: string) {
    this.logger.log(`Getting SKU ${id}`);
    const sku = await this.skusService.findOne(id);
    return {
      code: 200,
      message: '获取SKU详情成功',
      data: sku,
    };
  }

  @Patch(':id')
  @ApiOperation({
    summary: '更新SKU',
    description: '更新SKU信息（不包括编码）',
  })
  @ApiResponse({
    status: 200,
    description: 'SKU更新成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'SKU更新成功' },
        data: { type: 'null' },
      },
    },
  })
  async update(@Param('id') id: string, @Body() updateSkuDto: UpdateSkuDto) {
    this.logger.log(`Updating SKU ${id}`);
    return await this.skusService.update(id, updateSkuDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: '删除SKU',
    description: '软删除SKU（标记为已删除）',
  })
  @ApiResponse({
    status: 200,
    description: 'SKU删除成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'SKU删除成功' },
        data: { type: 'null' },
      },
    },
  })
  async remove(@Param('id') id: string) {
    this.logger.log(`Deleting SKU ${id}`);
    return await this.skusService.remove(id);
  }

  @Get('export/excel')
  @ApiOperation({
    summary: '导出SKU数据为Excel（含图片下载）',
    description:
      '支持按品牌、供应商筛选或指定SKU ID列表导出，包含图片下载，限制50条数据',
  })
  @ApiQuery({
    name: 'brandCodeSearch',
    description: '品牌编码筛选',
    example: 'BRAND001',
    required: false,
  })
  @ApiQuery({
    name: 'supplierCodeSearch',
    description: '供应商编码筛选',
    example: 'SUP001',
    required: false,
  })
  @ApiQuery({
    name: 'skuIds',
    description: 'SKU ID列表（逗号分隔）',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  async exportToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log('Exporting SKUs to Excel');

    try {
      // 从原始请求中获取查询参数
      const { brandCodeSearch, supplierCodeSearch, skuIds } = req.query;

      // 构建导出参数
      const exportParams: any = {};

      if (brandCodeSearch) {
        exportParams.brandCodeSearch = brandCodeSearch;
      }

      if (supplierCodeSearch) {
        exportParams.supplierCodeSearch = supplierCodeSearch;
      }

      if (skuIds) {
        const decodedSkuIds = decodeURIComponent(skuIds);
        exportParams.skuIds = decodedSkuIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);
      }

      const excelBuffer = await this.skusService.exportToExcel(exportParams);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="skus-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }

  @Get('import/template')
  @ApiOperation({
    summary: '下载SKU导入模板',
    description: '下载Excel导入模板文件',
  })
  @ApiResponse({
    status: 200,
    description: '模板文件下载成功',
  })
  async downloadImportTemplate(@Res() res: Response) {
    this.logger.log('Generating SKU import template');

    const templateBuffer = await this.skusService.generateImportTemplate();

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': 'attachment; filename="sku_import_template.xlsx"',
      'Content-Length': templateBuffer.length.toString(),
    });

    res.send(templateBuffer);
  }

  @Post('import/excel')
  @ApiOperation({
    summary: '从Excel导入SKU数据',
    description: '从Excel文件导入SKU数据，颜色使用汉字，品牌和供应商使用编码',
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: '导入完成',
    type: SkuImportResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: '文件格式错误或数据验证失败',
  })
  @UseInterceptors(FileInterceptor('file'))
  async importFromExcel(@UploadedFile() file: Express.Multer.File) {
    this.logger.log(`Importing SKUs from Excel: ${file?.originalname}`);

    if (!file) {
      return {
        code: 400,
        message: '请选择要导入的Excel文件',
        data: null,
      };
    }

    const result = await this.skusService.importFromExcel(file);

    return {
      code: 200,
      message: 'SKU导入完成',
      data: result,
    };
  }

  @Get('debug/accessory/:code')
  @ApiOperation({
    summary: '调试辅料信息',
    description: '检查指定编码的辅料是否存在',
  })
  async debugAccessory(@Param('code') code: string) {
    this.logger.log(`Debugging accessory: ${code}`);
    const accessory = await this.skusService.debugAccessory(code);
    return {
      code: 200,
      message: '调试完成',
      data: accessory,
    };
  }
}
