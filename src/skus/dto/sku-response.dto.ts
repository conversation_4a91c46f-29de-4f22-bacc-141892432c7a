import { ApiProperty } from '@nestjs/swagger';
import { Sku } from '../entities/sku.entity';

export class SkuListItemDto {
  @ApiProperty({
    description: 'SKU ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'SKU编码',
    example: '256770101',
  })
  code: string;

  @ApiProperty({
    description: '商品名称',
    example: '时尚T恤',
    nullable: true,
  })
  name: string | null;

  @ApiProperty({
    description: '厂商编码',
    example: 'MFG001',
    nullable: true,
  })
  manufacturerCode: string | null;

  @ApiProperty({
    description: '品牌编码',
    example: 'BRAND001',
  })
  brandCode: string;

  @ApiProperty({
    description: '品牌名称',
    example: '知名品牌',
  })
  brandName: string;

  @ApiProperty({
    description: '供应商编码',
    example: 'SUP001',
  })
  supplierCode: string;

  @ApiProperty({
    description: '供应商名称',
    example: '优质供应商',
  })
  supplierName: string;

  @ApiProperty({
    description: '商品分类编码',
    example: 'CAT001',
  })
  categoryCode: string;

  @ApiProperty({
    description: '商品分类名称',
    example: '服装',
  })
  categoryName: string;

  @ApiProperty({
    description: '颜色编码',
    example: 'COLOR001',
  })
  colorCode: string;

  @ApiProperty({
    description: '颜色名称',
    example: '红色',
  })
  colorName: string;

  @ApiProperty({
    description: '工艺描述',
    example: '数码印花+手工刺绣',
    nullable: true,
  })
  craftDescription: string | null;

  @ApiProperty({
    description: '服装成本',
    example: 50.0,
    nullable: true,
  })
  clothingCost: number | null;

  @ApiProperty({
    description: '零售价',
    example: 120.0,
    nullable: true,
  })
  retailPrice: number | null;

  @ApiProperty({
    description: '预订价',
    example: 100.0,
    nullable: true,
  })
  preOrderPrice: number | null;

  @ApiProperty({
    description: '补货价',
    example: 110.0,
    nullable: true,
  })
  restockPrice: number | null;

  @ApiProperty({
    description: '现货价',
    example: 115.0,
    nullable: true,
  })
  spotPrice: number | null;

  @ApiProperty({
    description: '辅料成本',
    example: 15.5,
  })
  accessoryCost: number;

  @ApiProperty({
    description: '总成本',
    example: 65.5,
  })
  totalCost: number;

  @ApiProperty({
    description: 'SKU图片数组',
    example: ['https://example.com/sku-image1.jpg'],
    nullable: true,
  })
  images: string[] | null;

  @ApiProperty({
    description: '创建时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class SkuListResponseDto {
  @ApiProperty({
    description: 'SKU列表',
    type: [SkuListItemDto],
  })
  skus: SkuListItemDto[];

  @ApiProperty({
    description: '总数量',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: '当前页码',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: '每页数量',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: '总页数',
    example: 10,
  })
  totalPages: number;
}

export class SkuResponseDto {
  @ApiProperty({
    description: '响应状态码',
    example: 200,
  })
  code: number;

  @ApiProperty({
    description: '响应消息',
    example: '获取SKU列表成功',
  })
  message: string;

  @ApiProperty({
    description: '响应数据',
    type: SkuListResponseDto,
  })
  data: SkuListResponseDto;
}
