import { ApiProperty } from '@nestjs/swagger';

export class SkuImportResult {
  @ApiProperty({
    description: '成功导入的数量',
    example: 5,
  })
  successCount: number;

  @ApiProperty({
    description: '导入失败的数量',
    example: 2,
  })
  failureCount: number;

  @ApiProperty({
    description: '错误信息列表',
    example: ['第3行：品牌编码BRAND999不存在', '第5行：供应商编码SUP999不存在'],
    type: [String],
  })
  errors: string[];

  @ApiProperty({
    description: '成功导入的SKU编码列表',
    example: ['256770101', '256770102', '256770103'],
    type: [String],
  })
  successCodes: string[];
}

export class SkuImportResponseDto {
  @ApiProperty({
    description: '响应状态码',
    example: 200,
  })
  code: number;

  @ApiProperty({
    description: '响应消息',
    example: 'SKU导入完成',
  })
  message: string;

  @ApiProperty({
    description: '导入结果',
    type: SkuImportResult,
  })
  data: SkuImportResult;
}
