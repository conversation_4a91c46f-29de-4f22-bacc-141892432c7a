import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

// 辅料数量DTO
export class AccessoryQuantityDto {
  @ApiProperty({
    description: '辅料编码',
    example: 'ACC001',
  })
  @IsString()
  @IsNotEmpty({ message: '辅料编码不能为空' })
  accessoryCode: string;

  @ApiProperty({
    description: '辅料数量',
    example: 2,
    minimum: 1,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '辅料数量必须是数字' })
  @Min(1, { message: '辅料数量不能小于1' })
  quantity: number;
}

export class CreateSkuDto {
  // 注意：SKU编码将由后端自动生成，不需要前端传入

  @ApiProperty({
    description: '商品名称（选填）- 产品的显示名称，用于前端展示和搜索',
    example: '时尚T恤',
    required: false,
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: '厂商编码（选填）- 生产厂商的编码字符串，用于标识产品的生产方',
    example: 'MFG001',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  manufacturerCode?: string;

  @ApiProperty({
    description: '品牌编码（必填）- 关联品牌表的编码，用于SKU编码生成',
    example: 'BRAND001',
    required: true,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty({ message: '品牌编码不能为空' })
  brandCode: string;

  @ApiProperty({
    description: '供应商编码（必填）- 关联供应商表的编码，用于SKU编码生成',
    example: 'SUP001',
    required: true,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty({ message: '供应商编码不能为空' })
  supplierCode: string;

  @ApiProperty({
    description: '商品分类编码（必填）- 关联产品分类表的编码，用于SKU编码生成',
    example: 'CAT001',
    required: true,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty({ message: '商品分类编码不能为空' })
  categoryCode: string;

  @ApiProperty({
    description:
      '颜色编码（必填）- 关联颜色表的编码，每个SKU只有一个颜色，用于SKU编码生成',
    example: 'COLOR001',
    required: true,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty({ message: '颜色编码不能为空' })
  colorCode: string;

  @ApiProperty({
    description: '工艺描述（选填）- 产品的制作工艺说明',
    example: '数码印花+手工刺绣',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  craftDescription?: string;

  @ApiProperty({
    description:
      '服装成本（选填）- 产品的基础服装制作成本，用于成本核算和利润计算',
    example: 50.0,
    required: false,
    type: 'number',
    format: 'decimal',
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '服装成本必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '服装成本不能小于0' })
  clothingCost?: number;

  @ApiProperty({
    description: '零售价（选填）- 产品的基础零售价格，面向终端消费者的销售价格',
    example: 120.0,
    required: false,
    type: 'number',
    format: 'decimal',
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '零售价必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '零售价不能小于0' })
  retailPrice?: number;

  @ApiProperty({
    description: '预订价（选填）- 产品的预订销售价格，通常用于预售或批量订购',
    example: 100.0,
    required: false,
    type: 'number',
    format: 'decimal',
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '预订价必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '预订价不能小于0' })
  preOrderPrice?: number;

  @ApiProperty({
    description: '补货价（选填）- 产品的补货销售价格，用于库存补充时的定价',
    example: 110.0,
    required: false,
    type: 'number',
    format: 'decimal',
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '补货价必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '补货价不能小于0' })
  restockPrice?: number;

  @ApiProperty({
    description: '现货价（选填）- 产品的现货销售价格，用于现有库存的即时销售',
    example: 115.0,
    required: false,
    type: 'number',
    format: 'decimal',
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '现货价必须是数字且最多保留两位小数' },
  )
  @Min(0, { message: '现货价不能小于0' })
  spotPrice?: number;

  @ApiProperty({
    description: '辅料数组（选填）- 产品使用的辅料配置，如标签、包装袋等材料',
    example: [
      { accessoryCode: 'ACC001', quantity: 2 }, // 标签
      { accessoryCode: 'ACC002', quantity: 1 }, // 包装袋
    ],
    required: false,
    type: [AccessoryQuantityDto],
    isArray: true,
  })
  @IsOptional()
  @IsArray({ message: '辅料必须是数组格式' })
  @ValidateNested({ each: true })
  @Type(() => AccessoryQuantityDto)
  accessories?: AccessoryQuantityDto[];

  @ApiProperty({
    description: 'SKU图片数组（选填）- 存储SKU对应的图片URL列表',
    example: [
      'https://example.com/sku-image1.jpg',
      'https://example.com/sku-image2.jpg',
    ],
    required: false,
    type: [String],
    isArray: true,
  })
  @IsOptional()
  @IsArray({ message: '图片必须是数组格式' })
  @IsString({ each: true, message: '每个图片URL必须是字符串' })
  images?: string[];
}
