import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsInt, Min, IsString } from 'class-validator';

export class QuerySkusDto {
  @ApiProperty({
    description: '页码（必填）',
    example: 1,
    minimum: 1,
  })
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码不能小于1' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填）',
    example: 10,
    minimum: 1,
  })
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量不能小于1' })
  pageSize: number;

  @ApiProperty({
    description: 'SKU名称搜索（选填）',
    example: 'T恤',
    required: false,
  })
  @IsOptional()
  @IsString()
  nameSearch?: string;

  @ApiProperty({
    description: 'SKU编码搜索（选填）',
    example: '256770101',
    required: false,
  })
  @IsOptional()
  @IsString()
  codeSearch?: string;

  @ApiProperty({
    description: '品牌编码筛选（选填）',
    example: 'BRAND001',
    required: false,
  })
  @IsOptional()
  @IsString()
  brandCodeSearch?: string;

  @ApiProperty({
    description: '供应商编码筛选（选填）',
    example: 'SUP001',
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierCodeSearch?: string;

  @ApiProperty({
    description: '商品分类编码筛选（选填）',
    example: 'CAT001',
    required: false,
  })
  @IsOptional()
  @IsString()
  categoryCodeSearch?: string;

  @ApiProperty({
    description: '颜色编码筛选（选填）',
    example: 'COLOR001',
    required: false,
  })
  @IsOptional()
  @IsString()
  colorCodeSearch?: string;
}
