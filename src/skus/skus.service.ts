import {
  Injectable,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { CreateSkuDto } from './dto/create-sku.dto';
import { UpdateSkuDto } from './dto/update-sku.dto';
import { QuerySkusDto } from './dto/query-skus.dto';
import { SkuListResponseDto, SkuListItemDto } from './dto/sku-response.dto';
import { SkuImportResult } from './dto/import-sku.dto';
import { Sku } from './entities/sku.entity';
import { Brand } from '@/brands/entities/brand.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { ProductCategory } from '@/product-categories/entities/product-category.entity';
import { Color } from '@/colors/entities/color.entity';
import { Accessory } from '@/accessories/entities/accessory.entity';
import { SkuInventory } from '@/skus-inventory/entities/sku-inventory.entity';
import * as ExcelJS from 'exceljs';
import axios from 'axios';

@Injectable()
export class SkusService {
  private readonly logger = new Logger(SkusService.name);

  constructor(
    @InjectRepository(Sku)
    private skusRepository: Repository<Sku>,
    @InjectRepository(Brand)
    private brandsRepository: Repository<Brand>,
    @InjectRepository(Supplier)
    private suppliersRepository: Repository<Supplier>,
    @InjectRepository(Color)
    private colorsRepository: Repository<Color>,
    @InjectRepository(Accessory)
    private accessoriesRepository: Repository<Accessory>,
    @InjectRepository(ProductCategory)
    private productCategoriesRepository: Repository<ProductCategory>,
    @InjectRepository(SkuInventory)
    private skuInventoryRepository: Repository<SkuInventory>,
    private dataSource: DataSource,
  ) {}

  /**
   * 创建SKU（自动生成SKU编码）
   */
  async create(createSkuDto: CreateSkuDto) {
    this.logger.log(
      `Creating SKU for brand: ${createSkuDto.brandCode}, supplier: ${createSkuDto.supplierCode}, color: ${createSkuDto.colorCode}`,
    );

    // 自动生成SKU编码
    const skuCode = await this.generateSkuCode(
      createSkuDto.brandCode,
      createSkuDto.supplierCode,
      createSkuDto.categoryCode,
      createSkuDto.colorCode,
    );

    this.logger.log(`Generated SKU code: ${skuCode}`);

    // 验证品牌是否存在
    const brand = await this.brandsRepository.findOne({
      where: { code: createSkuDto.brandCode, isDeleted: false },
    });
    if (!brand) {
      throw new BadRequestException(`品牌 ${createSkuDto.brandCode} 不存在`);
    }

    // 验证供应商是否存在
    const supplier = await this.suppliersRepository.findOne({
      where: { code: createSkuDto.supplierCode, isDeleted: false },
    });
    if (!supplier) {
      throw new BadRequestException(
        `供应商 ${createSkuDto.supplierCode} 不存在`,
      );
    }

    // 验证商品分类是否存在
    const category = await this.productCategoriesRepository.findOne({
      where: { code: createSkuDto.categoryCode, isDeleted: false },
    });
    if (!category) {
      throw new BadRequestException(
        `商品分类 ${createSkuDto.categoryCode} 不存在`,
      );
    }

    // 验证颜色是否存在
    const color = await this.colorsRepository.findOne({
      where: { code: createSkuDto.colorCode, isDeleted: false },
    });
    if (!color) {
      throw new BadRequestException(`颜色 ${createSkuDto.colorCode} 不存在`);
    }

    // 验证辅料是否存在（如果有提供）
    if (createSkuDto.accessories && createSkuDto.accessories.length > 0) {
      const accessoryCodes = createSkuDto.accessories.map(
        (acc) => acc.accessoryCode,
      );
      const accessories = await this.accessoriesRepository.find({
        where: accessoryCodes.map((code) => ({
          articleNumber: code,
          isDeleted: false,
        })),
      });

      const foundAccessoryCodes = accessories.map((acc) => acc.articleNumber);
      const missingAccessories = accessoryCodes.filter(
        (code) => !foundAccessoryCodes.includes(code),
      );

      if (missingAccessories.length > 0) {
        throw new BadRequestException(
          `辅料不存在: ${missingAccessories.join(', ')}`,
        );
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 创建SKU
      const sku = this.skusRepository.create({
        code: skuCode,
        name: createSkuDto.name,
        manufacturerCode: createSkuDto.manufacturerCode || null,
        brandCode: createSkuDto.brandCode,
        supplierCode: createSkuDto.supplierCode,
        categoryCode: createSkuDto.categoryCode,
        colorCode: createSkuDto.colorCode,
        craftDescription: createSkuDto.craftDescription || null,
        clothingCost: createSkuDto.clothingCost || null,
        retailPrice: createSkuDto.retailPrice || null,
        preOrderPrice: createSkuDto.preOrderPrice || null,
        restockPrice: createSkuDto.restockPrice || null,
        spotPrice: createSkuDto.spotPrice || null,
        accessories: createSkuDto.accessories || null,
        images: createSkuDto.images || null,
        isDeleted: false,
      });

      const savedSku = await queryRunner.manager.save(sku);

      // 自动为该SKU的所有尺码创建初始库存记录（全部为0）
      await this.createInitialInventoryRecords(savedSku, category, queryRunner);

      await queryRunner.commitTransaction();

      this.logger.log(`SKU created successfully: ${savedSku.code}`);

      return {
        code: 200,
        message: 'SKU创建成功',
        data: null,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Failed to create SKU', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 自动生成SKU编码
   * 格式：品牌编码(3位) + 供应商编码(3位) + 分类编码(2位) + 颜色编码(2位) + 序列号(3位)
   */
  private async generateSkuCode(
    brandCode: string,
    supplierCode: string,
    categoryCode: string,
    colorCode: string,
  ): Promise<string> {
    // 验证并获取编码
    const brand = await this.brandsRepository.findOne({
      where: { code: brandCode, isDeleted: false },
    });
    if (!brand) {
      throw new BadRequestException(`品牌 ${brandCode} 不存在`);
    }

    const supplier = await this.suppliersRepository.findOne({
      where: { code: supplierCode, isDeleted: false },
    });
    if (!supplier) {
      throw new BadRequestException(`供应商 ${supplierCode} 不存在`);
    }

    const category = await this.productCategoriesRepository.findOne({
      where: { code: categoryCode, isDeleted: false },
    });
    if (!category) {
      throw new BadRequestException(`商品分类 ${categoryCode} 不存在`);
    }

    const color = await this.colorsRepository.findOne({
      where: { code: colorCode, isDeleted: false },
    });
    if (!color) {
      throw new BadRequestException(`颜色 ${colorCode} 不存在`);
    }

    // 确保编码长度符合要求
    const brandCodePart = brandCode.padStart(3, '0').substring(0, 3);
    const supplierCodePart = supplierCode.padStart(3, '0').substring(0, 3);
    const categoryCodePart = categoryCode.padStart(2, '0').substring(0, 2);
    const colorCodePart = colorCode.padStart(2, '0').substring(0, 2);

    // 生成基础SKU编码（不含序列号）
    const baseSkuCode = `${brandCodePart}${supplierCodePart}${categoryCodePart}${colorCodePart}`;

    // 查找以该基础编码开头的最大序列号（包括软删除的记录，因为数据库唯一约束对所有记录有效）
    const existingSkus = await this.skusRepository
      .createQueryBuilder('sku')
      .where('sku.code LIKE :baseCode', { baseCode: `${baseSkuCode}%` })
      .orderBy('sku.code', 'DESC')
      .getMany();

    let nextSequence = 1;
    if (existingSkus.length > 0) {
      // 提取所有现有编码的序列号部分
      const sequences = existingSkus
        .map((sku) => {
          const code = sku.code;
          // 如果编码长度等于基础编码长度，说明是旧格式，视为序列号001
          if (code.length === baseSkuCode.length) {
            return 1;
          }
          // 如果编码长度大于基础编码长度，提取序列号部分
          if (
            code.length > baseSkuCode.length &&
            code.startsWith(baseSkuCode)
          ) {
            const sequencePart = code.substring(baseSkuCode.length);
            const sequenceNum = parseInt(sequencePart, 10);
            return isNaN(sequenceNum) ? 0 : sequenceNum;
          }
          return 0;
        })
        .filter((seq) => seq > 0);

      if (sequences.length > 0) {
        nextSequence = Math.max(...sequences) + 1;
      }
    }

    // 查找下一个不包含数字4且在数据库中唯一的序列号
    let finalSkuCode: string;
    let attempts = 0;
    const maxAttempts = 1000; // 防止无限循环

    do {
      // 跳过包含数字4的序列号
      while (this.containsDigit4(nextSequence)) {
        nextSequence++;
      }

      // 生成3位序列号（不足补0）
      const sequenceStr = nextSequence.toString().padStart(3, '0');
      finalSkuCode = `${baseSkuCode}${sequenceStr}`;

      // 检查编码是否在数据库中已存在（包括软删除的记录）
      const existingSkuWithCode = await this.skusRepository.findOne({
        where: { code: finalSkuCode },
        withDeleted: true, // 包括软删除的记录
      });

      if (!existingSkuWithCode) {
        // 编码唯一，可以使用
        break;
      }

      // 编码已存在，尝试下一个序列号
      nextSequence++;
      attempts++;

      if (attempts >= maxAttempts) {
        throw new BadRequestException(
          `无法生成唯一的SKU编码，已尝试${maxAttempts}次`,
        );
      }
    } while (true);

    this.logger.log(
      `Generated unique SKU code: ${finalSkuCode} (base: ${baseSkuCode}, attempts: ${attempts + 1})`,
    );

    return finalSkuCode;
  }

  /**
   * 检查数字是否包含数字4
   */
  private containsDigit4(num: number): boolean {
    return num.toString().includes('4');
  }

  async findAll() {
    return await this.skusRepository.find({
      where: { isDeleted: false },
      relations: ['brand', 'supplier', 'category', 'color'],
    });
  }

  async findOne(id: string) {
    const sku = await this.skusRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['brand', 'supplier', 'category', 'color'],
    });

    if (!sku) {
      throw new NotFoundException('SKU不存在');
    }

    return sku;
  }

  async update(id: string, updateSkuDto: UpdateSkuDto) {
    const sku = await this.findOne(id);

    // 验证品牌编码是否存在（如果有更新）
    if (updateSkuDto.brandCode) {
      const brand = await this.brandsRepository.findOne({
        where: { code: updateSkuDto.brandCode, isDeleted: false },
      });
      if (!brand) {
        throw new BadRequestException(`品牌 ${updateSkuDto.brandCode} 不存在`);
      }
    }

    // 验证供应商编码是否存在（如果有更新）
    if (updateSkuDto.supplierCode) {
      const supplier = await this.suppliersRepository.findOne({
        where: { code: updateSkuDto.supplierCode, isDeleted: false },
      });
      if (!supplier) {
        throw new BadRequestException(
          `供应商 ${updateSkuDto.supplierCode} 不存在`,
        );
      }
    }

    // 验证商品分类编码是否存在（如果有更新）
    if (updateSkuDto.categoryCode) {
      const category = await this.productCategoriesRepository.findOne({
        where: { code: updateSkuDto.categoryCode, isDeleted: false },
      });
      if (!category) {
        throw new BadRequestException(
          `商品分类 ${updateSkuDto.categoryCode} 不存在`,
        );
      }
    }

    // 验证颜色编码是否存在（如果有更新）
    if (updateSkuDto.colorCode) {
      const color = await this.colorsRepository.findOne({
        where: { code: updateSkuDto.colorCode, isDeleted: false },
      });
      if (!color) {
        throw new BadRequestException(`颜色 ${updateSkuDto.colorCode} 不存在`);
      }
    }

    // 验证辅料编码是否存在（如果有辅料配置）
    if (updateSkuDto.accessories && updateSkuDto.accessories.length > 0) {
      for (const accessory of updateSkuDto.accessories) {
        const accessoryEntity = await this.accessoriesRepository.findOne({
          where: { articleNumber: accessory.accessoryCode, isDeleted: false },
        });
        if (!accessoryEntity) {
          throw new BadRequestException(
            `辅料编码 ${accessory.accessoryCode} 不存在`,
          );
        }
      }
    }

    // 更新SKU信息（不包括编码）
    Object.assign(sku, updateSkuDto);
    await this.skusRepository.save(sku);

    this.logger.log(
      `SKU updated successfully: ${sku.code}, updated fields: ${JSON.stringify(Object.keys(updateSkuDto))}`,
    );

    return {
      code: 200,
      message: 'SKU更新成功',
      data: null,
    };
  }

  async remove(id: string) {
    const sku = await this.findOne(id);
    sku.isDeleted = true;
    await this.skusRepository.save(sku);

    return {
      code: 200,
      message: 'SKU删除成功',
      data: null,
    };
  }

  /**
   * 分页查询SKU列表（支持多种搜索条件）
   */
  async findAllPaginated(queryDto: QuerySkusDto): Promise<SkuListResponseDto> {
    this.logger.log(`Querying SKUs with params: ${JSON.stringify(queryDto)}`);

    const {
      page,
      pageSize,
      nameSearch,
      codeSearch,
      brandCodeSearch,
      supplierCodeSearch,
      categoryCodeSearch,
      colorCodeSearch,
    } = queryDto;

    // 构建查询条件
    const queryBuilder = this.skusRepository
      .createQueryBuilder('sku')
      .leftJoinAndSelect('sku.brand', 'brand')
      .leftJoinAndSelect('sku.supplier', 'supplier')
      .leftJoinAndSelect('sku.category', 'category')
      .leftJoinAndSelect('sku.color', 'color')
      .where('sku.isDeleted = :isDeleted', { isDeleted: false });

    // 添加搜索条件
    if (nameSearch) {
      queryBuilder.andWhere('sku.name ILIKE :nameSearch', {
        nameSearch: `%${nameSearch}%`,
      });
    }

    if (codeSearch) {
      queryBuilder.andWhere('sku.code ILIKE :codeSearch', {
        codeSearch: `%${codeSearch}%`,
      });
    }

    if (brandCodeSearch) {
      queryBuilder.andWhere('sku.brandCode = :brandCodeSearch', {
        brandCodeSearch,
      });
    }

    if (supplierCodeSearch) {
      queryBuilder.andWhere('sku.supplierCode = :supplierCodeSearch', {
        supplierCodeSearch,
      });
    }

    if (categoryCodeSearch) {
      queryBuilder.andWhere('sku.categoryCode = :categoryCodeSearch', {
        categoryCodeSearch,
      });
    }

    if (colorCodeSearch) {
      queryBuilder.andWhere('sku.colorCode = :colorCodeSearch', {
        colorCodeSearch,
      });
    }

    // 分页
    const offset = (page - 1) * pageSize;
    queryBuilder.skip(offset).take(pageSize);

    // 排序
    queryBuilder.orderBy('sku.createdAt', 'DESC');

    // 执行查询
    const [skus, total] = await queryBuilder.getManyAndCount();

    // 计算辅料成本和总成本
    const skuListItems: SkuListItemDto[] = await Promise.all(
      skus.map(async (sku) => {
        const accessoryCost = await this.calculateAccessoryCost(
          sku.accessories,
        );
        const totalCost = (sku.clothingCost || 0) + accessoryCost;

        return {
          id: sku.id,
          code: sku.code,
          name: sku.name,
          manufacturerCode: sku.manufacturerCode,
          brandCode: sku.brandCode,
          brandName: sku.brand?.name || '',
          supplierCode: sku.supplierCode,
          supplierName: sku.supplier?.name || '',
          categoryCode: sku.categoryCode,
          categoryName: sku.category?.name || '',
          colorCode: sku.colorCode,
          colorName: sku.color?.name || '',
          craftDescription: sku.craftDescription,
          clothingCost: sku.clothingCost,
          retailPrice: sku.retailPrice,
          preOrderPrice: sku.preOrderPrice,
          restockPrice: sku.restockPrice,
          spotPrice: sku.spotPrice,
          accessoryCost,
          totalCost,
          images: sku.images,
          createdAt: sku.createdAt,
          updatedAt: sku.updatedAt,
        };
      }),
    );

    const totalPages = Math.ceil(total / pageSize);

    return {
      skus: skuListItems,
      total,
      page,
      pageSize,
      totalPages,
    };
  }

  /**
   * 计算辅料成本
   */
  private async calculateAccessoryCost(
    accessories: { accessoryCode: string; quantity: number }[] | null,
  ): Promise<number> {
    if (!accessories || accessories.length === 0) {
      return 0;
    }

    let totalCost = 0;

    for (const accessory of accessories) {
      const accessoryEntity = await this.accessoriesRepository.findOne({
        where: { articleNumber: accessory.accessoryCode, isDeleted: false },
      });

      if (accessoryEntity) {
        const itemCost = accessoryEntity.costPrice * accessory.quantity;
        totalCost += itemCost;
        this.logger.debug(
          `Accessory ${accessory.accessoryCode}: price=${accessoryEntity.costPrice}, quantity=${accessory.quantity}, cost=${itemCost}`,
        );
      } else {
        this.logger.warn(`Accessory not found: ${accessory.accessoryCode}`);
      }
    }

    this.logger.debug(`Total accessory cost: ${totalCost}`);
    return totalCost;
  }

  /**
   * 调试方法：检查辅料是否存在
   */
  async debugAccessory(accessoryCode: string) {
    const accessoryEntity = await this.accessoriesRepository.findOne({
      where: { articleNumber: accessoryCode, isDeleted: false },
    });

    this.logger.log(
      `Debug accessory ${accessoryCode}: ${accessoryEntity ? 'FOUND' : 'NOT FOUND'}`,
    );
    if (accessoryEntity) {
      this.logger.log(
        `Accessory details: ${JSON.stringify({
          id: accessoryEntity.id,
          articleNumber: accessoryEntity.articleNumber,
          name: accessoryEntity.name,
          costPrice: accessoryEntity.costPrice,
          isDeleted: accessoryEntity.isDeleted,
        })}`,
      );
    }

    return accessoryEntity;
  }

  /**
   * 导出SKU数据为Excel（包含图片下载，限制50条）
   */
  async exportToExcel(queryParams?: {
    brandCodeSearch?: string;
    supplierCodeSearch?: string;
    skuIds?: string[];
  }): Promise<Buffer> {
    this.logger.log('Exporting SKUs to Excel with images');

    // 构建查询条件
    const queryBuilder = this.skusRepository
      .createQueryBuilder('sku')
      .leftJoinAndSelect('sku.brand', 'brand')
      .leftJoinAndSelect('sku.supplier', 'supplier')
      .leftJoinAndSelect('sku.category', 'category')
      .leftJoinAndSelect('sku.color', 'color')
      .where('sku.isDeleted = :isDeleted', { isDeleted: false });

    // 添加筛选条件
    if (queryParams?.brandCodeSearch) {
      queryBuilder.andWhere('sku.brandCode = :brandCodeSearch', {
        brandCodeSearch: queryParams.brandCodeSearch,
      });
    }

    if (queryParams?.supplierCodeSearch) {
      queryBuilder.andWhere('sku.supplierCode = :supplierCodeSearch', {
        supplierCodeSearch: queryParams.supplierCodeSearch,
      });
    }

    if (queryParams?.skuIds && queryParams.skuIds.length > 0) {
      queryBuilder.andWhere('sku.id IN (:...skuIds)', {
        skuIds: queryParams.skuIds,
      });
    }

    // 排序并限制50条
    queryBuilder.orderBy('sku.createdAt', 'DESC').limit(50);

    const skus = await queryBuilder.getMany();

    // 检查数量限制
    if (skus.length > 50) {
      throw new BadRequestException(
        '导出数量不能超过50条，请使用筛选条件减少数据量',
      );
    }

    this.logger.log(`Found ${skus.length} SKUs to export`);

    // 使用ExcelJS创建工作簿（支持图片嵌入）
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('SKU数据');

      // 设置列定义（删除不必要的编码字段）
      worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: 'SKU编码', key: 'code', width: 15 },
        { header: '商品名称', key: 'name', width: 20 },
        { header: '厂商编码', key: 'manufacturerCode', width: 15 },
        { header: '品牌名称', key: 'brandName', width: 20 },
        { header: '供应商名称', key: 'supplierName', width: 20 },
        { header: '商品分类名称', key: 'categoryName', width: 20 },
        { header: '颜色名称', key: 'colorName', width: 15 },
        { header: '工艺描述', key: 'craftDescription', width: 30 },
        { header: '服装成本', key: 'clothingCost', width: 12 },
        { header: '零售价', key: 'retailPrice', width: 12 },
        { header: '预订价', key: 'preOrderPrice', width: 12 },
        { header: '补货价', key: 'restockPrice', width: 12 },
        { header: '现货价', key: 'spotPrice', width: 12 },
        { header: '辅料成本', key: 'accessoryCost', width: 12 },
        { header: '总成本', key: 'totalCost', width: 12 },
        { header: '辅料配置', key: 'accessories', width: 30 },
        { header: '图片', key: 'images', width: 40 },
        { header: '创建时间', key: 'createDate', width: 15 },
      ];

      // 设置标题行样式
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };

      // 处理每个SKU数据
      for (let i = 0; i < skus.length; i++) {
        const sku = skus[i];
        const accessoryCost = await this.calculateAccessoryCost(
          sku.accessories,
        );
        const totalCost = (sku.clothingCost || 0) + accessoryCost;

        // 添加基础数据行
        const row = worksheet.addRow({
          index: i + 1,
          code: sku.code,
          name: sku.name || '',
          manufacturerCode: sku.manufacturerCode || '',
          brandName: sku.brand?.name || '',
          supplierName: sku.supplier?.name || '',
          categoryName: sku.category?.name || '',
          colorName: sku.color?.name || '',
          craftDescription: sku.craftDescription || '',
          clothingCost: sku.clothingCost || 0,
          retailPrice: sku.retailPrice || 0,
          preOrderPrice: sku.preOrderPrice || 0,
          restockPrice: sku.restockPrice || 0,
          spotPrice: sku.spotPrice || 0,
          accessoryCost: accessoryCost,
          totalCost: totalCost,
          accessories: sku.accessories
            ? sku.accessories
                .map((acc) => `${acc.accessoryCode}:${acc.quantity}`)
                .join(';')
            : '',
          images: sku.images ? `${sku.images.length}张图片` : '无图片',
          createDate: sku.createdAt.toISOString().split('T')[0],
        });

        // 设置行高以适应图片
        row.height = 120;

        // 下载并插入图片
        if (sku.images && sku.images.length > 0) {
          this.logger.log(
            `Processing ${sku.images.length} images for SKU ${sku.code}`,
          );

          // 限制每个SKU最多5张图片
          const imagesToProcess = sku.images.slice(0, 5);

          for (
            let imgIndex = 0;
            imgIndex < imagesToProcess.length;
            imgIndex++
          ) {
            const imageUrl = imagesToProcess[imgIndex];
            try {
              this.logger.log(
                `Downloading image ${imgIndex + 1}/${imagesToProcess.length}: ${imageUrl}`,
              );

              const imageResponse = await axios.get(imageUrl, {
                responseType: 'arraybuffer',
                timeout: 10000,
              });

              const imageBuffer = Buffer.from(imageResponse.data);

              // 添加图片到工作簿
              const imageId = workbook.addImage({
                buffer: imageBuffer,
                extension: 'jpeg',
              });

              // 在图片列插入图片
              worksheet.addImage(imageId, {
                tl: { col: 17, row: i + 1 + imgIndex * 0.2 }, // 图片列（第18列，索引17）
                ext: { width: 150, height: 100 }, // 图片大小
              });

              this.logger.log(
                `Image ${imgIndex + 1} inserted successfully for SKU ${sku.code}`,
              );
            } catch (imageError) {
              this.logger.warn(
                `Failed to download image ${imgIndex + 1} for SKU ${sku.code}: ${imageUrl}`,
                imageError.message,
              );
            }
          }
        }
      }

      // 设置边框
      const borderStyle = {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const },
      };

      // 为表格添加边框
      const tableStartRow = 1;
      const tableEndRow = skus.length + 1;
      for (let row = tableStartRow; row <= tableEndRow; row++) {
        for (let col = 1; col <= 19; col++) {
          // 更新为19列
          worksheet.getCell(row, col).border = borderStyle;
        }
      }

      // 生成Excel缓冲区
      const excelBuffer = await workbook.xlsx.writeBuffer();
      return Buffer.from(excelBuffer);
    } catch (error) {
      this.logger.error('Excel generation failed', error);
      throw new BadRequestException('Excel生成失败');
    }
  }

  /**
   * 从Excel导入SKU数据
   */
  async importFromExcel(file: Express.Multer.File): Promise<SkuImportResult> {
    this.logger.log(`Importing SKUs from Excel: ${file.originalname}`);

    const result: SkuImportResult = {
      successCount: 0,
      failureCount: 0,
      errors: [],
      successCodes: [],
    };

    try {
      // 使用ExcelJS读取Excel文件
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(file.buffer);

      const worksheet = workbook.getWorksheet(1); // 获取第一个工作表
      if (!worksheet) {
        throw new BadRequestException('Excel文件为空或格式不正确');
      }

      const jsonData: any[] = [];

      // 获取标题行
      const headerRow = worksheet.getRow(1);
      const headers: string[] = [];
      headerRow.eachCell((cell, colNumber) => {
        headers[colNumber] = cell.value?.toString() || '';
      });

      // 读取数据行
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) return; // 跳过标题行

        const rowData: any = {};
        row.eachCell((cell, colNumber) => {
          const header = headers[colNumber];
          if (header) {
            rowData[header] = cell.value;
          }
        });

        // 只添加非空行
        if (Object.keys(rowData).length > 0) {
          jsonData.push(rowData);
        }
      });

      if (!jsonData || jsonData.length === 0) {
        throw new BadRequestException('Excel文件为空或格式不正确');
      }

      // 逐行处理数据
      for (let i = 0; i < jsonData.length; i++) {
        const rowIndex = i + 2; // Excel行号（从第2行开始）
        const row = jsonData[i] as any;

        try {
          // 验证必填字段（商品名称现在是可选的）
          if (!row['品牌编码']) {
            result.errors.push(`第${rowIndex}行：品牌编码不能为空`);
            result.failureCount++;
            continue;
          }

          if (!row['供应商编码']) {
            result.errors.push(`第${rowIndex}行：供应商编码不能为空`);
            result.failureCount++;
            continue;
          }

          if (!row['商品分类编码']) {
            result.errors.push(`第${rowIndex}行：商品分类编码不能为空`);
            result.failureCount++;
            continue;
          }

          if (!row['颜色']) {
            result.errors.push(`第${rowIndex}行：颜色不能为空`);
            result.failureCount++;
            continue;
          }

          // 根据颜色名称查找颜色编码
          const colorEntity = await this.colorsRepository.findOne({
            where: { name: row['颜色'], isDeleted: false },
          });

          if (!colorEntity) {
            result.errors.push(`第${rowIndex}行：颜色"${row['颜色']}"不存在`);
            result.failureCount++;
            continue;
          }

          // 解析辅料配置
          let accessories:
            | { accessoryCode: string; quantity: number }[]
            | undefined = undefined;
          if (row['辅料配置']) {
            try {
              const accessoryPairs = row['辅料配置'].split(';');
              accessories = accessoryPairs.map((pair: string) => {
                const [accessoryCode, quantity] = pair.split(':');
                return {
                  accessoryCode: accessoryCode.trim(),
                  quantity: parseInt(quantity.trim()),
                };
              });
            } catch (error) {
              result.errors.push(
                `第${rowIndex}行：辅料配置格式错误，应为"编码:数量;编码:数量"`,
              );
              result.failureCount++;
              continue;
            }
          }

          // 解析图片列表
          let images: string[] | undefined = undefined;
          if (row['图片列表']) {
            images = row['图片列表']
              .split(';')
              .map((img: string) => img.trim())
              .filter((img: string) => img.length > 0);
          }

          // 创建SKU DTO
          const createSkuDto: CreateSkuDto = {
            name: row['商品名称'],
            manufacturerCode: row['厂商编码'] || undefined,
            brandCode: row['品牌编码'],
            supplierCode: row['供应商编码'],
            categoryCode: row['商品分类编码'],
            colorCode: colorEntity.code,
            craftDescription: row['工艺描述'] || undefined,
            clothingCost: row['服装成本'] ? Number(row['服装成本']) : undefined,
            retailPrice: row['零售价'] ? Number(row['零售价']) : undefined,
            preOrderPrice: row['预订价'] ? Number(row['预订价']) : undefined,
            restockPrice: row['补货价'] ? Number(row['补货价']) : undefined,
            spotPrice: row['现货价'] ? Number(row['现货价']) : undefined,
            accessories,
            images,
          };

          // 创建SKU
          const createResult = await this.create(createSkuDto);
          if (createResult.code === 200) {
            result.successCount++;
            // 生成SKU编码用于记录
            const skuCode = await this.generateSkuCode(
              createSkuDto.brandCode,
              createSkuDto.supplierCode,
              createSkuDto.categoryCode,
              createSkuDto.colorCode,
            );
            result.successCodes.push(skuCode);
          }
        } catch (error) {
          result.errors.push(`第${rowIndex}行：${error.message}`);
          result.failureCount++;
        }
      }

      this.logger.log(
        `Import completed: ${result.successCount} success, ${result.failureCount} failed`,
      );

      return result;
    } catch (error) {
      this.logger.error('Excel import failed', error);
      throw new BadRequestException(`Excel导入失败: ${error.message}`);
    }
  }

  /**
   * 生成导入示例Excel文件
   */
  async generateImportTemplate(): Promise<Buffer> {
    this.logger.log('Generating SKU import template Excel');

    // 示例数据
    const templateData = [
      {
        商品名称: '时尚T恤',
        厂商编码: 'MFG001',
        品牌编码: 'BRAND001',
        供应商编码: 'SUP001',
        商品分类编码: 'CAT001',
        颜色: '红色',
        工艺描述: '数码印花+手工刺绣',
        服装成本: 50.0,
        零售价: 120.0,
        预订价: 100.0,
        补货价: 110.0,
        现货价: 115.0,
        辅料配置: 'ACC001:2;ACC002:1',
        图片列表:
          'https://example.com/image1.jpg;https://example.com/image2.jpg',
      },
      {
        商品名称: '休闲裤',
        厂商编码: 'MFG002',
        品牌编码: 'BRAND002',
        供应商编码: 'SUP002',
        商品分类编码: 'CAT002',
        颜色: '蓝色',
        工艺描述: '水洗工艺',
        服装成本: 80.0,
        零售价: 180.0,
        预订价: 150.0,
        补货价: 160.0,
        现货价: 170.0,
        辅料配置: 'ACC003:1',
        图片列表: 'https://example.com/image3.jpg',
      },
    ];

    // 使用ExcelJS创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('SKU导入模板');

    // 设置列定义
    worksheet.columns = [
      { header: '商品名称', key: 'name', width: 20 },
      { header: '厂商编码', key: 'manufacturerCode', width: 15 },
      { header: '品牌编码', key: 'brandCode', width: 15 },
      { header: '供应商编码', key: 'supplierCode', width: 15 },
      { header: '商品分类编码', key: 'categoryCode', width: 15 },
      { header: '颜色', key: 'color', width: 15 },
      { header: '工艺描述', key: 'craftDescription', width: 30 },
      { header: '服装成本', key: 'clothingCost', width: 12 },
      { header: '零售价', key: 'retailPrice', width: 12 },
      { header: '预订价', key: 'preOrderPrice', width: 12 },
      { header: '补货价', key: 'restockPrice', width: 12 },
      { header: '现货价', key: 'spotPrice', width: 12 },
      { header: '辅料配置', key: 'accessories', width: 30 },
      { header: '图片列表', key: 'images', width: 50 },
    ];

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 转换示例数据格式
    const excelData = templateData.map((item) => ({
      name: item.商品名称,
      manufacturerCode: item.厂商编码,
      brandCode: item.品牌编码,
      supplierCode: item.供应商编码,
      categoryCode: item.商品分类编码,
      color: item.颜色,
      craftDescription: item.工艺描述,
      clothingCost: item.服装成本,
      retailPrice: item.零售价,
      preOrderPrice: item.预订价,
      restockPrice: item.补货价,
      spotPrice: item.现货价,
      accessories: item.辅料配置,
      images: item.图片列表,
    }));

    // 添加数据行
    excelData.forEach((data) => {
      worksheet.addRow(data);
    });

    // 生成Excel文件Buffer
    const excelBuffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(excelBuffer);
  }

  /**
   * 为新创建的SKU自动创建初始库存记录
   * @param sku 新创建的SKU
   * @param category 商品分类（包含尺码信息）
   * @param queryRunner 数据库事务查询器
   */
  private async createInitialInventoryRecords(
    sku: Sku,
    category: ProductCategory,
    queryRunner: any,
  ): Promise<void> {
    this.logger.log(`Creating initial inventory records for SKU: ${sku.code}`);

    // 获取商品分类的尺码列表
    const sizes = category.sizes || [];

    if (sizes.length === 0) {
      this.logger.warn(
        `No sizes found for category ${category.code}, skipping inventory creation`,
      );
      return;
    }

    // 为每个尺码创建初始库存记录（全部为0）
    const inventoryRecords = sizes.map((size) => {
      const inventory = this.skuInventoryRepository.create({
        skuId: sku.id,
        size: size,
        currentStock: 0,
        purchasingStock: 0,
        needRestockStock: 0,
        isOutOfStock: true, // 初始库存为0，所以是缺货状态
        remarks: '系统自动创建的初始库存记录',
        isDeleted: false,
      });
      return inventory;
    });

    // 批量保存库存记录
    await queryRunner.manager.save(SkuInventory, inventoryRecords);

    this.logger.log(
      `Created ${inventoryRecords.length} initial inventory records for SKU: ${sku.code}, sizes: ${sizes.join(', ')}`,
    );
  }
}
