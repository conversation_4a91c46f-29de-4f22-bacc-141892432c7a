# SKU管理模块

## 概述

SKU（Stock Keeping Unit）管理模块提供了完整的SKU管理功能，包括创建、查询、更新、删除、导入导出等操作。

## 重要更新

### SKU编码格式优化

- **SKU编码格式已升级**：品牌编码(3位) + 供应商编码(3位) + 分类编码(2位) + 颜色编码(2位) + 序列号(3位)
- **支持同一组合下的多个产品**：通过序列号区分相同品牌、供应商、分类、颜色组合的不同产品
- **序列号特殊规则**：序列号不包含数字4，从001开始递增（跳过含4的数字，如004、014、040等）
- **向后兼容**：现有的10位编码视为序列号001，新编码为13位
- **示例**：2567701001、2567701002、2567701003、2567701005（跳过004）

### 商品名称字段变更

- **商品名称字段现在为可选字段**，允许为空值
- 创建SKU时不再强制要求填写商品名称
- 导入功能已更新，不再验证商品名称为必填项
- 搜索和导出功能已优化，正确处理空名称的情况

## 新增功能

### 1. 图片字段支持

- 新增 `images` 字段，支持存储SKU对应的图片URL列表
- 字段类型：字符串数组（可选）
- 存储格式：JSON数组

### 2. 分页查询接口

- 支持多种搜索条件的分页查询
- 返回辅料成本和总成本字段
- 支持按名称、编码、品牌、供应商、分类、颜色筛选

### 3. 导入导出功能

- Excel导入：支持批量导入SKU数据
- Excel导出：支持筛选导出SKU数据，**包含图片下载功能**
- 模板下载：提供标准导入模板
- 导出限制：单次最多导出50条数据（含图片下载）

## API接口

### 基础CRUD接口

#### 1. 创建SKU

```
POST /skus
```

#### 2. 获取SKU列表

```
GET /skus
```

#### 3. 分页查询SKU列表

```
GET /skus/paginated?page=1&pageSize=10&nameSearch=T恤&brandCodeSearch=BRAND001
```

**查询参数：**

- `page`: 页码（必填）
- `pageSize`: 每页数量（必填）
- `nameSearch`: SKU名称搜索（选填）
- `codeSearch`: SKU编码搜索（选填）
- `brandCodeSearch`: 品牌编码筛选（选填）
- `supplierCodeSearch`: 供应商编码筛选（选填）
- `categoryCodeSearch`: 商品分类编码筛选（选填）
- `colorCodeSearch`: 颜色编码筛选（选填）

**响应数据包含：**

- 基础SKU信息
- 辅料成本（自动计算）
- 总成本（服装成本 + 辅料成本）
- 关联的品牌、供应商、分类、颜色信息

#### 4. 获取SKU详情

```
GET /skus/:id
```

#### 5. 更新SKU

```
PATCH /skus/:id
```

#### 6. 删除SKU

```
DELETE /skus/:id
```

### 导入导出接口

#### 1. 导出SKU数据（含图片下载）

```
GET /skus/export/excel?brandCodeSearch=BRAND001&supplierCodeSearch=SUP001&skuIds=uuid1,uuid2
```

**查询参数：**

- `brandCodeSearch`: 品牌编码筛选（选填）
- `supplierCodeSearch`: 供应商编码筛选（选填）
- `skuIds`: SKU ID列表，逗号分隔（选填）

**功能特点：**

- 自动下载SKU关联的图片
- 限制单次导出50条数据
- 每个SKU最多下载5张图片
- 图片大小限制5MB
- 下载超时时间10秒
- 生成两个工作表：SKU数据 + 图片信息

#### 2. 下载导入模板

```
GET /skus/import/template
```

#### 3. 导入SKU数据

```
POST /skus/import/excel
Content-Type: multipart/form-data
```

**请求体：**

- `file`: Excel文件

## 导入格式说明

### Excel列结构

| 列名         | 类型   | 必填 | 说明                             |
| ------------ | ------ | ---- | -------------------------------- |
| 商品名称     | 字符串 | 是   | SKU名称                          |
| 厂商编码     | 字符串 | 否   | 生产厂商编码                     |
| 品牌编码     | 字符串 | 是   | 品牌编码（必须存在于系统中）     |
| 供应商编码   | 字符串 | 是   | 供应商编码（必须存在于系统中）   |
| 商品分类编码 | 字符串 | 是   | 商品分类编码（必须存在于系统中） |
| 颜色         | 字符串 | 是   | 颜色名称（如：红色、蓝色）       |
| 工艺描述     | 字符串 | 否   | 制作工艺说明                     |
| 服装成本     | 数字   | 否   | 服装制作成本                     |
| 零售价       | 数字   | 否   | 零售价格                         |
| 预订价       | 数字   | 否   | 预订价格                         |
| 补货价       | 数字   | 否   | 补货价格                         |
| 现货价       | 数字   | 否   | 现货价格                         |
| 辅料配置     | 字符串 | 否   | 格式：编码:数量;编码:数量        |
| 图片列表     | 字符串 | 否   | 格式：URL;URL;URL                |

### 导入注意事项

1. **颜色字段**：使用中文名称（如：红色、蓝色），系统会自动查找对应的颜色编码
2. **品牌和供应商**：必须使用编码，且编码必须在系统中存在
3. **辅料配置**：格式为 `辅料编码:数量;辅料编码:数量`，如：`ACC001:2;ACC002:1`
4. **图片列表**：多个图片URL用分号分隔，如：`url1.jpg;url2.jpg`
5. **SKU编码**：系统自动生成，无需在Excel中提供

## 成本计算

### 辅料成本计算

系统会根据SKU配置的辅料自动计算辅料成本：

- 查找每个辅料的成本价
- 乘以对应数量
- 累加得到总辅料成本

### 总成本计算

```
总成本 = 服装成本 + 辅料成本
```

## 数据库变更

### 新增字段

```sql
-- 为SKU表添加images字段
ALTER TABLE skus ADD COLUMN images jsonb;
COMMENT ON COLUMN skus.images IS 'SKU图片数组（JSON格式存储）';
CREATE INDEX idx_skus_images ON skus USING gin(images);
```

## 使用示例

### 1. 分页查询示例

```bash
curl -X GET "http://localhost:3000/skus/paginated?page=1&pageSize=10&nameSearch=T恤" \
  -H "Authorization: Bearer your-token"
```

### 2. 导出示例

```bash
curl -X GET "http://localhost:3000/skus/export/excel?brandCodeSearch=BRAND001" \
  -H "Authorization: Bearer your-token" \
  -o skus.xlsx
```

### 3. 导入示例

```bash
curl -X POST "http://localhost:3000/skus/import/excel" \
  -H "Authorization: Bearer your-token" \
  -F "file=@skus.xlsx"
```

## 错误处理

导入过程中的常见错误：

- 品牌编码不存在
- 供应商编码不存在
- 商品分类编码不存在
- 颜色名称不存在
- 辅料配置格式错误
- SKU编码重复（自动生成的编码已存在）

所有错误都会在导入结果中详细列出，包含具体的行号和错误原因。
