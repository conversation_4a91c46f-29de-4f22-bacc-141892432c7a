import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Brand } from '@/brands/entities/brand.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { ProductCategory } from '@/product-categories/entities/product-category.entity';
import { Color } from '@/colors/entities/color.entity';

// 辅料数量接口
export interface AccessoryQuantity {
  accessoryCode: string;
  quantity: number;
}

@Entity('skus')
export class Sku {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: 'SKU ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  @ApiProperty({
    description:
      'SKU编码（自动生成，唯一）- 格式：品牌编码(3位)+供应商编码(3位)+分类编码(2位)+颜色编码(2位)+序列号(3位，不含数字4)',
    example: '2567701001',
  })
  code: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  @ApiProperty({
    description: '商品名称（选填）',
    example: '时尚T恤',
    required: false,
  })
  name: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @ApiProperty({
    description: '厂商编码（选填，字符串）',
    example: 'MFG001',
    required: false,
  })
  manufacturerCode: string | null;

  // 品牌关联（多对一）
  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '品牌信息',
    type: () => Brand,
  })
  brand: Brand;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({
    description: '品牌编码（必填，关联brands表）',
    example: 'BRAND001',
  })
  brandCode: string;

  // 供应商关联（多对一）
  @ManyToOne(() => Supplier)
  @JoinColumn({ name: 'supplierCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '供应商信息',
    type: () => Supplier,
  })
  supplier: Supplier;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({
    description: '供应商编码（必填，关联suppliers表）',
    example: 'SUP001',
  })
  supplierCode: string;

  // 商品分类关联（多对一）
  @ManyToOne(() => ProductCategory)
  @JoinColumn({ name: 'categoryCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '商品分类信息',
    type: () => ProductCategory,
  })
  category: ProductCategory;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({
    description: '商品分类编码（必填，关联product_categories表）',
    example: 'CAT001',
  })
  categoryCode: string;

  // 颜色关联（多对一）
  @ManyToOne(() => Color)
  @JoinColumn({ name: 'colorCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '颜色信息',
    type: () => Color,
  })
  color: Color;

  @Column({ type: 'varchar', length: 50 })
  @ApiProperty({
    description: '颜色编码（必填，每个SKU只有一个颜色）',
    example: 'COLOR001',
  })
  colorCode: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @ApiProperty({
    description: '工艺描述（选填）- 产品的制作工艺说明',
    example: '数码印花+手工刺绣',
    required: false,
  })
  craftDescription: string | null;

  // 价格字段（全部选填）
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: '服装成本',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '服装成本（选填）',
    example: 50.0,
    required: false,
  })
  clothingCost: number | null;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: '零售价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '零售价（选填）',
    example: 120.0,
    required: false,
  })
  retailPrice: number | null;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: '预订价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '预订价（选填）',
    example: 100.0,
    required: false,
  })
  preOrderPrice: number | null;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: '补货价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '补货价（选填）',
    example: 110.0,
    required: false,
  })
  restockPrice: number | null;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: '现货价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({
    description: '现货价（选填）',
    example: 115.0,
    required: false,
  })
  spotPrice: number | null;

  // 辅料数组（JSON格式存储）
  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({
    description: '辅料数组（包含辅料编码和数量）',
    example: [
      { accessoryCode: 'ACC001', quantity: 2 },
      { accessoryCode: 'ACC002', quantity: 1 },
    ],
    required: false,
  })
  accessories: AccessoryQuantity[] | null;

  // SKU图片数组（JSON格式存储）
  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({
    description: 'SKU图片数组（选填）- 存储SKU对应的图片URL列表',
    example: [
      'https://example.com/sku-image1.jpg',
      'https://example.com/sku-image2.jpg',
    ],
    required: false,
    type: [String],
    isArray: true,
  })
  images: string[] | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;
}
