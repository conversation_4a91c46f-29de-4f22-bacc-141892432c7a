import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { SkusService } from './skus.service';
import { SkusController } from './skus.controller';
import { Sku } from './entities/sku.entity';
import { Brand } from '@/brands/entities/brand.entity';
import { Supplier } from '@/suppliers/entities/supplier.entity';
import { ProductCategory } from '@/product-categories/entities/product-category.entity';
import { Color } from '@/colors/entities/color.entity';
import { Accessory } from '@/accessories/entities/accessory.entity';
import { SkuInventory } from '@/skus-inventory/entities/sku-inventory.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Sku,
      Brand,
      Supplier,
      ProductCategory,
      Color,
      Accessory,
      SkuInventory,
    ]),
    MulterModule.register({
      dest: './uploads',
    }),
  ],
  controllers: [SkusController],
  providers: [SkusService],
  exports: [SkusService],
})
export class SkusModule {}
