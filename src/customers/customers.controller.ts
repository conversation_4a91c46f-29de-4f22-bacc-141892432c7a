import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Logger,
  UseInterceptors,
  UploadedFile,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { CustomersService } from './customers.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { QueryCustomersDto } from './dto/query-customers.dto';
import {
  CustomerResponseDto,
  CustomerListResponseDto,
  CustomerSearchResponseDto,
} from './dto/customer-response.dto';
import { SearchCustomerDto } from './dto/search-customer.dto';
import { CustomerImportResult } from './dto/import-customer.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';
import { getAllProvinces } from './province';

@ApiTags('customers')
@Controller('customers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CustomersController {
  private readonly logger = new Logger(CustomersController.name);

  constructor(private readonly customersService: CustomersService) {}

  @Post()
  @ApiOperation({ summary: '创建客户' })
  @ApiResponse({
    status: 200,
    description: '客户创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '客户编码已存在',
  })
  async create(@Body() createCustomerDto: CreateCustomerDto) {
    this.logger.log(`Creating customer ${createCustomerDto.name}`);

    await this.customersService.create(createCustomerDto);

    return {
      code: 200,
      data: null,
      message: '客户创建成功',
    };
  }

  @Get('search')
  @ApiOperation({ summary: '模糊搜索客户（根据手机号、拼音码或客户姓名）' })
  @ApiResponse({
    status: 200,
    description: '搜索成功',
    type: [CustomerSearchResponseDto],
  })
  async searchCustomers(@Query() searchDto: SearchCustomerDto) {
    this.logger.log(`Searching customers with keyword: ${searchDto.keyword}`);

    const result = await this.customersService.searchCustomers(
      searchDto.keyword,
    );

    return {
      code: 200,
      data: result,
      message: '搜索成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取客户列表（支持模糊搜索）' })
  @ApiResponse({
    status: 200,
    description: '获取客户列表成功',
    type: CustomerListResponseDto,
  })
  async findAll(@Query() queryDto: QueryCustomersDto) {
    this.logger.log(
      `Querying customers with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.customersService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取客户列表成功',
    };
  }

  @Get(':code')
  @ApiOperation({ summary: '根据客户编码获取客户详情' })
  @ApiParam({
    name: 'code',
    description: '客户编码',
    example: 'CUS001',
  })
  @ApiResponse({
    status: 200,
    description: '获取客户详情成功',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '客户不存在',
  })
  async findOne(@Param('code') code: string) {
    this.logger.log(`Getting customer details for code: ${code}`);

    const customer = await this.customersService.findOne(code);

    return {
      code: 200,
      data: {
        name: customer.name,
        pinyinCode: customer.pinyinCode,
        phone: customer.phone,
        address: customer.address,
        provinceCode: customer.provinceCode,
        provinceName: customer.provinceName,
        managerCode: customer.managerCode,
        managerName: customer.manager?.nickname || null,
        remarks: customer.remarks,
        updatedAt: customer.updatedAt,
      },
      message: '获取客户详情成功',
    };
  }

  @Patch(':code')
  @ApiOperation({ summary: '更新客户信息' })
  @ApiParam({
    name: 'code',
    description: '客户编码',
    example: 'CUS001',
  })
  @ApiResponse({
    status: 200,
    description: '客户更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '客户不存在',
  })
  async update(
    @Param('code') code: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
  ) {
    this.logger.log(`Updating customer ${code}`);

    await this.customersService.update(code, updateCustomerDto);

    return {
      code: 200,
      data: null,
      message: '客户更新成功',
    };
  }

  @Delete(':code')
  @ApiOperation({ summary: '删除客户（软删除）' })
  @ApiParam({
    name: 'code',
    description: '客户编码',
    example: 'CUS001',
  })
  @ApiResponse({
    status: 200,
    description: '客户删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '客户不存在',
  })
  async remove(@Param('code') code: string) {
    this.logger.log(`Deleting customer ${code}`);

    await this.customersService.remove(code);

    return {
      code: 200,
      data: null,
      message: '客户删除成功',
    };
  }

  @Get('provinces')
  @ApiOperation({ summary: '获取所有省份列表' })
  @ApiResponse({
    status: 200,
    description: '获取省份列表成功',
  })
  async getProvinces() {
    this.logger.log('Getting provinces list');

    const provinces = getAllProvinces();

    return {
      code: 200,
      data: provinces,
      message: '获取省份列表成功',
    };
  }

  @Get('export/excel')
  @ApiOperation({ summary: '导出客户数据为Excel' })
  @ApiResponse({
    status: 200,
    description: '导出成功',
    headers: {
      'Content-Type': {
        description:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
      'Content-Disposition': {
        description: 'attachment; filename="customers.xlsx"',
      },
    },
  })
  async exportToExcel(@Res() res: Response) {
    this.logger.log('Exporting customers to Excel');

    const excelBuffer = await this.customersService.exportToExcel();

    const filename = `customers_${new Date().toISOString().split('T')[0]}.xlsx`;

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Content-Length': excelBuffer.length.toString(),
    });

    res.send(excelBuffer);
  }

  @Get('import/template')
  @ApiOperation({ summary: '下载客户导入模板' })
  @ApiResponse({
    status: 200,
    description: '模板下载成功',
    headers: {
      'Content-Type': {
        description:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
      'Content-Disposition': {
        description: 'attachment; filename="customer_import_template.xlsx"',
      },
    },
  })
  async downloadImportTemplate(@Res() res: Response) {
    this.logger.log('Generating import template');

    const templateBuffer = await this.customersService.generateImportTemplate();

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition':
        'attachment; filename="customer_import_template.xlsx"',
      'Content-Length': templateBuffer.length.toString(),
    });

    res.send(templateBuffer);
  }

  @Post('import/excel')
  @ApiOperation({
    summary: '从Excel导入客户数据',
    description:
      '导入客户数据，拼音码将根据客户名称自动生成，无需在Excel中提供拼音码和负责人字段',
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: '导入完成',
    type: CustomerImportResult,
  })
  @ApiResponse({
    status: 400,
    description: '文件格式错误或数据验证失败',
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (_req, file, callback) => {
        if (!file.originalname.match(/\.(xlsx|xls)$/)) {
          return callback(new Error('只支持Excel文件格式'), false);
        }
        callback(null, true);
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  async importFromExcel(@UploadedFile() file: Express.Multer.File) {
    this.logger.log(`Importing customers from Excel: ${file?.originalname}`);

    if (!file) {
      return {
        code: 400,
        data: null,
        message: '请选择要导入的Excel文件',
      };
    }

    const result = await this.customersService.importFromExcel(file);

    return {
      code: 200,
      data: result,
      message: `导入完成：成功 ${result.successCount} 条，失败 ${result.failureCount} 条`,
    };
  }
}
