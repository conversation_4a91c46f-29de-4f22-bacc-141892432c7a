import {
  Entity,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@/users/entities/user.entity';
import { getProvinceName } from '../province';

@Entity('customers')
export class Customer {
  @PrimaryColumn({ type: 'varchar', length: 50 })
  @ApiProperty({ description: '客户编码，作为主键', example: 'CUS001' })
  code: string;

  @Column({ type: 'varchar', length: 100 })
  @ApiProperty({ description: '客户名称（必填）', example: '张三' })
  name: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @ApiProperty({ description: '拼音码', example: 'ZS', required: false })
  pinyinCode: string | null;

  @Column({ type: 'varchar', length: 20, nullable: true })
  @ApiProperty({
    description: '客户手机号',
    example: '13800138000',
    required: false,
  })
  phone: string | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  @ApiProperty({
    description: '客户地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  address: string | null;

  @Column({ type: 'int', nullable: true })
  @ApiProperty({
    description: '省份代码',
    example: 11,
    required: false,
  })
  provinceCode: number | null;

  /**
   * 获取省份名称（计算属性）
   */
  get provinceName(): string | null {
    return this.provinceCode ? getProvinceName(this.provinceCode) : null;
  }

  // 客户负责人关联（多对一）
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'managerCode', referencedColumnName: 'code' })
  @ApiProperty({
    description: '客户负责人信息',
    type: () => User,
    required: false,
  })
  manager: User | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @ApiProperty({
    description: '客户负责人编码',
    example: 'user001',
    required: false,
  })
  managerCode: string | null;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: '备注',
    example: '重要客户，优先处理',
    required: false,
  })
  remarks: string | null;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 软删除字段
  @Column({ default: false })
  @ApiProperty({ description: '是否已删除（软删除标记）' })
  isDeleted: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  @ApiProperty({ description: '删除时间', required: false })
  deletedAt: Date | null;
}
