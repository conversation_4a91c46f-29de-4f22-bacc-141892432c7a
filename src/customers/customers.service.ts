import {
  Injectable,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as XLSX from 'xlsx';
import { Customer } from './entities/customer.entity';
import { User } from '@/users/entities/user.entity';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { QueryCustomersDto } from './dto/query-customers.dto';
import { CustomerImportResult } from './dto/import-customer.dto';
import {
  isValidProvinceCode,
  getProvinceName,
  getProvinceCode,
} from './province';
import { PinyinUtil } from '@/utils/pinyin.util';

@Injectable()
export class CustomersService {
  private readonly logger = new Logger(CustomersService.name);

  constructor(
    @InjectRepository(Customer)
    private customersRepository: Repository<Customer>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  /**
   * 创建客户
   */
  async create(createCustomerDto: CreateCustomerDto): Promise<Customer> {
    this.logger.log(`Creating customer`);

    // 自动生成客户编码
    const generatedCode = await this.generateCustomerCode();
    if (createCustomerDto.phone) {
      const existingPhone = await this.customersRepository.findOne({
        where: { phone: createCustomerDto.phone, isDeleted: false },
      });

      if (existingPhone) {
        throw new BadRequestException('手机号已被注册');
      }
    }

    // 验证省份代码是否有效
    if (
      createCustomerDto.provinceCode !== undefined &&
      createCustomerDto.provinceCode !== null
    ) {
      if (!isValidProvinceCode(createCustomerDto.provinceCode)) {
        throw new BadRequestException(
          `省份代码 ${createCustomerDto.provinceCode} 无效`,
        );
      }
    }

    // 验证负责人是否存在
    let manager: User | null = null;
    let managerCode: string | null = null;

    if (
      createCustomerDto.managerCode &&
      createCustomerDto.managerCode.trim() !== ''
    ) {
      managerCode = createCustomerDto.managerCode.trim();
      manager = await this.usersRepository.findOne({
        where: { code: managerCode, isDeleted: false },
      });
      if (!manager) {
        throw new BadRequestException(`负责人用户 ${managerCode} 不存在`);
      }
    }

    // 自动生成拼音码（如果没有提供）
    let finalPinyinCode = createCustomerDto.pinyinCode;
    if (!finalPinyinCode || finalPinyinCode.trim() === '') {
      finalPinyinCode = PinyinUtil.generatePinyinCode(createCustomerDto.name);
    }

    // 创建客户
    const customer = this.customersRepository.create({
      code: generatedCode,
      name: createCustomerDto.name,
      pinyinCode: finalPinyinCode || null,
      phone:
        createCustomerDto.phone && createCustomerDto.phone.trim() !== ''
          ? createCustomerDto.phone.trim()
          : null,
      address:
        createCustomerDto.address && createCustomerDto.address.trim() !== ''
          ? createCustomerDto.address.trim()
          : null,
      provinceCode: createCustomerDto.provinceCode || null,
      managerCode,
      manager,
      remarks:
        createCustomerDto.remarks && createCustomerDto.remarks.trim() !== ''
          ? createCustomerDto.remarks.trim()
          : null,
      isDeleted: false,
    });

    return await this.customersRepository.save(customer);
  }

  /**
   * 分页查询客户列表（支持模糊搜索）
   */
  async findAll(queryDto: QueryCustomersDto) {
    this.logger.log(
      `Querying customers with params: ${JSON.stringify(queryDto)}`,
    );

    const { page, pageSize, search, provinceCode, pinyinCode, managerCode } =
      queryDto;

    // page和pageSize是必须参数
    if (page === undefined || pageSize === undefined) {
      return {
        customers: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // 处理分页参数：page=0&pageSize=0表示获取所有数据
    const isGetAll = page === 0 && pageSize === 0;
    const finalPage = isGetAll ? 1 : page;
    const finalPageSize = isGetAll ? undefined : pageSize;
    const skip = isGetAll ? 0 : (page - 1) * pageSize;

    const queryBuilder = this.customersRepository
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.manager', 'manager')
      .where('customer.isDeleted = :isDeleted', { isDeleted: false });

    // 模糊搜索客户编码和名称
    if (search) {
      queryBuilder.andWhere(
        '(customer.code ILIKE :search OR customer.name ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 省份过滤
    if (provinceCode) {
      queryBuilder.andWhere('customer.provinceCode = :provinceCode', {
        provinceCode,
      });
    }

    // 拼音码过滤 - 不区分大小写
    if (pinyinCode) {
      queryBuilder.andWhere(
        'UPPER(customer.pinyinCode) LIKE UPPER(:pinyinCode)',
        {
          pinyinCode: `%${pinyinCode}%`,
        },
      );
    }

    // 负责人过滤
    if (managerCode) {
      queryBuilder.andWhere('customer.managerCode = :managerCode', {
        managerCode,
      });
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const customers = await queryBuilder
      .select([
        'customer.code', // 需要包含主键字段用于TypeORM内部处理
        'customer.name',
        'customer.pinyinCode',
        'customer.phone',
        'customer.address',
        'customer.provinceCode',
        'customer.managerCode',
        'customer.remarks',
        'customer.updatedAt',
        'customer.createdAt', // 需要包含这个字段用于排序
        'manager.code',
        'manager.nickname',
      ])
      .orderBy('customer.createdAt', 'DESC')
      .skip(skip)
      .take(finalPageSize)
      .getMany();

    // 格式化返回数据 - 不返回客户编码
    const formattedCustomers = customers.map((customer) => ({
      name: customer.name,
      code: customer.code,
      pinyinCode: customer.pinyinCode,
      phone: customer.phone,
      address: customer.address,
      provinceName: customer.provinceCode
        ? getProvinceName(customer.provinceCode)
        : null,
      managerName: customer.manager?.nickname || null,
      remarks: customer.remarks,
    }));

    return {
      customers: formattedCustomers,
      total,
      page: isGetAll ? 0 : finalPage,
      pageSize: isGetAll ? 0 : finalPageSize!,
      totalPages: isGetAll ? 1 : Math.ceil(total / finalPageSize!),
    };
  }

  /**
   * 根据客户编码查找客户
   */
  async findByCode(code: string): Promise<Customer | null> {
    return await this.customersRepository.findOne({
      where: { code, isDeleted: false },
      relations: ['manager'],
    });
  }

  /**
   * 根据客户编码获取客户详情
   */
  async findOne(code: string): Promise<Customer> {
    const customer = await this.findByCode(code);
    if (!customer) {
      throw new NotFoundException('客户不存在');
    }
    return customer;
  }

  /**
   * 更新客户
   */
  async update(
    code: string,
    updateCustomerDto: UpdateCustomerDto,
  ): Promise<Customer> {
    this.logger.log(`Updating customer ${code}`);

    // 查找要更新的客户
    const customer = await this.findOne(code);

    // 验证省份代码是否有效
    if (updateCustomerDto.provinceCode !== undefined) {
      if (
        updateCustomerDto.provinceCode !== null &&
        !isValidProvinceCode(updateCustomerDto.provinceCode)
      ) {
        throw new BadRequestException(
          `省份代码 ${updateCustomerDto.provinceCode} 无效`,
        );
      }
    }

    // 验证负责人是否存在
    if (updateCustomerDto.managerCode !== undefined) {
      if (
        updateCustomerDto.managerCode &&
        updateCustomerDto.managerCode.trim() !== ''
      ) {
        const managerCode = updateCustomerDto.managerCode.trim();
        const manager = await this.usersRepository.findOne({
          where: { code: managerCode, isDeleted: false },
        });
        if (!manager) {
          throw new BadRequestException(`负责人用户 ${managerCode} 不存在`);
        }
        customer.manager = manager;
        customer.managerCode = managerCode;
      } else {
        customer.manager = null;
        customer.managerCode = null;
      }
    }

    // 更新其他字段
    if (updateCustomerDto.name !== undefined) {
      customer.name = updateCustomerDto.name;
    }
    if (updateCustomerDto.pinyinCode !== undefined) {
      customer.pinyinCode = updateCustomerDto.pinyinCode;
    }
    if (updateCustomerDto.phone !== undefined) {
      customer.phone =
        updateCustomerDto.phone && updateCustomerDto.phone.trim() !== ''
          ? updateCustomerDto.phone.trim()
          : null;
    }
    if (updateCustomerDto.address !== undefined) {
      customer.address =
        updateCustomerDto.address && updateCustomerDto.address.trim() !== ''
          ? updateCustomerDto.address.trim()
          : null;
    }
    if (updateCustomerDto.provinceCode !== undefined) {
      customer.provinceCode = updateCustomerDto.provinceCode;
    }
    if (updateCustomerDto.remarks !== undefined) {
      customer.remarks =
        updateCustomerDto.remarks && updateCustomerDto.remarks.trim() !== ''
          ? updateCustomerDto.remarks.trim()
          : null;
    }

    return await this.customersRepository.save(customer);
  }

  /**
   * 软删除客户
   */
  async remove(code: string): Promise<void> {
    this.logger.log(`Soft deleting customer ${code}`);

    // 查找要删除的客户
    const customer = await this.findOne(code);

    // 软删除
    customer.isDeleted = true;
    customer.deletedAt = new Date();
    await this.customersRepository.save(customer);
  }

  /**
   * 统计客户数量
   */
  async countCustomers(): Promise<number> {
    return await this.customersRepository.count({
      where: { isDeleted: false },
    });
  }

  /**
   * 模糊搜索客户（根据手机号、拼音码或客户姓名）
   */
  async searchCustomers(keyword: string) {
    this.logger.log(`Searching customers with keyword: ${keyword}`);

    const queryBuilder = this.customersRepository
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.manager', 'manager')
      .where('customer.isDeleted = :isDeleted', { isDeleted: false });

    // 添加模糊搜索条件（手机号、拼音码或客户姓名，不区分大小写）
    queryBuilder.andWhere(
      '(LOWER(customer.phone) LIKE LOWER(:keyword) OR LOWER(customer.pinyinCode) LIKE LOWER(:keyword) OR LOWER(customer.name) LIKE LOWER(:keyword))',
      { keyword: `%${keyword}%` },
    );

    const customers = await queryBuilder.getMany();

    // 格式化返回数据
    const formattedCustomers = customers.map((customer) => ({
      code: customer.code,
      name: customer.name,
      phone: customer.phone,
      managerCode: customer.managerCode,
      managerName: customer.manager?.nickname || null,
    }));

    return formattedCustomers;
  }

  /**
   * 导出客户数据为Excel
   */
  async exportToExcel(): Promise<Buffer> {
    this.logger.log('Exporting customers to Excel');

    // 获取所有客户数据
    const customers = await this.customersRepository.find({
      where: { isDeleted: false },
      relations: ['manager'],
      order: { createdAt: 'DESC' },
    });

    // 准备Excel数据
    const excelData = customers.map((customer) => ({
      客户名称: customer.name,
      拼音码: customer.pinyinCode,
      客户手机号: customer.phone || '',
      客户地址: customer.address || '',
      省份名称: customer.provinceCode
        ? getProvinceName(customer.provinceCode)
        : '',
      备注: customer.remarks || '',
    }));

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // 设置列宽
    const colWidths = [
      { wch: 20 }, // 客户名称
      { wch: 15 }, // 拼音码
      { wch: 15 }, // 客户手机号
      { wch: 30 }, // 客户地址
      { wch: 20 }, // 省份名称
      { wch: 30 }, // 备注
    ];
    worksheet['!cols'] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '客户列表');

    // 生成Excel文件Buffer
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });
    return excelBuffer;
  }

  /**
   * 生成导入示例Excel文件
   */
  async generateImportTemplate(): Promise<Buffer> {
    this.logger.log('Generating import template Excel');

    // 示例数据
    const templateData = [
      {
        客户名称: '张三',
        客户手机号: '13800138000',
        客户地址: '北京市朝阳区xxx街道xxx号',
        省份名称: '北京市',
        备注: '重要客户',
      },
      {
        客户名称: '李四',
        客户手机号: '13900139000',
        客户地址: '上海市浦东新区xxx路xxx号',
        省份名称: '上海市',
        备注: '',
      },
    ];

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(templateData);

    // 设置列宽
    const colWidths = [
      { wch: 20 }, // 客户名称
      { wch: 15 }, // 客户手机号
      { wch: 30 }, // 客户地址
      { wch: 20 }, // 省份名称
      { wch: 30 }, // 备注
    ];
    worksheet['!cols'] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '客户导入模板');

    // 生成Excel文件Buffer
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });
    return excelBuffer;
  }

  /**
   * 从Excel导入客户数据
   */
  async importFromExcel(
    file: Express.Multer.File,
  ): Promise<CustomerImportResult> {
    this.logger.log(`Importing customers from Excel: ${file.originalname}`);

    const result: CustomerImportResult = {
      successCount: 0,
      failureCount: 0,
      errors: [],
      successCodes: [],
    };

    try {
      // 读取Excel文件
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 将工作表转换为JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (!jsonData || jsonData.length === 0) {
        throw new BadRequestException('Excel文件为空或格式不正确');
      }

      // 处理每一行数据
      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i] as any;
        const rowNumber = i + 2; // Excel行号（从第2行开始，第1行是标题）

        try {
          // 验证必填字段
          const name = this.getExcelCellValue(row, ['客户名称', 'name']);

          if (!name) {
            result.errors.push(`第${rowNumber}行：客户名称为必填项`);
            result.failureCount++;
            continue;
          }

          // 自动生成客户编码
          const generatedCode = await this.generateCustomerCode();

          // 自动生成拼音码
          const generatedPinyinCode = PinyinUtil.generatePinyinCode(
            name.toString(),
          );

          // 获取可选字段
          const phone = this.getExcelCellValue(row, ['客户手机号', 'phone']);
          const address = this.getExcelCellValue(row, ['客户地址', 'address']);
          const provinceName = this.getExcelCellValue(row, [
            '省份名称',
            'provinceName',
            '省份代码', // 兼容旧格式
            'provinceCode',
          ]);
          const remarks = this.getExcelCellValue(row, ['备注', 'remarks']);

          // 验证省份名称/代码是否有效
          let finalProvinceCode: number | null = null;
          if (
            provinceName !== null &&
            provinceName !== undefined &&
            provinceName !== ''
          ) {
            const provinceValue = provinceName.toString().trim();

            // 先尝试按省份名称查找
            let provinceCode = getProvinceCode(provinceValue);

            // 如果按名称找不到，尝试按数字代码查找（兼容旧格式）
            if (provinceCode === null) {
              const provinceCodeNum = Number(provinceValue);
              if (
                !isNaN(provinceCodeNum) &&
                isValidProvinceCode(provinceCodeNum)
              ) {
                provinceCode = provinceCodeNum;
              }
            }

            if (provinceCode === null) {
              result.errors.push(
                `第${rowNumber}行：省份 "${provinceValue}" 无效，请使用正确的省份名称（如：北京市、上海市）`,
              );
              result.failureCount++;
              continue;
            }
            finalProvinceCode = provinceCode;
          }

          // 不再验证负责人，设置为null
          let manager: User | null = null;
          let finalManagerCode: string | null = null;

          // 创建客户
          const customer = this.customersRepository.create({
            code: generatedCode,
            name: name.toString(),
            pinyinCode: generatedPinyinCode || null,
            phone:
              phone && phone.toString().trim() !== ''
                ? phone.toString().trim()
                : null,
            address:
              address && address.toString().trim() !== ''
                ? address.toString().trim()
                : null,
            provinceCode: finalProvinceCode,
            managerCode: finalManagerCode,
            manager,
            remarks:
              remarks && remarks.toString().trim() !== ''
                ? remarks.toString().trim()
                : null,
            isDeleted: false,
          });

          await this.customersRepository.save(customer);

          result.successCount++;
          result.successCodes.push(generatedCode);
        } catch (error) {
          result.errors.push(`第${rowNumber}行：${error.message}`);
          result.failureCount++;
        }
      }

      this.logger.log(
        `Import completed: ${result.successCount} success, ${result.failureCount} failures`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Import failed: ${error.message}`);
      throw new BadRequestException(`导入失败: ${error.message}`);
    }
  }

  /**
   * 从Excel行中获取单元格值（支持多种列名）
   */
  private getExcelCellValue(row: any, possibleKeys: string[]): any {
    for (const key of possibleKeys) {
      if (row[key] !== undefined && row[key] !== null && row[key] !== '') {
        return row[key];
      }
    }
    return null;
  }

  /**
   * 自动生成客户编码
   * 格式：6位数字字符串，从 001000 开始递增
   */
  private async generateCustomerCode(): Promise<string> {
    const startCode = 1000; // 起始编码数字

    // 查找当前最大的客户编码
    const lastCustomer = await this.customersRepository
      .createQueryBuilder('customer')
      .where('customer.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('customer.code ~ :pattern', { pattern: '^[0-9]{6}$' }) // 只匹配6位数字的编码
      .orderBy('CAST(customer.code AS INTEGER)', 'DESC')
      .limit(1)
      .getOne();

    let nextCodeNumber: number;

    if (lastCustomer && lastCustomer.code) {
      // 如果存在客户，获取最大编码并递增
      const lastCodeNumber = parseInt(lastCustomer.code, 10);
      nextCodeNumber = lastCodeNumber + 1;
    } else {
      // 如果没有客户，从起始编码开始
      nextCodeNumber = startCode;
    }

    // 格式化为6位数字字符串
    const generatedCode = nextCodeNumber.toString().padStart(6, '0');

    // 双重检查：确保生成的编码不存在（防止并发问题）
    const existingCustomer = await this.customersRepository.findOne({
      where: { code: generatedCode, isDeleted: false },
    });

    if (existingCustomer) {
      // 如果编码已存在，递归生成下一个
      this.logger.warn(
        `Generated code ${generatedCode} already exists, generating next one`,
      );
      return await this.generateCustomerCode();
    }

    this.logger.log(`Generated customer code: ${generatedCode}`);
    return generatedCode;
  }
}
