import { ApiProperty } from '@nestjs/swagger';

export class CustomerResponseDto {
  @ApiProperty({ description: '客户名称', example: '张三' })
  name: string;

  @ApiProperty({ description: '拼音码', example: 'ZS', required: false })
  pinyinCode: string | null;

  @ApiProperty({
    description: '客户手机号',
    example: '13800138000',
    required: false,
  })
  phone: string | null;

  @ApiProperty({
    description: '客户地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  address: string | null;

  @ApiProperty({
    description: '客户负责人编码',
    example: 'user001',
    required: false,
  })
  managerCode: string | null;

  @ApiProperty({
    description: '客户负责人姓名',
    example: '胡斯阔',
    required: false,
  })
  managerName: string | null;

  @ApiProperty({
    description: '省份代码',
    example: 11,
    required: false,
  })
  provinceCode: number | null;

  @ApiProperty({
    description: '省份名称',
    example: '北京市',
    required: false,
  })
  provinceName: string | null;

  @ApiProperty({
    description: '备注',
    example: '重要客户，优先处理',
    required: false,
  })
  remarks: string | null;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class CustomerListResponseDto {
  @ApiProperty({
    description: '客户列表',
    type: [CustomerResponseDto],
  })
  customers: CustomerResponseDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}

export class CustomerSearchResponseDto {
  @ApiProperty({ description: '客户编码', example: 'CUS001' })
  code: string;

  @ApiProperty({ description: '客户名称', example: '张三' })
  name: string;

  @ApiProperty({
    description: '客户手机号',
    example: '13800138000',
    required: false,
  })
  phone: string | null;

  @ApiProperty({
    description: '客户负责人编码',
    example: 'user001',
    required: false,
  })
  managerCode: string | null;

  @ApiProperty({
    description: '客户负责人姓名',
    example: '胡斯阔',
    required: false,
  })
  managerName: string | null;
}
