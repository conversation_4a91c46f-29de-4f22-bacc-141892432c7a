import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsMobilePhone,
  ValidateIf,
  IsInt,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCustomerDto {
  @ApiProperty({
    description: '客户名称（必填）',
    example: '张三',
  })
  @IsString()
  @IsNotEmpty({ message: '客户名称不能为空' })
  name: string;

  @ApiProperty({
    description: '拼音码（可选，如不提供将根据客户名称自动生成）',
    example: 'ZS',
    required: false,
  })
  @IsOptional()
  @IsString()
  pinyinCode?: string;

  @ApiProperty({
    description: '客户手机号',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.phone && o.phone.trim() !== '')
  @IsMobilePhone('zh-CN', {}, { message: '请输入有效的手机号码' })
  phone?: string;

  @ApiProperty({
    description: '客户地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: '客户负责人编码',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  managerCode?: string;

  @ApiProperty({
    description: '省份代码',
    example: 11,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '省份代码必须是整数' })
  @Min(1, { message: '省份代码必须大于0' })
  @ValidateIf((o) => o.provinceCode !== null && o.provinceCode !== undefined)
  provinceCode?: number;

  @ApiProperty({
    description: '备注',
    example: '重要客户，优先处理',
    required: false,
  })
  @IsOptional()
  @IsString()
  remarks?: string;
}
