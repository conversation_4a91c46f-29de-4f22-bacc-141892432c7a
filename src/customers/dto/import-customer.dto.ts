import { ApiProperty } from '@nestjs/swagger';

export class ImportCustomerDto {
  @ApiProperty({
    description: 'Excel文件',
    type: 'string',
    format: 'binary',
  })
  file: Express.Multer.File;
}

export class CustomerImportResult {
  @ApiProperty({ description: '成功导入的数量' })
  successCount: number;

  @ApiProperty({ description: '失败的数量' })
  failureCount: number;

  @ApiProperty({ description: '错误详情', type: [String] })
  errors: string[];

  @ApiProperty({ description: '成功导入的客户编码', type: [String] })
  successCodes: string[];
}

export class CustomerExcelRow {
  @ApiProperty({ description: '客户编码（必填）' })
  code: string;

  @ApiProperty({ description: '客户名称（必填）' })
  name: string;

  @ApiProperty({ description: '拼音码（必填）' })
  pinyinCode: string;

  @ApiProperty({ description: '客户手机号（可选）' })
  phone?: string;

  @ApiProperty({ description: '客户地址（可选）' })
  address?: string;

  @ApiProperty({ description: '省份名称（可选）', example: '北京市' })
  provinceName?: string;

  @ApiProperty({ description: '客户负责人编码（可选）' })
  managerCode?: string;

  @ApiProperty({ description: '备注（可选）' })
  remarks?: string;
}
