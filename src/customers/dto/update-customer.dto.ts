import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, Min, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateCustomerDto {
  @ApiProperty({
    description: '客户名称',
    example: '张三',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: '拼音码',
    example: 'ZS',
    required: false,
  })
  @IsOptional()
  @IsString()
  pinyinCode?: string;

  @ApiProperty({
    description: '客户手机号',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: '客户地址',
    example: '北京市朝阳区xxx街道xxx号',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: '客户负责人编码',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  managerCode?: string;

  @ApiProperty({
    description: '省份代码',
    example: 11,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '省份代码必须是整数' })
  @Min(1, { message: '省份代码必须大于0' })
  @ValidateIf((o) => o.provinceCode !== null && o.provinceCode !== undefined)
  provinceCode?: number;

  @ApiProperty({
    description: '备注',
    example: '重要客户，优先处理',
    required: false,
  })
  @IsOptional()
  @IsString()
  remarks?: string;
}
