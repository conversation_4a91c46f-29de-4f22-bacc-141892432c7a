import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class QueryCustomersDto {
  @ApiProperty({
    description: '页码（必填，0表示获取所有数据）',
    example: 1,
    required: true,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(0, { message: '页码必须大于等于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填，0表示获取所有数据）',
    example: 10,
    required: true,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(0, { message: '每页数量必须大于等于0' })
  pageSize: number;

  @ApiProperty({
    description: '搜索关键词（模糊搜索客户编码和名称）',
    example: 'CUS001',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '省份代码过滤',
    example: 11,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '省份代码必须是整数' })
  @Min(1, { message: '省份代码必须大于0' })
  provinceCode?: number;

  @ApiProperty({
    description: '拼音码过滤',
    example: 'ZS',
    required: false,
  })
  @IsOptional()
  @IsString()
  pinyinCode?: string;

  @ApiProperty({
    description: '负责人编码过滤',
    example: 'husky',
    required: false,
  })
  @IsOptional()
  @IsString()
  managerCode?: string;
}
