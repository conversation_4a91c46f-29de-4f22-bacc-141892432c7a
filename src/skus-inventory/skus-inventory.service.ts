import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { SkuInventory } from './entities/sku-inventory.entity';
import { Sku } from '@/skus/entities/sku.entity';
import { CreateSkuInventoryDto } from './dto/create-sku-inventory.dto';
import {
  UpdateSkuInventoryDto,
  BatchUpdateInventoryDto,
  InventoryCountDto,
} from './dto/update-sku-inventory.dto';
import {
  SkuInventoryDto,
  SkuInventorySummaryDto,
} from './dto/sku-inventory-response.dto';

@Injectable()
export class SkusInventoryService {
  constructor(
    @InjectRepository(SkuInventory)
    private readonly skuInventoryRepository: Repository<SkuInventory>,
    @InjectRepository(Sku)
    private readonly skuRepository: Repository<Sku>,
  ) {}

  // 创建库存记录
  async create(
    createSkuInventoryDto: CreateSkuInventoryDto,
  ): Promise<SkuInventory> {
    const { skuId, size } = createSkuInventoryDto;

    // 验证SKU是否存在
    const sku = await this.skuRepository.findOne({
      where: { id: skuId, isDeleted: false },
    });
    if (!sku) {
      throw new NotFoundException('SKU不存在');
    }

    // 检查是否已存在相同SKU和尺码的库存记录
    const existingInventory = await this.skuInventoryRepository.findOne({
      where: { skuId, size, isDeleted: false },
    });
    if (existingInventory) {
      throw new BadRequestException('该SKU的此尺码库存记录已存在');
    }

    const inventory = this.skuInventoryRepository.create({
      ...createSkuInventoryDto,
      currentStock: createSkuInventoryDto.currentStock || 0,
      purchasingStock: createSkuInventoryDto.purchasingStock || 0,
      needRestockStock: createSkuInventoryDto.needRestockStock || 0,
    });

    // 更新库存状态
    this.updateInventoryStatus(inventory);

    return await this.skuInventoryRepository.save(inventory);
  }

  // 分页查询库存
  async findAll(
    page: number = 1,
    pageSize: number = 10,
    search?: string,
    skuCode?: string,
    brandCode?: string,
    colorCode?: string,
    size?: string,
    stockStatus?: 'normal' | 'out_of_stock',
  ) {
    const queryBuilder = this.createQueryBuilder();

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        "(COALESCE(sku.name, '') ILIKE :search OR sku.code ILIKE :search OR inventory.size ILIKE :search)",
        { search: `%${search}%` },
      );
    }

    if (skuCode) {
      queryBuilder.andWhere('sku.code ILIKE :skuCode', {
        skuCode: `%${skuCode}%`,
      });
    }

    if (brandCode) {
      queryBuilder.andWhere('sku.brandCode = :brandCode', { brandCode });
    }

    if (colorCode) {
      queryBuilder.andWhere('sku.colorCode = :colorCode', { colorCode });
    }

    if (size) {
      queryBuilder.andWhere('inventory.size = :size', { size });
    }

    // 库存状态筛选
    if (stockStatus) {
      switch (stockStatus) {
        case 'out_of_stock':
          queryBuilder.andWhere('inventory.currentStock <= 0');
          break;
        case 'normal':
          queryBuilder.andWhere('inventory.currentStock > 0');
          break;
      }
    }

    // 只返回品牌启用的SKU库存
    queryBuilder.andWhere('brand.isActive = :brandIsActive', {
      brandIsActive: true,
    });

    // 分页
    const offset = (page - 1) * pageSize;
    queryBuilder.skip(offset).take(pageSize);

    // 排序
    queryBuilder.orderBy('inventory.updatedAt', 'DESC');

    const [inventories, total] = await queryBuilder.getManyAndCount();

    return {
      data: inventories.map((inventory) => this.transformToDto(inventory)),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  // 根据ID查询库存
  async findOne(id: string): Promise<SkuInventoryDto> {
    const inventory = await this.skuInventoryRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['sku', 'sku.brand', 'sku.color'],
    });

    if (!inventory) {
      throw new NotFoundException('库存记录不存在');
    }

    return this.transformToDto(inventory);
  }

  // 根据SKU ID查询所有尺码的库存汇总
  async findBySkuId(skuId: string): Promise<SkuInventorySummaryDto> {
    const sku = await this.skuRepository.findOne({
      where: { id: skuId, isDeleted: false },
      relations: ['brand', 'color'],
    });

    if (!sku) {
      throw new NotFoundException('SKU不存在');
    }

    const inventories = await this.skuInventoryRepository.find({
      where: { skuId, isDeleted: false },
      relations: ['sku', 'sku.brand', 'sku.color'],
      order: { size: 'ASC' },
    });

    const sizeInventories = inventories.map((inventory) =>
      this.transformToDto(inventory),
    );

    // 计算汇总数据
    const totalCurrentStock = inventories.reduce(
      (sum, inv) => sum + inv.currentStock,
      0,
    );
    const totalPurchasingStock = inventories.reduce(
      (sum, inv) => sum + inv.purchasingStock,
      0,
    );
    const totalNeedRestockStock = inventories.reduce(
      (sum, inv) => sum + inv.needRestockStock,
      0,
    );
    const grandTotalStock = totalCurrentStock + totalPurchasingStock;

    const hasWarning = false; // 简化版本不需要预警功能
    const hasOutOfStock = inventories.some((inv) => inv.isOutOfStock);

    return {
      skuId: sku.id,
      skuCode: sku.code,
      skuName: sku.name || '',
      brandName: sku.brand?.name || '',
      colorName: sku.color?.name || '',
      totalCurrentStock,
      totalPurchasingStock,
      totalNeedRestockStock,
      grandTotalStock,
      sizeInventories,
      hasWarning,
      hasOutOfStock,
    };
  }

  // 更新库存
  async update(
    id: string,
    updateSkuInventoryDto: UpdateSkuInventoryDto,
  ): Promise<SkuInventoryDto> {
    const inventory = await this.skuInventoryRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!inventory) {
      throw new NotFoundException('库存记录不存在');
    }

    Object.assign(inventory, updateSkuInventoryDto);

    // 更新库存状态
    this.updateInventoryStatus(inventory);

    await this.skuInventoryRepository.save(inventory);

    return this.findOne(id);
  }

  // 批量调整库存
  async batchUpdateStock(
    id: string,
    batchUpdateDto: BatchUpdateInventoryDto,
  ): Promise<SkuInventoryDto> {
    const inventory = await this.skuInventoryRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!inventory) {
      throw new NotFoundException('库存记录不存在');
    }

    const { operation, quantity } = batchUpdateDto;

    switch (operation) {
      case 'add':
        inventory.currentStock += quantity;
        break;
      case 'subtract':
        inventory.currentStock = Math.max(0, inventory.currentStock - quantity);
        break;
      case 'set':
        inventory.currentStock = quantity;
        break;
    }

    // 更新库存状态
    this.updateInventoryStatus(inventory);

    await this.skuInventoryRepository.save(inventory);

    return this.findOne(id);
  }

  // 库存盘点
  async inventoryCount(
    id: string,
    countDto: InventoryCountDto,
  ): Promise<SkuInventoryDto> {
    const inventory = await this.skuInventoryRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!inventory) {
      throw new NotFoundException('库存记录不存在');
    }

    inventory.currentStock = countDto.actualCount;
    inventory.lastInventoryDate = new Date();
    inventory.lastInventoryCount = countDto.actualCount;

    if (countDto.remarks) {
      inventory.remarks = countDto.remarks;
    }

    // 更新库存状态
    this.updateInventoryStatus(inventory);

    await this.skuInventoryRepository.save(inventory);

    return this.findOne(id);
  }

  // 删除库存记录
  async remove(id: string): Promise<void> {
    const inventory = await this.skuInventoryRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!inventory) {
      throw new NotFoundException('库存记录不存在');
    }

    inventory.isDeleted = true;
    await this.skuInventoryRepository.save(inventory);
  }

  // 创建查询构建器
  private createQueryBuilder(): SelectQueryBuilder<SkuInventory> {
    return this.skuInventoryRepository
      .createQueryBuilder('inventory')
      .leftJoinAndSelect('inventory.sku', 'sku')
      .leftJoinAndSelect('sku.brand', 'brand')
      .leftJoinAndSelect('sku.color', 'color')
      .where('inventory.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('sku.isDeleted = :skuIsDeleted', { skuIsDeleted: false });
  }

  // 更新库存状态
  private updateInventoryStatus(inventory: SkuInventory): void {
    inventory.isOutOfStock = inventory.currentStock <= 0;
  }

  // 转换为DTO
  private transformToDto(inventory: SkuInventory): SkuInventoryDto {
    return {
      id: inventory.id,
      skuId: inventory.skuId,
      skuCode: inventory.sku?.code || '',
      skuName: inventory.sku?.name || '',
      brandCode: inventory.sku?.brandCode || '',
      brandName: inventory.sku?.brand?.name || '',
      colorCode: inventory.sku?.colorCode || '',
      colorName: inventory.sku?.color?.name || '',
      size: inventory.size,
      currentStock: inventory.currentStock,
      purchasingStock: inventory.purchasingStock,
      needRestockStock: inventory.needRestockStock,
      totalStock: inventory.totalStock,
      availableStock: inventory.availableStock,
      isOutOfStock: inventory.isOutOfStock,
      stockStatus: inventory.stockStatus,
      brandIsActive: inventory.sku?.brand?.isActive || false,
      skuImages: inventory.sku?.images || [],
      remarks: inventory.remarks,
      lastInventoryDate: inventory.lastInventoryDate,
      lastInventoryCount: inventory.lastInventoryCount,
      createdAt: inventory.createdAt,
      updatedAt: inventory.updatedAt,
    };
  }
}
