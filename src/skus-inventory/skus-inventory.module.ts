import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SkusInventoryService } from './skus-inventory.service';
import { SkusInventoryController } from './skus-inventory.controller';
import { SkuInventory } from './entities/sku-inventory.entity';
import { Sku } from '@/skus/entities/sku.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SkuInventory, Sku])],
  controllers: [SkusInventoryController],
  providers: [SkusInventoryService],
  exports: [SkusInventoryService],
})
export class SkusInventoryModule {}
