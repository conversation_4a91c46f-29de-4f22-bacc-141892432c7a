import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Sku } from '@/skus/entities/sku.entity';

@Entity('skus_inventory')
@Index(['skuId', 'size'], { unique: true }) // 确保同一SKU的同一尺码只有一条库存记录
export class SkuInventory {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({
    description: '库存记录ID（UUID）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  // SKU关联（多对一）
  @ManyToOne(() => Sku, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'skuId' })
  @ApiProperty({
    description: 'SKU信息',
    type: () => Sku,
  })
  sku: Sku;

  @Column({ type: 'uuid' })
  @ApiProperty({
    description: 'SKU ID（关联skus表）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  skuId: string;

  @Column({ type: 'varchar', length: 20 })
  @ApiProperty({
    description: '尺码（如：S、M、L、XL等）',
    example: 'M',
  })
  size: string;

  // 库存数量字段
  @Column({
    type: 'int',
    default: 0,
    comment: '当前库存数量',
  })
  @ApiProperty({
    description: '当前库存数量',
    example: 100,
    default: 0,
  })
  currentStock: number;

  @Column({
    type: 'int',
    default: 0,
    comment: '采购中库存数量',
  })
  @ApiProperty({
    description: '采购中库存数量（已下单但未到货）',
    example: 50,
    default: 0,
  })
  purchasingStock: number;

  @Column({
    type: 'int',
    default: 0,
    comment: '需补库存数量',
  })
  @ApiProperty({
    description: '需补库存数量（建议补货数量）',
    example: 30,
    default: 0,
  })
  needRestockStock: number;

  // 简化的业务状态字段
  @Column({
    type: 'boolean',
    default: false,
    comment: '是否缺货',
  })
  @ApiProperty({
    description: '是否缺货（当前库存为0）',
    example: false,
    default: false,
  })
  isOutOfStock: boolean;

  // 备注字段
  @Column({ type: 'text', nullable: true })
  @ApiProperty({
    description: '库存备注（选填）',
    example: '季节性商品，注意及时补货',
    required: false,
  })
  remarks: string | null;

  // 最后盘点信息
  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '最后盘点时间',
  })
  @ApiProperty({
    description: '最后盘点时间',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  lastInventoryDate: Date | null;

  @Column({
    type: 'int',
    nullable: true,
    comment: '最后盘点数量',
  })
  @ApiProperty({
    description: '最后盘点数量',
    example: 95,
    required: false,
  })
  lastInventoryCount: number | null;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  // 计算属性：总库存（当前库存 + 采购中库存）
  get totalStock(): number {
    return this.currentStock + this.purchasingStock;
  }

  // 计算属性：可用库存（当前库存）
  get availableStock(): number {
    return this.currentStock;
  }

  // 计算属性：库存状态（简化版）
  get stockStatus(): 'normal' | 'out_of_stock' {
    return this.currentStock <= 0 ? 'out_of_stock' : 'normal';
  }
}
