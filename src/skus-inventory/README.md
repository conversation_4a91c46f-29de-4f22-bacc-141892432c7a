# SKU库存管理系统

## 概述

SKU库存管理系统是一个完整的库存管理解决方案，专为服装行业设计。系统支持多尺码库存管理，每个SKU可以有多个尺码（S、M、L、XL等），每个尺码都有独立的库存记录。系统通过SKU关联品牌信息，只显示启用品牌的库存数据。

## 核心功能

### 1. 库存记录管理

- **创建库存记录**：为SKU的特定尺码创建库存记录
- **更新库存信息**：修改库存数量、预警阈值等信息
- **删除库存记录**：软删除库存记录

### 2. 库存类型

- **当前库存**（currentStock）：实际可用的库存数量
- **采购中库存**（purchasingStock）：已下单但未到货的库存数量
- **需补库存**（needRestockStock）：建议补货的数量
- **总库存**：当前库存 + 采购中库存（计算属性）
- **SKU总库存**：某个SKU所有尺码的库存汇总

### 3. 库存状态管理

- **正常**（normal）：有库存可用
- **缺货**（out_of_stock）：库存为0

### 4. 品牌启用控制

- 只显示启用品牌的SKU库存数据
- 通过SKU关联品牌，自动过滤未启用品牌
- 在响应中包含品牌启用状态信息

### 4. 库存操作

- **批量调整**：支持增加、减少、设置库存数量
- **库存盘点**：记录实际盘点数量和时间
- **库存预警**：自动计算和更新预警状态

### 5. 查询功能

- **分页查询**：支持多条件筛选的分页查询
- **SKU汇总**：按SKU汇总所有尺码的库存信息
- **库存详情**：查询单个库存记录的详细信息

## 数据库设计

### 库存表（skus_inventory）

| 字段名             | 类型        | 说明           |
| ------------------ | ----------- | -------------- |
| id                 | UUID        | 主键           |
| skuId              | UUID        | SKU ID（外键） |
| size               | VARCHAR(20) | 尺码           |
| currentStock       | INT         | 当前库存数量   |
| purchasingStock    | INT         | 采购中库存数量 |
| needRestockStock   | INT         | 需补库存数量   |
| isOutOfStock       | BOOLEAN     | 是否缺货       |
| remarks            | TEXT        | 库存备注       |
| lastInventoryDate  | TIMESTAMP   | 最后盘点时间   |
| lastInventoryCount | INT         | 最后盘点数量   |
| isDeleted          | BOOLEAN     | 是否已删除     |
| createdAt          | TIMESTAMP   | 创建时间       |
| updatedAt          | TIMESTAMP   | 更新时间       |

### 索引设计

- **唯一索引**：(skuId, size) - 确保同一SKU的同一尺码只有一条库存记录
- **普通索引**：size, currentStock, isActive, isWarning, isOutOfStock, isDeleted, updatedAt

## API接口

### 1. 创建库存记录

```
POST /skus-inventory
```

### 2. 分页查询库存

```
GET /skus-inventory?page=1&limit=10&search=关键词&stockStatus=warning
```

支持的查询参数：

- `page`: 页码
- `limit`: 每页数量
- `search`: 搜索关键词（SKU名称、编码、尺码）
- `skuCode`: SKU编码筛选
- `brandCode`: 品牌编码筛选
- `supplierCode`: 供应商编码筛选
- `colorCode`: 颜色编码筛选
- `size`: 尺码筛选
- `stockStatus`: 库存状态筛选（normal/out_of_stock）

**注意**：系统自动过滤未启用品牌的SKU库存，只返回启用品牌的数据。

### 3. SKU库存汇总

```
GET /skus-inventory/sku/:skuId
```

### 4. 库存详情

```
GET /skus-inventory/:id
```

### 5. 更新库存

```
PATCH /skus-inventory/:id
```

### 6. 批量调整库存

```
PATCH /skus-inventory/:id/stock
```

### 7. 库存盘点

```
PATCH /skus-inventory/:id/count
```

### 8. 删除库存记录

```
DELETE /skus-inventory/:id
```

## 业务逻辑

### 1. 库存状态自动更新

系统会根据当前库存数量和预警阈值自动更新库存状态：

- 当前库存 <= 0：缺货状态
- 当前库存 <= 最低阈值：严重预警状态
- 当前库存 <= 预警阈值：预警状态
- 当前库存 > 预警阈值：正常状态

### 2. SKU汇总计算

系统会自动计算SKU的汇总库存信息：

- 总当前库存：所有尺码的当前库存之和
- 总采购中库存：所有尺码的采购中库存之和
- 总需补库存：所有尺码的需补库存之和
- 总库存：总当前库存 + 总采购中库存

### 3. 库存预警

- 系统会自动标记预警和缺货状态
- 支持按库存状态筛选查询
- SKU汇总中会显示是否有预警或缺货的尺码

## 使用示例

### 1. 创建库存记录

```json
{
  "skuId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "size": "M",
  "currentStock": 100,
  "purchasingStock": 50,
  "needRestockStock": 30,
  "warningThreshold": 10,
  "minThreshold": 5,
  "location": "A区-01货架",
  "remarks": "季节性商品，注意及时补货"
}
```

### 2. 批量调整库存

```json
{
  "operation": "add",
  "quantity": 50,
  "reason": "采购入库"
}
```

### 3. 库存盘点

```json
{
  "actualCount": 95,
  "remarks": "月度盘点"
}
```

## 注意事项

1. **唯一性约束**：同一SKU的同一尺码只能有一条库存记录
2. **级联删除**：删除SKU时会自动删除相关的库存记录
3. **软删除**：库存记录采用软删除机制，不会物理删除数据
4. **状态自动更新**：库存状态会在数据变更时自动更新
5. **成本计算**：库存成本信息从SKU表中获取，无需在库存表中重复存储

## 扩展功能

后续可以考虑添加的功能：

- 库存变动历史记录
- 库存预警通知
- 库存报表统计
- 库存转移功能
- 批量导入导出
