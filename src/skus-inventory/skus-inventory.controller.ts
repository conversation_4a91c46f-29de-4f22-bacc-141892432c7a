import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseUUIDPipe,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SkusInventoryService } from './skus-inventory.service';
import { CreateSkuInventoryDto } from './dto/create-sku-inventory.dto';
import {
  UpdateSkuInventoryDto,
  BatchUpdateInventoryDto,
  InventoryCountDto,
} from './dto/update-sku-inventory.dto';
import {
  SkuInventoryDto,
  SkuInventorySummaryDto,
} from './dto/sku-inventory-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('库存管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('skus-inventory')
export class SkusInventoryController {
  constructor(private readonly skusInventoryService: SkusInventoryService) {}

  @Post()
  @ApiOperation({ summary: '创建库存记录' })
  @ApiResponse({
    status: 200,
    description: '创建成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '创建成功' },
        data: { type: 'null' },
      },
    },
  })
  async create(@Body() createSkuInventoryDto: CreateSkuInventoryDto) {
    await this.skusInventoryService.create(createSkuInventoryDto);
    return {
      code: 200,
      message: '创建成功',
      data: null,
    };
  }

  @Get()
  @ApiOperation({ summary: '分页查询库存列表' })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: '每页数量',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: '搜索关键词（SKU名称、编码、尺码）',
  })
  @ApiQuery({ name: 'skuCode', required: false, description: 'SKU编码筛选' })
  @ApiQuery({ name: 'brandCode', required: false, description: '品牌编码筛选' })
  @ApiQuery({ name: 'colorCode', required: false, description: '颜色编码筛选' })
  @ApiQuery({ name: 'size', required: false, description: '尺码筛选' })
  @ApiQuery({
    name: 'stockStatus',
    required: false,
    description: '库存状态筛选',
    enum: ['normal', 'out_of_stock'],
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '查询成功' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/SkuInventoryDto' },
            },
            total: { type: 'number', example: 100 },
            page: { type: 'number', example: 1 },
            pageSize: { type: 'number', example: 10 },
            totalPages: { type: 'number', example: 10 },
          },
        },
      },
    },
  })
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('pageSize', new ParseIntPipe({ optional: true })) pageSize?: number,
    @Query('search') search?: string,
    @Query('skuCode') skuCode?: string,
    @Query('brandCode') brandCode?: string,
    @Query('colorCode') colorCode?: string,
    @Query('size') size?: string,
    @Query('stockStatus') stockStatus?: 'normal' | 'out_of_stock',
  ) {
    const result = await this.skusInventoryService.findAll(
      page,
      pageSize,
      search,
      skuCode,
      brandCode,
      colorCode,
      size,
      stockStatus,
    );
    return {
      code: 200,
      message: '查询成功',
      data: result,
    };
  }

  @Get('sku/:skuId')
  @ApiOperation({ summary: '根据SKU ID查询库存汇总' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '查询成功' },
        data: { $ref: '#/components/schemas/SkuInventorySummaryDto' },
      },
    },
  })
  async findBySkuId(@Param('skuId', ParseUUIDPipe) skuId: string) {
    const result = await this.skusInventoryService.findBySkuId(skuId);
    return {
      code: 200,
      message: '查询成功',
      data: result,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询库存详情' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '查询成功' },
        data: { $ref: '#/components/schemas/SkuInventoryDto' },
      },
    },
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    const result = await this.skusInventoryService.findOne(id);
    return {
      code: 200,
      message: '查询成功',
      data: result,
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新库存记录' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '更新成功' },
        data: { type: 'null' },
      },
    },
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateSkuInventoryDto: UpdateSkuInventoryDto,
  ) {
    await this.skusInventoryService.update(id, updateSkuInventoryDto);
    return {
      code: 200,
      message: '更新成功',
      data: null,
    };
  }

  @Patch(':id/stock')
  @ApiOperation({ summary: '批量调整库存' })
  @ApiResponse({
    status: 200,
    description: '调整成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '调整成功' },
        data: { type: 'null' },
      },
    },
  })
  async batchUpdateStock(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() batchUpdateDto: BatchUpdateInventoryDto,
  ) {
    await this.skusInventoryService.batchUpdateStock(id, batchUpdateDto);
    return {
      code: 200,
      message: '调整成功',
      data: null,
    };
  }

  @Patch(':id/count')
  @ApiOperation({ summary: '库存盘点' })
  @ApiResponse({
    status: 200,
    description: '盘点成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '盘点成功' },
        data: { type: 'null' },
      },
    },
  })
  async inventoryCount(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() countDto: InventoryCountDto,
  ) {
    await this.skusInventoryService.inventoryCount(id, countDto);
    return {
      code: 200,
      message: '盘点成功',
      data: null,
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除库存记录' })
  @ApiResponse({
    status: 200,
    description: '删除成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: '删除成功' },
        data: { type: 'null' },
      },
    },
  })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    await this.skusInventoryService.remove(id);
    return {
      code: 200,
      message: '删除成功',
      data: null,
    };
  }
}
