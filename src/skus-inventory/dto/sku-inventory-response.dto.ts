import { ApiProperty } from '@nestjs/swagger';

export class SkuInventoryDto {
  @ApiProperty({
    description: '库存记录ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'SKU ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  skuId: string;

  @ApiProperty({
    description: 'SKU编码',
    example: '256770101',
  })
  skuCode: string;

  @ApiProperty({
    description: 'SKU名称',
    example: '时尚T恤',
    nullable: true,
  })
  skuName: string;

  @ApiProperty({
    description: '品牌编码',
    example: 'BRAND001',
  })
  brandCode: string;

  @ApiProperty({
    description: '品牌名称',
    example: '时尚品牌',
  })
  brandName: string;

  @ApiProperty({
    description: '颜色编码',
    example: 'COLOR001',
  })
  colorCode: string;

  @ApiProperty({
    description: '颜色名称',
    example: '红色',
  })
  colorName: string;

  @ApiProperty({
    description: '尺码',
    example: 'M',
  })
  size: string;

  @ApiProperty({
    description: '当前库存数量',
    example: 100,
  })
  currentStock: number;

  @ApiProperty({
    description: '采购中库存数量',
    example: 50,
  })
  purchasingStock: number;

  @ApiProperty({
    description: '需补库存数量',
    example: 30,
  })
  needRestockStock: number;

  @ApiProperty({
    description: '总库存（当前库存 + 采购中库存）',
    example: 150,
  })
  totalStock: number;

  @ApiProperty({
    description: '可用库存（当前库存）',
    example: 100,
  })
  availableStock: number;

  @ApiProperty({
    description: '是否缺货',
    example: false,
  })
  isOutOfStock: boolean;

  @ApiProperty({
    description: '库存状态',
    enum: ['normal', 'out_of_stock'],
    example: 'normal',
  })
  stockStatus: 'normal' | 'out_of_stock';

  @ApiProperty({
    description: '品牌是否启用',
    example: true,
  })
  brandIsActive: boolean;

  @ApiProperty({
    description: 'SKU图片列表',
    example: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ],
    type: [String],
  })
  skuImages: string[];

  @ApiProperty({
    description: '库存备注',
    example: '季节性商品，注意及时补货',
    nullable: true,
  })
  remarks: string | null;

  @ApiProperty({
    description: '最后盘点时间',
    example: '2024-01-01T00:00:00.000Z',
    nullable: true,
  })
  lastInventoryDate: Date | null;

  @ApiProperty({
    description: '最后盘点数量',
    example: 95,
    nullable: true,
  })
  lastInventoryCount: number | null;

  @ApiProperty({
    description: '创建时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

// SKU汇总库存DTO（按SKU汇总所有尺码的库存）
export class SkuInventorySummaryDto {
  @ApiProperty({
    description: 'SKU ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  skuId: string;

  @ApiProperty({
    description: 'SKU编码',
    example: '256770101',
  })
  skuCode: string;

  @ApiProperty({
    description: 'SKU名称',
    example: '时尚T恤',
    nullable: true,
  })
  skuName: string;

  @ApiProperty({
    description: '品牌名称',
    example: '时尚品牌',
  })
  brandName: string;

  @ApiProperty({
    description: '颜色名称',
    example: '红色',
  })
  colorName: string;

  @ApiProperty({
    description: '总当前库存（所有尺码汇总）',
    example: 500,
  })
  totalCurrentStock: number;

  @ApiProperty({
    description: '总采购中库存（所有尺码汇总）',
    example: 200,
  })
  totalPurchasingStock: number;

  @ApiProperty({
    description: '总需补库存（所有尺码汇总）',
    example: 150,
  })
  totalNeedRestockStock: number;

  @ApiProperty({
    description: '总库存（所有尺码的当前库存+采购中库存）',
    example: 700,
  })
  grandTotalStock: number;

  @ApiProperty({
    description: '尺码库存详情',
    type: [SkuInventoryDto],
  })
  sizeInventories: SkuInventoryDto[];

  @ApiProperty({
    description: '是否有预警库存',
    example: false,
  })
  hasWarning: boolean;

  @ApiProperty({
    description: '是否有缺货',
    example: false,
  })
  hasOutOfStock: boolean;
}
