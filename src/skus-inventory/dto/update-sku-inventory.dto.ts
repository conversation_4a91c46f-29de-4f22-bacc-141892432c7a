import { PartialType } from '@nestjs/swagger';
import { CreateSkuInventoryDto } from './create-sku-inventory.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, Min, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateSkuInventoryDto extends PartialType(CreateSkuInventoryDto) {
  @ApiProperty({
    description: '最后盘点时间',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate({ message: '最后盘点时间必须是有效的日期格式' })
  lastInventoryDate?: Date;

  @ApiProperty({
    description: '最后盘点数量',
    example: 95,
    required: false,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '最后盘点数量必须是数字' })
  @Min(0, { message: '最后盘点数量不能小于0' })
  lastInventoryCount?: number;
}

// 批量更新库存DTO
export class BatchUpdateInventoryDto {
  @ApiProperty({
    description: '库存调整类型',
    enum: ['add', 'subtract', 'set'],
    example: 'add',
  })
  operation: 'add' | 'subtract' | 'set';

  @ApiProperty({
    description: '调整数量',
    example: 10,
    minimum: 0,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '调整数量必须是数字' })
  @Min(0, { message: '调整数量不能小于0' })
  quantity: number;

  @ApiProperty({
    description: '调整原因',
    example: '盘点调整',
    required: false,
  })
  @IsOptional()
  reason?: string;
}

// 库存盘点DTO
export class InventoryCountDto {
  @ApiProperty({
    description: '实际盘点数量',
    example: 95,
    minimum: 0,
  })
  @Type(() => Number)
  @IsNumber({}, { message: '实际盘点数量必须是数字' })
  @Min(0, { message: '实际盘点数量不能小于0' })
  actualCount: number;

  @ApiProperty({
    description: '盘点备注',
    example: '月度盘点',
    required: false,
  })
  @IsOptional()
  remarks?: string;
}
