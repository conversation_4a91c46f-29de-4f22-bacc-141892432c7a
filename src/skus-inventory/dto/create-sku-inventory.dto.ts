import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateSkuInventoryDto {
  @ApiProperty({
    description: 'SKU ID（关联skus表）',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @IsUUID(4, { message: 'SKU ID必须是有效的UUID格式' })
  @IsNotEmpty({ message: 'SKU ID不能为空' })
  skuId: string;

  @ApiProperty({
    description: '尺码（如：S、M、L、XL等）',
    example: 'M',
  })
  @IsString()
  @IsNotEmpty({ message: '尺码不能为空' })
  size: string;

  @ApiProperty({
    description: '当前库存数量',
    example: 100,
    default: 0,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '当前库存数量必须是数字' })
  @Min(0, { message: '当前库存数量不能小于0' })
  currentStock?: number;

  @ApiProperty({
    description: '采购中库存数量（已下单但未到货）',
    example: 50,
    default: 0,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '采购中库存数量必须是数字' })
  @Min(0, { message: '采购中库存数量不能小于0' })
  purchasingStock?: number;

  @ApiProperty({
    description: '需补库存数量（建议补货数量）',
    example: 30,
    default: 0,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '需补库存数量必须是数字' })
  @Min(0, { message: '需补库存数量不能小于0' })
  needRestockStock?: number;

  @ApiProperty({
    description: '库存备注（选填）',
    example: '季节性商品，注意及时补货',
    required: false,
  })
  @IsOptional()
  @IsString()
  remarks?: string;
}
