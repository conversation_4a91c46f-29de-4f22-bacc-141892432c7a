import { Injectable, BadRequestException, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { UpdateBrandDto } from './dto/update-brand.dto';
import { QueryBrandsDto } from './dto/query-brands.dto';

@Injectable()
export class BrandsService {
  private readonly logger = new Logger(BrandsService.name);

  constructor(
    @InjectRepository(Brand)
    private brandsRepository: Repository<Brand>,
  ) {}

  /**
   * 创建品牌
   */
  async create(createBrandDto: CreateBrandDto): Promise<Brand> {
    this.logger.log(`Creating brand with code: ${createBrandDto.code}`);

    // 检查品牌编码是否已存在
    const existingBrand = await this.brandsRepository.findOne({
      where: { code: createBrandDto.code, isDeleted: false },
    });

    if (existingBrand) {
      throw new BadRequestException('品牌编码已存在');
    }

    // 创建品牌
    const brand = this.brandsRepository.create({
      ...createBrandDto,
      isActive: createBrandDto.isActive ?? true,
      isDeleted: false,
    });

    return await this.brandsRepository.save(brand);
  }

  /**
   * 分页查询品牌列表（支持模糊搜索）
   */
  async findAll(queryDto: QueryBrandsDto) {
    this.logger.log(`Querying brands with params: ${JSON.stringify(queryDto)}`);

    const { page, pageSize, search } = queryDto;

    // page和pageSize是必须参数
    if (page === undefined || pageSize === undefined) {
      return {
        brands: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // 处理分页参数：page=0&pageSize=0表示获取所有数据
    const isGetAll = page === 0 && pageSize === 0;
    const finalPage = isGetAll ? 1 : page;
    const finalPageSize = isGetAll ? undefined : pageSize;
    const skip = isGetAll ? 0 : (page - 1) * pageSize;

    const queryBuilder = this.brandsRepository
      .createQueryBuilder('brand')
      .where('brand.isDeleted = :isDeleted', { isDeleted: false });

    // 模糊搜索品牌编码和名称
    if (search) {
      queryBuilder.andWhere(
        '(brand.code ILIKE :search OR brand.name ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const brands = await queryBuilder
      .select([
        'brand.code',
        'brand.name',
        'brand.orderPrice',
        'brand.restockPrice',
        'brand.spotPrice',
        'brand.isActive',
        'brand.createdAt',
        'brand.updatedAt',
      ])
      .orderBy('brand.createdAt', 'DESC')
      .skip(skip)
      .take(finalPageSize)
      .getMany();

    return {
      brands,
      total,
      page: isGetAll ? 0 : finalPage,
      pageSize: isGetAll ? 0 : finalPageSize!,
      totalPages: isGetAll ? 1 : Math.ceil(total / finalPageSize!),
    };
  }

  /**
   * 根据品牌编码查找品牌
   */
  async findByCode(code: string): Promise<Brand | null> {
    return await this.brandsRepository.findOne({
      where: { code, isDeleted: false },
    });
  }

  /**
   * 根据品牌编码获取品牌详情
   */
  async findOne(code: string): Promise<Brand> {
    const brand = await this.findByCode(code);
    if (!brand) {
      throw new NotFoundException('品牌不存在');
    }
    return brand;
  }

  /**
   * 更新品牌
   */
  async update(code: string, updateBrandDto: UpdateBrandDto): Promise<Brand> {
    this.logger.log(`Updating brand ${code}`);

    // 查找要更新的品牌
    const brand = await this.findOne(code);

    // 更新字段
    if (updateBrandDto.name !== undefined) {
      brand.name = updateBrandDto.name;
    }
    if (updateBrandDto.orderPrice !== undefined) {
      brand.orderPrice = updateBrandDto.orderPrice;
    }
    if (updateBrandDto.restockPrice !== undefined) {
      brand.restockPrice = updateBrandDto.restockPrice;
    }
    if (updateBrandDto.spotPrice !== undefined) {
      brand.spotPrice = updateBrandDto.spotPrice;
    }
    if (updateBrandDto.isActive !== undefined) {
      brand.isActive = updateBrandDto.isActive;
    }

    return await this.brandsRepository.save(brand);
  }

  /**
   * 软删除品牌
   */
  async remove(code: string): Promise<void> {
    this.logger.log(`Soft deleting brand ${code}`);

    // 查找要删除的品牌
    const brand = await this.findOne(code);

    // 软删除
    brand.isDeleted = true;
    brand.deletedAt = new Date();
    await this.brandsRepository.save(brand);
  }

  /**
   * 统计品牌数量
   */
  async countBrands(): Promise<number> {
    return await this.brandsRepository.count({
      where: { isDeleted: false },
    });
  }
}
