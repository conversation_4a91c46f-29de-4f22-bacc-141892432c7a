import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { BrandsService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { UpdateBrandDto } from './dto/update-brand.dto';
import { QueryBrandsDto } from './dto/query-brands.dto';
import { BrandListResponseDto } from './dto/brand-response.dto';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';

@ApiTags('brands')
@Controller('brands')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BrandsController {
  private readonly logger = new Logger(BrandsController.name);

  constructor(private readonly brandsService: BrandsService) {}

  @Post()
  @ApiOperation({ summary: '创建品牌' })
  @ApiResponse({
    status: 200,
    description: '品牌创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '品牌编码已存在',
  })
  async create(@Body() createBrandDto: CreateBrandDto) {
    this.logger.log(`Creating brand ${createBrandDto.code}`);

    await this.brandsService.create(createBrandDto);

    return {
      code: 200,
      data: null,
      message: '品牌创建成功',
    };
  }

  @Get()
  @ApiOperation({ summary: '获取品牌列表（支持模糊搜索）' })
  @ApiResponse({
    status: 200,
    description: '获取品牌列表成功',
    type: BrandListResponseDto,
  })
  async findAll(@Query() queryDto: QueryBrandsDto) {
    this.logger.log(`Querying brands with params: ${JSON.stringify(queryDto)}`);

    const result = await this.brandsService.findAll(queryDto);

    return {
      code: 200,
      data: result,
      message: '获取品牌列表成功',
    };
  }

  @Get(':code')
  @ApiOperation({ summary: '根据品牌编码获取品牌详情' })
  @ApiParam({
    name: 'code',
    description: '品牌编码',
    example: 'BRAND001',
  })
  @ApiResponse({
    status: 200,
    description: '获取品牌详情成功',
  })
  @ApiResponse({
    status: 404,
    description: '品牌不存在',
  })
  async findOne(@Param('code') code: string) {
    this.logger.log(`Getting brand details for code: ${code}`);

    const brand = await this.brandsService.findOne(code);

    return {
      code: 200,
      data: {
        code: brand.code,
        name: brand.name,
        orderPrice: brand.orderPrice,
        restockPrice: brand.restockPrice,
        spotPrice: brand.spotPrice,
        isActive: brand.isActive,
        createdAt: brand.createdAt,
        updatedAt: brand.updatedAt,
      },
      message: '获取品牌详情成功',
    };
  }

  @Patch(':code')
  @ApiOperation({ summary: '更新品牌信息' })
  @ApiParam({
    name: 'code',
    description: '品牌编码',
    example: 'BRAND001',
  })
  @ApiResponse({
    status: 200,
    description: '品牌更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '品牌不存在',
  })
  async update(
    @Param('code') code: string,
    @Body() updateBrandDto: UpdateBrandDto,
  ) {
    this.logger.log(`Updating brand ${code}`);

    await this.brandsService.update(code, updateBrandDto);

    return {
      code: 200,
      data: null,
      message: '品牌更新成功',
    };
  }

  @Delete(':code')
  @ApiOperation({ summary: '删除品牌（软删除）' })
  @ApiParam({
    name: 'code',
    description: '品牌编码',
    example: 'BRAND001',
  })
  @ApiResponse({
    status: 200,
    description: '品牌删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '品牌不存在',
  })
  async remove(@Param('code') code: string) {
    this.logger.log(`Deleting brand ${code}`);

    await this.brandsService.remove(code);

    return {
      code: 200,
      data: null,
      message: '品牌删除成功',
    };
  }
}
