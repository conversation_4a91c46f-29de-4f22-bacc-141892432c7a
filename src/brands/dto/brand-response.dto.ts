import { ApiProperty } from '@nestjs/swagger';

export class BrandResponseDto {
  @ApiProperty({ description: '品牌编码', example: 'BRAND001' })
  code: string;

  @ApiProperty({ description: '品牌名称', example: '耐克' })
  name: string;

  @ApiProperty({ description: '订货价', example: 99.99 })
  orderPrice: number;

  @ApiProperty({ description: '补货价', example: 109.99 })
  restockPrice: number;

  @ApiProperty({ description: '现货价', example: 119.99 })
  spotPrice: number;

  @ApiProperty({ description: '是否激活', example: true })
  isActive: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export class BrandListResponseDto {
  @ApiProperty({ 
    description: '品牌列表', 
    type: [BrandResponseDto] 
  })
  brands: BrandResponseDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  pageSize: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}
