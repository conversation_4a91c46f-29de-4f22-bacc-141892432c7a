import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsBoolean,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateBrandDto {
  @ApiProperty({
    description: '品牌编码（主键）',
    example: 'BRAND001',
  })
  @IsString()
  @IsNotEmpty({ message: '品牌编码不能为空' })
  code: string;

  @ApiProperty({
    description: '品牌名称',
    example: '耐克',
  })
  @IsString()
  @IsNotEmpty({ message: '品牌名称不能为空' })
  name: string;

  @ApiProperty({
    description: '订货价（保留两位小数，必须大于0且小于等于1）',
    example: 0.99,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '订货价必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '订货价必须大于0' })
  @Max(1, { message: '订货价不能大于1' })
  orderPrice: number;

  @ApiProperty({
    description: '补货价（保留两位小数，必须大于0且小于等于1）',
    example: 0.89,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '补货价必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '补货价必须大于0' })
  @Max(1, { message: '补货价不能大于1' })
  restockPrice: number;

  @ApiProperty({
    description: '现货价（保留两位小数，必须大于0且小于等于1）',
    example: 0.79,
  })
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '现货价必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '现货价必须大于0' })
  @Max(1, { message: '现货价不能大于1' })
  spotPrice: number;

  @ApiProperty({
    description: '是否激活',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: '激活状态必须是布尔值' })
  isActive?: boolean;
}
