import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsN<PERSON>ber,
  IsBoolean,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateBrandDto {
  @ApiProperty({
    description: '品牌名称',
    example: '耐克',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: '订货价（保留两位小数，必须大于0且小于等于1）',
    example: 0.99,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '订货价必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '订货价必须大于0' })
  @Max(1, { message: '订货价不能大于1' })
  orderPrice?: number;

  @ApiProperty({
    description: '补货价（保留两位小数，必须大于0且小于等于1）',
    example: 0.89,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '补货价必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '补货价必须大于0' })
  @Max(1, { message: '补货价不能大于1' })
  restockPrice?: number;

  @ApiProperty({
    description: '现货价（保留两位小数，必须大于0且小于等于1）',
    example: 0.79,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: '现货价必须是数字且最多保留两位小数' },
  )
  @Min(0.01, { message: '现货价必须大于0' })
  @Max(1, { message: '现货价不能大于1' })
  spotPrice?: number;

  @ApiProperty({
    description: '是否激活',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '激活状态必须是布尔值' })
  isActive?: boolean;
}
