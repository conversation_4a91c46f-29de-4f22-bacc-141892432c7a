import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('brands')
export class Brand {
  @PrimaryColumn({ type: 'varchar', length: 50, comment: '品牌编码' })
  code: string;

  @Column({ type: 'varchar', length: 100, comment: '品牌名称' })
  name: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '订货价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  orderPrice: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '补货价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  restockPrice: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '现货价',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  spotPrice: number;

  @Column({
    type: 'boolean',
    default: true,
    comment: '是否激活',
  })
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;
}
