import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as COS from 'cos-nodejs-sdk-v5';
import { v4 as uuidv4 } from 'uuid';

export interface FileUploadResult {
  url: string;
  originalName: string;
  fileName: string;
  size: number;
  mimeType: string;
  extension: string;
}

@Injectable()
export class FileUploadService {
  private readonly logger = new Logger(FileUploadService.name);
  private cos: COS;

  constructor(private configService: ConfigService) {
    // 初始化腾讯云COS实例
    this.cos = new COS({
      SecretId: this.configService.get('tencentCos.secretId'),
      SecretKey: this.configService.get('tencentCos.secretKey'),
    });
  }

  /**
   * 上传文件到腾讯云COS
   * @param file 文件对象
   * @param folder 存储文件夹路径
   * @returns 上传后的文件URL
   */
  async uploadFile(
    file: Express.Multer.File,
    folder: string = 'uploads',
  ): Promise<string> {
    if (!file) {
      throw new BadRequestException('文件不能为空');
    }

    try {
      this.logger.log(`Uploading file: ${file.originalname}`);

      // 生成唯一文件名
      const fileExt = file.originalname.split('.').pop();
      // 清理文件扩展名，移除可能的特殊字符
      const cleanExt = fileExt ? fileExt.replace(/[^a-zA-Z0-9]/g, '') : 'bin';
      const fileName = `${folder}/${uuidv4()}.${cleanExt}`;

      // 上传文件到腾讯云COS
      const result = await this.uploadToCOS(file.buffer, fileName);

      return result;
    } catch (error) {
      this.logger.error(`Failed to upload file: ${error.message}`);
      throw new BadRequestException(`文件上传失败: ${error.message}`);
    }
  }

  /**
   * 上传多个文件到腾讯云COS
   * @param files 文件对象数组
   * @param folder 存储文件夹路径
   * @returns 上传后的文件URL数组
   */
  async uploadMultipleFiles(
    files: Express.Multer.File[],
    folder: string = 'uploads',
  ): Promise<string[]> {
    if (!files || files.length === 0) {
      throw new BadRequestException('文件不能为空');
    }

    try {
      this.logger.log(`Uploading ${files.length} files`);

      const uploadPromises = files.map((file) => this.uploadFile(file, folder));
      return await Promise.all(uploadPromises);
    } catch (error) {
      this.logger.error(`Failed to upload multiple files: ${error.message}`);
      throw new BadRequestException(`文件上传失败: ${error.message}`);
    }
  }

  /**
   * 上传文件到腾讯云COS
   * @param fileBuffer 文件Buffer
   * @param fileName 文件名
   * @returns 上传后的文件URL
   */
  private async uploadToCOS(
    fileBuffer: Buffer,
    fileName: string,
  ): Promise<string> {
    const region = this.configService.get('tencentCos.region');
    const bucket = this.configService.get('tencentCos.bucket');
    const baseUrl = this.configService.get('tencentCos.baseUrl');

    return new Promise((resolve, reject) => {
      this.cos.putObject(
        {
          Bucket: bucket,
          Region: region,
          Key: fileName,
          Body: fileBuffer,
        },
        (err, _data) => {
          if (err) {
            this.logger.error(`COS upload error: ${err.message}`);
            return reject(err);
          }

          // 构建文件URL
          let fileUrl: string;
          if (baseUrl) {
            // 确保 baseUrl 不以斜杠结尾，fileName 不以斜杠开头
            const base = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
            const file = fileName.startsWith('/')
              ? fileName.substring(1)
              : fileName;
            fileUrl = `${base}/${file}`;
          } else {
            fileUrl = `https://${bucket}.cos.${region}.myqcloud.com/${fileName}`;
          }
          this.logger.log(`File uploaded successfully: ${fileUrl}`);
          resolve(fileUrl);
        },
      );
    });
  }

  /**
   * 上传通用文件到腾讯云COS（支持文档、PDF等）
   * @param file 文件对象
   * @param folder 存储文件夹路径
   * @returns 上传后的文件信息
   */
  async uploadGeneralFile(
    file: Express.Multer.File,
    folder: string = 'documents',
  ): Promise<FileUploadResult> {
    if (!file) {
      throw new BadRequestException('文件不能为空');
    }

    try {
      this.logger.log(`Uploading general file: ${file.originalname}`);

      // 生成唯一文件名
      const fileExt = file.originalname.split('.').pop();
      // 清理文件扩展名，移除可能的特殊字符
      const cleanExt = fileExt ? fileExt.replace(/[^a-zA-Z0-9]/g, '') : 'bin';
      const fileName = `${folder}/${uuidv4()}.${cleanExt}`;

      // 上传文件到腾讯云COS
      const fileUrl = await this.uploadToCOS(file.buffer, fileName);

      // 返回详细的文件信息
      return {
        url: fileUrl,
        originalName: file.originalname,
        fileName: fileName,
        size: file.size,
        mimeType: file.mimetype,
        extension: cleanExt,
      };
    } catch (error) {
      this.logger.error(`Failed to upload general file: ${error.message}`);
      throw new BadRequestException(`文件上传失败: ${error.message}`);
    }
  }

  /**
   * 上传多个通用文件到腾讯云COS
   * @param files 文件对象数组
   * @param folder 存储文件夹路径
   * @returns 上传后的文件信息数组
   */
  async uploadMultipleGeneralFiles(
    files: Express.Multer.File[],
    folder: string = 'documents',
  ): Promise<FileUploadResult[]> {
    if (!files || files.length === 0) {
      throw new BadRequestException('文件不能为空');
    }

    try {
      this.logger.log(`Uploading ${files.length} general files`);

      const uploadPromises = files.map((file) =>
        this.uploadGeneralFile(file, folder),
      );
      return await Promise.all(uploadPromises);
    } catch (error) {
      this.logger.error(
        `Failed to upload multiple general files: ${error.message}`,
      );
      throw new BadRequestException(`文件上传失败: ${error.message}`);
    }
  }

  /**
   * 验证文件类型是否为支持的通用文件类型
   * @param mimetype 文件MIME类型
   * @param filename 文件名
   * @returns 是否为支持的文件类型
   */
  isValidGeneralFileType(mimetype: string, filename: string): boolean {
    const allowedMimeTypes = [
      // PDF
      'application/pdf',
      // Microsoft Office
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      // Text files
      'text/plain',
      'text/csv',
      // Archives
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
    ];

    const allowedExtensions = [
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'txt',
      'csv',
      'zip',
      'rar',
      '7z',
    ];

    const fileExt = filename.split('.').pop()?.toLowerCase();

    return (
      allowedMimeTypes.includes(mimetype) ||
      (fileExt ? allowedExtensions.includes(fileExt) : false)
    );
  }

  /**
   * 删除腾讯云COS上的文件
   * @param fileUrl 文件URL
   */
  async deleteFile(fileUrl: string): Promise<void> {
    try {
      const region = this.configService.get('tencentCos.region');
      const bucket = this.configService.get('tencentCos.bucket');
      const baseUrl = this.configService.get('tencentCos.baseUrl');

      // 从URL中提取文件路径
      let key: string;
      if (baseUrl && fileUrl.startsWith(baseUrl)) {
        key = fileUrl.substring(baseUrl.length + 1);
      } else {
        const cosUrlPattern = new RegExp(
          `https://${bucket}.cos.${region}.myqcloud.com/(.*)`,
        );
        const match = fileUrl.match(cosUrlPattern);
        if (!match) {
          throw new Error('无效的文件URL');
        }
        key = match[1];
      }

      this.logger.log(`Deleting file: ${key}`);

      await new Promise<void>((resolve, reject) => {
        this.cos.deleteObject(
          {
            Bucket: bucket,
            Region: region,
            Key: key,
          },
          (err, _data) => {
            if (err) {
              this.logger.error(`COS delete error: ${err.message}`);
              return reject(err);
            }
            this.logger.log(`File deleted successfully: ${key}`);
            resolve();
          },
        );
      });
    } catch (error) {
      this.logger.error(`Failed to delete file: ${error.message}`);
      throw new BadRequestException(`文件删除失败: ${error.message}`);
    }
  }
}
