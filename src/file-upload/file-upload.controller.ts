import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  Logger,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
  Body,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';
import { FileUploadService, FileUploadResult } from './file-upload.service';

@ApiTags('file-upload')
@Controller('file-upload')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FileUploadController {
  private readonly logger = new Logger(FileUploadController.name);

  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('image')
  @ApiOperation({ summary: '上传单个图片' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        folder: {
          type: 'string',
          description: '存储文件夹路径，例如：products, brands 等',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ fileType: /(jpg|jpeg|png|gif|webp)$/ }),
        ],
      }),
    )
    file: Express.Multer.File,
    @Body('folder') folder: string = 'images',
  ) {
    this.logger.log(
      `Uploading image to folder ${folder}: ${file.originalname}`,
    );
    const imageUrl = await this.fileUploadService.uploadFile(file, folder);

    return {
      code: 200,
      data: { url: imageUrl },
      message: '图片上传成功',
    };
  }

  @Post('images')
  @ApiOperation({ summary: '上传多个图片' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        folder: {
          type: 'string',
          description: '存储文件夹路径，例如：products, brands 等',
        },
      },
    },
  })
  @UseInterceptors(FilesInterceptor('files', 10)) // 最多10张图片
  async uploadImages(
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ fileType: /(jpg|jpeg|png|gif|webp)$/ }),
        ],
      }),
    )
    files: Express.Multer.File[],
    @Body('folder') folder: string = 'images',
  ) {
    this.logger.log(`Uploading ${files.length} images to folder ${folder}`);
    const imageUrls = await this.fileUploadService.uploadMultipleFiles(
      files,
      folder,
    );

    return {
      code: 200,
      data: { urls: imageUrls },
      message: '图片上传成功',
    };
  }

  @Post('file')
  @ApiOperation({ summary: '上传单个通用文件（文档、PDF等）' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        folder: {
          type: 'string',
          description: '存储文件夹路径，例如：documents, contracts 等',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }), // 10MB
        ],
      }),
    )
    file: Express.Multer.File,
    @Body('folder') folder: string = 'documents',
  ) {
    // Validate file type
    if (
      !this.fileUploadService.isValidGeneralFileType(
        file.mimetype,
        file.originalname,
      )
    ) {
      throw new BadRequestException(
        '不支持的文件类型。支持的格式：PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, CSV, ZIP, RAR, 7Z',
      );
    }

    this.logger.log(`Uploading file to folder ${folder}: ${file.originalname}`);
    const fileResult: FileUploadResult =
      await this.fileUploadService.uploadGeneralFile(file, folder);

    return {
      code: 200,
      data: fileResult,
      message: '文件上传成功',
    };
  }

  @Post('files')
  @ApiOperation({ summary: '上传多个通用文件（文档、PDF等）' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        folder: {
          type: 'string',
          description: '存储文件夹路径，例如：documents, contracts 等',
        },
      },
    },
  })
  @UseInterceptors(FilesInterceptor('files', 5)) // 最多5个文件
  async uploadFiles(
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }), // 10MB
        ],
      }),
    )
    files: Express.Multer.File[],
    @Body('folder') folder: string = 'documents',
  ) {
    // Validate file types
    for (const file of files) {
      if (
        !this.fileUploadService.isValidGeneralFileType(
          file.mimetype,
          file.originalname,
        )
      ) {
        throw new BadRequestException(
          `文件 ${file.originalname} 不支持。支持的格式：PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, CSV, ZIP, RAR, 7Z`,
        );
      }
    }

    this.logger.log(`Uploading ${files.length} files to folder ${folder}`);
    const fileResults: FileUploadResult[] =
      await this.fileUploadService.uploadMultipleGeneralFiles(files, folder);

    return {
      code: 200,
      data: { files: fileResults },
      message: '文件上传成功',
    };
  }
}
