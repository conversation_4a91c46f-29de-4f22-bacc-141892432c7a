import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsEnum,
  ValidateIf,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CustomerProfileDetailType } from '../entities/customer-profile-detail.entity';

export class CreateCustomerProfileDetailDto {
  @ApiProperty({
    description: '客户编码（必填）',
    example: 'CUS001',
  })
  @IsString()
  @IsNotEmpty({ message: '客户编码不能为空' })
  customerCode: string;

  @ApiProperty({
    description: '明细类型',
    enum: CustomerProfileDetailType,
    example: CustomerProfileDetailType.EXCHANGE,
  })
  @IsEnum(CustomerProfileDetailType, {
    message: '明细类型必须是 exchange/refund/order/restock/drop_shipping 之一',
  })
  type: CustomerProfileDetailType;

  // 换货/退货相关字段
  @ApiProperty({
    description: '客户返回商品数量（换货/退货时必填）',
    example: 10,
    required: false,
  })
  @ValidateIf((o) => 
    o.type === CustomerProfileDetailType.EXCHANGE || 
    o.type === CustomerProfileDetailType.REFUND
  )
  @IsNotEmpty({ message: '换货/退货时客户返回商品数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '客户返回商品数量必须是整数' })
  @Min(1, { message: '客户返回商品数量必须大于0' })
  returnQuantity?: number;

  @ApiProperty({
    description: '商品总货款（换货/退货时必填）',
    example: 5000.00,
    required: false,
  })
  @ValidateIf((o) => 
    o.type === CustomerProfileDetailType.EXCHANGE || 
    o.type === CustomerProfileDetailType.REFUND
  )
  @IsNotEmpty({ message: '换货/退货时商品总货款不能为空' })
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '商品总货款必须是数字且最多保留两位小数' })
  @Min(0.01, { message: '商品总货款必须大于0' })
  totalGoodsAmount?: number;

  @ApiProperty({
    description: '换货订单字符串（换货/退货时可选）',
    example: 'EXC001',
    required: false,
  })
  @IsOptional()
  @IsString()
  exchangeOrderString?: string;

  // 订货相关字段
  @ApiProperty({
    description: '定金（订货时必填）',
    example: 2000.00,
    required: false,
  })
  @ValidateIf((o) => o.type === CustomerProfileDetailType.ORDER)
  @IsNotEmpty({ message: '订货时定金不能为空' })
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '定金必须是数字且最多保留两位小数' })
  @Min(0, { message: '定金不能小于0' })
  deposit?: number;

  @ApiProperty({
    description: '采购订单ID（订货时可选）',
    example: 'PO001',
    required: false,
  })
  @IsOptional()
  @IsString()
  purchaseOrderId?: string;

  @ApiProperty({
    description: '预定商品数量（订货时必填）',
    example: 50,
    required: false,
  })
  @ValidateIf((o) => o.type === CustomerProfileDetailType.ORDER)
  @IsNotEmpty({ message: '订货时预定商品数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '预定商品数量必须是整数' })
  @Min(1, { message: '预定商品数量必须大于0' })
  reservedQuantity?: number;

  // 补货相关字段
  @ApiProperty({
    description: '补货成交款（补货时必填）',
    example: 8000.00,
    required: false,
  })
  @ValidateIf((o) => o.type === CustomerProfileDetailType.RESTOCK)
  @IsNotEmpty({ message: '补货时补货成交款不能为空' })
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '补货成交款必须是数字且最多保留两位小数' })
  @Min(0.01, { message: '补货成交款必须大于0' })
  restockDealAmount?: number;

  @ApiProperty({
    description: '补货总数量（补货时必填）',
    example: 30,
    required: false,
  })
  @ValidateIf((o) => o.type === CustomerProfileDetailType.RESTOCK)
  @IsNotEmpty({ message: '补货时补货总数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '补货总数量必须是整数' })
  @Min(1, { message: '补货总数量必须大于0' })
  restockQuantity?: number;

  @ApiProperty({
    description: '补货订单ID（补货时可选）',
    example: 'RS001',
    required: false,
  })
  @IsOptional()
  @IsString()
  restockOrderId?: string;

  // 代发相关字段
  @ApiProperty({
    description: '代发金额（代发时必填）',
    example: 3000.00,
    required: false,
  })
  @ValidateIf((o) => o.type === CustomerProfileDetailType.DROP_SHIPPING)
  @IsNotEmpty({ message: '代发时代发金额不能为空' })
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '代发金额必须是数字且最多保留两位小数' })
  @Min(0.01, { message: '代发金额必须大于0' })
  dropShippingAmount?: number;

  @ApiProperty({
    description: '代发数量（代发时必填）',
    example: 20,
    required: false,
  })
  @ValidateIf((o) => o.type === CustomerProfileDetailType.DROP_SHIPPING)
  @IsNotEmpty({ message: '代发时代发数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '代发数量必须是整数' })
  @Min(1, { message: '代发数量必须大于0' })
  dropShippingQuantity?: number;

  @ApiProperty({
    description: '代发客户数量（代发时必填）',
    example: 5,
    required: false,
  })
  @ValidateIf((o) => o.type === CustomerProfileDetailType.DROP_SHIPPING)
  @IsNotEmpty({ message: '代发时代发客户数量不能为空' })
  @Type(() => Number)
  @IsInt({ message: '代发客户数量必须是整数' })
  @Min(1, { message: '代发客户数量必须大于0' })
  dropShippingCustomerCount?: number;

  // 通用可选字段
  @ApiProperty({
    description: '物流金额（可选）',
    example: 100.00,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: '物流金额必须是数字且最多保留两位小数' })
  @Min(0, { message: '物流金额不能小于0' })
  logisticsAmount?: number;

  @ApiProperty({
    description: '图片URL（可选）',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiProperty({
    description: '备注（可选）',
    example: '客户换货处理',
    required: false,
  })
  @IsOptional()
  @IsString()
  remark?: string;

  @ApiProperty({
    description: '负责人员编码（可选）',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  responsibleUserCode?: string;
}
