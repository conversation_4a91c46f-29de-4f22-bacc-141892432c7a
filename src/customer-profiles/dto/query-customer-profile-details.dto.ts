import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsDateString,
  IsInt,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CustomerProfileDetailType } from '../entities/customer-profile-detail.entity';

export class QueryCustomerProfileDetailsDto {
  @ApiProperty({
    description: '页码（必填）',
    example: 1,
  })
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码必须大于0' })
  page: number;

  @ApiProperty({
    description: '每页数量（必填）',
    example: 10,
  })
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量必须大于0' })
  pageSize: number;

  @ApiProperty({
    description: '客户编码（必填）',
    example: 'CUS001',
  })
  @IsString()
  @IsNotEmpty({ message: '客户编码不能为空' })
  customerCode: string;

  @ApiProperty({
    description: '明细类型筛选（可选）',
    enum: CustomerProfileDetailType,
    example: CustomerProfileDetailType.EXCHANGE,
    required: false,
  })
  @IsOptional()
  @IsEnum(CustomerProfileDetailType, {
    message: '明细类型必须是 exchange/refund/order/restock/drop_shipping 之一',
  })
  type?: CustomerProfileDetailType;

  @ApiProperty({
    description: '起始日期（可选）',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '起始日期格式不正确' })
  startTime?: string;

  @ApiProperty({
    description: '终止日期（可选）',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '终止日期格式不正确' })
  endTime?: string;

  @ApiProperty({
    description: '搜索关键词（可选，模糊搜索备注、负责人员姓名）',
    example: '换货',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '负责人员编码筛选（可选）',
    example: 'user001',
    required: false,
  })
  @IsOptional()
  @IsString()
  responsibleUserCode?: string;
}
