### 客户档案接口测试示例

### 1. 新增换货明细
POST http://localhost:3000/customer-profiles/details
Content-Type: application/json

{
  "customerCode": "CUS001",
  "type": "exchange",
  "returnQuantity": 10,
  "totalGoodsAmount": 5000.00,
  "exchangeOrderString": "EXC001",
  "imageUrl": "https://example.com/exchange-image.jpg",
  "remark": "客户换货处理",
  "responsibleUserCode": "husky"
}

### 2. 新增退货明细
POST http://localhost:3000/customer-profiles/details
Content-Type: application/json

{
  "customerCode": "CUS001",
  "type": "refund",
  "returnQuantity": 5,
  "totalGoodsAmount": 3000.00,
  "imageUrl": "https://example.com/refund-image.jpg",
  "remark": "客户退货处理",
  "responsibleUserCode": "husky"
}

### 3. 新增订货明细
POST http://localhost:3000/customer-profiles/details
Content-Type: application/json

{
  "customerCode": "CUS001",
  "type": "order",
  "deposit": 2000.00,
  "purchaseOrderId": "PO001",
  "reservedQuantity": 50,
  "logisticsAmount": 100.00,
  "remark": "客户订货",
  "responsibleUserCode": "husky"
}

### 4. 新增补货明细
POST http://localhost:3000/customer-profiles/details
Content-Type: application/json

{
  "customerCode": "CUS001",
  "type": "restock",
  "restockDealAmount": 8000.00,
  "restockQuantity": 30,
  "restockOrderId": "RS001",
  "imageUrl": "https://example.com/restock-image.jpg",
  "logisticsAmount": 150.00,
  "responsibleUserCode": "husky"
}

### 5. 新增代发明细
POST http://localhost:3000/customer-profiles/details
Content-Type: application/json

{
  "customerCode": "CUS001",
  "type": "drop_shipping",
  "dropShippingAmount": 3000.00,
  "dropShippingQuantity": 20,
  "dropShippingCustomerCount": 5,
  "responsibleUserCode": "husky"
}

### 6. 分页查询客户档案明细（必填参数）
GET http://localhost:3000/customer-profiles/details?page=1&pageSize=10&customerCode=CUS001

### 7. 分页查询客户档案明细（带筛选条件）
GET http://localhost:3000/customer-profiles/details?page=1&pageSize=10&customerCode=CUS001&type=exchange&startTime=2023-01-01&endTime=2023-12-31&search=换货

### 8. 获取明细详情
GET http://localhost:3000/customer-profiles/details/{{detailId}}

### 9. 更新明细
PATCH http://localhost:3000/customer-profiles/details/{{detailId}}
Content-Type: application/json

{
  "remark": "更新后的备注",
  "logisticsAmount": 200.00
}

### 10. 删除明细
DELETE http://localhost:3000/customer-profiles/details/{{detailId}}

### 11. 导出Excel（必填客户编码）
GET http://localhost:3000/customer-profiles/export?customerCode=CUS001

### 12. 导出Excel（带筛选条件）
GET http://localhost:3000/customer-profiles/export?customerCode=CUS001&startTime=2023-01-01&endTime=2023-12-31&type=exchange

### 13. 导出Excel（指定明细ID）
GET http://localhost:3000/customer-profiles/export?customerCode=CUS001&detailIds=uuid1,uuid2,uuid3
