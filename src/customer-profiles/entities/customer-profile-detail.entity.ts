import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { CustomerProfile } from './customer-profile.entity';
import { Customer } from '@/customers/entities/customer.entity';
import { User } from '@/users/entities/user.entity';

// 客户档案明细类型枚举
export enum CustomerProfileDetailType {
  EXCHANGE = 'exchange', // 换货
  REFUND = 'refund', // 退货
  ORDER = 'order', // 订货
  RESTOCK = 'restock', // 补货
  DROP_SHIPPING = 'drop_shipping', // 代发
}

@Entity('customer_profile_details')
export class CustomerProfileDetail {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '明细ID' })
  id: string;

  @Column({ type: 'varchar', length: 50, comment: '客户档案编码' })
  customerProfileCode: string;

  @Column({ type: 'varchar', length: 50, comment: '客户编码' })
  @ApiProperty({ description: '客户编码', example: 'CUS001' })
  customerCode: string;

  @Column({
    type: 'enum',
    enum: CustomerProfileDetailType,
    comment: '明细类型：换货/退货/订货/补货/代发',
  })
  @ApiProperty({
    description: '明细类型',
    enum: CustomerProfileDetailType,
    example: CustomerProfileDetailType.EXCHANGE,
  })
  type: CustomerProfileDetailType;

  // 换货/退货相关字段
  @Column({
    type: 'int',
    nullable: true,
    comment: '客户返回商品数量（换货/退货时使用）',
  })
  @ApiProperty({ description: '客户返回商品数量', example: 10, required: false })
  returnQuantity?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: '商品总货款（换货/退货时使用）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '商品总货款', example: 5000.00, required: false })
  totalGoodsAmount?: number;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '换货订单字符串（换货/退货时可选）',
  })
  @ApiProperty({ description: '换货订单字符串', example: 'EXC001', required: false })
  exchangeOrderString?: string;

  // 订货相关字段
  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: '定金（订货时使用）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '定金', example: 2000.00, required: false })
  deposit?: number;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '采购订单ID（订货时可选）',
  })
  @ApiProperty({ description: '采购订单ID', example: 'PO001', required: false })
  purchaseOrderId?: string;

  @Column({
    type: 'int',
    nullable: true,
    comment: '预定商品数量（订货时使用）',
  })
  @ApiProperty({ description: '预定商品数量', example: 50, required: false })
  reservedQuantity?: number;

  // 补货相关字段
  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: '补货成交款（补货时使用）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '补货成交款', example: 8000.00, required: false })
  restockDealAmount?: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '补货总数量（补货时使用）',
  })
  @ApiProperty({ description: '补货总数量', example: 30, required: false })
  restockQuantity?: number;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '补货订单ID（补货时可选）',
  })
  @ApiProperty({ description: '补货订单ID', example: 'RS001', required: false })
  restockOrderId?: string;

  // 代发相关字段
  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: '代发金额（代发时使用）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '代发金额', example: 3000.00, required: false })
  dropShippingAmount?: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '代发数量（代发时使用）',
  })
  @ApiProperty({ description: '代发数量', example: 20, required: false })
  dropShippingQuantity?: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '代发客户数量（代发时使用）',
  })
  @ApiProperty({ description: '代发客户数量', example: 5, required: false })
  dropShippingCustomerCount?: number;

  // 通用字段
  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: '物流金额（可选）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '物流金额', example: 100.00, required: false })
  logisticsAmount?: number;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '图片URL（可选）',
  })
  @ApiProperty({ description: '图片URL', example: 'https://example.com/image.jpg', required: false })
  imageUrl?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注（可选）',
  })
  @ApiProperty({ description: '备注', example: '客户换货处理', required: false })
  remark?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '负责人员编码（可选）',
  })
  @ApiProperty({ description: '负责人员编码', example: 'user001', required: false })
  responsibleUserCode?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联客户档案主表
  @ManyToOne(() => CustomerProfile, (profile) => profile.details)
  @JoinColumn({ name: 'customerProfileCode', referencedColumnName: 'customerCode' })
  customerProfile: CustomerProfile;

  // 关联客户表
  @ManyToOne(() => Customer, { nullable: false })
  @JoinColumn({ name: 'customerCode', referencedColumnName: 'code' })
  @ApiProperty({ description: '客户信息', type: () => Customer })
  customer: Customer;

  // 关联负责人员表（可选）
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'responsibleUserCode', referencedColumnName: 'code' })
  @ApiProperty({ description: '负责人员信息', type: () => User, required: false })
  responsibleUser?: User;
}
