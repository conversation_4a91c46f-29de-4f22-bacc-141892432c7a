import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Customer } from '@/customers/entities/customer.entity';
import { CustomerProfileDetail } from './customer-profile-detail.entity';

@Entity('customer_profiles')
export class CustomerProfile {
  @PrimaryColumn({ type: 'varchar', length: 50 })
  @ApiProperty({ description: '客户编码，作为主键', example: 'CUS001' })
  customerCode: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '成交总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '成交总金额', example: 50000.00 })
  totalDealAmount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '换货总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '换货总金额', example: 5000.00 })
  totalExchangeAmount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '退货总金额',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '退货总金额', example: 3000.00 })
  totalRefundAmount: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '换货率（百分比）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '换货率（百分比）', example: 10.50 })
  exchangeRate: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '退货率（百分比）',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '退货率（百分比）', example: 6.00 })
  refundRate: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '最大欠款额度',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '最大欠款额度', example: 100000.00 })
  maxDebtLimit: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否允许代发',
  })
  @ApiProperty({ description: '是否允许代发', example: true })
  allowDropShipping: boolean;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '当前欠款',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '当前欠款', example: 15000.00 })
  currentDebt: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: '当前货款',
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  @ApiProperty({ description: '当前货款', example: 8000.00 })
  currentPayment: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已删除',
  })
  isDeleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt: Date | null;

  // 关联客户表
  @ManyToOne(() => Customer, { nullable: false })
  @JoinColumn({ name: 'customerCode', referencedColumnName: 'code' })
  @ApiProperty({ description: '客户信息', type: () => Customer })
  customer: Customer;

  // 关联客户档案明细
  @OneToMany(() => CustomerProfileDetail, (detail) => detail.customerProfile)
  @ApiProperty({ description: '客户档案明细列表', type: () => [CustomerProfileDetail] })
  details: CustomerProfileDetail[];
}
