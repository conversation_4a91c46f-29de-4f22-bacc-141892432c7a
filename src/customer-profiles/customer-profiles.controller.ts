import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  HttpStatus,
  Res,
  Req,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CustomerProfilesService } from './customer-profiles.service';
import { CreateCustomerProfileDetailDto } from './dto/create-customer-profile-detail.dto';
import { UpdateCustomerProfileDetailDto } from './dto/update-customer-profile-detail.dto';
import { QueryCustomerProfileDetailsDto } from './dto/query-customer-profile-details.dto';

@ApiTags('customer-profiles')
@Controller('customer-profiles')
export class CustomerProfilesController {
  private readonly logger = new Logger(CustomerProfilesController.name);

  constructor(
    private readonly customerProfilesService: CustomerProfilesService,
  ) {}

  @Post('details')
  @ApiOperation({ summary: '新增客户档案明细' })
  @ApiResponse({
    status: 200,
    description: '客户档案明细创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async createDetail(@Body() createDetailDto: CreateCustomerProfileDetailDto) {
    this.logger.log(
      `Creating customer profile detail for customer: ${createDetailDto.customerCode}`,
    );

    await this.customerProfilesService.createDetail(createDetailDto);

    return {
      code: 200,
      data: null,
      message: '客户档案明细创建成功',
    };
  }

  @Get('details')
  @ApiOperation({ summary: '分页查询客户档案明细列表' })
  @ApiQuery({
    name: 'page',
    description: '页码（必填）',
    example: 1,
    required: true,
  })
  @ApiQuery({
    name: 'pageSize',
    description: '每页数量（必填）',
    example: 10,
    required: true,
  })
  @ApiQuery({
    name: 'customerCode',
    description: '客户编码（必填）',
    example: 'CUS001',
    required: true,
  })
  @ApiQuery({
    name: 'type',
    description: '明细类型筛选（可选）',
    example: 'exchange',
    required: false,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期（可选）',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期（可选）',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词（可选，模糊搜索备注、负责人员姓名）',
    example: '换货',
    required: false,
  })
  @ApiQuery({
    name: 'responsibleUserCode',
    description: '负责人员编码筛选（可选）',
    example: 'user001',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  async findAllDetails(@Query() queryDto: QueryCustomerProfileDetailsDto) {
    this.logger.log(
      `Querying customer profile details with params: ${JSON.stringify(queryDto)}`,
    );

    const result = await this.customerProfilesService.findAllDetails(queryDto);

    return {
      code: 200,
      data: result,
      message: '查询成功',
    };
  }

  @Get('details/:id')
  @ApiOperation({ summary: '获取客户档案明细详情' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async findDetailById(@Param('id') id: string) {
    this.logger.log(`Finding customer profile detail by id: ${id}`);

    const detail = await this.customerProfilesService.findDetailById(id);

    return {
      code: 200,
      data: detail,
      message: '查询成功',
    };
  }

  @Patch('details/:id')
  @ApiOperation({ summary: '更新客户档案明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async updateDetail(
    @Param('id') id: string,
    @Body() updateDetailDto: UpdateCustomerProfileDetailDto,
  ) {
    this.logger.log(`Updating customer profile detail ${id}`);

    await this.customerProfilesService.updateDetail(id, updateDetailDto);

    return {
      code: 200,
      data: null,
      message: '客户档案明细更新成功',
    };
  }

  @Delete('details/:id')
  @ApiOperation({ summary: '删除客户档案明细' })
  @ApiParam({
    name: 'id',
    description: '明细ID',
    example: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '明细不存在',
  })
  async removeDetail(@Param('id') id: string) {
    this.logger.log(`Removing customer profile detail ${id}`);

    await this.customerProfilesService.removeDetail(id);

    return {
      code: 200,
      data: null,
      message: '客户档案明细删除成功',
    };
  }

  @Get('export')
  @ApiOperation({ summary: '导出客户档案明细Excel' })
  @ApiQuery({
    name: 'customerCode',
    description: '客户编码（必填）',
    example: 'CUS001',
    required: true,
  })
  @ApiQuery({
    name: 'startTime',
    description: '起始日期（可选）',
    example: '2023-01-01',
    required: false,
  })
  @ApiQuery({
    name: 'endTime',
    description: '终止日期（可选）',
    example: '2023-12-31',
    required: false,
  })
  @ApiQuery({
    name: 'detailIds',
    description: '明细ID列表（逗号分隔，可选）',
    example: 'uuid1,uuid2,uuid3',
    required: false,
  })
  @ApiQuery({
    name: 'type',
    description: '明细类型筛选（可选）',
    example: 'exchange',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Excel文件导出成功',
  })
  async exportToExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Exporting customer profile details to Excel`);

    try {
      // 从原始请求中获取查询参数
      const { customerCode, startTime, endTime, detailIds, type } = req.query;

      if (!customerCode) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          code: 400,
          data: null,
          message: '客户编码不能为空',
        });
      }

      // 构建导出参数
      const exportParams: any = { customerCode };

      if (startTime) {
        exportParams.startTime = startTime;
      }

      if (endTime) {
        exportParams.endTime = endTime;
      }

      if (detailIds) {
        // 处理detailIds参数（逗号分隔的字符串转换为数组）
        const decodedDetailIds = decodeURIComponent(detailIds);
        exportParams.detailIds = decodedDetailIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0);
      }

      if (type) {
        exportParams.type = type;
      }

      const excelBuffer = await this.customerProfilesService.exportToExcel(exportParams);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="customer-profile-details-${customerCode}-${Date.now()}.xlsx"`,
      );

      // 发送Excel文件
      res.send(excelBuffer);
    } catch (error) {
      this.logger.error('Excel export failed', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: 500,
        data: null,
        message: 'Excel导出失败',
      });
    }
  }
}
