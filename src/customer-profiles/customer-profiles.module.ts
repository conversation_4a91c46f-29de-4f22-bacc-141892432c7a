import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerProfilesService } from './customer-profiles.service';
import { CustomerProfilesController } from './customer-profiles.controller';
import { CustomerProfile } from './entities/customer-profile.entity';
import { CustomerProfileDetail } from './entities/customer-profile-detail.entity';
import { Customer } from '@/customers/entities/customer.entity';
import { User } from '@/users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CustomerProfile,
      CustomerProfileDetail,
      Customer,
      User,
    ]),
  ],
  controllers: [CustomerProfilesController],
  providers: [CustomerProfilesService],
  exports: [CustomerProfilesService],
})
export class CustomerProfilesModule {}
