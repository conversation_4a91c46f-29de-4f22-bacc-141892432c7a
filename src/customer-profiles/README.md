# 客户档案管理接口

## 概述

客户档案管理接口提供了完整的客户交易记录管理功能，支持5种不同类型的交易明细：换货、退货、订货、补货、代发。

## 功能特性

### 客户档案统计
- 成交总金额
- 换货总金额、换货率
- 退货总金额、退货率
- 最大欠款额度
- 是否允许代发
- 当前欠款
- 当前货款

### 明细类型支持

#### 1. 换货 (exchange)
- 客户返回商品数量（必填）
- 商品总货款（必填）
- 换货订单字符串（可选）
- 图片（可选）
- 备注（可选）
- 负责人员（可选）

#### 2. 退货 (refund)
- 客户返回商品数量（必填）
- 商品总货款（必填）
- 换货订单字符串（可选）
- 图片（可选）
- 备注（可选）
- 负责人员（可选）

#### 3. 订货 (order)
- 定金（必填）
- 采购订单ID（可选）
- 预定商品数量（必填）
- 备注（可选）
- 物流金额（可选）
- 负责人员（可选）

#### 4. 补货 (restock)
- 补货成交款（必填）
- 补货总数量（必填）
- 补货订单ID（可选）
- 图片（可选）
- 物流金额（可选）
- 负责人员（可选）

#### 5. 代发 (drop_shipping)
- 代发金额（必填）
- 代发数量（必填）
- 代发客户数量（必填）
- 负责人员（可选）

## API 接口

### 1. 新增客户档案明细
```
POST /customer-profiles/details
```

**请求体示例（换货）：**
```json
{
  "customerCode": "CUS001",
  "type": "exchange",
  "returnQuantity": 10,
  "totalGoodsAmount": 5000.00,
  "exchangeOrderString": "EXC001",
  "imageUrl": "https://example.com/image.jpg",
  "remark": "客户换货处理",
  "responsibleUserCode": "user001"
}
```

**请求体示例（订货）：**
```json
{
  "customerCode": "CUS001",
  "type": "order",
  "deposit": 2000.00,
  "purchaseOrderId": "PO001",
  "reservedQuantity": 50,
  "logisticsAmount": 100.00,
  "remark": "客户订货",
  "responsibleUserCode": "user001"
}
```

### 2. 分页查询客户档案明细
```
GET /customer-profiles/details?page=1&pageSize=10&customerCode=CUS001
```

**必填参数：**
- `page`: 页码
- `pageSize`: 每页数量
- `customerCode`: 客户编码

**可选参数：**
- `type`: 明细类型筛选
- `startTime`: 起始日期
- `endTime`: 终止日期
- `search`: 搜索关键词
- `responsibleUserCode`: 负责人员编码筛选

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "details": [...],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "customerProfile": {
      "id": "uuid",
      "customerCode": "CUS001",
      "totalDealAmount": 50000.00,
      "totalExchangeAmount": 5000.00,
      "totalRefundAmount": 3000.00,
      "exchangeRate": 10.00,
      "refundRate": 6.00,
      "maxDebtLimit": 100000.00,
      "allowDropShipping": true,
      "currentDebt": 15000.00,
      "currentPayment": 8000.00
    }
  },
  "message": "查询成功"
}
```

### 3. 获取明细详情
```
GET /customer-profiles/details/:id
```

### 4. 更新明细
```
PATCH /customer-profiles/details/:id
```

### 5. 删除明细
```
DELETE /customer-profiles/details/:id
```

### 6. 导出Excel
```
GET /customer-profiles/export?customerCode=CUS001
```

**必填参数：**
- `customerCode`: 客户编码

**可选参数：**
- `startTime`: 起始日期
- `endTime`: 终止日期
- `detailIds`: 明细ID列表（逗号分隔）
- `type`: 明细类型筛选

**特性：**
- 支持图片导出到Excel
- 自动下载网络图片并嵌入Excel文件
- 如果图片下载失败，显示图片链接

## 业务逻辑

### 统计数据自动计算
系统会自动计算和更新以下统计数据：

1. **成交总金额**：订货定金 + 补货成交款 + 代发金额
2. **换货总金额**：所有换货明细的商品总货款之和
3. **退货总金额**：所有退货明细的商品总货款之和
4. **换货率**：换货总金额 / 成交总金额 * 100%
5. **退货率**：退货总金额 / 成交总金额 * 100%
6. **当前货款**：退货时增加的货款
7. **当前欠款**：订货时未支付定金产生的欠款

### 数据验证
- 客户编码必须存在于客户表中
- 负责人员编码必须存在于用户表中
- 不同类型的明细有不同的必填字段要求
- 金额字段最多保留两位小数
- 数量字段必须为正整数

## 数据库表结构

### customer_profiles（客户档案主表）
- id: UUID主键
- customerCode: 客户编码
- totalDealAmount: 成交总金额
- totalExchangeAmount: 换货总金额
- totalRefundAmount: 退货总金额
- exchangeRate: 换货率
- refundRate: 退货率
- maxDebtLimit: 最大欠款额度
- allowDropShipping: 是否允许代发
- currentDebt: 当前欠款
- currentPayment: 当前货款

### customer_profile_details（客户档案明细表）
- id: UUID主键
- customerProfileId: 客户档案ID
- customerCode: 客户编码
- type: 明细类型
- 各种业务字段（根据类型不同而不同）
- responsibleUserCode: 负责人员编码

## 注意事项

1. **分页查询必须传入page、pageSize和customerCode参数**
2. **Excel导出功能包含图片处理，确保网络连接正常**
3. **统计数据会在每次新增、更新、删除明细时自动重新计算**
4. **软删除机制，删除的数据不会物理删除**
5. **支持事务处理，确保数据一致性**
