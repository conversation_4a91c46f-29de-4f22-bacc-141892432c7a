import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import { CustomerProfile } from './entities/customer-profile.entity';
import {
  CustomerProfileDetail,
  CustomerProfileDetailType,
} from './entities/customer-profile-detail.entity';
import { Customer } from '@/customers/entities/customer.entity';
import { User } from '@/users/entities/user.entity';
import { CreateCustomerProfileDetailDto } from './dto/create-customer-profile-detail.dto';
import { UpdateCustomerProfileDetailDto } from './dto/update-customer-profile-detail.dto';
import { QueryCustomerProfileDetailsDto } from './dto/query-customer-profile-details.dto';

// 内部处理接口
interface ProcessedExportDto {
  customerCode: string;
  startTime?: string;
  endTime?: string;
  detailIds?: string[];
  type?: CustomerProfileDetailType;
}

@Injectable()
export class CustomerProfilesService {
  private readonly logger = new Logger(CustomerProfilesService.name);

  constructor(
    @InjectRepository(CustomerProfile)
    private customerProfileRepository: Repository<CustomerProfile>,
    @InjectRepository(CustomerProfileDetail)
    private customerProfileDetailRepository: Repository<CustomerProfileDetail>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
  ) {}

  // 获取或创建客户档案
  private async getOrCreateCustomerProfile(customerCode: string): Promise<CustomerProfile> {
    let customerProfile = await this.customerProfileRepository.findOne({
      where: { customerCode, isDeleted: false },
    });

    if (!customerProfile) {
      // 验证客户是否存在
      const customer = await this.customerRepository.findOne({
        where: { code: customerCode, isDeleted: false },
      });
      if (!customer) {
        throw new BadRequestException(`客户 ${customerCode} 不存在`);
      }

      customerProfile = this.customerProfileRepository.create({
        customerCode,
        totalDealAmount: 0,
        totalExchangeAmount: 0,
        totalRefundAmount: 0,
        exchangeRate: 0,
        refundRate: 0,
        maxDebtLimit: 0,
        allowDropShipping: false,
        currentDebt: 0,
        currentPayment: 0,
        isDeleted: false,
      });
      customerProfile = await this.customerProfileRepository.save(customerProfile);
      this.logger.log(`Created new customer profile for ${customerCode}`);
    }

    return customerProfile;
  }

  // 计算并更新客户档案统计数据
  private async updateCustomerProfileStats(customerCode: string): Promise<void> {
    const customerProfile = await this.getOrCreateCustomerProfile(customerCode);

    // 获取所有明细数据
    const details = await this.customerProfileDetailRepository.find({
      where: { customerCode, isDeleted: false },
    });

    // 重新计算统计数据
    let totalDealAmount = 0;
    let totalExchangeAmount = 0;
    let totalRefundAmount = 0;
    let currentDebt = 0;
    let currentPayment = 0;

    for (const detail of details) {
      switch (detail.type) {
        case CustomerProfileDetailType.EXCHANGE:
          totalExchangeAmount += detail.totalGoodsAmount || 0;
          break;
        case CustomerProfileDetailType.REFUND:
          totalRefundAmount += detail.totalGoodsAmount || 0;
          // 退货时增加货款
          currentPayment += detail.totalGoodsAmount || 0;
          break;
        case CustomerProfileDetailType.ORDER:
          totalDealAmount += detail.deposit || 0;
          // 如果没有支付定金，增加欠款
          if (!detail.deposit || detail.deposit === 0) {
            // 这里需要根据业务逻辑计算欠款金额，暂时使用预定商品数量 * 假设单价
            // 实际应用中可能需要关联商品表获取价格
            currentDebt += (detail.reservedQuantity || 0) * 100; // 假设单价100
          }
          break;
        case CustomerProfileDetailType.RESTOCK:
          totalDealAmount += detail.restockDealAmount || 0;
          break;
        case CustomerProfileDetailType.DROP_SHIPPING:
          totalDealAmount += detail.dropShippingAmount || 0;
          break;
      }
    }

    // 计算换货率和退货率
    const exchangeRate = totalDealAmount > 0 ? (totalExchangeAmount / totalDealAmount) * 100 : 0;
    const refundRate = totalDealAmount > 0 ? (totalRefundAmount / totalDealAmount) * 100 : 0;

    // 更新客户档案
    customerProfile.totalDealAmount = totalDealAmount;
    customerProfile.totalExchangeAmount = totalExchangeAmount;
    customerProfile.totalRefundAmount = totalRefundAmount;
    customerProfile.exchangeRate = Math.round(exchangeRate * 100) / 100; // 保留两位小数
    customerProfile.refundRate = Math.round(refundRate * 100) / 100; // 保留两位小数
    customerProfile.currentDebt = currentDebt;
    customerProfile.currentPayment = currentPayment;

    await this.customerProfileRepository.save(customerProfile);
  }

  // 创建客户档案明细
  async createDetail(createDetailDto: CreateCustomerProfileDetailDto): Promise<void> {
    this.logger.log(
      `Creating customer profile detail for customer: ${createDetailDto.customerCode}`,
    );

    // 验证客户是否存在
    const customer = await this.customerRepository.findOne({
      where: { code: createDetailDto.customerCode, isDeleted: false },
    });
    if (!customer) {
      throw new BadRequestException(`客户 ${createDetailDto.customerCode} 不存在`);
    }

    // 验证负责人员是否存在（如果提供了）
    if (createDetailDto.responsibleUserCode) {
      const user = await this.userRepository.findOne({
        where: { code: createDetailDto.responsibleUserCode, isDeleted: false },
      });
      if (!user) {
        throw new BadRequestException(
          `负责人员 ${createDetailDto.responsibleUserCode} 不存在`,
        );
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建客户档案
      const customerProfile = await this.getOrCreateCustomerProfile(createDetailDto.customerCode);

      // 创建明细记录
      const detail = this.customerProfileDetailRepository.create({
        customerProfileCode: customerProfile.customerCode,
        customerCode: createDetailDto.customerCode,
        type: createDetailDto.type,
        returnQuantity: createDetailDto.returnQuantity,
        totalGoodsAmount: createDetailDto.totalGoodsAmount,
        exchangeOrderString: createDetailDto.exchangeOrderString,
        deposit: createDetailDto.deposit,
        purchaseOrderId: createDetailDto.purchaseOrderId,
        reservedQuantity: createDetailDto.reservedQuantity,
        restockDealAmount: createDetailDto.restockDealAmount,
        restockQuantity: createDetailDto.restockQuantity,
        restockOrderId: createDetailDto.restockOrderId,
        dropShippingAmount: createDetailDto.dropShippingAmount,
        dropShippingQuantity: createDetailDto.dropShippingQuantity,
        dropShippingCustomerCount: createDetailDto.dropShippingCustomerCount,
        logisticsAmount: createDetailDto.logisticsAmount,
        imageUrl: createDetailDto.imageUrl,
        remark: createDetailDto.remark,
        responsibleUserCode: createDetailDto.responsibleUserCode,
        isDeleted: false,
      });

      await queryRunner.manager.save(detail);
      await queryRunner.commitTransaction();

      // 更新客户档案统计数据
      await this.updateCustomerProfileStats(createDetailDto.customerCode);

      this.logger.log(
        `Customer profile detail created successfully with id: ${detail.id}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Failed to create customer profile detail', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询客户档案明细
  async findAllDetails(queryDto: QueryCustomerProfileDetailsDto) {
    const {
      page,
      pageSize,
      customerCode,
      type,
      startTime,
      endTime,
      search,
      responsibleUserCode,
    } = queryDto;

    this.logger.log(
      `Querying customer profile details: page=${page}, pageSize=${pageSize}, customerCode=${customerCode}`,
    );

    // 获取客户档案统计信息
    const customerProfile = await this.getOrCreateCustomerProfile(customerCode);

    // 处理时间参数
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime) {
      if (startTime.length === 10) {
        processedStartTime = `${startTime}T00:00:00.000Z`;
      }
    }

    if (endTime) {
      if (endTime.length === 10) {
        processedEndTime = `${endTime}T23:59:59.999Z`;
      }
    }

    // 构建查询
    const queryBuilder = this.customerProfileDetailRepository
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.customer', 'customer')
      .leftJoinAndSelect('detail.responsibleUser', 'responsibleUser')
      .where('detail.isDeleted = false')
      .andWhere('detail.customerCode = :customerCode', { customerCode })
      .orderBy('detail.createdAt', 'DESC');

    // 添加时间范围过滤
    if (processedStartTime) {
      queryBuilder.andWhere('detail.createdAt >= :startTime', {
        startTime: processedStartTime,
      });
    }

    if (processedEndTime) {
      queryBuilder.andWhere('detail.createdAt <= :endTime', {
        endTime: processedEndTime,
      });
    }

    // 添加类型过滤
    if (type) {
      queryBuilder.andWhere('detail.type = :type', { type });
    }

    // 添加负责人员过滤
    if (responsibleUserCode) {
      queryBuilder.andWhere('detail.responsibleUserCode = :responsibleUserCode', {
        responsibleUserCode,
      });
    }

    // 添加搜索过滤
    if (search) {
      queryBuilder.andWhere(
        '(detail.remark ILIKE :search OR responsibleUser.nickname ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    const [details, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    // 格式化返回数据
    const formattedDetails = details.map((detail) =>
      this.formatDetailResponse(detail),
    );

    return {
      details: formattedDetails,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      customerProfile: {
        customerCode: customerProfile.customerCode,
        totalDealAmount: customerProfile.totalDealAmount,
        totalExchangeAmount: customerProfile.totalExchangeAmount,
        totalRefundAmount: customerProfile.totalRefundAmount,
        exchangeRate: customerProfile.exchangeRate,
        refundRate: customerProfile.refundRate,
        maxDebtLimit: customerProfile.maxDebtLimit,
        allowDropShipping: customerProfile.allowDropShipping,
        currentDebt: customerProfile.currentDebt,
        currentPayment: customerProfile.currentPayment,
      },
    };
  }

  // 格式化明细响应数据
  private formatDetailResponse(detail: CustomerProfileDetail) {
    const baseResponse = {
      id: detail.id,
      customerCode: detail.customerCode,
      type: detail.type,
      returnQuantity: detail.returnQuantity,
      totalGoodsAmount: detail.totalGoodsAmount,
      exchangeOrderString: detail.exchangeOrderString,
      deposit: detail.deposit,
      purchaseOrderId: detail.purchaseOrderId,
      reservedQuantity: detail.reservedQuantity,
      restockDealAmount: detail.restockDealAmount,
      restockQuantity: detail.restockQuantity,
      restockOrderId: detail.restockOrderId,
      dropShippingAmount: detail.dropShippingAmount,
      dropShippingQuantity: detail.dropShippingQuantity,
      dropShippingCustomerCount: detail.dropShippingCustomerCount,
      logisticsAmount: detail.logisticsAmount,
      imageUrl: detail.imageUrl,
      remark: detail.remark,
      responsibleUserCode: detail.responsibleUserCode,
      createdAt: detail.createdAt,
      updatedAt: detail.updatedAt,
    };

    // 添加关联数据
    if (detail.customer) {
      Object.assign(baseResponse, {
        customerName: detail.customer.name,
        customerPhone: detail.customer.phone,
        customerAddress: detail.customer.address,
      });
    }

    if (detail.responsibleUser) {
      Object.assign(baseResponse, {
        responsibleUserName: detail.responsibleUser.nickname,
      });
    }

    return baseResponse;
  }

  // 根据ID查询明细
  async findDetailById(id: string): Promise<any> {
    this.logger.log(`Finding customer profile detail by id: ${id}`);

    const detail = await this.customerProfileDetailRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['customer', 'responsibleUser'],
    });

    if (!detail) {
      throw new NotFoundException(`客户档案明细 ${id} 不存在`);
    }

    return this.formatDetailResponse(detail);
  }

  // 更新明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateCustomerProfileDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating customer profile detail ${id}`);

    const detail = await this.customerProfileDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException(`客户档案明细 ${id} 不存在`);
    }

    // 验证客户是否存在（如果更新了客户编码）
    if (updateDetailDto.customerCode && updateDetailDto.customerCode !== detail.customerCode) {
      const customer = await this.customerRepository.findOne({
        where: { code: updateDetailDto.customerCode, isDeleted: false },
      });
      if (!customer) {
        throw new BadRequestException(`客户 ${updateDetailDto.customerCode} 不存在`);
      }
    }

    // 验证负责人员是否存在（如果提供了）
    if (updateDetailDto.responsibleUserCode) {
      const user = await this.userRepository.findOne({
        where: { code: updateDetailDto.responsibleUserCode, isDeleted: false },
      });
      if (!user) {
        throw new BadRequestException(
          `负责人员 ${updateDetailDto.responsibleUserCode} 不存在`,
        );
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const oldCustomerCode = detail.customerCode;

      // 更新明细
      Object.assign(detail, updateDetailDto);
      await queryRunner.manager.save(detail);

      await queryRunner.commitTransaction();

      // 更新相关客户档案统计数据
      await this.updateCustomerProfileStats(oldCustomerCode);
      if (updateDetailDto.customerCode && updateDetailDto.customerCode !== oldCustomerCode) {
        await this.updateCustomerProfileStats(updateDetailDto.customerCode);
      }

      this.logger.log(`Customer profile detail ${id} updated successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update customer profile detail ${id}`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除明细
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing customer profile detail ${id}`);

    const detail = await this.customerProfileDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException(`客户档案明细 ${id} 不存在`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 软删除明细
      detail.isDeleted = true;
      detail.deletedAt = new Date();
      await queryRunner.manager.save(detail);

      await queryRunner.commitTransaction();

      // 更新客户档案统计数据
      await this.updateCustomerProfileStats(detail.customerCode);

      this.logger.log(`Customer profile detail ${id} removed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to remove customer profile detail ${id}`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出Excel
  async exportToExcel(exportDto: ProcessedExportDto): Promise<Buffer> {
    this.logger.log('Exporting customer profile details to Excel');

    let queryBuilder = this.customerProfileDetailRepository
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.customer', 'customer')
      .leftJoinAndSelect('detail.responsibleUser', 'responsibleUser')
      .where('detail.isDeleted = false')
      .andWhere('detail.customerCode = :customerCode', { customerCode: exportDto.customerCode })
      .orderBy('detail.createdAt', 'DESC');

    // 时间范围筛选
    if (exportDto.startTime) {
      let startTime = exportDto.startTime;
      if (startTime.length === 10) {
        startTime = `${startTime}T00:00:00.000Z`;
      }
      queryBuilder.andWhere('detail.createdAt >= :startTime', { startTime });
    }

    if (exportDto.endTime) {
      let endTime = exportDto.endTime;
      if (endTime.length === 10) {
        endTime = `${endTime}T23:59:59.999Z`;
      }
      queryBuilder.andWhere('detail.createdAt <= :endTime', { endTime });
    }

    // 类型筛选
    if (exportDto.type) {
      queryBuilder.andWhere('detail.type = :type', { type: exportDto.type });
    }

    // 选择性导出
    if (exportDto.detailIds && exportDto.detailIds.length > 0) {
      queryBuilder.andWhere('detail.id IN (:...detailIds)', {
        detailIds: exportDto.detailIds,
      });
    }

    const details = await queryBuilder.getMany();

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('客户档案明细');

    // 设置列宽
    worksheet.columns = [
      { header: '序号', key: 'index', width: 8 },
      { header: '客户编码', key: 'customerCode', width: 15 },
      { header: '客户名称', key: 'customerName', width: 20 },
      { header: '明细类型', key: 'type', width: 15 },
      { header: '返回数量', key: 'returnQuantity', width: 12 },
      { header: '商品总货款', key: 'totalGoodsAmount', width: 15 },
      { header: '换货订单', key: 'exchangeOrderString', width: 15 },
      { header: '定金', key: 'deposit', width: 12 },
      { header: '采购订单ID', key: 'purchaseOrderId', width: 15 },
      { header: '预定数量', key: 'reservedQuantity', width: 12 },
      { header: '补货成交款', key: 'restockDealAmount', width: 15 },
      { header: '补货数量', key: 'restockQuantity', width: 12 },
      { header: '补货订单ID', key: 'restockOrderId', width: 15 },
      { header: '代发金额', key: 'dropShippingAmount', width: 15 },
      { header: '代发数量', key: 'dropShippingQuantity', width: 12 },
      { header: '代发客户数', key: 'dropShippingCustomerCount', width: 12 },
      { header: '物流金额', key: 'logisticsAmount', width: 12 },
      { header: '图片', key: 'imageUrl', width: 30 },
      { header: '备注', key: 'remark', width: 30 },
      { header: '负责人员', key: 'responsibleUserName', width: 15 },
      { header: '创建时间', key: 'createdAt', width: 20 },
    ];

    // 设置标题行样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 类型映射
    const typeMap = {
      [CustomerProfileDetailType.EXCHANGE]: '换货',
      [CustomerProfileDetailType.REFUND]: '退货',
      [CustomerProfileDetailType.ORDER]: '订货',
      [CustomerProfileDetailType.RESTOCK]: '补货',
      [CustomerProfileDetailType.DROP_SHIPPING]: '代发',
    };

    // 添加数据行
    let currentRow = 2;
    for (let i = 0; i < details.length; i++) {
      const detail = details[i];

      const row = worksheet.addRow({
        index: i + 1,
        customerCode: detail.customerCode,
        customerName: detail.customer?.name || '',
        type: typeMap[detail.type] || detail.type,
        returnQuantity: detail.returnQuantity || '',
        totalGoodsAmount: detail.totalGoodsAmount || '',
        exchangeOrderString: detail.exchangeOrderString || '',
        deposit: detail.deposit || '',
        purchaseOrderId: detail.purchaseOrderId || '',
        reservedQuantity: detail.reservedQuantity || '',
        restockDealAmount: detail.restockDealAmount || '',
        restockQuantity: detail.restockQuantity || '',
        restockOrderId: detail.restockOrderId || '',
        dropShippingAmount: detail.dropShippingAmount || '',
        dropShippingQuantity: detail.dropShippingQuantity || '',
        dropShippingCustomerCount: detail.dropShippingCustomerCount || '',
        logisticsAmount: detail.logisticsAmount || '',
        imageUrl: detail.imageUrl || '',
        remark: detail.remark || '',
        responsibleUserName: detail.responsibleUser?.nickname || '',
        createdAt: detail.createdAt.toISOString().split('T')[0],
      });

      // 下载并插入图片
      try {
        if (detail.imageUrl) {
          this.logger.log(`Downloading image: ${detail.imageUrl}`);

          const imageResponse = await axios.get(detail.imageUrl, {
            responseType: 'arraybuffer',
            timeout: 10000,
          });

          const imageBuffer = Buffer.from(imageResponse.data);

          // 添加图片到工作簿
          const imageId = workbook.addImage({
            buffer: imageBuffer,
            extension: 'jpeg',
          });

          // 在图片列插入图片
          worksheet.addImage(imageId, {
            tl: { col: 17, row: currentRow - 1 }, // 图片列（第18列，索引17）
            ext: { width: 200, height: 100 }, // 图片大小
          });

          this.logger.log(`Image inserted successfully for detail ${i + 1}`);
        }
      } catch (imageError) {
        this.logger.warn(
          `Failed to load image for detail ${i + 1}: ${detail.imageUrl}`,
          imageError.message,
        );
        // 如果图片加载失败，在图片列显示链接
        row.getCell(18).value = detail.imageUrl;
      }

      currentRow++;
    }

    // 生成Excel缓冲区
    const excelBuffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(excelBuffer);
  }
}
