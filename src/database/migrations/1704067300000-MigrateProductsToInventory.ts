import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateProductsToInventory1704067300000
  implements MigrationInterface
{
  name = 'MigrateProductsToInventory1704067300000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('开始迁移现有商品数据到库存表...');

    // 查询所有现有的商品
    const products = await queryRunner.query(`
      SELECT 
        p.code as "productCode",
        p."colorSizeCombinations"
      FROM products p 
      WHERE p."isDeleted" = false 
        AND p."colorSizeCombinations" IS NOT NULL
        AND p."colorSizeCombinations" != '[]'::jsonb
    `);

    console.log(`找到 ${products.length} 个商品需要迁移`);

    let totalInventoryRecords = 0;

    for (const product of products) {
      const { productCode, colorSizeCombinations } = product;

      try {
        // 解析颜色尺寸组合
        const combinations = JSON.parse(colorSizeCombinations);

        if (!Array.isArray(combinations)) {
          console.warn(`商品 ${productCode} 的颜色尺寸组合格式不正确，跳过`);
          continue;
        }

        // 为每个颜色尺寸组合创建库存记录
        for (const combo of combinations) {
          if (!combo.colorCode || !combo.sizes || !Array.isArray(combo.sizes)) {
            console.warn(
              `商品 ${productCode} 的颜色组合格式不正确，跳过: ${JSON.stringify(combo)}`,
            );
            continue;
          }

          for (const size of combo.sizes) {
            // 检查颜色是否存在
            const colorExists = await queryRunner.query(
              `
              SELECT 1 FROM colors WHERE code = $1 AND "isDeleted" = false
            `,
              [combo.colorCode],
            );

            if (colorExists.length === 0) {
              console.warn(
                `颜色 ${combo.colorCode} 不存在，跳过商品 ${productCode} 的该颜色组合`,
              );
              continue;
            }

            // 检查是否已存在相同的库存记录
            const existingRecord = await queryRunner.query(
              `
              SELECT 1 FROM inventory_details 
              WHERE "productCode" = $1 AND "colorCode" = $2 AND size = $3 AND "isDeleted" = false
            `,
              [productCode, combo.colorCode, size],
            );

            if (existingRecord.length > 0) {
              console.log(
                `库存记录 ${productCode}-${combo.colorCode}-${size} 已存在，跳过`,
              );
              continue;
            }

            // 创建库存记录（初始库存为0）
            await queryRunner.query(
              `
              INSERT INTO inventory_details (
                "productCode",
                "colorCode",
                size,
                quantity,
                "reservedQuantity",
                "availableQuantity",
                "safetyStock",
                "isDeleted",
                "createdAt",
                "updatedAt"
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `,
              [
                productCode,
                combo.colorCode,
                size,
                0, // 初始库存数量为0
                0, // 预留数量为0
                0, // 可用数量为0
                0, // 安全库存为0
                false, // 未删除
              ],
            );

            totalInventoryRecords++;

            if (totalInventoryRecords % 100 === 0) {
              console.log(`已创建 ${totalInventoryRecords} 条库存记录...`);
            }
          }
        }

        console.log(`商品 ${productCode} 迁移完成`);
      } catch (error) {
        console.error(`迁移商品 ${productCode} 时出错:`, error.message);
        // 继续处理下一个商品，不中断整个迁移过程
      }
    }

    console.log(`迁移完成！总共创建了 ${totalInventoryRecords} 条库存记录`);

    // 验证迁移结果
    const inventoryCount = await queryRunner.query(`
      SELECT COUNT(*) as count FROM inventory_details WHERE "isDeleted" = false
    `);

    console.log(`验证：库存表中现有 ${inventoryCount[0].count} 条记录`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('回滚：删除所有迁移生成的库存记录...');

    // 删除所有库存记录（这是一个危险操作，在生产环境中要谨慎）
    await queryRunner.query(`
      DELETE FROM inventory_details 
      WHERE quantity = 0 
        AND "reservedQuantity" = 0 
        AND "availableQuantity" = 0 
        AND "safetyStock" = 0
        AND "latestCost" IS NULL
        AND "warehouseLocation" IS NULL
        AND remark IS NULL
    `);

    console.log('回滚完成');
  }
}
