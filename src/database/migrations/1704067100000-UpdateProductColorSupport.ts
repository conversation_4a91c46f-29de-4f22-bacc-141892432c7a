import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProductColorSupport1704067100000 implements MigrationInterface {
  name = 'UpdateProductColorSupport1704067100000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 检查products表是否存在
    const tableExists = await queryRunner.hasTable('products');
    if (!tableExists) {
      console.log('⚠️  Products表不存在，跳过颜色支持更新');
      return;
    }

    // 1. 确保colorSizeCombinations字段存在且为jsonb类型
    const hasColorSizeCombinations = await queryRunner.hasColumn('products', 'colorSizeCombinations');
    if (!hasColorSizeCombinations) {
      await queryRunner.query(`
        ALTER TABLE "products" 
        ADD COLUMN "colorSizeCombinations" jsonb NOT NULL DEFAULT '[]'::jsonb
      `);
      console.log('✅ 添加colorSizeCombinations字段');
    } else {
      // 确保字段类型正确
      await queryRunner.query(`
        ALTER TABLE "products" 
        ALTER COLUMN "colorSizeCombinations" TYPE jsonb USING "colorSizeCombinations"::jsonb
      `);
      console.log('✅ 更新colorSizeCombinations字段类型为jsonb');
    }

    // 2. 添加字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN "products"."colorSizeCombinations" IS '颜色尺寸组合，包含颜色级别的图片、辅料、价格配置'
    `);

    // 3. 更新现有数据，确保颜色配置的完整性
    // 为缺少完整配置的颜色添加默认配置
    await queryRunner.query(`
      UPDATE "products" 
      SET "colorSizeCombinations" = (
        SELECT jsonb_agg(
          CASE 
            WHEN combo->'images' IS NULL OR jsonb_array_length(combo->'images') = 0 THEN
              combo || jsonb_build_object('images', '["default-product.jpg"]'::jsonb)
            ELSE combo
          END ||
          CASE 
            WHEN combo->'accessories' IS NULL THEN
              jsonb_build_object('accessories', '[]'::jsonb)
            ELSE '{}'::jsonb
          END ||
          CASE 
            WHEN combo->'priceAdjustments' IS NULL THEN
              jsonb_build_object('priceAdjustments', '{
                "clothingCostAdjustment": 0,
                "accessoryCostAdjustment": 0,
                "retailPriceAdjustment": 0,
                "preOrderPriceAdjustment": 0,
                "restockPriceAdjustment": 0,
                "spotPriceAdjustment": 0
              }'::jsonb)
            ELSE '{}'::jsonb
          END
        )
        FROM jsonb_array_elements("colorSizeCombinations") AS combo
      )
      WHERE "colorSizeCombinations" IS NOT NULL 
        AND jsonb_array_length("colorSizeCombinations") > 0
        AND "isDeleted" = false
    `);

    // 4. 为没有颜色配置的商品添加默认配置
    await queryRunner.query(`
      UPDATE "products" 
      SET "colorSizeCombinations" = '[{
        "colorCode": "DEFAULT",
        "sizes": ["S", "M", "L"],
        "images": ["default-product.jpg"],
        "accessories": [],
        "priceAdjustments": {
          "clothingCostAdjustment": 0,
          "accessoryCostAdjustment": 0,
          "retailPriceAdjustment": 0,
          "preOrderPriceAdjustment": 0,
          "restockPriceAdjustment": 0,
          "spotPriceAdjustment": 0
        },
        "remark": "默认配置，请更新为实际颜色信息"
      }]'::jsonb
      WHERE ("colorSizeCombinations" IS NULL 
        OR jsonb_array_length("colorSizeCombinations") = 0)
        AND "isDeleted" = false
    `);

    // 5. 创建索引以提高查询性能
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_products_color_combinations" 
      ON "products" USING GIN ("colorSizeCombinations")
    `);

    // 6. 验证数据完整性
    const incompleteProducts = await queryRunner.query(`
      SELECT COUNT(*) as count
      FROM "products" p,
      jsonb_array_elements(p."colorSizeCombinations") AS combo
      WHERE p."isDeleted" = false
        AND (
          combo->'images' IS NULL 
          OR jsonb_array_length(combo->'images') = 0
          OR combo->'priceAdjustments' IS NULL
        )
    `);

    if (incompleteProducts[0].count > 0) {
      console.log(`⚠️  发现 ${incompleteProducts[0].count} 个不完整的颜色配置，已添加默认值`);
    }

    console.log('✅ Product表颜色支持更新完成');
    console.log('✅ 所有商品现在都有完整的颜色级别配置');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_products_color_combinations"
    `);

    // 恢复简单的颜色尺寸组合格式
    await queryRunner.query(`
      UPDATE "products" 
      SET "colorSizeCombinations" = (
        SELECT jsonb_agg(
          jsonb_build_object(
            'colorCode', combo->>'colorCode',
            'sizes', combo->'sizes'
          )
        )
        FROM jsonb_array_elements("colorSizeCombinations") AS combo
      )
      WHERE "colorSizeCombinations" IS NOT NULL 
        AND jsonb_array_length("colorSizeCombinations") > 0
    `);

    console.log('✅ Product表颜色支持回滚完成');
  }
}
