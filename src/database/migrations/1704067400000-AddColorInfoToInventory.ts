import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColorInfoToInventory1704067400000 implements MigrationInterface {
  name = 'AddColorInfoToInventory1704067400000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加颜色信息字段到库存明细表
    await queryRunner.query(`
      ALTER TABLE "inventory_details" 
      ADD COLUMN "colorImages" jsonb,
      ADD COLUMN "colorPriceInfo" jsonb,
      ADD COLUMN "colorAccessories" jsonb
    `);

    // 添加字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN "inventory_details"."colorImages" IS '颜色对应图片数组'
    `);
    
    await queryRunner.query(`
      COMMENT ON COLUMN "inventory_details"."colorPriceInfo" IS '颜色价格信息'
    `);
    
    await queryRunner.query(`
      COMMENT ON COLUMN "inventory_details"."colorAccessories" IS '颜色对应辅料'
    `);

    // 从商品表中迁移颜色信息到库存表
    await queryRunner.query(`
      UPDATE "inventory_details" 
      SET 
        "colorImages" = COALESCE(
          (
            SELECT combo->>'images'
            FROM "products" p,
            jsonb_array_elements(p."colorSizeCombinations") AS combo
            WHERE p.code = "inventory_details"."productCode"
            AND combo->>'colorCode' = "inventory_details"."colorCode"
            AND combo->'images' IS NOT NULL
            LIMIT 1
          )::jsonb,
          '[]'::jsonb
        ),
        "colorAccessories" = COALESCE(
          (
            SELECT combo->'accessories'
            FROM "products" p,
            jsonb_array_elements(p."colorSizeCombinations") AS combo
            WHERE p.code = "inventory_details"."productCode"
            AND combo->>'colorCode' = "inventory_details"."colorCode"
            AND combo->'accessories' IS NOT NULL
            LIMIT 1
          ),
          '[]'::jsonb
        ),
        "colorPriceInfo" = (
          SELECT jsonb_build_object(
            'clothingCost', p."clothingCost" + COALESCE((combo->'priceAdjustments'->>'clothingCostAdjustment')::numeric, 0),
            'accessoryCost', p."accessoryCost" + COALESCE((combo->'priceAdjustments'->>'accessoryCostAdjustment')::numeric, 0),
            'retailPrice', p."retailPrice" + COALESCE((combo->'priceAdjustments'->>'retailPriceAdjustment')::numeric, 0),
            'preOrderPrice', p."preOrderPrice" + COALESCE((combo->'priceAdjustments'->>'preOrderPriceAdjustment')::numeric, 0),
            'restockPrice', p."restockPrice" + COALESCE((combo->'priceAdjustments'->>'restockPriceAdjustment')::numeric, 0),
            'spotPrice', p."spotPrice" + COALESCE((combo->'priceAdjustments'->>'spotPriceAdjustment')::numeric, 0)
          )
          FROM "products" p,
          jsonb_array_elements(p."colorSizeCombinations") AS combo
          WHERE p.code = "inventory_details"."productCode"
          AND combo->>'colorCode' = "inventory_details"."colorCode"
          AND p."isDeleted" = false
          LIMIT 1
        )
      WHERE "inventory_details"."isDeleted" = false
    `);

    console.log('✅ 库存表颜色信息字段添加完成');
    console.log('✅ 现有库存记录的颜色信息迁移完成');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除添加的字段
    await queryRunner.query(`
      ALTER TABLE "inventory_details" 
      DROP COLUMN IF EXISTS "colorImages",
      DROP COLUMN IF EXISTS "colorPriceInfo",
      DROP COLUMN IF EXISTS "colorAccessories"
    `);

    console.log('✅ 库存表颜色信息字段回滚完成');
  }
}
