import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateInventoryTables1704067200000 implements MigrationInterface {
  name = 'CreateInventoryTables1704067200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建库存明细表
    await queryRunner.createTable(
      new Table({
        name: 'inventory_details',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'productCode',
            type: 'varchar',
            length: '100',
            comment: '商品编码',
          },
          {
            name: 'colorCode',
            type: 'varchar',
            length: '50',
            comment: '颜色编码',
          },
          {
            name: 'size',
            type: 'varchar',
            length: '10',
            comment: '尺寸',
          },
          {
            name: 'quantity',
            type: 'int',
            default: 0,
            comment: '库存数量',
          },
          {
            name: 'reservedQuantity',
            type: 'int',
            default: 0,
            comment: '预留数量',
          },
          {
            name: 'availableQuantity',
            type: 'int',
            default: 0,
            comment: '可用数量',
          },
          {
            name: 'safetyStock',
            type: 'int',
            default: 0,
            comment: '安全库存',
          },
          {
            name: 'latestCost',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
            comment: '最新进货成本',
          },
          {
            name: 'warehouseLocation',
            type: 'varchar',
            length: '100',
            isNullable: true,
            comment: '仓库位置',
          },
          {
            name: 'colorImages',
            type: 'jsonb',
            isNullable: true,
            comment: '颜色对应图片数组',
          },
          {
            name: 'colorPriceInfo',
            type: 'jsonb',
            isNullable: true,
            comment: '颜色价格信息',
          },
          {
            name: 'colorAccessories',
            type: 'jsonb',
            isNullable: true,
            comment: '颜色对应辅料',
          },
          {
            name: 'remark',
            type: 'text',
            isNullable: true,
            comment: '备注',
          },
          {
            name: 'isDeleted',
            type: 'boolean',
            default: false,
            comment: '是否已删除',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            comment: '创建时间',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            comment: '更新时间',
          },
          {
            name: 'deletedAt',
            type: 'timestamp',
            isNullable: true,
            comment: '删除时间',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['productCode'],
            referencedTableName: 'products',
            referencedColumnNames: ['code'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['colorCode'],
            referencedTableName: 'colors',
            referencedColumnNames: ['code'],
            onDelete: 'CASCADE',
          },
        ],
      }),
      true,
    );

    // 创建唯一索引
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_inventory_unique" ON "inventory_details" ("productCode", "colorCode", "size")
    `);

    // 创建库存变动记录表
    await queryRunner.createTable(
      new Table({
        name: 'inventory_transactions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'productCode',
            type: 'varchar',
            length: '100',
            comment: '商品编码',
          },
          {
            name: 'colorCode',
            type: 'varchar',
            length: '50',
            comment: '颜色编码',
          },
          {
            name: 'size',
            type: 'varchar',
            length: '10',
            comment: '尺寸',
          },
          {
            name: 'transactionType',
            type: 'enum',
            enum: ['IN', 'OUT', 'ADJUST', 'RESERVE', 'RELEASE'],
            comment: '变动类型',
          },
          {
            name: 'quantity',
            type: 'int',
            comment: '变动数量',
          },
          {
            name: 'beforeQuantity',
            type: 'int',
            comment: '变动前数量',
          },
          {
            name: 'afterQuantity',
            type: 'int',
            comment: '变动后数量',
          },
          {
            name: 'referenceNumber',
            type: 'varchar',
            length: '100',
            isNullable: true,
            comment: '关联单据号',
          },
          {
            name: 'reason',
            type: 'text',
            isNullable: true,
            comment: '变动原因',
          },
          {
            name: 'operatorCode',
            type: 'varchar',
            length: '50',
            isNullable: true,
            comment: '操作人',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            comment: '创建时间',
          },
        ],
      }),
      true,
    );

    // 创建索引
    await queryRunner.query(`
      CREATE INDEX "IDX_transaction_product" ON "inventory_transactions" ("productCode", "colorCode", "size")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_transaction_type" ON "inventory_transactions" ("transactionType")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_transaction_created" ON "inventory_transactions" ("createdAt")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('inventory_transactions');
    await queryRunner.dropTable('inventory_details');
  }
}
