module.exports = {
  apps: [{
    name: 'web-manager-backend',
    script: 'start-production.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000,
      HOST: '0.0.0.0'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    // 进程管理配置
    kill_timeout: 5000,
    listen_timeout: 3000,
    // 自动重启配置
    min_uptime: '10s',
    max_restarts: 10,
    // 内存和CPU监控
    monitoring: false,
    // 日志配置
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    // 集群模式配置（如果需要）
    exec_mode: 'fork'
  }],

  deploy: {
    production: {
      user: 'root',
      host: '*************',
      ref: 'origin/main',
      repo: '**************:username/web-manager-backend.git',
      path: '/var/www/web-manager-backend',
      'pre-deploy-local': '',
      'post-deploy': 'pnpm install && pnpm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
