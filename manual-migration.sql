-- 手动数据库迁移脚本
-- 用于处理产品和库存表的结构变更

-- 1. 为现有的库存记录设置默认的sizeCode值（从size字段复制）
UPDATE inventory_details 
SET size = COALESCE(size, 'M') 
WHERE size IS NULL;

-- 2. 添加sizeCode字段（可空）
ALTER TABLE inventory_details 
ADD COLUMN IF NOT EXISTS sizeCode varchar(10);

-- 3. 将size字段的值复制到sizeCode字段
UPDATE inventory_details 
SET sizeCode = size 
WHERE sizeCode IS NULL;

-- 4. 为现有记录生成SKU编码
UPDATE inventory_details 
SET skuCode = CONCAT(productCode, '-', colorCode, '-', COALESCE(sizeCode, size))
WHERE skuCode IS NULL;

-- 5. 添加新的库存管理字段
ALTER TABLE inventory_details 
ADD COLUMN IF NOT EXISTS totalStock integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS actualStock integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS reservedStock integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS damagedStock integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS purchasingStock integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS needPurchaseStock integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS safetyStock integer DEFAULT 10,
ADD COLUMN IF NOT EXISTS minStock integer DEFAULT 5,
ADD COLUMN IF NOT EXISTS maxStock integer DEFAULT 1000,
ADD COLUMN IF NOT EXISTS avgCost decimal(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS latestCost decimal(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS isActive boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS warehouseLocation varchar(100) DEFAULT 'A区',
ADD COLUMN IF NOT EXISTS totalInbound integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS totalOutbound integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS lastInboundDate timestamp,
ADD COLUMN IF NOT EXISTS lastOutboundDate timestamp;

-- 6. 将现有的quantity值迁移到新的库存字段
UPDATE inventory_details 
SET 
  totalStock = COALESCE(quantity, 0),
  actualStock = COALESCE(quantity, 0)
WHERE totalStock = 0 AND actualStock = 0;

-- 7. 为产品表添加categoryCode字段（已经添加了，这里确保存在）
-- ALTER TABLE products ADD COLUMN IF NOT EXISTS categoryCode varchar(50);

-- 8. 创建默认商品分类（如果不存在）
INSERT INTO product_categories (id, code, name, sizes, "isDeleted", "createdAt", "updatedAt")
SELECT 
  uuid_generate_v4(),
  'DEFAULT',
  '默认分类',
  ARRAY['S', 'M', 'L', 'XL'],
  false,
  NOW(),
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM product_categories WHERE code = 'DEFAULT' AND "isDeleted" = false
);

-- 9. 为没有分类的产品设置默认分类
UPDATE products 
SET categoryCode = 'DEFAULT'
WHERE categoryCode IS NULL;

-- 10. 创建索引
CREATE INDEX IF NOT EXISTS idx_inventory_details_sku_code ON inventory_details(skuCode);
CREATE INDEX IF NOT EXISTS idx_inventory_details_product_color_size ON inventory_details(productCode, colorCode, sizeCode);
CREATE INDEX IF NOT EXISTS idx_products_category_code ON products(categoryCode);

-- 完成
SELECT 'Migration completed successfully' as status;
