# 🚀 更新后的部署指南 - 支持颜色信息的库存系统

## ⚠️ 重要更新说明

**新增功能：**
1. ✅ 库存表新增颜色信息字段（图片、价格、辅料）
2. ✅ 商品创建时强制要求颜色配置（图片、辅料、价格必填）
3. ✅ 库存查询按颜色分组，一个颜色对应多个尺码
4. ✅ 支持颜色级别的价格差异和辅料配置

**数据库变更：**
- 新增迁移：`1704067400000-AddColorInfoToInventory.ts`
- 库存表新增字段：`colorImages`, `colorPriceInfo`, `colorAccessories`

---

## 📋 部署前检查

### 1. 数据库备份（必须！）
```bash
# 创建完整备份
pg_dump -h localhost -U postgres -d manager > backup_before_color_update_$(date +%Y%m%d_%H%M%S).sql

# 验证备份文件
ls -lh backup_before_color_update_*.sql
```

### 2. 验证当前数据
```bash
# 检查现有商品数据
psql -h localhost -U postgres -d manager -c "
SELECT 
  code, 
  name, 
  jsonb_array_length(\"colorSizeCombinations\") as color_count
FROM products 
WHERE \"isDeleted\" = false 
LIMIT 5;
"

# 检查现有库存数据
psql -h localhost -U postgres -d manager -c "
SELECT COUNT(*) as inventory_count FROM inventory_details WHERE \"isDeleted\" = false;
"
```

---

## 🚀 部署步骤

### 步骤1: 停止服务
```bash
pm2 stop all
# 或
sudo systemctl stop manager-api
```

### 步骤2: 部署代码
```bash
# Git部署
git pull origin main
npm install --production
npm run build
```

### 步骤3: 执行数据库迁移（关键！）
```bash
# 查看待执行的迁移
npm run migration:show

# 应该看到3个迁移：
# - CreateInventoryTables1704067200000
# - MigrateProductsToInventory1704067300000  
# - AddColorInfoToInventory1704067400000 (新增)

# 执行迁移
npm run migration:run

# 验证迁移结果
npm run verify:migration
```

### 步骤4: 验证颜色信息迁移
```bash
# 检查新字段是否添加成功
psql -h localhost -U postgres -d manager -c "
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'inventory_details' 
AND column_name IN ('colorImages', 'colorPriceInfo', 'colorAccessories');
"

# 检查颜色信息是否迁移成功
psql -h localhost -U postgres -d manager -c "
SELECT 
  \"productCode\",
  \"colorCode\",
  \"colorImages\",
  \"colorPriceInfo\"->>'retailPrice' as retail_price
FROM inventory_details 
WHERE \"colorImages\" IS NOT NULL 
LIMIT 3;
"
```

### 步骤5: 启动服务
```bash
pm2 start ecosystem.config.js
# 或
sudo systemctl start manager-api
```

### 步骤6: 验证功能
```bash
# 测试健康检查
curl http://localhost:3000/health

# 测试商品接口
curl http://localhost:3000/products?page=1&pageSize=5

# 测试库存接口（新的按颜色分组结构）
curl http://localhost:3000/inventory?page=1&pageSize=5

# 测试商品库存接口（按颜色分组）
curl "http://localhost:3000/inventory/products/PROD001"
```

---

## 🔍 新功能验证

### 1. 创建支持颜色差异的商品
```bash
curl -X POST http://localhost:3000/products \
  -H "Content-Type: application/json" \
  -d '{
    "code": "TEST001",
    "name": "测试商品",
    "brandCode": "BRAND001",
    "supplierCode": "SUP001",
    "clothingCost": 50.00,
    "accessoryCost": 10.00,
    "retailPrice": 120.00,
    "colorSizeCombinations": [
      {
        "colorCode": "RED",
        "sizes": ["S", "M", "L"],
        "images": ["red-1.jpg", "red-2.jpg"],
        "accessories": [
          {"accessoryId": "acc-uuid-1", "quantity": 2}
        ],
        "priceAdjustments": {
          "retailPriceAdjustment": 20.00
        },
        "remark": "红色特别款"
      }
    ]
  }'
```

### 2. 验证库存自动生成
```bash
# 查看按颜色分组的库存
curl "http://localhost:3000/inventory/products/TEST001"

# 预期返回结构：
{
  "productInfo": {...},
  "colorInventories": [
    {
      "colorCode": "RED",
      "colorName": "红色",
      "totalQuantity": 0,
      "images": ["red-1.jpg", "red-2.jpg"],
      "priceInfo": {
        "retailPrice": 140.00  // 120 + 20调整
      },
      "accessories": [...],
      "sizeDetails": [
        {"size": "S", "quantity": 0},
        {"size": "M", "quantity": 0},
        {"size": "L", "quantity": 0}
      ]
    }
  ]
}
```

### 3. 测试库存调整
```bash
# 调整红色M码库存
curl -X POST http://localhost:3000/inventory/adjust \
  -H "Content-Type: application/json" \
  -d '{
    "productCode": "TEST001",
    "colorCode": "RED",
    "size": "M",
    "adjustType": "SET",
    "quantity": 100,
    "reason": "测试库存"
  }'
```

### 4. 验证排序功能
```bash
# 按库存数量排序
curl "http://localhost:3000/inventory?sortBy=quantity&sortOrder=DESC&page=1&pageSize=10"

# 按价格排序
curl "http://localhost:3000/inventory?sortBy=latestCost&sortOrder=DESC&page=1&pageSize=10"
```

---

## 🚨 常见问题解决

### 问题1: 迁移失败 - 颜色信息字段添加失败
```bash
# 检查错误
npm run migration:show

# 手动回滚
npm run migration:revert

# 检查数据完整性
psql -h localhost -U postgres -d manager -c "
SELECT COUNT(*) FROM products WHERE \"colorSizeCombinations\" IS NULL;
"

# 重新执行迁移
npm run migration:run
```

### 问题2: 现有商品缺少必填的颜色信息
```bash
# 检查哪些商品缺少颜色配置
psql -h localhost -U postgres -d manager -c "
SELECT code, name 
FROM products 
WHERE \"colorSizeCombinations\" IS NULL 
   OR jsonb_array_length(\"colorSizeCombinations\") = 0
   OR \"isDeleted\" = false;
"

# 需要手动更新这些商品，添加完整的颜色配置
```

### 问题3: 库存记录缺少颜色信息
```bash
# 检查缺少颜色信息的库存记录
psql -h localhost -U postgres -d manager -c "
SELECT COUNT(*) 
FROM inventory_details 
WHERE \"colorImages\" IS NULL 
   OR \"colorPriceInfo\" IS NULL 
   OR \"isDeleted\" = false;
"

# 重新运行颜色信息迁移
psql -h localhost -U postgres -d manager -f src/database/migrations/1704067400000-AddColorInfoToInventory.ts
```

### 问题4: 商品创建失败 - 缺少必填字段
**错误信息：** "每个颜色必须至少有一张图片"

**解决方案：** 更新商品创建请求，确保每个颜色都包含：
- `images`: 至少一张图片
- `accessories`: 至少一个辅料配置
- `priceAdjustments`: 价格调整信息（可以全为0）

---

## 📊 数据结构对比

### 旧结构（按尺码返回）
```json
{
  "inventoryDetails": [
    {"productCode": "PROD001", "colorCode": "RED", "size": "S", "quantity": 10},
    {"productCode": "PROD001", "colorCode": "RED", "size": "M", "quantity": 15},
    {"productCode": "PROD001", "colorCode": "RED", "size": "L", "quantity": 8}
  ]
}
```

### 新结构（按颜色分组）
```json
{
  "colorInventories": [
    {
      "colorCode": "RED",
      "colorName": "红色",
      "totalQuantity": 33,
      "images": ["red-1.jpg", "red-2.jpg"],
      "priceInfo": {"retailPrice": 140.00},
      "sizeDetails": [
        {"size": "S", "quantity": 10},
        {"size": "M", "quantity": 15},
        {"size": "L", "quantity": 8}
      ]
    }
  ]
}
```

---

## ✅ 部署成功标志

- [ ] ✅ 3个数据库迁移全部执行成功
- [ ] ✅ 库存表新增颜色信息字段
- [ ] ✅ 现有库存记录的颜色信息迁移完成
- [ ] ✅ 商品创建要求颜色配置必填
- [ ] ✅ 库存查询按颜色分组返回
- [ ] ✅ 库存排序功能正常
- [ ] ✅ 颜色价格差异计算正确

---

## 🔄 紧急回滚

如果新功能有问题，可以回滚：

```bash
# 1. 停止服务
pm2 stop all

# 2. 回滚数据库迁移
npm run migration:revert  # 回滚颜色信息字段
npm run migration:revert  # 回滚库存数据迁移
npm run migration:revert  # 回滚库存表创建

# 3. 恢复数据库备份
psql -h localhost -U postgres -d manager < backup_before_color_update_YYYYMMDD_HHMMSS.sql

# 4. 回滚代码
git checkout HEAD~1

# 5. 重新构建启动
npm run build
pm2 start ecosystem.config.js
```

---

## 📞 技术支持

### 验证脚本
```bash
# 完整验证
npm run verify:deployment
npm run verify:migration

# 颜色信息验证
psql -h localhost -U postgres -d manager -c "
SELECT 
  i.\"productCode\",
  i.\"colorCode\",
  CASE WHEN i.\"colorImages\" IS NOT NULL THEN '✅' ELSE '❌' END as has_images,
  CASE WHEN i.\"colorPriceInfo\" IS NOT NULL THEN '✅' ELSE '❌' END as has_price,
  CASE WHEN i.\"colorAccessories\" IS NOT NULL THEN '✅' ELSE '❌' END as has_accessories
FROM inventory_details i 
WHERE i.\"isDeleted\" = false 
LIMIT 10;
"
```

### 性能监控
```bash
# 查询性能测试
time curl "http://localhost:3000/inventory/products/PROD001"

# 数据库连接数
psql -h localhost -U postgres -d manager -c "
SELECT count(*) as connections 
FROM pg_stat_activity 
WHERE datname = 'manager';
"
```

---

## 🎉 新功能亮点

1. **颜色信息完整性** - 每个库存记录包含完整的颜色信息
2. **按颜色分组展示** - 前端友好的数据结构
3. **价格差异支持** - 不同颜色可以有不同价格
4. **强制数据完整性** - 创建商品时必须提供完整颜色配置
5. **向后兼容** - 现有数据自动迁移，不影响原有功能

**部署完成后，您的库存系统将完美支持按颜色分组的精确管理！** 🎊
