-- 创建销售需求订单和商品采购订单相关表
-- 执行时间：2025-01-19

-- 1. 创建销售需求订单表
CREATE TABLE IF NOT EXISTS sales_demand_orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_number VARCHAR(50) UNIQUE NOT NULL,
  customer_code VARCHAR(50),
  sales_person_code VARCHAR(50) NOT NULL,
  demand_date DATE NOT NULL,
  expected_delivery_date DATE,
  priority_level VARCHAR(20) DEFAULT 'normal' CHECK (priority_level IN ('urgent', 'high', 'normal', 'low')),
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'approved', 'merged', 'completed', 'cancelled')),
  total_quantity INTEGER DEFAULT 0,
  estimated_total_amount DECIMAL(15,2) DEFAULT 0,
  remark TEXT,
  created_by_user_code VARCHAR(50),
  approved_by_user_code VARCHAR(50),
  approved_at TIMESTAMP,
  merged_to_purchase_order_id UUID,
  merged_at TIMESTAMP,
  is_deleted BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deleted_at TIMESTAMP
);

-- 2. 创建销售需求订单明细表
CREATE TABLE IF NOT EXISTS sales_demand_order_details (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sales_demand_order_id UUID NOT NULL,
  product_code VARCHAR(100) NOT NULL,
  color_code VARCHAR(50) NOT NULL,
  size_code VARCHAR(10) NOT NULL,
  sku_code VARCHAR(150) NOT NULL,
  demand_quantity INTEGER NOT NULL,
  current_stock INTEGER DEFAULT 0,
  shortage_quantity INTEGER DEFAULT 0,
  unit_price DECIMAL(10,2),
  estimated_amount DECIMAL(15,2) DEFAULT 0,
  remark TEXT,
  category_code VARCHAR(50),
  brand_code VARCHAR(50),
  supplier_code VARCHAR(50),
  is_deleted BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  FOREIGN KEY (sales_demand_order_id) REFERENCES sales_demand_orders(id),
  FOREIGN KEY (product_code) REFERENCES products(code),
  FOREIGN KEY (sku_code) REFERENCES inventory_details(skuCode)
);

-- 3. 创建商品采购订单表
CREATE TABLE IF NOT EXISTS product_purchase_orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_number VARCHAR(50) UNIQUE NOT NULL,
  supplier_code VARCHAR(50) NOT NULL,
  order_date DATE NOT NULL,
  expected_delivery_date DATE,
  total_amount DECIMAL(15,2) DEFAULT 0,
  total_quantity INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'producing', 'shipped', 'received', 'completed', 'cancelled')),
  created_by_user_code VARCHAR(50),
  merged_demand_order_ids TEXT,
  merged_demand_order_count INTEGER DEFAULT 0,
  unique_sku_count INTEGER DEFAULT 0,
  unique_product_count INTEGER DEFAULT 0,
  remark TEXT,
  confirmed_at TIMESTAMP,
  confirmed_by_user_code VARCHAR(50),
  received_at TIMESTAMP,
  received_by_user_code VARCHAR(50),
  is_deleted BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deleted_at TIMESTAMP,
  
  FOREIGN KEY (supplier_code) REFERENCES suppliers(code)
);

-- 4. 创建商品采购订单明细表
CREATE TABLE IF NOT EXISTS product_purchase_order_details (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_purchase_order_id UUID NOT NULL,
  product_code VARCHAR(100) NOT NULL,
  color_code VARCHAR(50) NOT NULL,
  size_code VARCHAR(10) NOT NULL,
  sku_code VARCHAR(150) NOT NULL,
  purchase_quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_amount DECIMAL(15,2) NOT NULL,
  source_demand_orders TEXT,
  source_demand_order_count INTEGER DEFAULT 0,
  total_demand_quantity INTEGER DEFAULT 0,
  delivery_date DATE,
  remark TEXT,
  category_code VARCHAR(50),
  brand_code VARCHAR(50),
  supplier_code VARCHAR(50),
  received_quantity INTEGER DEFAULT 0,
  pending_receive_quantity INTEGER DEFAULT 0,
  last_received_at TIMESTAMP,
  is_deleted BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  FOREIGN KEY (product_purchase_order_id) REFERENCES product_purchase_orders(id),
  FOREIGN KEY (product_code) REFERENCES products(code),
  FOREIGN KEY (sku_code) REFERENCES inventory_details(skuCode)
);

-- 5. 创建索引以提高查询性能和支持数据统计

-- 销售需求订单索引
CREATE INDEX IF NOT EXISTS idx_sales_demand_orders_order_number ON sales_demand_orders(order_number);
CREATE INDEX IF NOT EXISTS idx_sales_demand_orders_sales_person_demand_date ON sales_demand_orders(sales_person_code, demand_date);
CREATE INDEX IF NOT EXISTS idx_sales_demand_orders_status_priority ON sales_demand_orders(status, priority_level);
CREATE INDEX IF NOT EXISTS idx_sales_demand_orders_created_at ON sales_demand_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_sales_demand_orders_customer_code ON sales_demand_orders(customer_code) WHERE customer_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_sales_demand_orders_approved_at ON sales_demand_orders(approved_at) WHERE approved_at IS NOT NULL;

-- 销售需求订单明细索引
CREATE INDEX IF NOT EXISTS idx_sales_demand_order_details_order_id ON sales_demand_order_details(sales_demand_order_id);
CREATE INDEX IF NOT EXISTS idx_sales_demand_order_details_product_color_size ON sales_demand_order_details(product_code, color_code, size_code);
CREATE INDEX IF NOT EXISTS idx_sales_demand_order_details_sku_code ON sales_demand_order_details(sku_code);
CREATE INDEX IF NOT EXISTS idx_sales_demand_order_details_brand_code ON sales_demand_order_details(brand_code) WHERE brand_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_sales_demand_order_details_supplier_code ON sales_demand_order_details(supplier_code) WHERE supplier_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_sales_demand_order_details_category_code ON sales_demand_order_details(category_code) WHERE category_code IS NOT NULL;

-- 商品采购订单索引
CREATE INDEX IF NOT EXISTS idx_product_purchase_orders_order_number ON product_purchase_orders(order_number);
CREATE INDEX IF NOT EXISTS idx_product_purchase_orders_supplier_order_date ON product_purchase_orders(supplier_code, order_date);
CREATE INDEX IF NOT EXISTS idx_product_purchase_orders_status_order_date ON product_purchase_orders(status, order_date);
CREATE INDEX IF NOT EXISTS idx_product_purchase_orders_created_at ON product_purchase_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_product_purchase_orders_confirmed_at ON product_purchase_orders(confirmed_at) WHERE confirmed_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_product_purchase_orders_received_at ON product_purchase_orders(received_at) WHERE received_at IS NOT NULL;

-- 商品采购订单明细索引
CREATE INDEX IF NOT EXISTS idx_product_purchase_order_details_order_id ON product_purchase_order_details(product_purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_product_purchase_order_details_product_color_size ON product_purchase_order_details(product_code, color_code, size_code);
CREATE INDEX IF NOT EXISTS idx_product_purchase_order_details_sku_code ON product_purchase_order_details(sku_code);
CREATE INDEX IF NOT EXISTS idx_product_purchase_order_details_brand_code ON product_purchase_order_details(brand_code) WHERE brand_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_product_purchase_order_details_supplier_code ON product_purchase_order_details(supplier_code) WHERE supplier_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_product_purchase_order_details_delivery_date ON product_purchase_order_details(delivery_date) WHERE delivery_date IS NOT NULL;

-- 6. 添加注释
COMMENT ON TABLE sales_demand_orders IS '销售需求订单表';
COMMENT ON TABLE sales_demand_order_details IS '销售需求订单明细表';
COMMENT ON TABLE product_purchase_orders IS '商品采购订单表';
COMMENT ON TABLE product_purchase_order_details IS '商品采购订单明细表';

COMMENT ON COLUMN sales_demand_orders.order_number IS '需求订单编号（格式：SD + 年月日时分秒）';
COMMENT ON COLUMN sales_demand_orders.priority_level IS '优先级：urgent/high/normal/low';
COMMENT ON COLUMN sales_demand_orders.status IS '订单状态：draft/submitted/approved/merged/completed/cancelled';
COMMENT ON COLUMN sales_demand_orders.estimated_total_amount IS '预估总金额（自动计算）';

COMMENT ON COLUMN sales_demand_order_details.shortage_quantity IS '缺货数量（需求-库存）';
COMMENT ON COLUMN sales_demand_order_details.estimated_amount IS '预估小计金额（需求数量 × 单价）';

COMMENT ON COLUMN product_purchase_orders.order_number IS '采购订单编号（格式：PPO-供应商编码-年月日时分秒）';
COMMENT ON COLUMN product_purchase_orders.merged_demand_order_ids IS '合并的需求订单ID列表（JSON格式）';
COMMENT ON COLUMN product_purchase_orders.unique_sku_count IS '不同SKU数量（统计字段）';
COMMENT ON COLUMN product_purchase_orders.unique_product_count IS '涉及商品数量（统计字段）';

COMMENT ON COLUMN product_purchase_order_details.source_demand_orders IS '来源需求订单（JSON格式，记录哪些需求订单贡献了这个SKU的采购量）';
COMMENT ON COLUMN product_purchase_order_details.total_demand_quantity IS '总需求数量（来自所有需求订单的总和）';
COMMENT ON COLUMN product_purchase_order_details.pending_receive_quantity IS '待入库数量（采购数量-已入库数量）';

-- 7. 创建用于数据统计的视图

-- 需求订单统计视图
CREATE OR REPLACE VIEW v_sales_demand_order_stats AS
SELECT 
  DATE_TRUNC('month', demand_date) as month,
  status,
  priority_level,
  sales_person_code,
  COUNT(*) as order_count,
  SUM(total_quantity) as total_quantity,
  SUM(estimated_total_amount) as total_amount,
  AVG(estimated_total_amount) as avg_amount
FROM sales_demand_orders 
WHERE is_deleted = false
GROUP BY DATE_TRUNC('month', demand_date), status, priority_level, sales_person_code;

-- 采购订单统计视图
CREATE OR REPLACE VIEW v_product_purchase_order_stats AS
SELECT 
  DATE_TRUNC('month', order_date) as month,
  status,
  supplier_code,
  COUNT(*) as order_count,
  SUM(total_quantity) as total_quantity,
  SUM(total_amount) as total_amount,
  AVG(total_amount) as avg_amount,
  SUM(merged_demand_order_count) as total_merged_demands,
  SUM(unique_sku_count) as total_unique_skus,
  SUM(unique_product_count) as total_unique_products
FROM product_purchase_orders 
WHERE is_deleted = false
GROUP BY DATE_TRUNC('month', order_date), status, supplier_code;

-- 商品需求统计视图
CREATE OR REPLACE VIEW v_product_demand_stats AS
SELECT 
  product_code,
  brand_code,
  supplier_code,
  category_code,
  COUNT(DISTINCT sdo.id) as demand_order_count,
  SUM(demand_quantity) as total_demand_quantity,
  SUM(shortage_quantity) as total_shortage_quantity,
  AVG(unit_price) as avg_unit_price
FROM sales_demand_order_details sdod
JOIN sales_demand_orders sdo ON sdod.sales_demand_order_id = sdo.id
WHERE sdod.is_deleted = false AND sdo.is_deleted = false
GROUP BY product_code, brand_code, supplier_code, category_code;

-- 输出创建完成信息
SELECT 'Sales demand orders and product purchase orders tables created successfully!' as result;
