#!/bin/bash

# 最终PDF修复脚本 - 专门解决服务器PDF生成问题

set -e

echo "=========================================="
echo "开始最终PDF修复..."
echo "=========================================="

# 1. 停止服务
echo "1. 停止当前服务..."
pm2 stop backend || echo "服务未运行"

# 2. 清理旧的Puppeteer安装
echo "2. 清理旧的Puppeteer安装..."
rm -rf node_modules/puppeteer
rm -rf ~/.cache/puppeteer

# 3. 安装系统依赖
echo "3. 安装系统依赖..."
yum update -y
yum install -y \
    alsa-lib.x86_64 \
    atk.x86_64 \
    cups-libs.x86_64 \
    gtk3.x86_64 \
    ipa-gothic-fonts \
    libXcomposite.x86_64 \
    libXcursor.x86_64 \
    libXdamage.x86_64 \
    libXext.x86_64 \
    libXi.x86_64 \
    libXrandr.x86_64 \
    libXScrnSaver.x86_64 \
    libXtst.x86_64 \
    pango.x86_64 \
    xorg-x11-fonts-100dpi \
    xorg-x11-fonts-75dpi \
    xorg-x11-fonts-cyrillic \
    xorg-x11-fonts-misc \
    xorg-x11-fonts-Type1 \
    xorg-x11-utils \
    nss \
    nspr \
    libdrm \
    libxkbcommon \
    libxss1 \
    libasound2

# 4. 安装Chrome
echo "4. 安装Chrome..."
if ! command -v google-chrome &> /dev/null; then
    cd /tmp
    wget -q https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
    yum localinstall -y google-chrome-stable_current_x86_64.rpm
    rm -f google-chrome-stable_current_x86_64.rpm
    cd -
fi

# 5. 重新安装Puppeteer
echo "5. 重新安装Puppeteer..."
npm install puppeteer --save --unsafe-perm=true --allow-root

# 6. 设置环境变量
echo "6. 设置环境变量..."
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=false
export PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome

# 7. 测试Chrome
echo "7. 测试Chrome..."
google-chrome --version
google-chrome --headless --disable-gpu --dump-dom https://www.google.com > /dev/null

# 8. 测试Puppeteer
echo "8. 测试Puppeteer..."
node -e "
const puppeteer = require('puppeteer');
(async () => {
  try {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      executablePath: '/usr/bin/google-chrome'
    });
    const page = await browser.newPage();
    await page.setContent('<h1>Test</h1>');
    const pdf = await page.pdf();
    await browser.close();
    console.log('Puppeteer测试成功，PDF大小:', pdf.length, 'bytes');
  } catch (e) {
    console.error('Puppeteer测试失败:', e.message);
    process.exit(1);
  }
})();
"

# 9. 重新构建项目
echo "9. 重新构建项目..."
npm run build

# 10. 启动服务
echo "10. 启动服务..."
pm2 start backend

# 11. 等待服务启动
echo "11. 等待服务启动..."
sleep 15

# 12. 检查服务状态
echo "12. 检查服务状态..."
pm2 status

# 13. 测试API
echo "13. 测试API..."
sleep 5
if curl -s http://localhost:8080/api/commission-contracts > /dev/null; then
    echo "✅ API服务正常"
else
    echo "❌ API服务异常"
fi

echo ""
echo "=========================================="
echo "PDF修复完成！"
echo "=========================================="
echo ""
echo "修复内容："
echo "✅ 安装了所有必要的系统依赖"
echo "✅ 重新安装了Chrome浏览器"
echo "✅ 重新安装了Puppeteer"
echo "✅ 添加了备用PDF生成方法"
echo "✅ 优化了浏览器启动参数"
echo "✅ 增加了超时时间和错误处理"
echo ""
echo "如果PDF生成仍然失败，请："
echo "1. 检查日志: pm2 logs backend"
echo "2. 检查内存: free -h"
echo "3. 检查磁盘空间: df -h"
echo "4. 重启服务器: reboot"
