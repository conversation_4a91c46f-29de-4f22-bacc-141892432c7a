#!/bin/bash

# ========================================
# 服务器紧急修复脚本 - 资产模块500错误
# ========================================
# 在服务器上执行: bash server-emergency-fix.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_info "🚨 开始紧急修复资产模块500错误..."

# 1. 检查当前数据库字段名
log_info "检查数据库字段名..."
PGPASSWORD=54188 psql -h localhost -U postgres -d manager << 'EOF'
\echo '📋 检查当前字段名状态...'
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public'
    AND table_name IN (
        'operating_assets', 'operating_asset_details',
        'fixed_assets', 'fixed_asset_details',
        'rd_costs', 'rd_cost_details',
        'rental_assets', 'rental_asset_details'
    )
    AND column_name IN ('createDate', 'create_date')
ORDER BY table_name, column_name;
EOF

# 2. 修复数据库字段名（如果需要）
log_info "修复数据库字段名..."
PGPASSWORD=54188 psql -h localhost -U postgres -d manager << 'EOF'
-- 修复字段名：create_date -> createDate

-- 1. 修复 operating_assets 表
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_assets' AND column_name = 'create_date') THEN
        ALTER TABLE operating_assets RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'operating_assets.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'operating_assets.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 2. 修复 operating_asset_details 表
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'create_date') THEN
        ALTER TABLE operating_asset_details RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'operating_asset_details.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'operating_asset_details.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 3. 修复 fixed_assets 表
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'fixed_assets' AND column_name = 'create_date') THEN
        ALTER TABLE fixed_assets RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'fixed_assets.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'fixed_assets.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 4. 修复 fixed_asset_details 表
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'fixed_asset_details' AND column_name = 'create_date') THEN
        ALTER TABLE fixed_asset_details RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'fixed_asset_details.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'fixed_asset_details.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 5. 修复 rd_costs 表
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rd_costs' AND column_name = 'create_date') THEN
        ALTER TABLE rd_costs RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'rd_costs.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'rd_costs.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 6. 修复 rd_cost_details 表
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rd_cost_details' AND column_name = 'create_date') THEN
        ALTER TABLE rd_cost_details RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'rd_cost_details.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'rd_cost_details.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 7. 修复 rental_assets 表
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rental_assets' AND column_name = 'create_date') THEN
        ALTER TABLE rental_assets RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'rental_assets.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'rental_assets.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 8. 修复 rental_asset_details 表
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rental_asset_details' AND column_name = 'create_date') THEN
        ALTER TABLE rental_asset_details RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'rental_asset_details.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'rental_asset_details.create_date 字段不存在，跳过';
    END IF;
END $$;

\echo '✅ 数据库字段名修复完成！'
EOF

if [ $? -eq 0 ]; then
    log_success "数据库字段名修复完成"
else
    log_error "数据库字段名修复失败"
    exit 1
fi

# 3. 进入项目目录
cd /root/manager-web-backend

# 4. 重新构建应用
log_info "重新构建应用..."
npm run build

if [ $? -eq 0 ]; then
    log_success "应用构建完成"
else
    log_error "应用构建失败"
    exit 1
fi

# 5. 重启服务
log_info "重启服务..."
pm2 restart web-manager-backend

if [ $? -eq 0 ]; then
    log_success "服务重启完成"
else
    log_error "服务重启失败，尝试停止并重新启动..."
    pm2 stop web-manager-backend
    pm2 start ecosystem.config.js --env production
fi

# 6. 等待服务启动
log_info "等待服务启动..."
sleep 10

# 7. 检查服务状态
log_info "检查服务状态..."
if pm2 status | grep -q "online"; then
    log_success "✅ 服务修复完成！服务正在正常运行"
    
    # 测试API
    log_info "测试API..."
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/operating-assets/details?page=1&pageSize=5)
    if [ "$response" = "200" ]; then
        log_success "✅ API测试通过！"
        
        # 显示API响应
        log_info "API响应示例："
        curl -s "http://localhost:3000/operating-assets/details?page=1&pageSize=5" | head -c 500
        echo ""
    else
        log_error "❌ API测试失败，返回状态码: $response"
        log_info "查看错误日志:"
        pm2 logs web-manager-backend --lines 10
    fi
else
    log_error "❌ 服务启动失败"
    log_info "查看错误日志:"
    pm2 logs web-manager-backend --lines 20
    exit 1
fi

log_success "🎉 紧急修复完成！"
