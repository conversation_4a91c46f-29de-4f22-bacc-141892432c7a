# 销售订单财务集成测试用例

## 测试场景

### 场景1：寄付订单完成 - 应创建财务记录

**前置条件**：
- 销售订单状态为"待发货"或其他非完成状态
- 物流方式为"寄付"(PREPAID)
- 运费金额 > 0
- 存在物流供应商(LOGISTICS)

**测试步骤**：
1. 调用更新订单状态API，将状态改为"已完成"
2. 检查财务支出表是否新增记录

**预期结果**：
- 订单状态成功更新为"已完成"
- 财务支出表新增一条记录
- 支出金额等于订单运费
- 备注包含客户名称、销售员、订单号

### 场景2：到付订单完成 - 不应创建财务记录

**前置条件**：
- 销售订单状态为"待发货"
- 物流方式为"到付"(COD)
- 运费金额 > 0

**测试步骤**：
1. 调用更新订单状态API，将状态改为"已完成"
2. 检查财务支出表

**预期结果**：
- 订单状态成功更新为"已完成"
- 财务支出表不新增记录

### 场景3：寄付订单但运费为0 - 不应创建财务记录

**前置条件**：
- 销售订单状态为"待发货"
- 物流方式为"寄付"(PREPAID)
- 运费金额 = 0

**测试步骤**：
1. 调用更新订单状态API，将状态改为"已完成"
2. 检查财务支出表

**预期结果**：
- 订单状态成功更新为"已完成"
- 财务支出表不新增记录

## API测试

### 1. 更新订单状态API

```bash
PUT /sales-orders/{orderId}/status
Content-Type: application/json

{
  "status": "completed"
}
```

### 2. 查询财务支出记录

```bash
GET /expenses/details?supplierCode=LOGISTICS&orderBy=createDate&sortOrder=desc
```

## 数据验证

### 1. 销售订单表验证

```sql
SELECT 
  id,
  order_number,
  status,
  shipping_method,
  shipping_fee,
  customer_code,
  sales_person_code
FROM sales_orders 
WHERE id = '{orderId}';
```

### 2. 财务支出表验证

```sql
SELECT 
  ed.id,
  ed.supplier_code,
  ed.amount,
  ed.create_date,
  ed.remark,
  e.total_amount
FROM expense_details ed
JOIN expenses e ON ed.expense_id = e.id
WHERE ed.supplier_code = 'LOGISTICS'
  AND ed.remark LIKE '%{orderNumber}%'
ORDER BY ed.created_at DESC;
```

## 测试数据准备

### 1. 创建测试订单

```sql
-- 创建寄付测试订单
INSERT INTO sales_orders (
  id,
  order_number,
  customer_code,
  sales_person_code,
  status,
  shipping_method,
  shipping_fee,
  total_amount,
  created_at
) VALUES (
  'test-order-001',
  'SO20240704001',
  'CUST001',
  'SALES001',
  'pending_shipment',
  'prepaid',
  50.00,
  1000.00,
  NOW()
);
```

### 2. 确保供应商存在

```sql
-- 检查物流供应商是否存在
SELECT * FROM suppliers WHERE code = 'LOGISTICS';

-- 如果不存在则创建
INSERT INTO suppliers (
  id,
  code,
  name,
  contact_person,
  phone,
  is_deleted,
  created_at
) VALUES (
  UUID(),
  'LOGISTICS',
  '物流供应商',
  '物流客服',
  '************',
  false,
  NOW()
);
```

## 错误场景测试

### 场景1：供应商不存在

**测试目的**：验证当物流供应商不存在时的错误处理

**测试步骤**：
1. 删除或禁用物流供应商
2. 完成寄付订单
3. 检查错误日志和订单状态

**预期结果**：
- 订单状态仍然更新为"已完成"
- 记录错误日志
- 财务记录创建失败但不影响订单流程

### 场景2：财务服务异常

**测试目的**：验证财务服务异常时的容错处理

**测试步骤**：
1. 模拟财务服务异常
2. 完成寄付订单
3. 检查错误处理

**预期结果**：
- 订单状态正常更新
- 记录详细错误日志
- 不影响主业务流程

## 性能测试

### 批量订单完成测试

**测试目的**：验证大量订单同时完成时的性能表现

**测试数据**：
- 100个寄付订单同时完成
- 监控数据库性能
- 检查财务记录创建时间

**性能指标**：
- 订单状态更新响应时间 < 2秒
- 财务记录创建成功率 > 99%
- 数据库连接池正常

## 回归测试

### 1. 原有功能验证

确保新增的财务集成功能不影响原有的订单管理功能：

- 订单创建功能正常
- 订单查询功能正常
- 其他状态更新功能正常
- 订单删除功能正常

### 2. 财务模块验证

确保销售订单集成不影响财务模块的原有功能：

- 手动创建支出记录功能正常
- 支出查询和统计功能正常
- 支出编辑和删除功能正常

## 监控和告警

### 1. 业务监控

- 监控寄付订单完成数量
- 监控财务记录创建成功率
- 监控财务记录创建失败次数

### 2. 告警设置

- 财务记录创建失败率 > 5% 时告警
- 连续10个订单财务记录创建失败时告警
- 物流供应商不存在时告警

## 验收标准

### 功能验收

✅ 寄付订单完成时自动创建财务记录  
✅ 到付订单完成时不创建财务记录  
✅ 运费为0时不创建财务记录  
✅ 财务记录包含完整的业务信息  
✅ 错误情况下不影响订单状态更新  

### 性能验收

✅ 单个订单处理时间增加 < 500ms  
✅ 批量处理时系统稳定性良好  
✅ 数据库性能无明显下降  

### 数据验收

✅ 财务数据准确性100%  
✅ 业务数据一致性100%  
✅ 审计日志完整性100%  

这个测试方案确保了销售订单与财务系统集成的可靠性和稳定性。
