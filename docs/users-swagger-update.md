# 用户表Swagger文档更新

## 更新内容

### 1. 实体类(User Entity) ✅
用户实体类的Swagger文档已经完整，包含：

- **基本字段**：用户编码、昵称、密码
- **权限字段**：路由权限配置、超级管理员、公司管理员
- **公司关联**：公司编码、公司信息
- **状态字段**：激活状态、软删除标记
- **时间字段**：创建时间、更新时间、删除时间
- **银行信息**：开户银行账户名、银行卡号

### 2. DTO类更新

#### CreateUserDto ✅
创建用户DTO已包含所有必要字段：
- 用户编码、昵称、密码（必填）
- 权限路由配置（可选）
- 公司管理员标识（可选）
- 所属公司编码（可选）
- 激活状态（可选）
- 银行账户信息（可选）

#### UpdateUserDto ✅ 新增字段
更新用户DTO新增了以下字段：
- `isCompanyAdmin`：是否为公司管理员
- `companyCode`：所属公司编码

#### UserResponseDto ✅ 新增字段
用户响应DTO新增了以下字段：
- `isCompanyAdmin`：是否为公司管理员
- `companyCode`：所属公司编码
- `company`：所属公司信息

#### QueryUsersDto ✅
查询用户DTO已完整，支持：
- 分页参数（page、pageSize）
- 搜索关键词（模糊搜索用户编码和昵称）
- 激活状态筛选（includeInactive）

### 3. 控制器(Controller)更新

#### 统一响应格式 ✅
所有API接口的响应格式已统一为：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": null | object | array
}
```

#### 更新的接口文档

1. **创建用户** `POST /users`
   - 详细的响应schema定义
   - 完整的错误状态码说明

2. **获取用户列表** `GET /users`
   - 详细的分页响应结构
   - UserResponseDto引用

3. **获取用户详情** `GET /users/:code`
   - 统一的响应格式
   - UserResponseDto引用

4. **更新用户** `PATCH /users/:code`
   - 统一的响应格式
   - 详细的错误状态码

5. **删除用户** `DELETE /users/:code`
   - 统一的响应格式
   - 软删除说明

6. **管理员管理接口**
   - 设置/取消超级管理员
   - 设置/取消公司管理员
   - 统一的响应格式

## API接口完整性

### 基础CRUD操作 ✅
- ✅ 创建用户（仅超级管理员）
- ✅ 查询用户列表（支持搜索和筛选）
- ✅ 获取用户详情（仅超级管理员）
- ✅ 更新用户信息（仅超级管理员）
- ✅ 删除用户（软删除，仅超级管理员）

### 权限管理操作 ✅
- ✅ 设置超级管理员（全局唯一）
- ✅ 取消超级管理员权限
- ✅ 设置公司管理员
- ✅ 取消公司管理员权限

### 权限控制 ✅
- ✅ JWT认证保护
- ✅ 超级管理员权限验证
- ✅ 操作日志记录

## Swagger文档特性

### 1. 完整的字段描述
每个字段都有：
- 清晰的中文描述
- 示例值
- 是否必填标识
- 数据类型说明

### 2. 详细的API文档
每个接口都包含：
- 操作摘要
- 参数说明
- 响应格式
- 错误状态码
- 权限要求

### 3. 统一的响应格式
所有接口都遵循统一的响应结构：
```typescript
{
  code: number;    // 状态码
  message: string; // 消息
  data: any;       // 数据
}
```

### 4. 权限路由配置示例
提供了详细的权限配置示例：
```json
{
  "/users": {
    "create": true,
    "read": true,
    "update": true,
    "delete": true,
    "export": true,
    "import": true
  },
  "/products": {
    "create": false,
    "read": true,
    "update": false,
    "delete": false,
    "export": true,
    "import": false
  }
}
```

## 业务逻辑说明

### 1. 用户类型
- **超级管理员**：全局唯一，拥有所有权限
- **公司管理员**：每个公司可以有多个，管理本公司用户
- **普通用户**：基础权限，可配置路由权限

### 2. 权限控制
- **路由权限**：细粒度的功能权限控制
- **公司隔离**：公司管理员只能管理本公司用户
- **激活状态**：控制用户是否可被搜索和使用

### 3. 数据安全
- **密码加密**：密码字段使用@Exclude()隐藏
- **软删除**：删除操作不会物理删除数据
- **操作日志**：记录所有管理操作

## 使用建议

### 1. 开发者
- 使用Swagger UI查看完整的API文档
- 参考示例值进行接口调用
- 注意权限要求和错误处理

### 2. 前端开发
- 统一的响应格式便于处理
- 详细的字段说明便于表单设计
- 完整的错误码便于错误处理

### 3. 测试人员
- 使用Swagger UI进行接口测试
- 参考文档验证业务逻辑
- 测试各种权限场景

## 总结

用户表的Swagger文档已经完全更新，具备了：

✅ **完整性**：覆盖所有字段和接口  
✅ **准确性**：详细的类型和约束说明  
✅ **一致性**：统一的响应格式和命名规范  
✅ **实用性**：丰富的示例和清晰的描述  
✅ **安全性**：完整的权限控制说明  

现在用户相关的API文档已经可以为开发、测试和集成提供完整的参考。
