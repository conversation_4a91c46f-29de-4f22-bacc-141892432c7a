# 产品档案接口字段详细说明

## 创建产品接口 (CreateProductDto)

### 基础信息字段

| 字段名             | 类型   | 必填 | 最大长度 | 说明                                                                     | 示例                |
| ------------------ | ------ | ---- | -------- | ------------------------------------------------------------------------ | ------------------- |
| `code`             | string | ✅   | 100      | 商品编码，产品的唯一标识符，用于系统内部识别和管理，不可重复             | `PROD001`           |
| `name`             | string | ✅   | 200      | 商品名称，产品的显示名称，用于前端展示和销售                             | `时尚T恤`           |
| `manufacturerCode` | string | ❌   | 100      | 厂商编码，生产厂商的编码，用于标识产品的生产方                           | `MFG001`            |
| `brandCode`        | string | ✅   | 50       | 品牌编码，关联品牌表的编码，用于标识产品所属品牌                         | `BRAND001`          |
| `supplierCode`     | string | ✅   | 50       | 供应商编码，关联供应商表的编码，用于标识产品的供应商                     | `SUP001`            |
| `categoryCode`     | string | ❌   | 50       | 商品分类编码，关联产品分类表的编码，用于产品分类管理，不填则使用默认分类 | `CAT001`            |
| `craftDescription` | string | ❌   | 500      | 工艺描述，产品的制作工艺说明，如印花工艺、刺绣工艺等                     | `数码印花+手工刺绣` |

### 价格字段（基础价格）

| 字段名          | 类型   | 必填 | 格式          | 说明                                                       | 示例     |
| --------------- | ------ | ---- | ------------- | ---------------------------------------------------------- | -------- |
| `clothingCost`  | number | ✅   | decimal(10,2) | 服装成本，产品的基础服装制作成本，用于成本核算和利润计算   | `50.00`  |
| `accessoryCost` | number | ✅   | decimal(10,2) | 辅料成本，产品的基础辅料成本，包括拉链、纽扣等辅助材料成本 | `10.00`  |
| `retailPrice`   | number | ✅   | decimal(10,2) | 零售价，产品的基础零售价格，面向终端消费者的销售价格       | `120.00` |
| `preOrderPrice` | number | ✅   | decimal(10,2) | 预订价，产品的预订销售价格，通常用于预售或批量订购         | `100.00` |
| `restockPrice`  | number | ✅   | decimal(10,2) | 补货价，产品的补货销售价格，用于库存补充时的定价           | `110.00` |
| `spotPrice`     | number | ✅   | decimal(10,2) | 现货价，产品的现货销售价格，用于现有库存的即时销售         | `115.00` |

### 辅料配置字段

| 字段名        | 类型                   | 必填 | 说明                                                           | 示例              |
| ------------- | ---------------------- | ---- | -------------------------------------------------------------- | ----------------- |
| `accessories` | AccessoryQuantityDto[] | ❌   | 全局辅料数组，所有颜色通用的辅料配置，如标签、包装袋等通用材料 | 见下方辅料DTO说明 |

### 颜色配置字段

| 字段名           | 类型               | 必填 | 说明                                                                                                 | 示例              |
| ---------------- | ------------------ | ---- | ---------------------------------------------------------------------------------------------------- | ----------------- |
| `selectedColors` | SelectedColorDto[] | ✅   | 选择的颜色列表，产品支持的颜色配置，至少需要选择一个颜色，每个颜色包含图片、价格调整、专属辅料等信息 | 见下方颜色DTO说明 |

## 辅料数量DTO (AccessoryQuantityDto)

| 字段名        | 类型   | 必填 | 说明                                             | 示例                                   |
| ------------- | ------ | ---- | ------------------------------------------------ | -------------------------------------- |
| `accessoryId` | string | ✅   | 辅料ID，关联辅料表的UUID主键，用于标识具体的辅料 | `a1b2c3d4-e5f6-7890-abcd-ef1234567890` |
| `quantity`    | number | ✅   | 辅料数量，该辅料在产品中的使用数量，必须为正整数 | `2`                                    |

## 价格调整DTO (PriceAdjustmentDto)

用于定义相对于基础价格的增减调整，所有字段均为可选，可为负数表示减少。

| 字段名                    | 类型   | 必填 | 格式          | 说明                                       | 示例    |
| ------------------------- | ------ | ---- | ------------- | ------------------------------------------ | ------- |
| `clothingCostAdjustment`  | number | ❌   | decimal(10,2) | 服装成本调整，相对于基础服装成本的增减金额 | `5.00`  |
| `accessoryCostAdjustment` | number | ❌   | decimal(10,2) | 辅料成本调整，相对于基础辅料成本的增减金额 | `3.00`  |
| `retailPriceAdjustment`   | number | ❌   | decimal(10,2) | 零售价调整，相对于基础零售价的增减金额     | `20.00` |
| `preOrderPriceAdjustment` | number | ❌   | decimal(10,2) | 预订价调整，相对于基础预订价的增减金额     | `10.00` |
| `restockPriceAdjustment`  | number | ❌   | decimal(10,2) | 补货价调整，相对于基础补货价的增减金额     | `15.00` |
| `spotPriceAdjustment`     | number | ❌   | decimal(10,2) | 现货价调整，相对于基础现货价的增减金额     | `18.00` |

## 选择的颜色配置DTO (SelectedColorDto)

用于创建产品时指定具体的颜色配置。

| 字段名             | 类型                   | 必填 | 说明                                                                   | 示例                                |
| ------------------ | ---------------------- | ---- | ---------------------------------------------------------------------- | ----------------------------------- |
| `colorCode`        | string                 | ✅   | 颜色编码，关联颜色表的编码，用于标识具体颜色                           | `COLOR001`                          |
| `images`           | string[]               | ✅   | 颜色专属图片，每个颜色至少需要一张展示图片，用于前端商品展示和库存管理 | `["red-front.jpg", "red-back.jpg"]` |
| `priceAdjustments` | PriceAdjustmentDto     | ❌   | 颜色价格调整，相对于基础价格的增减调整，不填则使用基础价格             | 见上方价格调整DTO                   |
| `accessories`      | AccessoryQuantityDto[] | ❌   | 颜色专属辅料，该颜色独有的辅料配置，与全局辅料配合使用                 | 见上方辅料DTO说明                   |
| `remark`           | string                 | ❌   | 颜色备注，该颜色的特殊说明或备注信息                                   | `红色款式，春季主推`                |

## 辅料设计说明

### 两层辅料结构

1. **全局辅料** (`CreateProductDto.accessories`)

   - 所有颜色通用的辅料
   - 如：标签、包装袋、说明书等
   - 可选字段

2. **颜色专属辅料** (`SelectedColorDto.accessories`)
   - 每个颜色独有的辅料配置
   - 如：红色专用拉链、蓝色专用纽扣等
   - 可选字段

### 辅料使用逻辑

- 最终每个SKU的辅料 = 全局辅料 + 该颜色的专属辅料
- 系统会自动验证所有辅料ID是否存在于辅料表中
- 辅料数量必须为正整数

## 价格计算说明

### 价格计算公式

```
最终价格 = 基础价格 + 颜色价格调整值
```

### 价格存储位置

1. **产品表** - 存储基础价格（6种价格）
2. **库存明细表** - 存储每个SKU的最终计算价格

### 价格调整规则

- 价格调整值可以为正数（增加）或负数（减少）
- 不填写调整值时，使用基础价格（调整值为0）
- 最终价格不能为负数

## 创建流程说明

1. **验证基础信息** - 检查品牌、供应商、分类是否存在
2. **验证辅料** - 检查全局辅料和颜色专属辅料是否存在
3. **创建产品档案** - 保存SPU信息到产品表
4. **生成SKU库存** - 根据颜色和尺寸组合自动生成库存明细
5. **计算价格** - 为每个SKU计算并存储最终价格
6. **返回结果** - 返回创建的SPU和生成的SKU列表

## 注意事项

- 商品编码必须唯一，不可重复
- 至少需要选择一个颜色
- 每个颜色必须至少有一张图片
- 所有价格字段不能为负数
- 辅料ID必须存在于辅料表中
- 工艺描述为新增字段，可选填写

## API请求示例

### 创建产品完整示例

```json
{
  "code": "PROD001",
  "name": "时尚印花T恤",
  "manufacturerCode": "MFG001",
  "brandCode": "BRAND001",
  "supplierCode": "SUP001",
  "categoryCode": "CAT001",
  "craftDescription": "数码印花+手工刺绣+特殊洗水工艺",
  "clothingCost": 45.0,
  "accessoryCost": 8.0,
  "retailPrice": 128.0,
  "preOrderPrice": 98.0,
  "restockPrice": 108.0,
  "spotPrice": 118.0,
  "accessories": [
    {
      "accessoryId": "label-uuid-001",
      "quantity": 1
    },
    {
      "accessoryId": "bag-uuid-001",
      "quantity": 1
    }
  ],
  "selectedColors": [
    {
      "colorCode": "RED001",
      "images": ["red-t-shirt-front.jpg", "red-t-shirt-back.jpg"],
      "priceAdjustments": {
        "clothingCostAdjustment": 5.0,
        "retailPriceAdjustment": 20.0,
        "preOrderPriceAdjustment": 15.0
      },
      "accessories": [
        {
          "accessoryId": "red-zipper-uuid",
          "quantity": 1
        }
      ],
      "remark": "红色款，春季主推色"
    },
    {
      "colorCode": "BLUE001",
      "images": ["blue-t-shirt-front.jpg"],
      "priceAdjustments": {
        "retailPriceAdjustment": 10.0
      },
      "remark": "蓝色款，经典色"
    }
  ]
}
```

### 最终价格计算示例

基于上述请求，系统会计算出以下最终价格：

**红色款最终价格：**

- 服装成本：45.00 + 5.00 = 50.00
- 辅料成本：8.00 + 0 = 8.00
- 零售价：128.00 + 20.00 = 148.00
- 预订价：98.00 + 15.00 = 113.00
- 补货价：108.00 + 0 = 108.00
- 现货价：118.00 + 0 = 118.00

## 字段总结表

### 必填字段 ✅

| 字段名                       | 类型   | 说明               |
| ---------------------------- | ------ | ------------------ |
| `code`                       | string | 商品编码，唯一标识 |
| `name`                       | string | 商品名称           |
| `brandCode`                  | string | 品牌编码           |
| `supplierCode`               | string | 供应商编码         |
| `clothingCost`               | number | 服装成本           |
| `accessoryCost`              | number | 辅料成本           |
| `retailPrice`                | number | 零售价             |
| `preOrderPrice`              | number | 预订价             |
| `restockPrice`               | number | 补货价             |
| `spotPrice`                  | number | 现货价             |
| `selectedColors`             | array  | 颜色配置列表       |
| `selectedColors[].colorCode` | string | 颜色编码           |
| `selectedColors[].images`    | array  | 颜色图片           |

### 可选字段 ❌

| 字段名                              | 类型   | 说明         |
| ----------------------------------- | ------ | ------------ |
| `manufacturerCode`                  | string | 厂商编码     |
| `categoryCode`                      | string | 商品分类编码 |
| `craftDescription`                  | string | 工艺描述     |
| `accessories`                       | array  | 全局辅料配置 |
| `selectedColors[].priceAdjustments` | object | 颜色价格调整 |
| `selectedColors[].accessories`      | array  | 颜色专属辅料 |
| `selectedColors[].remark`           | string | 颜色备注     |

### 价格调整字段（全部可选）

| 字段名                    | 类型   | 说明         |
| ------------------------- | ------ | ------------ |
| `clothingCostAdjustment`  | number | 服装成本调整 |
| `accessoryCostAdjustment` | number | 辅料成本调整 |
| `retailPriceAdjustment`   | number | 零售价调整   |
| `preOrderPriceAdjustment` | number | 预订价调整   |
| `restockPriceAdjustment`  | number | 补货价调整   |
| `spotPriceAdjustment`     | number | 现货价调整   |

## 辅料数量配置说明

### 当前实现状态 ✅

您的系统辅料数量配置是**完全正确的**！

1. ✅ **辅料数量DTO**：支持辅料ID + 数量配置
2. ✅ **两层辅料结构**：全局辅料 + 颜色专属辅料
3. ✅ **数据验证**：完整的字段验证和业务逻辑验证
4. ✅ **数据存储**：正确存储到产品表和库存表
5. ✅ **导入导出**：支持Excel导入导出辅料配置

### 辅料配置逻辑

**全局辅料**：

- 所有颜色通用的辅料（如标签、包装袋）
- 存储在 `CreateProductDto.accessories` 字段

**颜色专属辅料**：

- 每个颜色独有的辅料（如红色专用拉链、蓝色专用纽扣）
- 存储在 `SelectedColorDto.accessories` 字段

**最终辅料配置** = 全局辅料 + 颜色专属辅料

### 使用示例

```json
{
  "code": "PROD001",
  "name": "时尚T恤",
  "accessories": [
    { "accessoryId": "label-uuid", "quantity": 1 },
    { "accessoryId": "bag-uuid", "quantity": 1 }
  ],
  "selectedColors": [
    {
      "colorCode": "RED001",
      "accessories": [{ "accessoryId": "red-zipper-uuid", "quantity": 1 }]
    }
  ]
}
```

**说明**：辅料不需要库存管理，只需要配置每个产品使用的辅料种类和数量即可。

## 更新日志

- **2024-12-17**: 新增 `craftDescription` 工艺描述字段（可选）
- **2024-12-17**: 完善所有字段的详细说明和用途
- **2024-12-17**: 更新导入导出模板以支持工艺字段
- **2024-12-17**: 添加完整的API请求示例和价格计算示例
- **2024-12-17**: 确认辅料数量配置功能完整正确，无需库存管理
- 现货价：118.00 + 0 = 118.00
