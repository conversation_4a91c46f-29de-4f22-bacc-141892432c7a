# 购物车商品档案同步改进方案

## 问题描述

您提出的问题非常重要：购物车中存储的商品信息是快照数据，当商品档案（SKU、品牌、颜色等）发生变更时，购物车中的信息不会自动更新，可能导致数据不一致。

## 原有问题

1. **商品信息冗余存储**：购物车商品表中存储了大量冗余的商品信息
2. **数据不一致风险**：商品档案更新后，购物车中的信息可能过时
3. **库存信息实时性**：虽然库存是实时的，但商品基本信息使用快照数据

## 改进方案

### 1. 优化数据获取策略

修改了 `transformItemToDto` 方法，优先使用实时的商品档案信息：

```typescript
// 优先使用实时数据，如果不存在则使用快照数据
skuCode: sku?.code || item.skuCode || '',
productName: sku?.name || item.productName || '',
colorCode: sku?.colorCode || item.colorCode || '',
colorName: color?.name || item.colorName || '',
```

### 2. 自动检查和更新机制

在每次获取购物车时，自动检查商品档案信息是否有变化：

- `checkAndUpdateOutdatedItems()`: 检查并更新过时的商品档案信息
- 只有当检测到变化时才进行数据库更新，提高性能

### 3. 手动刷新接口

提供了手动刷新购物车商品档案信息的接口：

```
POST /shopping-cart/refresh-items-info?userCode=user001
```

### 4. 批量清理机制

提供了批量清理无效购物车商品的功能：

```
POST /shopping-cart/cleanup-invalid-items
```

清理以下情况的商品：
- 库存记录已删除
- SKU已删除
- 关联的商品档案不存在

## 使用建议

### 1. 日常使用

- 系统会在每次获取购物车时自动检查商品档案变化
- 无需手动干预，保证数据一致性

### 2. 批量维护

建议定期（如每天凌晨）执行以下操作：

1. 调用清理无效商品接口
2. 可以考虑添加定时任务自动执行

### 3. 商品档案变更后

当进行大批量商品档案更新后，可以：

1. 调用批量清理接口清理无效商品
2. 系统会在用户下次访问购物车时自动更新档案信息

## 性能考虑

1. **增量更新**：只更新有变化的商品信息
2. **批量操作**：使用事务和批量更新减少数据库操作
3. **缓存友好**：保留快照数据作为备用，确保系统稳定性

## 数据一致性保证

1. **实时优先**：优先使用关联查询的实时数据
2. **快照备用**：快照数据作为备用，防止关联数据丢失
3. **自动同步**：检测到变化时自动更新快照数据
4. **清理机制**：定期清理无效数据

## 向后兼容性

- 保留了原有的快照字段，确保向后兼容
- 即使关联数据丢失，仍可使用快照数据
- 不影响现有的购物车功能

## 总结

这个改进方案解决了您提出的商品档案同步问题，通过以下方式确保数据一致性：

1. **实时数据优先**：优先使用最新的商品档案信息
2. **自动检查更新**：在获取购物车时自动检查并更新过时信息
3. **手动刷新机制**：提供手动刷新接口
4. **批量清理功能**：定期清理无效数据
5. **性能优化**：只更新有变化的数据，减少不必要的数据库操作

这样既保证了数据的实时性和一致性，又保持了系统的性能和稳定性。
