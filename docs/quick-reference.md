# 销售开单与退单快速参考

## 🚀 核心改进点

### 销售退单简化
- ✅ **原订单ID改为可选** - 不再强制关联原订单
- ✅ **物流单号必填** - 确保退货可追踪  
- ✅ **商品选择统一** - 与销售开单逻辑一致
- ✅ **向后兼容** - 仍支持关联原订单

---

## 📋 必填字段速查

### 销售开单必填字段
```
✅ salesPersonCode     - 销售人员编码
✅ customerCode        - 客户编码  
✅ orderDate          - 订单日期
✅ details            - 订单明细数组
  ✅ productCode      - 商品编码
  ✅ colorCode        - 颜色编码
  ✅ sizeCode         - 尺寸编码
  ✅ skuCode          - SKU编码
  ✅ quantity         - 销售数量 (≥1)
  ✅ priceType        - 价格类型
  ✅ unitPrice        - 单价 (≥0)
```

### 销售退单必填字段
```
✅ customerCode           - 客户编码
✅ applicantCode          - 申请人编码
✅ returnDate            - 退单日期
✅ returnType            - 退单类型
✅ reason                - 退单原因
✅ returnTrackingNumber  - 退货物流单号 ⭐新增必填
✅ details               - 退单明细数组
  ✅ productCode         - 商品编码
  ✅ colorCode           - 颜色编码
  ✅ sizeCode            - 尺寸编码
  ✅ skuCode             - SKU编码
  ✅ quantity            - 退货数量 (≥1)
  ✅ unitPrice           - 单价 (≥0)
  ✅ totalAmount         - 小计金额
  ✅ actualAmount        - 实际金额
```

---

## 🔄 枚举值速查

### 订单状态
```javascript
SalesOrderStatus = {
  'draft': '草稿',
  'confirmed': '已确认', 
  'shipped': '已发货',
  'delivered': '已送达',
  'completed': '已完成',
  'cancelled': '已取消'
}
```

### 退单类型
```javascript
ReturnType = {
  'return': '退货',
  'exchange': '换货', 
  'return_exchange': '退换货'
}
```

### 退单状态
```javascript
ReturnStatus = {
  'draft': '草稿',
  'submitted': '已提交',
  'approved': '已审核',
  'processing': '处理中', 
  'completed': '已完成',
  'rejected': '已拒绝',
  'cancelled': '已取消'
}
```

### 退单原因
```javascript
ReturnReason = {
  'quality_issue': '质量问题',
  'size_issue': '尺寸问题',
  'color_issue': '颜色问题',
  'customer_change': '客户变更',
  'delivery_delay': '交货延迟',
  'wrong_product': '发错商品',
  'damaged': '商品损坏',
  'other': '其他原因'
}
```

### 价格类型
```javascript
PriceType = {
  'retail': '零售价',
  'pre_order': '预订价',
  'restock': '补货价', 
  'spot': '现货价'
}
```

### 物流方式
```javascript
ShippingMethod = {
  'collect': '到付',
  'prepaid': '寄付'
}
```

---

## 🔧 API端点速查

### 销售开单
```
POST   /sales-orders           - 创建订单
GET    /sales-orders           - 查询订单列表
GET    /sales-orders/{id}      - 获取订单详情
PUT    /sales-orders/{id}      - 更新订单
DELETE /sales-orders/{id}      - 删除订单
POST   /sales-orders/{id}/confirm  - 确认订单
```

### 销售退单  
```
POST   /sales-returns          - 创建退单
GET    /sales-returns          - 查询退单列表
GET    /sales-returns/{id}     - 获取退单详情
PUT    /sales-returns/{id}     - 更新退单
DELETE /sales-returns/{id}     - 删除退单
POST   /sales-returns/{id}/submit  - 提交退单
POST   /sales-returns/{id}/approve - 审核退单
```

---

## 💡 前端开发要点

### 表单验证
```javascript
// 销售开单验证
const validateSalesOrder = (data) => {
  if (!data.salesPersonCode) return '请选择销售人员';
  if (!data.customerCode) return '请选择客户';
  if (!data.details?.length) return '请添加商品明细';
  // ...
};

// 销售退单验证
const validateSalesReturn = (data) => {
  if (!data.customerCode) return '请选择客户';
  if (!data.returnTrackingNumber) return '请填写物流单号'; // ⭐重要
  if (!data.details?.length) return '请添加退货明细';
  // ...
};
```

### 金额自动计算
```javascript
// 订单明细金额计算
const calculateOrderDetail = (quantity, unitPrice, discountAmount = 0) => {
  const totalAmount = quantity * unitPrice;
  const actualAmount = totalAmount - discountAmount;
  return { totalAmount, actualAmount };
};

// 退单明细金额计算  
const calculateReturnDetail = (quantity, unitPrice, discountRate = 0) => {
  const totalAmount = quantity * unitPrice;
  const discountAmount = totalAmount * (discountRate / 100);
  const actualAmount = totalAmount - discountAmount;
  return { totalAmount, discountAmount, actualAmount };
};
```

### 商品选择联动
```javascript
// 商品-颜色-尺寸三级联动
const handleProductChange = (productCode) => {
  // 1. 获取该商品的可用颜色
  const colors = getProductColors(productCode);
  setAvailableColors(colors);
  
  // 2. 清空颜色和尺寸选择
  setSelectedColor('');
  setSelectedSize('');
  setAvailableSizes([]);
};

const handleColorChange = (colorCode) => {
  // 1. 获取该商品该颜色的可用尺寸
  const sizes = getProductSizes(selectedProduct, colorCode);
  setAvailableSizes(sizes);
  
  // 2. 清空尺寸选择
  setSelectedSize('');
};

const handleSizeChange = (sizeCode) => {
  // 自动生成SKU
  const sku = `${selectedProduct}-${selectedColor}-${sizeCode}`;
  setSkuCode(sku);
  
  // 获取库存和价格信息
  const inventory = getInventoryBySku(sku);
  setStockInfo(inventory);
};
```

### 条件显示逻辑
```javascript
// 代发信息显示条件
const showDropShipInfo = formData.isDropShipping === true;

// 换货信息显示条件  
const showExchangeInfo = ['exchange', 'return_exchange'].includes(formData.returnType);

// 原订单选择显示条件
const showOriginalOrder = true; // 始终显示，但为可选
```

---

## ⚠️ 重要提醒

1. **退单物流单号必填** - 这是新的必填字段，前端必须验证
2. **原订单ID可选** - 退单不再强制关联原订单
3. **商品选择统一** - 退单商品选择逻辑与开单保持一致
4. **金额自动计算** - 所有金额字段应自动计算，避免手动输入错误
5. **状态流转控制** - 不同状态下的操作权限要严格控制

---

*快速参考 v1.0 | 更新时间: 2025-06-23*
