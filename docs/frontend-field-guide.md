# 销售开单与销售退单前端字段说明文档

## 📋 目录

- [销售开单字段说明](#销售开单字段说明)
- [销售退单字段说明](#销售退单字段说明)
- [枚举值说明](#枚举值说明)
- [字段验证规则](#字段验证规则)
- [前端开发注意事项](#前端开发注意事项)

---

## 🛒 销售开单字段说明

### 主表字段 (CreateSalesOrderDto)

#### 基础信息

| 字段名                 | 类型   | 必填 | 说明         | 示例值       | 前端组件建议     |
| ---------------------- | ------ | ---- | ------------ | ------------ | ---------------- |
| `salesPersonCode`      | string | ✅   | 销售人员编码 | "SALES001"   | 下拉选择器       |
| `customerCode`         | string | ✅   | 订单客户编码 | "CUST001"    | 搜索选择器       |
| `orderDate`            | string | ✅   | 订单日期     | "2025-06-23" | 日期选择器       |
| `expectedDeliveryDate` | string | ❌   | 期望交货日期 | "2025-07-23" | 日期选择器       |
| `priority`             | enum   | ❌   | 订单优先级   | "normal"     | 单选按钮组       |
| `status`               | enum   | ❌   | 订单状态     | "draft"      | 状态标签（只读） |

#### 业务选项

| 字段名           | 类型    | 必填 | 说明     | 示例值 | 前端组件建议 |
| ---------------- | ------- | ---- | -------- | ------ | ------------ |
| `isDropShipping` | boolean | ❌   | 是否代发 | false  | 开关组件     |
| `isReleased`     | boolean | ❌   | 是否放单 | true   | 开关组件     |

#### 物流信息

| 字段名            | 类型   | 必填 | 说明     | 示例值     | 前端组件建议 |
| ----------------- | ------ | ---- | -------- | ---------- | ------------ |
| `shippingMethod`  | enum   | ❌   | 物流方式 | "collect"  | 单选按钮组   |
| `shippingFee`     | number | ❌   | 运费金额 | 15.0       | 数字输入框   |
| `shippingCompany` | string | ❌   | 物流公司 | "顺丰快递" | 文本输入框   |

#### 代发信息（当 isDropShipping=true 时显示）

| 字段名                        | 类型   | 必填 | 说明             | 示例值              | 前端组件建议 |
| ----------------------------- | ------ | ---- | ---------------- | ------------------- | ------------ |
| `dropShipReceiverName`        | string | 🔶   | 代发收货人姓名   | "李四"              | 文本输入框   |
| `dropShipReceiverPhone`       | string | 🔶   | 代发收货人电话   | "13900139000"       | 电话输入框   |
| `dropShipAddress`             | string | 🔶   | 代发收货地址     | "上海市浦东新区..." | 文本域       |
| `dropShipProvinceCode`        | number | ❌   | 代发收货省份代码 | 31                  | 省市区选择器 |
| `dropShipCity`                | string | ❌   | 代发收货城市     | "上海市"            | 省市区选择器 |
| `dropShipDistrict`            | string | ❌   | 代发收货区县     | "浦东新区"          | 省市区选择器 |
| `dropShipSpecialRequirements` | string | ❌   | 代发特殊要求     | "包装要求精美"      | 文本域       |

#### 其他字段

| 字段名              | 类型   | 必填 | 说明             | 示例值             | 前端组件建议         |
| ------------------- | ------ | ---- | ---------------- | ------------------ | -------------------- |
| `createdByUserCode` | string | ❌   | 创建人编码       | "USER001"          | 隐藏字段（自动填充） |
| `remark`            | string | ❌   | 备注             | "客户要求加急处理" | 文本域               |
| `details`           | array  | ✅   | 销售订单明细列表 | []                 | 动态表格             |

### 明细表字段 (CreateSalesOrderDetailDto)

#### 商品信息

| 字段名        | 类型   | 必填 | 说明     | 示例值               | 前端组件建议     |
| ------------- | ------ | ---- | -------- | -------------------- | ---------------- |
| `productCode` | string | ✅   | 商品编码 | "PROD001"            | 商品选择器       |
| `colorCode`   | string | ✅   | 颜色编码 | "COLOR001"           | 颜色选择器       |
| `sizeCode`    | string | ✅   | 尺寸编码 | "M"                  | 尺寸选择器       |
| `skuCode`     | string | ✅   | SKU编码  | "PROD001-COLOR001-M" | 自动生成（只读） |

#### 数量和价格

| 字段名           | 类型   | 必填 | 说明     | 示例值   | 前端组件建议     |
| ---------------- | ------ | ---- | -------- | -------- | ---------------- |
| `quantity`       | number | ✅   | 销售数量 | 50       | 数字输入框（≥1） |
| `priceType`      | enum   | ✅   | 价格类型 | "retail" | 下拉选择器       |
| `unitPrice`      | number | ✅   | 单价     | 100.0    | 数字输入框（≥0） |
| `discountAmount` | number | ❌   | 折扣金额 | 10.0     | 数字输入框（≥0） |

#### 其他明细字段

| 字段名             | 类型   | 必填 | 说明         | 示例值         | 前端组件建议 |
| ------------------ | ------ | ---- | ------------ | -------------- | ------------ |
| `expectedShipDate` | string | ❌   | 期望发货日期 | "2025-07-01"   | 日期选择器   |
| `remark`           | string | ❌   | 明细备注     | "客户指定颜色" | 文本输入框   |

---

## 🔄 销售退单字段说明

### 主表字段 (CreateSalesReturnDto)

#### 基础信息

| 字段名            | 类型   | 必填 | 说明             | 示例值                | 前端组件建议       |
| ----------------- | ------ | ---- | ---------------- | --------------------- | ------------------ |
| `originalOrderId` | string | ❌   | 原销售订单ID     | "550e8400-e29b..."    | 订单选择器（可选） |
| `customerCode`    | string | ✅   | 客户编码         | "CUST001"             | 搜索选择器         |
| `applicantCode`   | string | ✅   | 申请人编码       | "USER001"             | 用户选择器         |
| `returnDate`      | string | ✅   | 退单日期         | "2025-06-23"          | 日期选择器         |
| `returnType`      | enum   | ✅   | 退单类型         | "return"              | 单选按钮组         |
| `status`          | enum   | ❌   | 退单状态         | "draft"               | 状态标签（只读）   |
| `reason`          | enum   | ✅   | 退单原因         | "quality_issue"       | 下拉选择器         |
| `reasonDetail`    | string | ❌   | 退单原因详细说明 | "商品存在质量缺陷..." | 文本域             |
| `handlingFee`     | number | ❌   | 手续费           | 50.0                  | 数字输入框（≥0）   |

#### 物流信息

| 字段名                  | 类型   | 必填 | 说明         | 示例值         | 前端组件建议 |
| ----------------------- | ------ | ---- | ------------ | -------------- | ------------ |
| `returnShippingCompany` | string | ❌   | 退货物流公司 | "顺丰快递"     | 文本输入框   |
| `returnTrackingNumber`  | string | ✅   | 退货物流单号 | "SF1234567890" | 文本输入框   |

#### 其他字段

| 字段名    | 类型   | 必填 | 说明         | 示例值             | 前端组件建议 |
| --------- | ------ | ---- | ------------ | ------------------ | ------------ |
| `remark`  | string | ❌   | 备注         | "客户要求加急处理" | 文本域       |
| `details` | array  | ✅   | 退单明细列表 | []                 | 动态表格     |

### 明细表字段 (CreateSalesReturnDetailDto)

#### 关联信息

| 字段名             | 类型   | 必填 | 说明         | 示例值             | 前端组件建议         |
| ------------------ | ------ | ---- | ------------ | ------------------ | -------------------- |
| `originalDetailId` | string | ❌   | 原订单明细ID | "550e8400-e29b..." | 隐藏字段（自动关联） |

#### 商品信息

| 字段名        | 类型   | 必填 | 说明     | 示例值               | 前端组件建议     |
| ------------- | ------ | ---- | -------- | -------------------- | ---------------- |
| `productCode` | string | ✅   | 商品编码 | "PROD001"            | 商品选择器       |
| `colorCode`   | string | ✅   | 颜色编码 | "COLOR001"           | 颜色选择器       |
| `sizeCode`    | string | ✅   | 尺寸编码 | "M"                  | 尺寸选择器       |
| `skuCode`     | string | ✅   | SKU编码  | "PROD001-COLOR001-M" | 自动生成（只读） |

#### 退货信息

| 字段名           | 类型   | 必填 | 说明      | 示例值 | 前端组件建议     |
| ---------------- | ------ | ---- | --------- | ------ | ---------------- |
| `quantity`       | number | ✅   | 退货数量  | 50     | 数字输入框（≥1） |
| `unitPrice`      | number | ✅   | 单价      | 100.0  | 数字输入框（≥0） |
| `totalAmount`    | number | ✅   | 小计金额  | 5000.0 | 自动计算（只读） |
| `discountRate`   | number | ❌   | 折扣率(%) | 10     | 数字输入框（≥0） |
| `discountAmount` | number | ❌   | 折扣金额  | 500.0  | 自动计算（只读） |
| `actualAmount`   | number | ✅   | 实际金额  | 4500.0 | 自动计算（只读） |

#### 换货信息（当退单类型包含换货时显示）

| 字段名                | 类型   | 必填 | 说明         | 示例值               | 前端组件建议     |
| --------------------- | ------ | ---- | ------------ | -------------------- | ---------------- |
| `exchangeProductCode` | string | 🔶   | 换货商品编码 | "PROD002"            | 商品选择器       |
| `exchangeColorCode`   | string | 🔶   | 换货颜色编码 | "COLOR002"           | 颜色选择器       |
| `exchangeSizeCode`    | string | 🔶   | 换货尺寸编码 | "L"                  | 尺寸选择器       |
| `exchangeSkuCode`     | string | 🔶   | 换货SKU编码  | "PROD002-COLOR002-L" | 自动生成（只读） |
| `exchangeQuantity`    | number | 🔶   | 换货数量     | 30                   | 数字输入框（≥0） |
| `exchangeUnitPrice`   | number | 🔶   | 换货单价     | 120.0                | 数字输入框（≥0） |

#### 其他明细字段

| 字段名   | 类型   | 必填 | 说明     | 示例值             | 前端组件建议 |
| -------- | ------ | ---- | -------- | ------------------ | ------------ |
| `remark` | string | ❌   | 明细备注 | "商品存在质量问题" | 文本输入框   |

---

## 📊 枚举值说明

### 销售订单相关枚举

#### 订单状态 (SalesOrderStatus)

| 值          | 中文名称 | 说明         | 颜色建议 |
| ----------- | -------- | ------------ | -------- |
| `draft`     | 草稿     | 订单草稿状态 | 灰色     |
| `confirmed` | 已确认   | 订单已确认   | 蓝色     |
| `shipped`   | 已发货   | 订单已发货   | 橙色     |
| `delivered` | 已送达   | 订单已送达   | 绿色     |
| `completed` | 已完成   | 订单已完成   | 深绿色   |
| `cancelled` | 已取消   | 订单已取消   | 红色     |

#### 物流方式 (ShippingMethod)

| 值        | 中文名称 | 说明     |
| --------- | -------- | -------- |
| `collect` | 到付     | 货到付款 |
| `prepaid` | 寄付     | 预付运费 |

#### 订单优先级 (OrderPriority)

| 值       | 中文名称 | 说明       | 颜色建议 |
| -------- | -------- | ---------- | -------- |
| `urgent` | 紧急     | 紧急订单   | 红色     |
| `high`   | 高       | 高优先级   | 橙色     |
| `normal` | 普通     | 普通优先级 | 蓝色     |
| `low`    | 低       | 低优先级   | 灰色     |

#### 价格类型 (PriceType)

| 值          | 中文名称 | 说明     |
| ----------- | -------- | -------- |
| `retail`    | 零售价   | 零售价格 |
| `pre_order` | 预订价   | 预订价格 |
| `restock`   | 补货价   | 补货价格 |
| `spot`      | 现货价   | 现货价格 |

### 销售退单相关枚举

#### 退单类型 (ReturnType)

| 值                | 中文名称 | 说明             |
| ----------------- | -------- | ---------------- |
| `return`          | 退货     | 纯退货           |
| `exchange`        | 换货     | 纯换货           |
| `return_exchange` | 退换货   | 部分退货部分换货 |

#### 退单状态 (ReturnStatus)

| 值           | 中文名称 | 说明       | 颜色建议 |
| ------------ | -------- | ---------- | -------- |
| `draft`      | 草稿     | 退单草稿   | 灰色     |
| `submitted`  | 已提交   | 退单已提交 | 蓝色     |
| `approved`   | 已审核   | 退单已审核 | 绿色     |
| `processing` | 处理中   | 退单处理中 | 橙色     |
| `completed`  | 已完成   | 退单已完成 | 深绿色   |
| `rejected`   | 已拒绝   | 退单已拒绝 | 红色     |
| `cancelled`  | 已取消   | 退单已取消 | 灰色     |

#### 退单原因 (ReturnReason)

| 值                | 中文名称 | 说明         |
| ----------------- | -------- | ------------ |
| `quality_issue`   | 质量问题 | 商品质量问题 |
| `size_issue`      | 尺寸问题 | 尺寸不合适   |
| `color_issue`     | 颜色问题 | 颜色不符     |
| `customer_change` | 客户变更 | 客户需求变更 |
| `delivery_delay`  | 交货延迟 | 交货时间延迟 |
| `wrong_product`   | 发错商品 | 发货错误     |
| `damaged`         | 商品损坏 | 商品损坏     |
| `other`           | 其他原因 | 其他原因     |

---

## ✅ 字段验证规则

### 通用验证规则

- **必填字段**: 标记为 ✅ 的字段必须填写
- **条件必填**: 标记为 🔶 的字段在特定条件下必填
- **数字字段**: 最小值限制，通常 ≥ 0 或 ≥ 1
- **日期字段**: 格式为 YYYY-MM-DD
- **字符串长度**: 根据数据库字段长度限制

### 特殊验证规则

#### 销售开单

1. **代发字段验证**: 当 `isDropShipping = true` 时，代发相关字段变为必填
2. **明细数量验证**: `quantity` 必须 ≥ 1
3. **价格验证**: `unitPrice` 必须 ≥ 0
4. **SKU唯一性**: 同一订单中不能有重复的SKU

#### 销售退单

1. **物流单号**: `returnTrackingNumber` 为必填字段
2. **换货字段验证**: 当 `returnType` 包含换货时，换货相关字段变为必填
3. **退货数量验证**: `quantity` 必须 ≥ 1
4. **金额计算**:
   - `totalAmount = quantity × unitPrice`
   - `actualAmount = totalAmount - discountAmount`

---

## 💡 前端开发注意事项

### 1. 用户体验优化

- **商品选择**: 提供商品搜索和筛选功能
- **自动计算**: 金额字段应自动计算，减少用户输入错误
- **表单验证**: 实时验证，及时提示错误信息
- **保存草稿**: 支持保存草稿功能，避免数据丢失

### 2. 数据联动

- **商品-颜色-尺寸**: 三级联动选择
- **SKU自动生成**: 根据商品、颜色、尺寸自动生成SKU
- **库存检查**: 选择SKU时显示当前库存数量
- **价格联动**: 根据价格类型自动填充单价

### 3. 状态管理

- **订单状态**: 不同状态下的操作权限不同
- **字段禁用**: 某些状态下特定字段应禁用编辑
- **流程控制**: 按业务流程控制表单的可编辑性

### 4. 性能优化

- **分页加载**: 明细表格支持分页或虚拟滚动
- **懒加载**: 下拉选项数据懒加载
- **缓存策略**: 商品、客户等基础数据适当缓存

### 5. 移动端适配

- **响应式设计**: 支持移动端操作
- **触摸优化**: 适配触摸操作
- **简化界面**: 移动端可适当简化界面

---

## 🔧 API接口说明

### 销售开单接口

| 接口     | 方法   | 路径                          | 说明             |
| -------- | ------ | ----------------------------- | ---------------- |
| 创建订单 | POST   | `/sales-orders`               | 创建新的销售订单 |
| 查询订单 | GET    | `/sales-orders`               | 分页查询销售订单 |
| 订单详情 | GET    | `/sales-orders/{id}`          | 获取订单详细信息 |
| 更新订单 | PUT    | `/sales-orders/{id}`          | 更新订单信息     |
| 删除订单 | DELETE | `/sales-orders/{id}`          | 删除订单         |
| 确认订单 | POST   | `/sales-orders/{id}/confirm`  | 确认订单         |
| 发货     | POST   | `/sales-orders/{id}/ship`     | 订单发货         |
| 完成订单 | POST   | `/sales-orders/{id}/complete` | 完成订单         |

### 销售退单接口

| 接口     | 方法   | 路径                           | 说明             |
| -------- | ------ | ------------------------------ | ---------------- |
| 创建退单 | POST   | `/sales-returns`               | 创建新的销售退单 |
| 查询退单 | GET    | `/sales-returns`               | 分页查询销售退单 |
| 退单详情 | GET    | `/sales-returns/{id}`          | 获取退单详细信息 |
| 更新退单 | PUT    | `/sales-returns/{id}`          | 更新退单信息     |
| 删除退单 | DELETE | `/sales-returns/{id}`          | 删除退单         |
| 提交退单 | POST   | `/sales-returns/{id}/submit`   | 提交退单审核     |
| 审核退单 | POST   | `/sales-returns/{id}/approve`  | 审核退单         |
| 处理退单 | POST   | `/sales-returns/{id}/process`  | 处理退单         |
| 完成退单 | POST   | `/sales-returns/{id}/complete` | 完成退单         |

---

## 📝 表单设计建议

### 销售开单表单布局

```
┌─────────────────────────────────────────────────────────────┐
│ 基础信息                                                      │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│ │ 销售人员     │ │ 客户        │ │ 订单日期     │              │
│ └─────────────┘ └─────────────┘ └─────────────┘              │
│ ┌─────────────┐ ┌─────────────┐                              │
│ │ 期望交货日期  │ │ 优先级      │                              │
│ └─────────────┘ └─────────────┘                              │
├─────────────────────────────────────────────────────────────┤
│ 业务选项                                                      │
│ ☐ 代发    ☐ 放单                                             │
├─────────────────────────────────────────────────────────────┤
│ 物流信息                                                      │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│ │ 物流方式     │ │ 运费金额     │ │ 物流公司     │              │
│ └─────────────┘ └─────────────┘ └─────────────┘              │
├─────────────────────────────────────────────────────────────┤
│ 代发信息（当选择代发时显示）                                    │
│ ┌─────────────┐ ┌─────────────┐                              │
│ │ 收货人姓名   │ │ 收货人电话   │                              │
│ └─────────────┘ └─────────────┘                              │
│ ┌─────────────────────────────────────────────────────────┐  │
│ │ 收货地址                                                 │  │
│ └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│ 商品明细                                                      │
│ ┌─────────────────────────────────────────────────────────┐  │
│ │ 商品  │ 颜色  │ 尺寸  │ 数量  │ 价格类型 │ 单价 │ 折扣 │ 操作 │  │
│ ├─────────────────────────────────────────────────────────┤  │
│ │ ...   │ ...   │ ...   │ ...   │ ...     │ ... │ ... │ ... │  │
│ └─────────────────────────────────────────────────────────┘  │
│ [+ 添加商品]                                                  │
├─────────────────────────────────────────────────────────────┤
│ 备注                                                          │
│ ┌─────────────────────────────────────────────────────────┐  │
│ │                                                         │  │
│ └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                                    [保存草稿] [确认提交]      │
└─────────────────────────────────────────────────────────────┘
```

### 销售退单表单布局

```
┌─────────────────────────────────────────────────────────────┐
│ 基础信息                                                      │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│ │ 客户        │ │ 申请人      │ │ 退单日期     │              │
│ └─────────────┘ └─────────────┘ └─────────────┘              │
│ ┌─────────────┐ ┌─────────────┐                              │
│ │ 退单类型     │ │ 退单原因     │                              │
│ └─────────────┘ └─────────────┘                              │
│ ┌─────────────────────────────────────────────────────────┐  │
│ │ 原因详细说明                                             │  │
│ └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│ 关联订单（可选）                                              │
│ ┌─────────────────────────────────────────────────────────┐  │
│ │ 原销售订单 [选择订单]                                    │  │
│ └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│ 物流信息                                                      │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│ │ 物流公司     │ │ 物流单号*    │ │ 手续费      │              │
│ └─────────────┘ └─────────────┘ └─────────────┘              │
├─────────────────────────────────────────────────────────────┤
│ 退货明细                                                      │
│ ┌─────────────────────────────────────────────────────────┐  │
│ │ 商品  │ 颜色  │ 尺寸  │ 数量  │ 单价  │ 金额  │ 备注  │ 操作 │  │
│ ├─────────────────────────────────────────────────────────┤  │
│ │ ...   │ ...   │ ...   │ ...   │ ...  │ ...  │ ...  │ ... │  │
│ └─────────────────────────────────────────────────────────┘  │
│ [+ 添加商品]                                                  │
├─────────────────────────────────────────────────────────────┤
│ 换货明细（当退单类型包含换货时显示）                            │
│ ┌─────────────────────────────────────────────────────────┐  │
│ │ 换货商品 │ 颜色 │ 尺寸 │ 数量 │ 单价 │ 金额 │ 备注 │ 操作 │  │
│ ├─────────────────────────────────────────────────────────┤  │
│ │ ...     │ ... │ ... │ ... │ ... │ ... │ ... │ ... │  │
│ └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│ 备注                                                          │
│ ┌─────────────────────────────────────────────────────────┐  │
│ │                                                         │  │
│ └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                                    [保存草稿] [提交审核]      │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎨 UI组件建议

### 商品选择器组件

```javascript
// 商品选择器应支持以下功能：
{
  searchable: true,        // 支持搜索
  filterable: true,        // 支持筛选
  showStock: true,         // 显示库存
  showPrice: true,         // 显示价格
  cascading: true,         // 商品-颜色-尺寸级联
  validation: true,        // 实时验证
  placeholder: "请选择商品"
}
```

### 金额计算组件

```javascript
// 自动计算金额的表格行组件
const calculateAmount = (quantity, unitPrice, discountAmount = 0) => {
  const totalAmount = quantity * unitPrice;
  const actualAmount = totalAmount - discountAmount;
  return { totalAmount, actualAmount };
};
```

### 状态标签组件

```javascript
// 状态标签的颜色映射
const statusColors = {
  draft: 'gray',
  confirmed: 'blue',
  shipped: 'orange',
  delivered: 'green',
  completed: 'dark-green',
  cancelled: 'red',
};
```

---

## 📞 技术支持

如有疑问，请联系后端开发团队或查看API文档：

- **Swagger文档**: http://127.0.0.1:8080/api
- **接口前缀**: `/sales-orders` 和 `/sales-returns`
- **统一响应格式**: `{ code: number, message: string, data: any }`
- **分页格式**: `{ items: [], total: number, page: number, limit: number }`

---

_文档版本: v1.0 | 更新时间: 2025-06-23_
