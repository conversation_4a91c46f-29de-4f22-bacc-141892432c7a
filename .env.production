# ========================================
# 生产环境配置文件
# ========================================
# 此文件用于腾讯云服务器生产环境
# 服务器IP: *************
# 复制此文件为 .env 并修改相应的值

# 环境配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 数据库配置 (生产环境 - 直接连接本地PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=54188
DB_DATABASE=manager

# JWT配置 - 生产环境请务必修改为强密码
JWT_SECRET=your_super_secret_jwt_key_change_this_sosososos_sostrong_artificial_intelligence_in_production_min_32_chars_very_secure
JWT_EXPIRATION=7d

# Redis配置 (生产环境 - 直接连接本地Redis)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 腾讯云短信服务配置
TENCENT_SECRET_ID=your_tencent_secret_id
TENCENT_SECRET_KEY=your_tencent_secret_key
TENCENT_REGION=ap-guangzhou
TENCENT_SMS_SDK_APP_ID=your_sms_sdk_app_id
TENCENT_SMS_SIGN_NAME=your_sms_sign_name
TENCENT_SMS_TEMPLATE_ID=your_sms_template_id

# 腾讯云对象存储配置
TENCENT_COS_SECRET_ID=AKIDnRuAC6qPmw6C8ta9XiEYeaL3YptjLlyx
TENCENT_COS_SECRET_KEY=oOkwvhGbidOxLYYM0UmqgIgq37yeAb0M
TENCENT_COS_REGION=ap-guangzhou
TENCENT_COS_BUCKET=vasa-1302219791
TENCENT_COS_BASE_URL=https://vasa-1302219791.cos.ap-guangzhou.myqcloud.com

# 短信验证码配置
SMS_CODE_EXPIRES_IN=300
SMS_CODE_LENGTH=6
ADMIN_PHONE_NUMBER=16604583277

# 生产环境特殊配置
# 生产模式下关闭数据库同步，使用迁移管理数据库结构
DB_SYNCHRONIZE=false
# 生产模式下关闭数据库日志
DB_LOGGING=false
# 生产模式下使用较少的日志级别
LOG_LEVEL=info

# 安全配置
# 生产环境下可以设置更严格的CORS
CORS_ORIGIN=http://*************,https://*************
# 生产环境下可以设置信任的代理
TRUST_PROXY=true

# 性能配置
# 生产环境下可以设置更大的请求体限制
MAX_REQUEST_SIZE=50mb
# 生产环境下可以设置请求超时
REQUEST_TIMEOUT=30000
