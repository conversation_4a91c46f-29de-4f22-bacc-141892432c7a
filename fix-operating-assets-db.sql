-- ========================================
-- 修复运营资产模块数据库问题
-- ========================================
-- 执行前请确保数据库连接正常
-- 数据库: manager
-- 用户: postgres
-- 密码: 54188

\echo '🚨 开始修复运营资产模块数据库问题...'

-- 开始事务
BEGIN;

-- 1. 检查当前表结构
\echo '📋 检查当前表结构...'
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public'
    AND table_name IN ('operating_assets', 'operating_asset_details')
ORDER BY table_name, ordinal_position;

\echo ''

-- 2. 检查并创建枚举类型
\echo '🔧 检查并创建枚举类型...'
DO $$
BEGIN
    -- 创建运营资产类型枚举（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'operating_asset_type') THEN
        CREATE TYPE operating_asset_type AS ENUM (
            'human_resources',
            'warehouse_logistics', 
            'administrative_consumption',
            'other'
        );
        RAISE NOTICE '✅ 已创建 operating_asset_type 枚举类型';
    ELSE
        RAISE NOTICE '✅ operating_asset_type 枚举类型已存在';
    END IF;

    -- 创建人力资源审核状态枚举（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'human_resources_audit_status_enum') THEN
        CREATE TYPE human_resources_audit_status_enum AS ENUM ('pending', 'approved');
        RAISE NOTICE '✅ 已创建 human_resources_audit_status_enum 枚举类型';
    ELSE
        RAISE NOTICE '✅ human_resources_audit_status_enum 枚举类型已存在';
    END IF;

    -- 创建仓储物流收支类型枚举（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'warehouse_logistics_type_enum') THEN
        CREATE TYPE warehouse_logistics_type_enum AS ENUM('income', 'expense');
        RAISE NOTICE '✅ 已创建 warehouse_logistics_type_enum 枚举类型';
    ELSE
        RAISE NOTICE '✅ warehouse_logistics_type_enum 枚举类型已存在';
    END IF;
END $$;

-- 3. 修复 operating_assets 表
\echo '🔧 修复 operating_assets 表...'
DO $$
BEGIN
    -- 检查表是否存在，如果不存在则创建
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'operating_assets') THEN
        CREATE TABLE operating_assets (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            "totalAmount" DECIMAL(15,2) NOT NULL DEFAULT 0,
            "isDeleted" BOOLEAN NOT NULL DEFAULT false,
            "createdAt" VARCHAR(20) NULL
        );
        
        COMMENT ON TABLE operating_assets IS '运营资产表';
        COMMENT ON COLUMN operating_assets."totalAmount" IS '运营资产总金额';
        COMMENT ON COLUMN operating_assets."isDeleted" IS '是否已删除';
        COMMENT ON COLUMN operating_assets."createdAt" IS '创建日期（字符串格式，必填）';
        
        RAISE NOTICE '✅ 已创建 operating_assets 表';
    ELSE
        RAISE NOTICE '✅ operating_assets 表已存在';
        
        -- 检查并添加缺失的字段
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_assets' AND column_name = 'createdAt') THEN
            -- 检查是否有其他名称的日期字段需要重命名
            IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_assets' AND column_name = 'create_date') THEN
                ALTER TABLE operating_assets RENAME COLUMN create_date TO "createdAt";
                RAISE NOTICE '✅ operating_assets.create_date 已重命名为 createdAt';
            ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_assets' AND column_name = 'createDate') THEN
                ALTER TABLE operating_assets RENAME COLUMN "createDate" TO "createdAt";
                RAISE NOTICE '✅ operating_assets.createDate 已重命名为 createdAt';
            ELSE
                ALTER TABLE operating_assets ADD COLUMN "createdAt" VARCHAR(20) NULL;
                COMMENT ON COLUMN operating_assets."createdAt" IS '创建日期（字符串格式，必填）';
                RAISE NOTICE '✅ 已添加 operating_assets.createdAt 字段';
            END IF;
        ELSE
            RAISE NOTICE '✅ operating_assets.createdAt 字段已存在';
        END IF;
    END IF;
END $$;

-- 4. 修复 operating_asset_details 表
\echo '🔧 修复 operating_asset_details 表...'
DO $$
BEGIN
    -- 检查表是否存在，如果不存在则创建
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'operating_asset_details') THEN
        CREATE TABLE operating_asset_details (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            "operatingAssetId" UUID NOT NULL,
            type operating_asset_type NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            screenshot VARCHAR(500) NOT NULL,
            remark TEXT NULL,
            "humanResourcesAuditStatus" human_resources_audit_status_enum NULL,
            "employeeInfo" VARCHAR(255) NULL,
            warehouselogisticstype warehouse_logistics_type_enum NULL,
            "isDeleted" BOOLEAN NOT NULL DEFAULT false,
            "createdAt" VARCHAR(20) NULL,
            
            CONSTRAINT fk_operating_asset_details_operating_asset 
                FOREIGN KEY ("operatingAssetId") REFERENCES operating_assets(id)
        );
        
        -- 添加注释
        COMMENT ON TABLE operating_asset_details IS '运营资产明细表';
        COMMENT ON COLUMN operating_asset_details."operatingAssetId" IS '运营资产ID';
        COMMENT ON COLUMN operating_asset_details.type IS '运营资产类型';
        COMMENT ON COLUMN operating_asset_details.amount IS '明细金额（保留两位小数）';
        COMMENT ON COLUMN operating_asset_details.screenshot IS '截图URL（必填）';
        COMMENT ON COLUMN operating_asset_details.remark IS '备注（可选）';
        COMMENT ON COLUMN operating_asset_details."humanResourcesAuditStatus" IS '人力资产审核状态（仅人力资产类型需要）';
        COMMENT ON COLUMN operating_asset_details."employeeInfo" IS '员工信息（仅人力资产类型需要）';
        COMMENT ON COLUMN operating_asset_details.warehouselogisticstype IS '仓储物流收支类型（仅仓储物流类型需要）';
        COMMENT ON COLUMN operating_asset_details."isDeleted" IS '是否已删除';
        COMMENT ON COLUMN operating_asset_details."createdAt" IS '创建日期（字符串格式，必填）';
        
        RAISE NOTICE '✅ 已创建 operating_asset_details 表';
    ELSE
        RAISE NOTICE '✅ operating_asset_details 表已存在';
        
        -- 修复字段名问题
        -- 1. 修复 createDate 字段
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'create_date') THEN
            ALTER TABLE operating_asset_details RENAME COLUMN create_date TO "createdAt";
            RAISE NOTICE '✅ operating_asset_details.create_date 已重命名为 createdAt';
        ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'createDate') THEN
            ALTER TABLE operating_asset_details RENAME COLUMN "createDate" TO "createdAt";
            RAISE NOTICE '✅ operating_asset_details.createDate 已重命名为 createdAt';
        ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'createdAt') THEN
            ALTER TABLE operating_asset_details ADD COLUMN "createdAt" VARCHAR(20) NULL;
            COMMENT ON COLUMN operating_asset_details."createdAt" IS '创建日期（字符串格式，必填）';
            RAISE NOTICE '✅ 已添加 operating_asset_details.createdAt 字段';
        ELSE
            RAISE NOTICE '✅ operating_asset_details.createdAt 字段已存在';
        END IF;
        
        -- 2. 修复 warehouseLogisticsType 字段
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'warehouseLogisticsType') THEN
            ALTER TABLE operating_asset_details RENAME COLUMN "warehouseLogisticsType" TO warehouselogisticstype;
            RAISE NOTICE '✅ operating_asset_details.warehouseLogisticsType 已重命名为 warehouselogisticstype';
        ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'warehouselogisticstype') THEN
            ALTER TABLE operating_asset_details ADD COLUMN warehouselogisticstype warehouse_logistics_type_enum NULL;
            COMMENT ON COLUMN operating_asset_details.warehouselogisticstype IS '仓储物流收支类型（仅仓储物流类型需要）';
            RAISE NOTICE '✅ 已添加 operating_asset_details.warehouselogisticstype 字段';
        ELSE
            RAISE NOTICE '✅ operating_asset_details.warehouselogisticstype 字段已存在';
        END IF;
    END IF;
END $$;

-- 5. 验证修复结果
\echo ''
\echo '📋 验证修复结果...'
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public'
    AND table_name IN ('operating_assets', 'operating_asset_details')
    AND column_name IN ('createdAt', 'warehouselogisticstype', 'operatingAssetId', 'totalAmount')
ORDER BY table_name, column_name;

\echo ''
\echo '📋 检查枚举类型...'
SELECT 
    t.typname as enum_name,
    e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid 
WHERE t.typname IN ('operating_asset_type', 'warehouse_logistics_type_enum', 'human_resources_audit_status_enum')
ORDER BY t.typname, e.enumsortorder;

-- 提交事务
COMMIT;

\echo ''
\echo '✅ 运营资产模块数据库修复完成！'
\echo ''
\echo '🔧 修复内容总结：'
\echo '   1. 创建/检查所有必要的枚举类型'
\echo '   2. 创建/修复 operating_assets 表结构'
\echo '   3. 创建/修复 operating_asset_details 表结构'
\echo '   4. 统一字段名为实体映射要求的格式'
\echo '   5. 确保所有字段类型和约束正确'
\echo ''
\echo '⚠️  请重启应用程序以使更改生效'
