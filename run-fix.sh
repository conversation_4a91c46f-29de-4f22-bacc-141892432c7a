#!/bin/bash

# ========================================
# 运营资产数据库修复执行脚本
# ========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 数据库连接参数
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="postgres"
DB_NAME="manager"
DB_PASSWORD="54188"

# 检查 psql 是否安装
check_psql() {
    if ! command -v psql &> /dev/null; then
        log_error "psql 命令未找到，请安装 PostgreSQL 客户端"
        exit 1
    fi
    log_success "psql 命令可用"
}

# 测试数据库连接
test_connection() {
    log_info "测试数据库连接..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
        log_success "数据库连接成功"
    else
        log_error "数据库连接失败，请检查："
        echo "  - 主机: $DB_HOST"
        echo "  - 端口: $DB_PORT"
        echo "  - 用户: $DB_USER"
        echo "  - 数据库: $DB_NAME"
        echo "  - 密码: $DB_PASSWORD"
        exit 1
    fi
}

# 备份数据库
backup_database() {
    log_info "创建数据库备份..."
    
    local backup_file="backup_operating_assets_$(date +%Y%m%d_%H%M%S).sql"
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --table=operating_assets --table=operating_asset_details \
        --data-only --inserts > "$backup_file" 2>/dev/null; then
        log_success "数据备份已保存到: $backup_file"
    else
        log_warning "数据备份失败，但继续执行修复（可能是表不存在）"
    fi
}

# 执行修复脚本
run_fix() {
    log_info "执行数据库修复脚本..."
    
    if [ ! -f "fix-operating-assets-db.sql" ]; then
        log_error "修复脚本文件 fix-operating-assets-db.sql 不存在"
        exit 1
    fi
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "fix-operating-assets-db.sql"; then
        log_success "数据库修复完成"
    else
        log_error "数据库修复失败"
        exit 1
    fi
}

# 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 检查表是否存在
    local tables_exist=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_name IN ('operating_assets', 'operating_asset_details');" 2>/dev/null | tr -d ' ')
    
    if [ "$tables_exist" = "2" ]; then
        log_success "运营资产相关表已存在"
    else
        log_error "运营资产相关表缺失"
        exit 1
    fi
    
    # 检查关键字段是否存在
    local key_columns=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name IN ('createdAt', 'warehouselogisticstype');" 2>/dev/null | tr -d ' ')
    
    if [ "$key_columns" = "2" ]; then
        log_success "关键字段已正确创建"
    else
        log_warning "部分关键字段可能缺失，请检查日志"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "运营资产数据库修复工具"
    echo "========================================"
    echo ""
    
    check_psql
    test_connection
    backup_database
    run_fix
    verify_fix
    
    echo ""
    log_success "修复流程完成！"
    echo ""
    echo "接下来的步骤："
    echo "1. 重启您的应用程序"
    echo "2. 测试运营资产模块的功能"
    echo "3. 如有问题，请检查应用程序日志"
    echo ""
}

# 执行主函数
main "$@"
