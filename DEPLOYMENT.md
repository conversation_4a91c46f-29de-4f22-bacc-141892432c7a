# 生产环境部署指南

## 服务器信息
- **服务器IP**: *************
- **操作系统**: Linux
- **数据库**: PostgreSQL
- **应用端口**: 3000
- **数据库名称**: manager (从 vasa 更改)

## 部署前准备

### 1. 服务器环境检查
确保服务器已安装以下软件：
- Node.js (v18+)
- PostgreSQL
- Nginx
- Git

### 2. 上传代码到服务器
```bash
# 在服务器上克隆或更新代码
git clone <your-repo-url> /var/www/web-manager-backend
cd /var/www/web-manager-backend

# 或者如果已存在，则更新
git pull origin main
```

## 部署步骤

### 方式一：使用自动部署脚本（推荐）

#### 完整部署（包括数据库创建）
```bash
chmod +x deploy-production.sh
./deploy-production.sh full
```

#### 更新部署（仅更新代码）
```bash
./deploy-production.sh update
```

### 方式二：手动部署

#### 1. 初始化数据库
```bash
chmod +x scripts/init-database.sh
./scripts/init-database.sh
```

#### 2. 安装依赖
```bash
pnpm install --frozen-lockfile
```

#### 3. 构建项目
```bash
cp .env.production .env
pnpm run build
```

#### 4. 启动服务
```bash
# 安装PM2（如果未安装）
npm install -g pm2

# 启动服务
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

## 数据库迁移

### 从 vasa 数据库迁移到 manager 数据库

如果您需要从旧的 `vasa` 数据库迁移数据到新的 `manager` 数据库：

```bash
# 1. 备份 vasa 数据库
sudo -u postgres pg_dump vasa > vasa_backup.sql

# 2. 创建 manager 数据库（如果未创建）
sudo -u postgres psql -c "CREATE DATABASE manager;"

# 3. 导入数据到 manager 数据库
sudo -u postgres psql manager < vasa_backup.sql

# 4. 验证数据迁移
PGPASSWORD=54188 psql -h localhost -U postgres -d manager -c "\dt"
```

## 服务管理

### PM2 常用命令
```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs web-manager-backend

# 重启服务
pm2 restart web-manager-backend

# 停止服务
pm2 stop web-manager-backend

# 删除服务
pm2 delete web-manager-backend

# 监控服务
pm2 monit
```

### 系统服务命令
```bash
# 查看PostgreSQL状态
sudo systemctl status postgresql

# 重启PostgreSQL
sudo systemctl restart postgresql

# 查看Nginx状态
sudo systemctl status nginx

# 重启Nginx
sudo systemctl restart nginx
```

## 配置文件说明

### 环境变量 (.env.production)
- `DB_DATABASE=manager` - 数据库名称已更改为 manager
- `PORT=3000` - 应用端口
- `NODE_ENV=production` - 生产环境
- `DB_SYNCHRONIZE=false` - 生产环境关闭自动同步

### Nginx 配置
确保 nginx.conf 中的代理配置正确：
```nginx
location /api/ {
    proxy_pass http://127.0.0.1:3000/;
    # ... 其他配置
}
```

## 验证部署

### 1. 检查服务状态
```bash
# 检查端口监听
netstat -tuln | grep :3000

# 检查进程
ps aux | grep node
```

### 2. 测试API
```bash
# 测试API文档
curl http://localhost:3000/api

# 测试健康检查
curl http://localhost:3000/

# 通过Nginx测试
curl http://*************/api/
```

### 3. 访问应用
- **API文档**: http://*************:3000/api
- **通过Nginx**: http://*************/api/

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查PostgreSQL服务
   sudo systemctl status postgresql
   
   # 检查数据库是否存在
   sudo -u postgres psql -l | grep manager
   ```

2. **端口被占用**
   ```bash
   # 查看端口占用
   sudo lsof -i :3000
   
   # 杀死占用进程
   sudo kill -9 <PID>
   ```

3. **权限问题**
   ```bash
   # 检查文件权限
   ls -la /var/www/web-manager-backend
   
   # 修改权限
   sudo chown -R $USER:$USER /var/www/web-manager-backend
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 检查PM2内存限制
   pm2 show web-manager-backend
   ```

### 日志查看
```bash
# PM2 日志
pm2 logs web-manager-backend

# 系统日志
sudo journalctl -u postgresql
sudo journalctl -u nginx

# 应用日志
tail -f logs/combined.log
```

## 安全建议

1. **更改默认密码**
   - 修改 PostgreSQL 密码
   - 更新 JWT_SECRET

2. **防火墙配置**
   ```bash
   # 只允许必要端口
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS
   sudo ufw enable
   ```

3. **定期备份**
   ```bash
   # 创建备份脚本
   sudo -u postgres pg_dump manager > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

## 更新部署

当有代码更新时：
```bash
# 拉取最新代码
git pull origin main

# 快速更新部署
./deploy-production.sh update
```
