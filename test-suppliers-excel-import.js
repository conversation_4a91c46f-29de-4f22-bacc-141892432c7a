// 测试供应商Excel导入功能（使用新的供应商编码）
// 使用内置的 fetch (Node.js 18+)
const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://127.0.0.1:8080';

async function testSuppliersExcelImport() {
  console.log('🚀 开始测试供应商Excel导入功能（新数据）...');

  try {
    // 1. 登录获取token
    console.log('\n📝 正在登录...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userCode: 'husky',
        password: '541888',
      }),
    });

    const loginData = await loginResponse.json();
    if (loginData.code !== 200) {
      throw new Error(`登录失败: ${loginData.message}`);
    }

    const token = loginData.data.accessToken;
    console.log('✅ 登录成功');

    const headers = {
      'Authorization': `Bearer ${token}`,
    };

    // 2. 创建测试Excel文件
    console.log('\n📝 创建测试Excel文件...');
    const timestamp = Date.now();
    const testData = [
      {
        '供应商编码': `TEST${timestamp}1`,
        '供应商名称': '测试供应商1',
        '供应商地址': '测试地址1',
        '联系人姓名': '测试联系人1',
        '联系人手机号': '13800138001',
      },
      {
        '供应商编码': `TEST${timestamp}2`,
        '供应商名称': '测试供应商2',
        '供应商地址': '测试地址2',
        '联系人姓名': '测试联系人2',
        '联系人手机号': '13800138002',
      },
      {
        '供应商编码': `TEST${timestamp}3`,
        '供应商名称': '测试供应商3',
        '供应商地址': '',
        '联系人姓名': '',
        '联系人手机号': '',
      },
    ];

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(testData);
    XLSX.utils.book_append_sheet(workbook, worksheet, '供应商导入测试');

    // 保存Excel文件
    const testFilePath = path.join(__dirname, 'test_suppliers_import.xlsx');
    XLSX.writeFile(workbook, testFilePath);
    console.log(`✅ 测试Excel文件创建成功: ${testFilePath}`);

    // 3. 测试Excel导入功能
    console.log('\n📝 测试Excel导入功能...');
    
    // 创建FormData
    const formData = new FormData();
    const fileBuffer = fs.readFileSync(testFilePath);
    const blob = new Blob([fileBuffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    formData.append('file', blob, 'test_suppliers_import.xlsx');

    const importResponse = await fetch(`${BASE_URL}/suppliers/import/excel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    const importData = await importResponse.json();
    console.log('导入结果:', JSON.stringify(importData, null, 2));

    if (importData.code === 200) {
      console.log('✅ Excel导入测试成功');
      console.log(`📊 导入统计: 成功 ${importData.data.successCount} 条，失败 ${importData.data.failureCount} 条`);
      
      if (importData.data.errors.length > 0) {
        console.log('⚠️ 导入错误详情:');
        importData.data.errors.forEach(error => console.log(`   - ${error}`));
      }

      if (importData.data.successCodes.length > 0) {
        console.log('✅ 成功导入的供应商编码:');
        importData.data.successCodes.forEach(code => console.log(`   - ${code}`));
      }
    } else {
      console.log('⚠️ Excel导入测试失败:', importData.message);
    }

    // 4. 验证导入的数据
    if (importData.data.successCodes.length > 0) {
      console.log('\n📝 验证导入的数据...');
      for (const code of importData.data.successCodes) {
        try {
          const detailResponse = await fetch(`${BASE_URL}/suppliers/${code}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          });

          const detailData = await detailResponse.json();
          if (detailData.code === 200) {
            console.log(`✅ 供应商 ${code} 数据验证成功:`, {
              name: detailData.data.name,
              address: detailData.data.address,
              contactName: detailData.data.contactName,
              contactPhone: detailData.data.contactPhone,
            });
          }
        } catch (error) {
          console.log(`⚠️ 验证供应商 ${code} 失败:`, error.message);
        }
      }
    }

    // 5. 清理测试文件
    console.log('\n📝 清理测试文件...');
    try {
      if (fs.existsSync(testFilePath)) {
        fs.unlinkSync(testFilePath);
        console.log('✅ 测试文件已删除');
      }
    } catch (error) {
      console.log('⚠️ 清理文件时出错:', error.message);
    }

    // 6. 清理测试数据
    if (importData.data.successCodes.length > 0) {
      console.log('\n📝 清理测试数据...');
      for (const code of importData.data.successCodes) {
        try {
          const deleteResponse = await fetch(`${BASE_URL}/suppliers/${code}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          });

          const deleteData = await deleteResponse.json();
          if (deleteData.code === 200) {
            console.log(`✅ 删除供应商 ${code} 成功`);
          }
        } catch (error) {
          console.log(`⚠️ 删除供应商 ${code} 失败:`, error.message);
        }
      }
    }

    console.log('\n🎉 Excel导入功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

testSuppliersExcelImport();
