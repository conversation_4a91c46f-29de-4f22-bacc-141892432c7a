const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting production server...');

// 检查 dist 目录是否存在
const distPath = path.join(__dirname, 'dist');
if (!fs.existsSync(distPath)) {
  console.error('❌ dist directory not found. Please run "npm run build" first.');
  process.exit(1);
}

// 检查 .env 文件是否存在
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('📝 .env file not found, copying from .env.production...');
  const envProductionPath = path.join(__dirname, '.env.production');
  if (fs.existsSync(envProductionPath)) {
    fs.copyFileSync(envProductionPath, envPath);
    console.log('✅ .env file created from .env.production');
  } else {
    console.error('❌ .env.production file not found.');
    process.exit(1);
  }
}

// 启动应用
const mainPath = path.join(distPath, 'main.js');
console.log(`📂 Starting application from: ${mainPath}`);

const child = spawn('node', [mainPath], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'production'
  }
});

child.on('error', (error) => {
  console.error('❌ Failed to start application:', error);
  process.exit(1);
});

child.on('exit', (code) => {
  console.log(`🔄 Application exited with code ${code}`);
  process.exit(code);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  child.kill('SIGTERM');
});
