const https = require('http');

// 测试登录接口
function testLogin() {
  const data = JSON.stringify({
    userCode: 'husky',
    password: '541888'
  });

  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  const req = https.request(options, (res) => {
    console.log(`登录接口状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('登录响应:', JSON.stringify(response, null, 2));
        
        if (response.data && response.data.accessToken) {
          console.log('✅ 登录成功！');
          testUserListComparison(response.data.accessToken);
        } else {
          console.log('❌ 登录失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.write(data);
  req.end();
}

// 测试用户列表对比
function testUserListComparison(token) {
  console.log('\n🔍 开始测试用户列表功能...');
  
  // 先测试不包含未激活用户
  testUserListWithoutInactive(token, () => {
    // 再测试包含未激活用户
    testUserListWithInactive(token);
  });
}

// 测试不包含未激活用户的列表
function testUserListWithoutInactive(token, callback) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/users?page=1&pageSize=10',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  const req = https.request(options, (res) => {
    console.log(`\n📋 用户列表（默认，不包含未激活）状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('用户列表响应:', JSON.stringify(response, null, 2));
        
        if (response.code === 200) {
          console.log(`✅ 获取用户列表成功！共 ${response.data.total} 个激活用户`);
          if (callback) callback();
        } else {
          console.log('❌ 用户列表获取失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

// 测试包含未激活用户的列表
function testUserListWithInactive(token) {
  const options = {
    hostname: '127.0.0.1',
    port: 8080,
    path: '/users?page=1&pageSize=10&includeInactive=true',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  const req = https.request(options, (res) => {
    console.log(`\n📋 用户列表（包含未激活）状态码: ${res.statusCode}`);
    
    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(responseData);
        console.log('用户列表响应:', JSON.stringify(response, null, 2));
        
        if (response.code === 200) {
          console.log(`✅ 获取用户列表成功！共 ${response.data.total} 个用户（包含未激活）`);
          
          // 分析用户状态
          const activeUsers = response.data.users.filter(user => user.isActive);
          const inactiveUsers = response.data.users.filter(user => !user.isActive);
          
          console.log(`\n📊 用户状态统计:`);
          console.log(`   激活用户: ${activeUsers.length} 个`);
          console.log(`   未激活用户: ${inactiveUsers.length} 个`);
          
          if (inactiveUsers.length > 0) {
            console.log(`\n🔍 未激活用户列表:`);
            inactiveUsers.forEach(user => {
              console.log(`   - ${user.code} (${user.nickname}) - 激活状态: ${user.isActive}`);
            });
          }
          
          console.log('\n🎉 includeInactive 参数功能测试完成！');
        } else {
          console.log('❌ 用户列表获取失败');
        }
      } catch (error) {
        console.log('解析响应失败:', error.message);
        console.log('原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.error('请求错误:', error.message);
  });

  req.end();
}

console.log('🚀 开始测试 includeInactive 参数功能...');
testLogin();
