-- ========================================
-- 紧急数据库修复脚本
-- ========================================
-- 修复字段名不匹配问题：create_date -> createDate

\echo '🚨 开始紧急修复数据库字段名问题...'

-- 1. 修复 expenses 表
\echo '修复 expenses 表字段名...'
DO $$
BEGIN
    -- 检查 create_date 字段是否存在
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expenses' AND column_name = 'create_date') THEN
        -- 重命名字段
        ALTER TABLE expenses RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'expenses.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'expenses.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 2. 修复 expense_details 表
\echo '修复 expense_details 表字段名...'
DO $$
BEGIN
    -- 检查 create_date 字段是否存在
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expense_details' AND column_name = 'create_date') THEN
        -- 重命名字段
        ALTER TABLE expense_details RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'expense_details.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'expense_details.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 3. 修复 incomes 表
\echo '修复 incomes 表字段名...'
DO $$
BEGIN
    -- 检查 create_date 字段是否存在
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'incomes' AND column_name = 'create_date') THEN
        -- 重命名字段
        ALTER TABLE incomes RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'incomes.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'incomes.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 4. 修复 income_details 表
\echo '修复 income_details 表字段名...'
DO $$
BEGIN
    -- 检查 create_date 字段是否存在
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'income_details' AND column_name = 'create_date') THEN
        -- 重命名字段
        ALTER TABLE income_details RENAME COLUMN create_date TO "createDate";
        RAISE NOTICE 'income_details.create_date 已重命名为 createDate';
    ELSE
        RAISE NOTICE 'income_details.create_date 字段不存在，跳过';
    END IF;
END $$;

-- 5. 验证修复结果
\echo '验证修复结果...'
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public'
    AND table_name IN ('expenses', 'expense_details', 'incomes', 'income_details')
    AND column_name IN ('createDate', 'create_date')
ORDER BY table_name, column_name;

\echo '✅ 紧急修复完成！'
