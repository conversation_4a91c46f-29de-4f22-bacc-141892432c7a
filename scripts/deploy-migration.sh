#!/bin/bash

# 部署数据库迁移脚本
# 使用方法: ./scripts/deploy-migration.sh [环境]
# 环境: development | production

set -e  # 遇到错误立即退出

ENVIRONMENT=${1:-development}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="./backups"

echo "🚀 开始部署数据库迁移 - 环境: $ENVIRONMENT"
echo "📅 时间: $(date)"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 根据环境设置数据库连接参数
if [ "$ENVIRONMENT" = "production" ]; then
    DB_HOST="*************"
    DB_USER="postgres"
    DB_NAME="manager"
    DB_PASSWORD="54188"
    echo "🔴 生产环境部署 - 请确认!"
    read -p "确认要在生产环境执行迁移吗? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        echo "❌ 部署已取消"
        exit 1
    fi
else
    DB_HOST="localhost"
    DB_USER="postgres"
    DB_NAME="manager"
    DB_PASSWORD="54188"
    echo "🟡 开发环境部署"
fi

echo "📊 数据库信息:"
echo "  主机: $DB_HOST"
echo "  数据库: $DB_NAME"
echo "  用户: $DB_USER"

# 1. 备份数据库
echo ""
echo "📦 步骤 1: 备份数据库..."
BACKUP_FILE="$BACKUP_DIR/backup_${ENVIRONMENT}_${TIMESTAMP}.sql"

export PGPASSWORD=$DB_PASSWORD
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > $BACKUP_FILE

if [ $? -eq 0 ]; then
    echo "✅ 数据库备份成功: $BACKUP_FILE"
else
    echo "❌ 数据库备份失败"
    exit 1
fi

# 2. 检查当前数据库状态
echo ""
echo "🔍 步骤 2: 检查数据库状态..."

# 检查是否已经存在新字段
EXISTING_FIELDS=$(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "
    SELECT column_name 
    FROM information_schema.columns 
    WHERE table_name = 'inventory_details' 
    AND column_name IN ('skucode', 'sizecode', 'totalstock', 'actualstock')
" 2>/dev/null | wc -l)

if [ $EXISTING_FIELDS -gt 0 ]; then
    echo "⚠️  检测到部分迁移字段已存在，将跳过已存在的字段"
else
    echo "✅ 数据库状态正常，可以执行迁移"
fi

# 3. 执行TypeORM迁移
echo ""
echo "🔄 步骤 3: 执行TypeORM迁移..."

# 设置环境变量
export NODE_ENV=$ENVIRONMENT

if [ "$ENVIRONMENT" = "production" ]; then
    export DB_HOST="*************"
    export DB_PASSWORD="54188"
fi

# 运行迁移
npm run migration:run

if [ $? -eq 0 ]; then
    echo "✅ TypeORM迁移执行成功"
else
    echo "❌ TypeORM迁移执行失败"
    echo "🔄 正在恢复数据库..."
    
    # 恢复数据库
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME < $BACKUP_FILE
    
    if [ $? -eq 0 ]; then
        echo "✅ 数据库已恢复到迁移前状态"
    else
        echo "❌ 数据库恢复失败，请手动检查!"
    fi
    
    exit 1
fi

# 4. 验证迁移结果
echo ""
echo "✅ 步骤 4: 验证迁移结果..."

# 检查新字段是否存在
NEW_FIELDS_COUNT=$(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_name = 'inventory_details' 
    AND column_name IN ('skucode', 'sizecode', 'totalstock', 'actualstock', 'reservedstock')
" 2>/dev/null)

if [ $NEW_FIELDS_COUNT -ge 5 ]; then
    echo "✅ 新字段创建成功"
else
    echo "❌ 新字段创建不完整"
    exit 1
fi

# 检查数据迁移
MIGRATED_RECORDS=$(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "
    SELECT COUNT(*) 
    FROM inventory_details 
    WHERE skucode IS NOT NULL AND sizecode IS NOT NULL
" 2>/dev/null)

echo "📊 已迁移记录数: $MIGRATED_RECORDS"

# 5. 启动应用验证
echo ""
echo "🚀 步骤 5: 启动应用验证..."

if [ "$ENVIRONMENT" = "development" ]; then
    echo "🔧 开发环境 - 请手动启动应用进行验证"
    echo "   命令: npm run start:dev"
else
    echo "🔧 生产环境 - 请重启应用服务"
    echo "   命令: pm2 restart all"
fi

# 6. 清理旧备份（保留最近5个）
echo ""
echo "🧹 步骤 6: 清理旧备份..."
cd $BACKUP_DIR
ls -t backup_${ENVIRONMENT}_*.sql | tail -n +6 | xargs -r rm
echo "✅ 备份清理完成"

echo ""
echo "🎉 数据库迁移部署完成!"
echo "📋 迁移摘要:"
echo "  - 备份文件: $BACKUP_FILE"
echo "  - 迁移记录: $MIGRATED_RECORDS 条"
echo "  - 完成时间: $(date)"
echo ""
echo "📝 后续步骤:"
echo "  1. 验证应用功能正常"
echo "  2. 检查新的SKU生成功能"
echo "  3. 测试库存管理功能"
echo "  4. 如有问题，可使用备份文件恢复: psql -h $DB_HOST -U $DB_USER -d $DB_NAME < $BACKUP_FILE"
