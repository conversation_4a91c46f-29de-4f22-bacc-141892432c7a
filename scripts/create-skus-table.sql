-- 创建 SKUs 表的 SQL 脚本
-- 如果 TypeORM 迁移不工作，可以直接执行此脚本

-- 检查表是否已存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'skus') THEN
        -- 创建 SKUs 表
        CREATE TABLE skus (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            code VARCHAR(100) UNIQUE NOT NULL,
            name VARCHAR(200) NOT NULL,
            "manufacturerCode" VARCHAR(100),
            "brandCode" VARCHAR(50) NOT NULL,
            "supplierCode" VARCHAR(50) NOT NULL,
            "categoryCode" VARCHAR(50) NOT NULL,
            "colorCode" VARCHAR(50) NOT NULL,
            "craftDescription" VARCHAR(500),
            "clothingCost" DECIMAL(10,2),
            "retailPrice" DECIMAL(10,2),
            "preOrderPrice" DECIMAL(10,2),
            "restockPrice" DECIMAL(10,2),
            "spotPrice" DECIMAL(10,2),
            accessories JSONB,
            images JSONB,
            "isDeleted" BOOLEAN DEFAULT FALSE,
            "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 添加注释
        COMMENT ON TABLE skus IS 'SKU（库存单位）表';
        COMMENT ON COLUMN skus.id IS 'SKU ID（UUID）';
        COMMENT ON COLUMN skus.code IS 'SKU编码（自动生成，唯一）';
        COMMENT ON COLUMN skus.name IS '商品名称';
        COMMENT ON COLUMN skus."manufacturerCode" IS '厂商编码';
        COMMENT ON COLUMN skus."brandCode" IS '品牌编码';
        COMMENT ON COLUMN skus."supplierCode" IS '供应商编码';
        COMMENT ON COLUMN skus."categoryCode" IS '商品分类编码';
        COMMENT ON COLUMN skus."colorCode" IS '颜色编码';
        COMMENT ON COLUMN skus."craftDescription" IS '工艺描述';
        COMMENT ON COLUMN skus."clothingCost" IS '服装成本';
        COMMENT ON COLUMN skus."retailPrice" IS '零售价';
        COMMENT ON COLUMN skus."preOrderPrice" IS '预订价';
        COMMENT ON COLUMN skus."restockPrice" IS '补货价';
        COMMENT ON COLUMN skus."spotPrice" IS '现货价';
        COMMENT ON COLUMN skus.accessories IS '辅料数组（JSON格式存储）';
        COMMENT ON COLUMN skus.images IS 'SKU图片数组（JSON格式存储）';
        COMMENT ON COLUMN skus."isDeleted" IS '是否已删除';
        COMMENT ON COLUMN skus."createdAt" IS '创建时间';
        COMMENT ON COLUMN skus."updatedAt" IS '更新时间';

        -- 创建索引
        CREATE INDEX "IDX_skus_code" ON skus(code);
        CREATE INDEX "IDX_skus_brand_code" ON skus("brandCode");
        CREATE INDEX "IDX_skus_supplier_code" ON skus("supplierCode");
        CREATE INDEX "IDX_skus_category_code" ON skus("categoryCode");
        CREATE INDEX "IDX_skus_color_code" ON skus("colorCode");
        CREATE INDEX "IDX_skus_is_deleted" ON skus("isDeleted");
        
        -- 为 JSONB 字段创建 GIN 索引
        CREATE INDEX "IDX_skus_accessories_gin" ON skus USING gin(accessories);
        CREATE INDEX "IDX_skus_images_gin" ON skus USING gin(images);

        -- 创建更新时间触发器
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW."updatedAt" = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';

        CREATE TRIGGER update_skus_updated_at 
            BEFORE UPDATE ON skus 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();

        RAISE NOTICE 'SKUs table created successfully';
    ELSE
        RAISE NOTICE 'SKUs table already exists';
    END IF;
END
$$;

-- 验证表创建
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'skus' 
ORDER BY ordinal_position;

-- 验证索引创建
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'skus';

-- 检查表是否可以正常插入数据（测试）
-- 注意：这需要相关的依赖表存在
/*
INSERT INTO skus (
    code, 
    name, 
    "brandCode", 
    "supplierCode", 
    "categoryCode", 
    "colorCode"
) VALUES (
    'TEST001',
    '测试商品',
    'BRAND001',
    'SUP001', 
    'CAT001',
    'COLOR001'
) ON CONFLICT (code) DO NOTHING;

SELECT * FROM skus WHERE code = 'TEST001';

-- 清理测试数据
DELETE FROM skus WHERE code = 'TEST001';
*/
