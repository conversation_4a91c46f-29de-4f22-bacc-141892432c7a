#!/bin/bash

# ========================================
# 数据库初始化脚本
# ========================================
# 用于在服务器上创建和初始化manager数据库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查PostgreSQL是否运行
check_postgresql() {
    log_info "检查PostgreSQL服务状态..."
    
    if ! systemctl is-active --quiet postgresql; then
        log_warning "PostgreSQL服务未运行，尝试启动..."
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    fi
    
    log_success "PostgreSQL服务正常运行"
}

# 创建数据库
create_database() {
    log_info "创建数据库 'manager'..."
    
    # 检查数据库是否已存在
    DB_EXISTS=$(sudo -u postgres psql -tAc "SELECT 1 FROM pg_database WHERE datname='manager'")
    
    if [ "$DB_EXISTS" = "1" ]; then
        log_warning "数据库 'manager' 已存在"
        
        read -p "是否要删除现有数据库并重新创建？(y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "删除现有数据库..."
            sudo -u postgres psql -c "DROP DATABASE IF EXISTS manager;"
            sudo -u postgres psql -c "CREATE DATABASE manager;"
            log_success "数据库重新创建完成"
        else
            log_info "保留现有数据库"
            return 0
        fi
    else
        sudo -u postgres psql -c "CREATE DATABASE manager;"
        log_success "数据库 'manager' 创建完成"
    fi
}

# 设置数据库权限
setup_permissions() {
    log_info "设置数据库权限..."
    
    # 确保postgres用户有足够的权限
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE manager TO postgres;"
    
    log_success "数据库权限设置完成"
}

# 测试数据库连接
test_connection() {
    log_info "测试数据库连接..."
    
    # 使用环境变量中的配置测试连接
    if PGPASSWORD=54188 psql -h localhost -U postgres -d manager -c "SELECT version();" >/dev/null 2>&1; then
        log_success "数据库连接测试成功"
    else
        log_error "数据库连接测试失败"
        exit 1
    fi
}

# 显示数据库信息
show_database_info() {
    log_success "数据库初始化完成！"
    echo
    echo "=========================================="
    echo "📊 数据库信息"
    echo "=========================================="
    echo "数据库名称: manager"
    echo "主机地址: localhost"
    echo "端口: 5432"
    echo "用户名: postgres"
    echo "密码: 54188"
    echo "=========================================="
    echo
    echo "连接命令:"
    echo "PGPASSWORD=54188 psql -h localhost -U postgres -d manager"
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始初始化数据库..."
    
    check_postgresql
    create_database
    setup_permissions
    test_connection
    show_database_info
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
