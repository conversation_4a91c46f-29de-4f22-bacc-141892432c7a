#!/bin/bash

# 产品和库存管理系统迁移回滚脚本
# 使用方法: ./scripts/rollback-product-migration.sh [环境] [备份文件]

set -e  # 遇到错误立即退出

ENVIRONMENT=${1:-development}
BACKUP_FILE=${2}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo "🔄 开始回滚产品和库存管理系统迁移 - 环境: $ENVIRONMENT"
echo "📅 时间: $(date)"

# 根据环境设置数据库连接参数
if [ "$ENVIRONMENT" = "production" ]; then
    DB_HOST="*************"
    DB_USER="postgres"
    DB_NAME="manager"
    DB_PASSWORD="54188"
    echo "🔴 生产环境回滚 - 请确认!"
    read -p "确认要在生产环境执行回滚吗? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        echo "❌ 回滚已取消"
        exit 1
    fi
else
    DB_HOST="localhost"
    DB_USER="postgres"
    DB_NAME="manager"
    DB_PASSWORD="54188"
    echo "🟡 开发环境回滚"
fi

export PGPASSWORD=$DB_PASSWORD

# 如果提供了备份文件，使用备份恢复
if [ -n "$BACKUP_FILE" ]; then
    if [ -f "$BACKUP_FILE" ]; then
        echo "📦 使用备份文件恢复: $BACKUP_FILE"
        
        # 创建当前状态备份
        CURRENT_BACKUP="./backups/before_rollback_${ENVIRONMENT}_${TIMESTAMP}.sql"
        mkdir -p ./backups
        pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > $CURRENT_BACKUP
        echo "✅ 当前状态已备份到: $CURRENT_BACKUP"
        
        # 恢复数据库
        psql -h $DB_HOST -U $DB_USER -d $DB_NAME < $BACKUP_FILE
        
        if [ $? -eq 0 ]; then
            echo "✅ 数据库已从备份恢复"
        else
            echo "❌ 数据库恢复失败"
            exit 1
        fi
    else
        echo "❌ 备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
else
    echo "🔄 执行手动回滚..."
    
    # 手动回滚步骤
    echo "📋 步骤 1: 删除新增的库存字段..."
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME << EOF
-- 删除库存表的新字段
ALTER TABLE inventory_details 
DROP COLUMN IF EXISTS skucode,
DROP COLUMN IF EXISTS sizecode,
DROP COLUMN IF EXISTS totalstock,
DROP COLUMN IF EXISTS actualstock,
DROP COLUMN IF EXISTS reservedstock,
DROP COLUMN IF EXISTS damagedstock,
DROP COLUMN IF EXISTS purchasingstock,
DROP COLUMN IF EXISTS needpurchasestock,
DROP COLUMN IF EXISTS safetystock,
DROP COLUMN IF EXISTS minstock,
DROP COLUMN IF EXISTS maxstock,
DROP COLUMN IF EXISTS avgcost,
DROP COLUMN IF EXISTS latestcost,
DROP COLUMN IF EXISTS isactive,
DROP COLUMN IF EXISTS warehouselocation,
DROP COLUMN IF EXISTS totalinbound,
DROP COLUMN IF EXISTS totaloutbound,
DROP COLUMN IF EXISTS lastinbounddate,
DROP COLUMN IF EXISTS lastoutbounddate;
EOF

    echo "📋 步骤 2: 删除产品表的categoryCode字段..."
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME << EOF
-- 删除外键约束
ALTER TABLE products DROP CONSTRAINT IF EXISTS "FK_products_categoryCode";

-- 删除categoryCode字段
ALTER TABLE products DROP COLUMN IF EXISTS "categoryCode";
EOF

    echo "📋 步骤 3: 删除相关索引..."
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME << EOF
-- 删除新增的索引
DROP INDEX IF EXISTS "IDX_inventory_details_skuCode";
DROP INDEX IF EXISTS "IDX_inventory_details_product_color_size";
DROP INDEX IF EXISTS "IDX_products_categoryCode";
EOF

    echo "📋 步骤 4: 清理默认分类（可选）..."
    read -p "是否删除默认分类 'DEFAULT'? (yes/no): " delete_default
    if [ "$delete_default" = "yes" ]; then
        psql -h $DB_HOST -U $DB_USER -d $DB_NAME << EOF
-- 删除默认分类
DELETE FROM product_categories WHERE code = 'DEFAULT';
EOF
        echo "✅ 默认分类已删除"
    else
        echo "⏭️  保留默认分类"
    fi
fi

# 验证回滚结果
echo ""
echo "🔍 验证回滚结果..."

# 检查字段是否已删除
REMAINING_FIELDS=$(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_name = 'inventory_details' 
    AND column_name IN ('skucode', 'sizecode', 'totalstock', 'actualstock')
" 2>/dev/null)

if [ $REMAINING_FIELDS -eq 0 ]; then
    echo "✅ 库存表新字段已删除"
else
    echo "⚠️  部分库存表字段仍然存在"
fi

# 检查产品表categoryCode字段
CATEGORY_FIELD_EXISTS=$(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_name = 'products' AND column_name = 'categoryCode'
" 2>/dev/null)

if [ $CATEGORY_FIELD_EXISTS -eq 0 ]; then
    echo "✅ 产品表categoryCode字段已删除"
else
    echo "⚠️  产品表categoryCode字段仍然存在"
fi

echo ""
echo "🎉 迁移回滚完成!"
echo "📋 回滚摘要:"
echo "  - 环境: $ENVIRONMENT"
echo "  - 完成时间: $(date)"
if [ -n "$BACKUP_FILE" ]; then
    echo "  - 恢复方式: 备份文件恢复"
    echo "  - 备份文件: $BACKUP_FILE"
else
    echo "  - 恢复方式: 手动回滚"
fi

echo ""
echo "📝 后续步骤:"
echo "  1. 重启应用服务"
echo "  2. 验证应用功能正常"
echo "  3. 检查数据完整性"
echo "  4. 如需要，可以重新执行迁移"
