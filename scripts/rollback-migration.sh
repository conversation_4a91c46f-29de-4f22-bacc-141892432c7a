#!/bin/bash

# ========================================
# 数据库迁移回滚脚本
# ========================================
# 用于回滚最近的数据库迁移

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示当前迁移状态
show_migration_status() {
    log_info "当前迁移状态:"
    npm run migration:show 2>/dev/null || {
        log_warning "无法显示迁移状态"
        return 1
    }
}

# 回滚迁移
rollback_migration() {
    log_warning "准备回滚最近的迁移..."
    
    # 确认操作
    read -p "确定要回滚最近的迁移吗？这可能会导致数据丢失！(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行回滚
    log_info "执行迁移回滚..."
    if npm run migration:revert; then
        log_success "迁移回滚成功"
    else
        log_error "迁移回滚失败"
        exit 1
    fi
}

# 从备份恢复数据库
restore_from_backup() {
    local backup_dir="./backups"
    
    if [ ! -d "$backup_dir" ]; then
        log_error "备份目录不存在: $backup_dir"
        exit 1
    fi
    
    # 查找最新的备份文件
    local latest_backup=$(ls -t "$backup_dir"/manager_backup_*.sql 2>/dev/null | head -n 1)
    
    if [ -z "$latest_backup" ]; then
        log_error "没有找到备份文件"
        exit 1
    fi
    
    log_warning "准备从备份恢复数据库: $latest_backup"
    
    # 确认操作
    read -p "确定要从备份恢复数据库吗？这将覆盖当前数据库！(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行恢复
    log_info "执行数据库恢复..."
    if psql -h localhost -U postgres -d manager < "$latest_backup"; then
        log_success "数据库恢复成功"
    else
        log_error "数据库恢复失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "数据库迁移回滚脚本"
    echo
    echo "用法:"
    echo "  $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -s, --status        显示迁移状态"
    echo "  -r, --rollback      回滚最近的迁移"
    echo "  -b, --backup        从最新备份恢复数据库"
    echo
    echo "示例:"
    echo "  $0 --status         # 查看迁移状态"
    echo "  $0 --rollback       # 回滚最近的迁移"
    echo "  $0 --backup         # 从备份恢复数据库"
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -s|--status)
            show_migration_status
            ;;
        -r|--rollback)
            show_migration_status
            rollback_migration
            show_migration_status
            ;;
        -b|--backup)
            restore_from_backup
            ;;
        "")
            log_info "数据库迁移回滚脚本"
            echo
            show_migration_status
            echo
            echo "可用操作:"
            echo "1. 回滚最近的迁移 (--rollback)"
            echo "2. 从备份恢复数据库 (--backup)"
            echo "3. 显示帮助信息 (--help)"
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
