#!/bin/bash

# ========================================
# 部署前检查脚本
# ========================================
# 检查数据库迁移和部署准备情况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查迁移文件
check_migrations() {
    log_info "检查数据库迁移文件..."
    
    if [ ! -d "src/migrations" ]; then
        log_error "迁移目录 src/migrations 不存在"
        return 1
    fi
    
    migration_count=$(find src/migrations -name "*.ts" | wc -l)
    log_info "发现 $migration_count 个迁移文件"
    
    # 检查最新的迁移文件
    latest_migration=$(find src/migrations -name "*.ts" | sort | tail -1)
    if [ -n "$latest_migration" ]; then
        log_info "最新迁移文件: $(basename "$latest_migration")"
    fi
    
    log_success "迁移文件检查完成"
}

# 检查环境配置
check_environment() {
    log_info "检查环境配置..."
    
    if [ ! -f ".env.production" ]; then
        log_error ".env.production 文件不存在"
        return 1
    fi
    
    # 检查关键配置
    if grep -q "DB_SYNCHRONIZE=false" .env.production; then
        log_success "生产环境正确配置为使用迁移"
    else
        log_warning "生产环境配置可能有问题，请检查 DB_SYNCHRONIZE 设置"
    fi
    
    log_success "环境配置检查完成"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 从 .env.production 读取数据库配置
    source .env.production
    
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USERNAME -d $DB_DATABASE -c "SELECT 1;" >/dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "无法连接到数据库，请检查配置和服务状态"
        return 1
    fi
}

# 检查构建状态
check_build() {
    log_info "检查项目构建状态..."
    
    if [ ! -d "dist" ]; then
        log_warning "dist 目录不存在，需要先构建项目"
        log_info "运行: pnpm run build"
        return 1
    fi
    
    if [ ! -f "dist/main.js" ]; then
        log_warning "主文件 dist/main.js 不存在，需要重新构建"
        return 1
    fi
    
    log_success "项目构建检查完成"
}

# 检查依赖
check_dependencies() {
    log_info "检查项目依赖..."
    
    if [ ! -f "package.json" ]; then
        log_error "package.json 文件不存在"
        return 1
    fi
    
    if [ ! -d "node_modules" ]; then
        log_warning "node_modules 目录不存在，需要安装依赖"
        log_info "运行: pnpm install"
        return 1
    fi
    
    log_success "依赖检查完成"
}

# 主检查函数
main() {
    log_info "开始部署前检查..."
    echo
    
    local errors=0
    
    check_dependencies || ((errors++))
    echo
    
    check_environment || ((errors++))
    echo
    
    check_migrations || ((errors++))
    echo
    
    check_database || ((errors++))
    echo
    
    check_build || ((errors++))
    echo
    
    if [ $errors -eq 0 ]; then
        log_success "所有检查通过，可以开始部署！"
        echo
        echo "=========================================="
        echo "🚀 准备部署"
        echo "=========================================="
        echo "运行部署命令: ./deploy-production.sh"
        echo "=========================================="
    else
        log_error "发现 $errors 个问题，请修复后再部署"
        exit 1
    fi
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
