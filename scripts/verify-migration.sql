-- 数据库迁移验证脚本
-- 在执行迁移后运行此脚本验证结果

\echo '🔍 开始验证数据库迁移结果...'
\echo ''

-- 1. 检查 expenses/incomes 表结构更新
\echo '📋 检查 expenses 和 incomes 表结构...'
SELECT
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
    AND table_name IN ('expenses', 'expense_details', 'incomes', 'income_details')
    AND column_name = 'createDate'
ORDER BY table_name, column_name;

\echo ''

-- 2. 检查是否还存在旧的时间字段
\echo '📋 检查是否还存在旧的时间字段...'
SELECT
    table_name,
    column_name
FROM information_schema.columns
WHERE table_schema = 'public'
    AND table_name IN ('expenses', 'expense_details', 'incomes', 'income_details')
    AND column_name IN ('created_at', 'updated_at', 'deleted_at');

\echo ''

-- 3. 检查库存相关表
\echo '📋 检查库存相关表...'
SELECT
    schemaname,
    tablename,
    tableowner
FROM pg_tables
WHERE tablename IN ('inventory_details', 'inventory_transactions')
ORDER BY tablename;

\echo ''

-- 4. 检查数据完整性
\echo '📋 检查数据完整性...'
SELECT
    'expenses' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN "createDate" IS NOT NULL THEN 1 END) as records_with_create_date
FROM expenses
WHERE "isDeleted" = false

UNION ALL

SELECT
    'expense_details' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN "createDate" IS NOT NULL THEN 1 END) as records_with_create_date
FROM expense_details
WHERE "isDeleted" = false

UNION ALL

SELECT
    'incomes' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN "createDate" IS NOT NULL THEN 1 END) as records_with_create_date
FROM incomes
WHERE "isDeleted" = false

UNION ALL

SELECT
    'income_details' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN "createDate" IS NOT NULL THEN 1 END) as records_with_create_date
FROM income_details
WHERE "isDeleted" = false;

\echo ''

-- 5. 检查表结构
\echo '📋 检查 inventory_details 表结构...'
\d inventory_details;

\echo ''
\echo '📋 检查 inventory_transactions 表结构...'
\d inventory_transactions;

\echo ''

-- 3. 检查索引
\echo '📋 检查索引...'
SELECT 
    indexname,
    tablename,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('inventory_details', 'inventory_transactions')
ORDER BY tablename, indexname;

\echo ''

-- 4. 检查外键约束
\echo '📋 检查外键约束...'
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('inventory_details', 'inventory_transactions');

\echo ''

-- 5. 检查库存记录生成情况
\echo '📋 检查库存记录生成情况...'
SELECT 
    COUNT(*) as total_inventory_records
FROM inventory_details 
WHERE "isDeleted" = false;

\echo ''

-- 6. 检查商品和库存的关联情况
\echo '📋 检查商品和库存关联情况...'
SELECT 
    p.code as product_code,
    p.name as product_name,
    COUNT(i.id) as inventory_count,
    COUNT(DISTINCT i."colorCode") as color_count,
    COUNT(DISTINCT i.size) as size_count
FROM products p 
LEFT JOIN inventory_details i ON p.code = i."productCode" AND i."isDeleted" = false
WHERE p."isDeleted" = false 
GROUP BY p.code, p.name 
ORDER BY inventory_count DESC
LIMIT 10;

\echo ''

-- 7. 检查颜色尺寸组合的完整性
\echo '📋 检查颜色尺寸组合完整性...'
WITH product_combinations AS (
    SELECT 
        p.code,
        p.name,
        jsonb_array_elements(p."colorSizeCombinations") as combo
    FROM products p 
    WHERE p."isDeleted" = false 
        AND p."colorSizeCombinations" IS NOT NULL
        AND jsonb_array_length(p."colorSizeCombinations") > 0
),
expected_inventory AS (
    SELECT 
        pc.code,
        pc.combo->>'colorCode' as color_code,
        jsonb_array_elements_text(pc.combo->'sizes') as size
    FROM product_combinations pc
),
actual_inventory AS (
    SELECT 
        "productCode" as code,
        "colorCode" as color_code,
        size
    FROM inventory_details 
    WHERE "isDeleted" = false
)
SELECT 
    'Missing inventory records' as check_type,
    COUNT(*) as count
FROM expected_inventory ei
LEFT JOIN actual_inventory ai ON ei.code = ai.code 
    AND ei.color_code = ai.color_code 
    AND ei.size = ai.size
WHERE ai.code IS NULL

UNION ALL

SELECT 
    'Extra inventory records' as check_type,
    COUNT(*) as count
FROM actual_inventory ai
LEFT JOIN expected_inventory ei ON ai.code = ei.code 
    AND ai.color_code = ei.color_code 
    AND ai.size = ei.size
WHERE ei.code IS NULL;

\echo ''

-- 8. 检查数据类型和约束
\echo '📋 检查数据类型和约束...'
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'inventory_details'
ORDER BY ordinal_position;

\echo ''

-- 9. 检查迁移历史
\echo '📋 检查迁移历史...'
SELECT 
    id,
    timestamp,
    name
FROM migrations 
ORDER BY timestamp DESC
LIMIT 5;

\echo ''

-- 10. 性能检查 - 查询计划
\echo '📋 检查查询性能...'
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    i.*,
    p.name as product_name,
    c.name as color_name
FROM inventory_details i
JOIN products p ON i."productCode" = p.code
JOIN colors c ON i."colorCode" = c.code
WHERE i."isDeleted" = false
LIMIT 10;

\echo ''
\echo '✅ 数据库迁移验证完成！'
\echo ''
\echo '📝 验证要点：'
\echo '1. 确保所有表和索引都已创建'
\echo '2. 确保库存记录数量合理'
\echo '3. 确保没有缺失或多余的库存记录'
\echo '4. 确保外键约束正常工作'
\echo '5. 确保查询性能可接受'
