#!/bin/bash

# 简化版 SKUs 修复脚本
# 专门用于修复 SKUs 表缺失问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_info "=========================================="
echo_info "SKUs 表修复脚本"
echo_info "=========================================="

# 1. 检查 SKUs 表是否存在
echo_info "检查 SKUs 表是否存在..."
SKUS_TABLE_EXISTS=$(sudo -u postgres psql -d manager -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'skus');" | tr -d ' ')

if [ "$SKUS_TABLE_EXISTS" = "t" ]; then
    echo_success "SKUs 表已存在"
    
    # 检查表结构
    COLUMN_COUNT=$(sudo -u postgres psql -d manager -t -c "SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'skus';" | tr -d ' ')
    echo_info "表结构: $COLUMN_COUNT 列"
    
    # 检查数据量
    ROW_COUNT=$(sudo -u postgres psql -d manager -t -c "SELECT COUNT(*) FROM skus WHERE \"isDeleted\" = false;" | tr -d ' ')
    echo_info "数据量: $ROW_COUNT 条记录"
    
    echo_success "SKUs 表状态正常，无需修复"
    exit 0
else
    echo_warn "SKUs 表不存在，开始创建..."
fi

# 2. 直接使用 SQL 创建 SKUs 表
echo_info "使用 SQL 脚本创建 SKUs 表..."

sudo -u postgres psql -d manager << 'EOF'
-- 创建 SKUs 表
CREATE TABLE IF NOT EXISTS skus (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    "manufacturerCode" VARCHAR(100),
    "brandCode" VARCHAR(50) NOT NULL,
    "supplierCode" VARCHAR(50) NOT NULL,
    "categoryCode" VARCHAR(50) NOT NULL,
    "colorCode" VARCHAR(50) NOT NULL,
    "craftDescription" VARCHAR(500),
    "clothingCost" DECIMAL(10,2),
    "retailPrice" DECIMAL(10,2),
    "preOrderPrice" DECIMAL(10,2),
    "restockPrice" DECIMAL(10,2),
    "spotPrice" DECIMAL(10,2),
    accessories JSONB,
    images JSONB,
    "isDeleted" BOOLEAN DEFAULT FALSE,
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE skus IS 'SKU（库存单位）表';
COMMENT ON COLUMN skus.code IS 'SKU编码（自动生成，唯一）';
COMMENT ON COLUMN skus.name IS '商品名称';
COMMENT ON COLUMN skus."brandCode" IS '品牌编码';
COMMENT ON COLUMN skus."supplierCode" IS '供应商编码';
COMMENT ON COLUMN skus."categoryCode" IS '商品分类编码';
COMMENT ON COLUMN skus."colorCode" IS '颜色编码';

-- 创建索引
CREATE INDEX IF NOT EXISTS "IDX_skus_code" ON skus(code);
CREATE INDEX IF NOT EXISTS "IDX_skus_brand_code" ON skus("brandCode");
CREATE INDEX IF NOT EXISTS "IDX_skus_supplier_code" ON skus("supplierCode");
CREATE INDEX IF NOT EXISTS "IDX_skus_category_code" ON skus("categoryCode");
CREATE INDEX IF NOT EXISTS "IDX_skus_color_code" ON skus("colorCode");
CREATE INDEX IF NOT EXISTS "IDX_skus_is_deleted" ON skus("isDeleted");

-- 为 JSONB 字段创建 GIN 索引
CREATE INDEX IF NOT EXISTS "IDX_skus_accessories_gin" ON skus USING gin(accessories);
CREATE INDEX IF NOT EXISTS "IDX_skus_images_gin" ON skus USING gin(images);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_skus_updated_at ON skus;
CREATE TRIGGER update_skus_updated_at 
    BEFORE UPDATE ON skus 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

\echo 'SKUs table created successfully'
EOF

# 3. 验证表创建
echo_info "验证 SKUs 表创建..."
SKUS_TABLE_EXISTS_AFTER=$(sudo -u postgres psql -d manager -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'skus');" | tr -d ' ')

if [ "$SKUS_TABLE_EXISTS_AFTER" = "t" ]; then
    echo_success "SKUs 表创建成功"
    
    # 显示表结构
    echo_info "表结构:"
    sudo -u postgres psql -d manager -c "\d skus"
    
    # 显示索引
    echo_info "索引:"
    sudo -u postgres psql -d manager -c "SELECT indexname FROM pg_indexes WHERE tablename = 'skus';"
    
else
    echo_error "SKUs 表创建失败"
    exit 1
fi

# 4. 重启应用服务
echo_info "重启应用服务..."
if command -v pm2 >/dev/null 2>&1; then
    pm2 restart all
    echo_success "应用服务重启完成"
else
    echo_warn "未检测到 PM2，请手动重启应用"
fi

# 5. 测试 API
echo_info "等待应用启动..."
sleep 5

echo_info "测试 SKUs API..."
API_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000/skus" || echo "000")

if [ "$API_RESPONSE" = "200" ] || [ "$API_RESPONSE" = "401" ]; then
    echo_success "SKUs API 响应正常 (HTTP $API_RESPONSE)"
else
    echo_warn "SKUs API 响应异常 (HTTP $API_RESPONSE)"
    echo_info "请检查应用日志: pm2 logs"
fi

echo_info "=========================================="
echo_success "SKUs 表修复完成！"
echo_info "=========================================="
echo_info "如果仍有问题，请检查："
echo_info "1. 应用日志: pm2 logs"
echo_info "2. 依赖表是否存在: brands, suppliers, product_categories, colors, accessories"
echo_info "3. 数据库权限是否正确"
echo_info "=========================================="
