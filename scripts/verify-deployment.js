#!/usr/bin/env node

/**
 * 部署验证脚本
 * 用于验证数据库迁移和应用部署是否成功
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始部署验证...\n');

// 验证步骤
const verificationSteps = [
  {
    name: '检查Node.js版本',
    check: () => {
      const version = process.version;
      const majorVersion = parseInt(version.slice(1).split('.')[0]);
      if (majorVersion < 16) {
        throw new Error(`Node.js版本过低: ${version}，需要 >= 16.x`);
      }
      return `✅ Node.js版本: ${version}`;
    }
  },
  {
    name: '检查项目构建',
    check: () => {
      if (!fs.existsSync('dist')) {
        throw new Error('dist目录不存在，请先运行 npm run build');
      }
      if (!fs.existsSync('dist/main.js')) {
        throw new Error('主文件不存在，构建可能失败');
      }
      return '✅ 项目构建文件存在';
    }
  },
  {
    name: '检查环境配置',
    check: () => {
      const envFiles = ['.env', '.env.production', '.env.local'].filter(file => 
        fs.existsSync(file)
      );
      if (envFiles.length === 0) {
        throw new Error('未找到环境配置文件，请创建 .env 文件');
      }
      return `✅ 环境配置文件: ${envFiles.join(', ')}`;
    }
  },
  {
    name: '检查数据库配置',
    check: () => {
      const configPath = 'src/config/data-source.ts';
      if (!fs.existsSync(configPath)) {
        throw new Error('数据库配置文件不存在');
      }
      return '✅ 数据库配置文件存在';
    }
  },
  {
    name: '检查迁移文件',
    check: () => {
      const migrationDir = 'src/database/migrations';
      if (!fs.existsSync(migrationDir)) {
        throw new Error('迁移目录不存在');
      }
      
      const migrationFiles = fs.readdirSync(migrationDir)
        .filter(file => file.endsWith('.ts'))
        .sort();
      
      const expectedMigrations = [
        '1704067200000-CreateInventoryTables.ts',
        '1704067300000-MigrateProductsToInventory.ts'
      ];
      
      for (const expected of expectedMigrations) {
        if (!migrationFiles.includes(expected)) {
          throw new Error(`缺少迁移文件: ${expected}`);
        }
      }
      
      return `✅ 迁移文件: ${migrationFiles.length}个`;
    }
  },
  {
    name: '检查依赖包',
    check: () => {
      if (!fs.existsSync('node_modules')) {
        throw new Error('依赖包未安装，请运行 npm install');
      }
      
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const requiredDeps = [
        '@nestjs/core',
        '@nestjs/typeorm',
        'typeorm',
        'pg',
        'exceljs'
      ];
      
      for (const dep of requiredDeps) {
        if (!packageJson.dependencies[dep] && !packageJson.devDependencies[dep]) {
          throw new Error(`缺少必要依赖: ${dep}`);
        }
      }
      
      return '✅ 核心依赖包已安装';
    }
  }
];

// 执行验证
let allPassed = true;

for (const step of verificationSteps) {
  try {
    console.log(`📋 ${step.name}...`);
    const result = step.check();
    console.log(`   ${result}\n`);
  } catch (error) {
    console.error(`   ❌ ${error.message}\n`);
    allPassed = false;
  }
}

// 数据库连接测试（可选）
console.log('📋 测试数据库连接...');
try {
  // 这里可以添加数据库连接测试
  console.log('   ⚠️  请手动验证数据库连接\n');
} catch (error) {
  console.error(`   ❌ 数据库连接失败: ${error.message}\n`);
  allPassed = false;
}

// 输出结果
console.log('=' * 50);
if (allPassed) {
  console.log('🎉 所有验证通过！可以进行部署。\n');
  console.log('📝 下一步操作：');
  console.log('1. 备份数据库: pg_dump -h localhost -U postgres -d manager > backup.sql');
  console.log('2. 运行迁移: npm run migration:run');
  console.log('3. 启动应用: npm start 或 pm2 start ecosystem.config.js');
  console.log('4. 验证接口: curl http://localhost:3000/health');
  process.exit(0);
} else {
  console.log('❌ 验证失败！请修复上述问题后重试。\n');
  console.log('📝 常见解决方案：');
  console.log('1. 运行 npm install 安装依赖');
  console.log('2. 运行 npm run build 构建项目');
  console.log('3. 创建 .env 文件并配置数据库连接');
  console.log('4. 确保PostgreSQL服务正在运行');
  process.exit(1);
}
