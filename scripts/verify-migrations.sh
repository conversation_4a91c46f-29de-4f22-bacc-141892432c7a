#!/bin/bash

# ========================================
# 数据库迁移验证脚本
# ========================================
# 用于验证数据库迁移是否成功执行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 数据库连接参数
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_USER=${DB_USERNAME:-postgres}
DB_NAME=${DB_DATABASE:-manager}

# 验证数据库连接
verify_connection() {
    log_info "验证数据库连接..."
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        exit 1
    fi
}

# 检查必要的表是否存在
verify_tables() {
    log_info "检查必要的表是否存在..."
    
    local required_tables=(
        "users"
        "companies" 
        "suppliers"
        "customers"
        "brands"
        "accessories"
        "products"
        "inventory"
        "memos"
        "migrations"
    )
    
    local missing_tables=()
    
    for table in "${required_tables[@]}"; do
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
           -c "SELECT 1 FROM information_schema.tables WHERE table_name='$table';" \
           | grep -q "1"; then
            log_success "表 '$table' 存在"
        else
            log_error "表 '$table' 不存在"
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -eq 0 ]; then
        log_success "所有必要的表都存在"
    else
        log_error "缺少以下表: ${missing_tables[*]}"
        exit 1
    fi
}

# 检查迁移状态
verify_migrations() {
    log_info "检查迁移状态..."
    
    # 检查迁移表是否存在
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
         -c "SELECT 1 FROM information_schema.tables WHERE table_name='migrations';" \
         | grep -q "1"; then
        log_error "迁移表不存在，可能迁移未正确执行"
        exit 1
    fi
    
    # 显示已执行的迁移
    log_info "已执行的迁移:"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
         -c "SELECT id, timestamp, name FROM migrations ORDER BY timestamp;" || true
    
    log_success "迁移状态检查完成"
}

# 验证 memos 表结构
verify_memos_table() {
    log_info "验证 memos 表结构..."
    
    local expected_columns=(
        "id:uuid"
        "title:character varying"
        "details:text"
        "image:character varying"
        "file:character varying"
        "isDeleted:boolean"
        "deletedAt:timestamp without time zone"
        "createdAt:timestamp without time zone"
        "updatedAt:timestamp without time zone"
    )
    
    log_info "memos 表的列信息:"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
         -c "SELECT column_name, data_type, is_nullable, column_default 
             FROM information_schema.columns 
             WHERE table_name = 'memos' 
             ORDER BY ordinal_position;" || true
    
    # 检查关键列是否存在
    for col_info in "${expected_columns[@]}"; do
        local col_name="${col_info%%:*}"
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
           -c "SELECT 1 FROM information_schema.columns 
               WHERE table_name='memos' AND column_name='$col_name';" \
           | grep -q "1"; then
            log_success "列 '$col_name' 存在"
        else
            log_error "列 '$col_name' 不存在"
        fi
    done
}

# 测试基本操作
test_basic_operations() {
    log_info "测试基本数据库操作..."
    
    # 测试插入
    local test_id=$(uuidgen 2>/dev/null || echo "test-$(date +%s)")
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
       -c "INSERT INTO memos (id, title, \"isDeleted\") VALUES ('$test_id', 'Test Memo', false);" >/dev/null 2>&1; then
        log_success "插入测试成功"
        
        # 测试查询
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
           -c "SELECT id FROM memos WHERE id='$test_id';" | grep -q "$test_id"; then
            log_success "查询测试成功"
        else
            log_error "查询测试失败"
        fi
        
        # 清理测试数据
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
             -c "DELETE FROM memos WHERE id='$test_id';" >/dev/null 2>&1 || true
        log_info "测试数据已清理"
    else
        log_error "插入测试失败"
    fi
}

# 主函数
main() {
    log_info "开始验证数据库迁移..."
    
    verify_connection
    verify_tables
    verify_migrations
    verify_memos_table
    test_basic_operations
    
    log_success "数据库迁移验证完成！"
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
