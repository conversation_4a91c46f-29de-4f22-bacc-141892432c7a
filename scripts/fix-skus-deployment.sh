#!/bin/bash

# SKUs 模块部署修复脚本
# 解决服务器上 SKUs 接口 500 错误问题

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 读取生产环境配置
load_env_config() {
    if [ -f ".env.production" ]; then
        echo_info "读取生产环境配置..."
        # 读取配置文件，忽略注释和空行
        while IFS='=' read -r key value; do
            # 跳过注释和空行
            [[ $key =~ ^[[:space:]]*# ]] && continue
            [[ -z $key ]] && continue

            # 移除值两边的引号
            value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')

            # 导出环境变量
            export "$key"="$value"
        done < .env.production
        echo_success "生产环境配置加载完成"
    else
        echo_warn "未找到 .env.production 文件，使用默认配置"
    fi
}

# 数据库连接参数（从环境变量读取）
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_USER=${DB_USERNAME:-"postgres"}
DB_NAME=${DB_DATABASE:-"manager"}
DB_PASSWORD=${DB_PASSWORD:-"54188"}

echo_info "开始修复 SKUs 模块部署问题..."
echo_info "数据库连接: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"

# 1. 检查数据库连接
check_database_connection() {
    echo_info "检查数据库连接..."
    
    if PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
        echo_success "数据库连接正常"
    else
        echo_error "数据库连接失败，请检查连接参数"
        exit 1
    fi
}

# 2. 检查 SKUs 表是否存在
check_skus_table() {
    echo_info "检查 SKUs 表是否存在..."
    
    local table_exists=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'skus');" | tr -d ' ')
    
    if [ "$table_exists" = "t" ]; then
        echo_success "SKUs 表已存在"
        return 0
    else
        echo_warn "SKUs 表不存在，需要创建"
        return 1
    fi
}

# 3. 检查依赖表是否存在
check_dependency_tables() {
    echo_info "检查依赖表是否存在..."
    
    local required_tables=("brands" "suppliers" "product_categories" "colors" "accessories")
    local missing_tables=()
    
    for table in "${required_tables[@]}"; do
        local table_exists=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" | tr -d ' ')
        
        if [ "$table_exists" = "t" ]; then
            echo_success "表 '$table' 存在"
        else
            echo_warn "表 '$table' 不存在"
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -eq 0 ]; then
        echo_success "所有依赖表都存在"
        return 0
    else
        echo_error "缺少以下依赖表: ${missing_tables[*]}"
        echo_warn "这可能导致 SKUs 接口出现关联查询错误"
        return 1
    fi
}

# 4. 构建项目
build_project() {
    echo_info "构建项目..."
    
    if [ ! -f "package.json" ]; then
        echo_error "package.json 文件不存在，请确保在项目根目录执行此脚本"
        exit 1
    fi
    
    # 安装依赖
    echo_info "安装依赖..."
    pnpm install
    
    # 构建项目
    echo_info "构建项目..."
    pnpm run build
    
    if [ -f "dist/main.js" ]; then
        echo_success "项目构建成功"
    else
        echo_error "项目构建失败"
        exit 1
    fi
}

# 5. 运行迁移
run_migrations() {
    echo_info "运行数据库迁移..."
    
    # 检查迁移文件是否存在
    if [ ! -f "src/migrations/1749365279200-CreateSkusTable.ts" ]; then
        echo_error "SKUs 表迁移文件不存在"
        exit 1
    fi
    
    # 运行迁移
    echo_info "执行迁移..."
    pnpm run migration:run
    
    echo_success "迁移执行完成"
}

# 6. 验证 SKUs 表创建
verify_skus_table() {
    echo_info "验证 SKUs 表创建..."
    
    # 检查表结构
    echo_info "检查表结构..."
    PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "\d skus"
    
    # 检查索引
    echo_info "检查索引..."
    PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "SELECT indexname, indexdef FROM pg_indexes WHERE tablename = 'skus';"
    
    echo_success "SKUs 表验证完成"
}

# 7. 重启应用服务
restart_application() {
    echo_info "重启应用服务..."
    
    # 检查是否使用 PM2
    if command -v pm2 >/dev/null 2>&1; then
        echo_info "使用 PM2 重启应用..."
        pm2 restart all
        echo_success "PM2 应用重启完成"
    else
        echo_warn "未检测到 PM2，请手动重启应用服务"
    fi
}

# 8. 测试 SKUs 接口
test_skus_api() {
    echo_info "测试 SKUs 接口..."
    
    # 等待应用启动
    sleep 5
    
    # 测试基础接口
    local api_url="http://localhost:8080/skus"
    echo_info "测试接口: $api_url"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$api_url" || echo "000")
    
    if [ "$response" = "200" ] || [ "$response" = "401" ]; then
        echo_success "SKUs 接口响应正常 (HTTP $response)"
    else
        echo_error "SKUs 接口仍然返回错误 (HTTP $response)"
        echo_warn "请检查应用日志获取详细错误信息"
    fi
}

# 主执行流程
main() {
    echo_info "=========================================="
    echo_info "SKUs 模块部署修复脚本"
    echo_info "=========================================="

    # 加载环境配置
    load_env_config

    # 重新设置数据库连接参数（在加载配置后）
    DB_HOST=${DB_HOST:-"localhost"}
    DB_PORT=${DB_PORT:-"5432"}
    DB_USER=${DB_USERNAME:-"postgres"}
    DB_NAME=${DB_DATABASE:-"manager"}
    DB_PASSWORD=${DB_PASSWORD:-"54188"}

    echo_info "使用数据库连接: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"

    # 检查数据库连接
    check_database_connection
    
    # 检查依赖表
    check_dependency_tables
    
    # 检查 SKUs 表
    if ! check_skus_table; then
        echo_info "需要创建 SKUs 表..."
        
        # 构建项目
        build_project
        
        # 运行迁移
        run_migrations
        
        # 验证表创建
        verify_skus_table
        
        # 重启应用
        restart_application
        
        # 测试接口
        test_skus_api
    else
        echo_info "SKUs 表已存在，检查其他可能的问题..."
        
        # 重启应用试试
        restart_application
        
        # 测试接口
        test_skus_api
    fi
    
    echo_info "=========================================="
    echo_success "SKUs 模块修复完成！"
    echo_info "=========================================="
    echo_info "如果问题仍然存在，请检查："
    echo_info "1. 应用日志: pm2 logs"
    echo_info "2. 数据库日志"
    echo_info "3. 网络连接"
    echo_info "=========================================="
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
