-- 检查Product表结构脚本

\echo '🔍 检查Product表结构...'

-- 1. 检查表是否存在
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename = 'products';

\echo ''

-- 2. 检查表结构
\d products;

\echo ''

-- 3. 检查colorSizeCombinations字段的数据结构
\echo '📋 检查colorSizeCombinations字段数据...'
SELECT 
    code,
    name,
    "colorSizeCombinations"
FROM products 
WHERE "isDeleted" = false 
LIMIT 3;

\echo ''

-- 4. 检查是否有颜色级别的配置
\echo '📋 检查颜色级别配置...'
SELECT 
    code,
    name,
    combo->>'colorCode' as color_code,
    combo->'sizes' as sizes,
    combo->'images' as images,
    combo->'accessories' as accessories,
    combo->'priceAdjustments' as price_adjustments
FROM products p,
jsonb_array_elements(p."colorSizeCombinations") AS combo
WHERE p."isDeleted" = false 
LIMIT 5;

\echo ''

-- 5. 统计颜色配置完整性
\echo '📊 统计颜色配置完整性...'
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN "colorSizeCombinations" IS NOT NULL THEN 1 END) as has_color_combinations,
    COUNT(CASE WHEN jsonb_array_length("colorSizeCombinations") > 0 THEN 1 END) as has_valid_combinations
FROM products 
WHERE "isDeleted" = false;

\echo ''

-- 6. 检查缺少颜色配置的商品
\echo '⚠️  检查缺少完整颜色配置的商品...'
WITH color_analysis AS (
    SELECT 
        p.code,
        p.name,
        combo->>'colorCode' as color_code,
        CASE WHEN combo->'images' IS NOT NULL AND jsonb_array_length(combo->'images') > 0 THEN true ELSE false END as has_images,
        CASE WHEN combo->'accessories' IS NOT NULL AND jsonb_array_length(combo->'accessories') > 0 THEN true ELSE false END as has_accessories,
        CASE WHEN combo->'priceAdjustments' IS NOT NULL THEN true ELSE false END as has_price_adjustments
    FROM products p,
    jsonb_array_elements(p."colorSizeCombinations") AS combo
    WHERE p."isDeleted" = false
)
SELECT 
    code,
    name,
    color_code,
    CASE WHEN has_images THEN '✅' ELSE '❌' END as images_status,
    CASE WHEN has_accessories THEN '✅' ELSE '❌' END as accessories_status,
    CASE WHEN has_price_adjustments THEN '✅' ELSE '❌' END as price_adjustments_status
FROM color_analysis
ORDER BY code, color_code;

\echo ''
\echo '✅ Product表结构检查完成！'
