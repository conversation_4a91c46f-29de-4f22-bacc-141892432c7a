-- 产品和库存管理系统迁移验证脚本
-- 用于验证产品重构迁移是否成功执行

\echo '🔍 开始验证产品和库存管理系统迁移结果...'
\echo ''

-- 1. 检查产品表的categoryCode字段
\echo '📋 1. 检查产品表结构:'
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'products' AND column_name = 'categoryCode'
        ) THEN '✅ categoryCode字段已添加'
        ELSE '❌ categoryCode字段缺失'
    END as products_categorycode_status;

-- 2. 检查库存表的新字段
\echo ''
\echo '📋 2. 检查库存表新字段:'
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    CASE 
        WHEN column_name IN ('skucode', 'sizecode', 'totalstock', 'actualstock', 'reservedstock', 'damagedstock') 
        THEN '✅ 核心字段'
        WHEN column_name IN ('purchasingstock', 'needpurchasestock', 'safetystock', 'minstock', 'maxstock')
        THEN '✅ 预警字段'
        WHEN column_name IN ('avgcost', 'latestcost', 'isactive', 'warehouselocation')
        THEN '✅ 业务字段'
        WHEN column_name IN ('totalinbound', 'totaloutbound', 'lastinbounddate', 'lastoutbounddate')
        THEN '✅ 统计字段'
        ELSE '📝 其他字段'
    END as field_category
FROM information_schema.columns 
WHERE table_name = 'inventory_details' 
AND column_name IN (
    'skucode', 'sizecode', 'totalstock', 'actualstock', 'reservedstock', 'damagedstock',
    'purchasingstock', 'needpurchasestock', 'safetystock', 'minstock', 'maxstock',
    'avgcost', 'latestcost', 'isactive', 'warehouselocation',
    'totalinbound', 'totaloutbound', 'lastinbounddate', 'lastoutbounddate'
)
ORDER BY 
    CASE 
        WHEN column_name IN ('skucode', 'sizecode', 'totalstock', 'actualstock') THEN 1
        WHEN column_name IN ('reservedstock', 'damagedstock', 'purchasingstock') THEN 2
        ELSE 3
    END,
    column_name;

-- 3. 检查数据迁移情况
\echo ''
\echo '📋 3. 检查数据迁移情况:'

-- 3.1 SKU编码生成情况
SELECT 
    COUNT(*) as total_records,
    COUNT(skucode) as records_with_sku,
    COUNT(sizecode) as records_with_sizecode,
    CASE 
        WHEN COUNT(*) = COUNT(skucode) AND COUNT(*) = COUNT(sizecode) 
        THEN '✅ 所有记录已迁移'
        ELSE '⚠️ 部分记录未迁移'
    END as migration_status
FROM inventory_details;

-- 3.2 库存数据迁移情况
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN totalstock > 0 OR actualstock > 0 THEN 1 END) as records_with_stock,
    AVG(COALESCE(totalstock, 0)) as avg_total_stock,
    AVG(COALESCE(actualstock, 0)) as avg_actual_stock
FROM inventory_details;

-- 4. 检查默认分类
\echo ''
\echo '📋 4. 检查默认分类:'
SELECT 
    code,
    name,
    sizes,
    "isDeleted",
    "createdAt",
    CASE 
        WHEN code = 'DEFAULT' AND "isDeleted" = false THEN '✅ 默认分类正常'
        ELSE '⚠️ 默认分类异常'
    END as status
FROM product_categories 
WHERE code = 'DEFAULT';

-- 5. 检查产品分类关联
\echo ''
\echo '📋 5. 检查产品分类关联:'
SELECT 
    "categoryCode",
    COUNT(*) as product_count,
    CASE 
        WHEN "categoryCode" = 'DEFAULT' THEN '✅ 默认分类'
        WHEN "categoryCode" IS NULL THEN '❌ 未设置分类'
        ELSE '✅ 自定义分类'
    END as category_status
FROM products 
WHERE "isDeleted" = false
GROUP BY "categoryCode"
ORDER BY product_count DESC;

-- 6. 数据完整性检查
\echo ''
\echo '📋 6. 数据完整性检查:'

-- 6.1 检查SKU编码格式
SELECT 
    COUNT(*) as total_skus,
    COUNT(CASE WHEN skucode ~ '^[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$' THEN 1 END) as valid_format_skus,
    COUNT(CASE WHEN skucode IS NULL OR skucode = '' THEN 1 END) as empty_skus,
    CASE 
        WHEN COUNT(CASE WHEN skucode ~ '^[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$' THEN 1 END) = COUNT(*) 
        THEN '✅ SKU格式正确'
        ELSE '⚠️ 部分SKU格式异常'
    END as sku_format_status
FROM inventory_details;

-- 6.2 检查重复SKU
SELECT 
    COUNT(*) as total_skus,
    COUNT(DISTINCT skucode) as unique_skus,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT skucode) THEN '✅ 无重复SKU'
        ELSE '❌ 存在重复SKU'
    END as duplicate_status
FROM inventory_details 
WHERE skucode IS NOT NULL;

-- 7. 迁移总结
\echo ''
\echo '📋 7. 迁移总结:'
SELECT 
    '产品和库存管理系统重构' as migration_name,
    CURRENT_TIMESTAMP as verification_time,
    (SELECT COUNT(*) FROM products WHERE "isDeleted" = false) as total_products,
    (SELECT COUNT(*) FROM inventory_details WHERE "isDeleted" = false) as total_inventory_records,
    (SELECT COUNT(*) FROM inventory_details WHERE skucode IS NOT NULL) as migrated_records,
    '✅ 迁移验证完成' as status;

\echo ''
\echo '🎉 产品和库存管理系统迁移验证完成!'
\echo ''
\echo '📝 如发现问题，请检查:'
\echo '   1. 迁移脚本是否完整执行'
\echo '   2. 数据备份是否可用'
\echo '   3. 应用代码是否与数据库结构匹配'
