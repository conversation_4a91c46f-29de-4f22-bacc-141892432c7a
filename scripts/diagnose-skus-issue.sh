#!/bin/bash

# SKUs 接口问题诊断脚本
# 快速检查 SKUs 模块相关的所有可能问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

echo_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 读取生产环境配置
if [ -f ".env.production" ]; then
    echo_info "读取生产环境配置..."
    # 读取配置文件，忽略注释和空行
    while IFS='=' read -r key value; do
        # 跳过注释和空行
        [[ $key =~ ^[[:space:]]*# ]] && continue
        [[ -z $key ]] && continue

        # 移除值两边的引号
        value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')

        # 导出环境变量
        export "$key"="$value"
    done < .env.production
fi

# 数据库连接参数（从环境变量读取）
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_USER=${DB_USERNAME:-"postgres"}
DB_NAME=${DB_DATABASE:-"manager"}
DB_PASSWORD=${DB_PASSWORD:-"54188"}

echo_info "=========================================="
echo_info "SKUs 接口问题诊断报告"
echo_info "=========================================="
echo_info "数据库: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
echo_info "时间: $(date)"
echo_info "=========================================="

# 1. 检查数据库连接
echo_info "1. 检查数据库连接..."
if PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" >/dev/null 2>&1; then
    echo_success "数据库连接正常"
    DB_VERSION=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT version();" | head -1 | xargs)
    echo_info "   版本: $DB_VERSION"
else
    echo_error "数据库连接失败"
    exit 1
fi

# 2. 检查 SKUs 表
echo_info "2. 检查 SKUs 表..."
SKUS_TABLE_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
    -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'skus');" | tr -d ' ')

if [ "$SKUS_TABLE_EXISTS" = "t" ]; then
    echo_success "SKUs 表存在"
    
    # 检查表结构
    echo_info "   检查表结构..."
    COLUMN_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'skus';" | tr -d ' ')
    echo_info "   列数: $COLUMN_COUNT"
    
    # 检查数据量
    ROW_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM skus WHERE \"isDeleted\" = false;" | tr -d ' ')
    echo_info "   数据量: $ROW_COUNT 条记录"
    
else
    echo_error "SKUs 表不存在！这是导致 500 错误的主要原因"
fi

# 3. 检查依赖表
echo_info "3. 检查依赖表..."
DEPENDENCY_TABLES=("brands" "suppliers" "product_categories" "colors" "accessories")
MISSING_TABLES=()

for table in "${DEPENDENCY_TABLES[@]}"; do
    TABLE_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" | tr -d ' ')
    
    if [ "$TABLE_EXISTS" = "t" ]; then
        ROW_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            -t -c "SELECT COUNT(*) FROM $table WHERE \"isDeleted\" = false;" 2>/dev/null | tr -d ' ' || echo "0")
        echo_success "表 '$table' 存在 ($ROW_COUNT 条记录)"
    else
        echo_error "表 '$table' 不存在"
        MISSING_TABLES+=("$table")
    fi
done

# 4. 检查迁移状态
echo_info "4. 检查迁移状态..."
MIGRATIONS_TABLE_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
    -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'migrations');" | tr -d ' ')

if [ "$MIGRATIONS_TABLE_EXISTS" = "t" ]; then
    echo_success "迁移表存在"
    MIGRATION_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM migrations;" | tr -d ' ')
    echo_info "   已执行迁移数: $MIGRATION_COUNT"
    
    # 检查是否有 SKUs 相关迁移
    SKUS_MIGRATION=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM migrations WHERE name LIKE '%Skus%' OR name LIKE '%SKU%';" | tr -d ' ')
    
    if [ "$SKUS_MIGRATION" -gt 0 ]; then
        echo_success "找到 SKUs 相关迁移"
    else
        echo_warn "未找到 SKUs 相关迁移"
    fi
else
    echo_warn "迁移表不存在"
fi

# 5. 检查应用状态
echo_info "5. 检查应用状态..."
if command -v pm2 >/dev/null 2>&1; then
    echo_info "   PM2 进程状态:"
    pm2 list | grep -E "(name|status)" || echo_warn "   未找到运行中的 PM2 进程"
else
    echo_warn "   未安装 PM2"
fi

# 6. 检查端口占用
echo_info "6. 检查端口占用..."
if command -v lsof >/dev/null 2>&1; then
    PORT_8080=$(lsof -i :8080 | grep LISTEN || echo "")
    if [ -n "$PORT_8080" ]; then
        echo_success "端口 8080 被占用"
        echo_info "   $PORT_8080"
    else
        echo_warn "端口 8080 未被占用"
    fi
else
    echo_warn "   lsof 命令不可用"
fi

# 7. 检查项目文件
echo_info "7. 检查项目文件..."
if [ -f "dist/main.js" ]; then
    echo_success "构建文件存在"
    BUILD_TIME=$(stat -c %y "dist/main.js" 2>/dev/null || stat -f %Sm "dist/main.js" 2>/dev/null || echo "未知")
    echo_info "   构建时间: $BUILD_TIME"
else
    echo_error "构建文件不存在"
fi

if [ -f "src/skus/skus.module.ts" ]; then
    echo_success "SKUs 模块文件存在"
else
    echo_error "SKUs 模块文件不存在"
fi

# 8. 生成诊断总结
echo_info "=========================================="
echo_info "诊断总结"
echo_info "=========================================="

if [ "$SKUS_TABLE_EXISTS" != "t" ]; then
    echo_error "主要问题: SKUs 表不存在"
    echo_info "解决方案:"
    echo_info "1. 运行迁移: pnpm run migration:run"
    echo_info "2. 或执行 SQL: psql -f scripts/create-skus-table.sql"
    echo_info "3. 或运行修复脚本: bash scripts/fix-skus-deployment.sh"
elif [ ${#MISSING_TABLES[@]} -gt 0 ]; then
    echo_warn "依赖表缺失: ${MISSING_TABLES[*]}"
    echo_info "这可能导致关联查询失败"
else
    echo_success "数据库表结构正常"
    echo_info "问题可能在于:"
    echo_info "1. 应用未正确启动"
    echo_info "2. 代码同步问题"
    echo_info "3. 环境配置问题"
fi

echo_info "=========================================="
echo_info "建议的修复步骤:"
echo_info "1. bash scripts/fix-skus-deployment.sh"
echo_info "2. 检查应用日志: pm2 logs"
echo_info "3. 重启应用: pm2 restart all"
echo_info "=========================================="
